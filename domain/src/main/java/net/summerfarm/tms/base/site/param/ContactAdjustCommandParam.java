package net.summerfarm.tms.base.site.param;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description:点位调整命令参数
 * date: 2024/1/30 15:13
 *
 * <AUTHOR>
 */
@Data
public class ContactAdjustCommandParam {

    /**
     * 用户id
     */
    private Integer mId;

    /**
     * 地址表id
     */
    private Integer contactId;

    /**
     * 新poi
     */
    private String newPoi;

    /**
     * 状态 0 待审核, 1 审核通过 ,2 拒绝重新交, 3 审核失败
     */
    private Integer status;

    /**
     * 省
     */
    private String newProvince;

    /**
     * 市
     */
    private String newCity;

    /**
     * 区域
     */
    private String newArea;

    /**
     * 详细地址
     */
    private String newAddress;

    /**
     * 门牌号
     */
    private String newHouseNumber;

    /**
     * 添加时间
     */
    private LocalDate addTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    public void create(Integer mId, Integer contactId) {
        this.mId = mId;
        this.contactId = contactId;
        this.addTime = LocalDate.now();
        this.updateTime = LocalDateTime.now();
    }
}
