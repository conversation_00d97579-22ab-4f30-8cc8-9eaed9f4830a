package net.summerfarm.tms.base.distance;

import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.base.distance.entity.OutDistanceEntity;
import net.summerfarm.tms.enums.TmsOutDistanceEnums;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/7/17 15:27<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class OutDistanceDomainService {

    private final OutDistanceRepository outDistanceRepository;

    /**
     * 保存
     * @param outDistanceEntityReq 城配仓距离实体
     */
    public void save(OutDistanceEntity outDistanceEntityReq) {
        OutDistanceEntity outDistanceEntity = outDistanceRepository.queryByStoreNo(outDistanceEntityReq.getStoreNo());
        if(outDistanceEntity != null){
            throw new TmsRuntimeException("存在此城配仓数据");
        }
        outDistanceRepository.save(outDistanceEntityReq);
    }

    /**
     * 更新
     * @param outDistanceEntityReq 更新实体
     */
    public void update(OutDistanceEntity outDistanceEntityReq) {
        if(outDistanceEntityReq == null){
            return;
        }
        OutDistanceEntity outDistanceEntityDb = outDistanceRepository.queryById(outDistanceEntityReq.getId());
        if(outDistanceEntityDb == null){
            throw new TmsRuntimeException("数据不存在");
        }
        outDistanceEntityDb.setAdminId(outDistanceEntityReq.getAdminId());
        outDistanceEntityDb.setState(outDistanceEntityReq.getState());
        outDistanceEntityDb.setOutDistance(outDistanceEntityReq.getOutDistance());
        //更新
        outDistanceRepository.update(outDistanceEntityDb);
    }
}
