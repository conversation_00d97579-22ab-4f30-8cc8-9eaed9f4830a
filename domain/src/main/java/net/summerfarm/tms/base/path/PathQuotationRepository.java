package net.summerfarm.tms.base.path;

import net.summerfarm.tms.base.path.entity.PathQuotationEntity;

import java.util.List;

/**
 * 路由报价信息
 * <AUTHOR>
 * @Date 2023-05-15
 **/
public interface PathQuotationRepository {

    /**
     * 根据路由id删除报价信息
     * @param pathId 路由id
     */
    void removeByPathId(Long pathId);

    /**
     * 根据路由查询报价信息
     * @param pathId
     * @return
     */
    List<PathQuotationEntity> queryByPathId(Long pathId);
}
