package net.summerfarm.tms.base.carrier.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: 承运商发票信息
 * date: 2023/10/12 15:14<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CarrierInvoiceEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * 承运商id
     */
    private Long carrierId;

    /**
     * 发票抬头
     */
    private String invoiceHead;

    /**
     * 税号
     */
    private String taxNo;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

}
