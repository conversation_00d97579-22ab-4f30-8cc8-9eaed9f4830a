package net.summerfarm.tms.base.distance.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/7/14 14:47<br/>
 *
 * <AUTHOR> />
 */
@Data
public class OutDistanceEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 签收距离（公里）
     */
    private BigDecimal outDistance;

    /**
     * 状态0正常 1暂停
     */
    private Integer state;

    /**
     * 操作人id
     */
    private Integer adminId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 创建人名称
     */
    private String adminName;

    /**
     * 城配仓编号
     */
    private String storeName;
}
