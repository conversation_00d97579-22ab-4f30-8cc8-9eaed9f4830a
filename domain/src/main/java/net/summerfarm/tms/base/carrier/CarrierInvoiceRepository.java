package net.summerfarm.tms.base.carrier;

import net.summerfarm.tms.base.carrier.entity.CarrierInvoiceEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/10/12 15:18<br/>
 *
 * <AUTHOR> />
 */
public interface CarrierInvoiceRepository {

    /**
     * 通过承运商id查发票属性
     *
     * @param carrierIdList
     * @return
     */
    List<CarrierInvoiceEntity> queryByCarrierIdList(List<Long> carrierIdList);

}
