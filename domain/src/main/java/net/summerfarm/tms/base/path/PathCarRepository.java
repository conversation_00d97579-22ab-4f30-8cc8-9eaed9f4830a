package net.summerfarm.tms.base.path;

import net.summerfarm.tms.base.path.entity.PathCarEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-05-15
 **/
public interface PathCarRepository {

    /**
     * 根据路由id删除车次信息
     * @param pathId 路由id
     */
    void removeByPathId(Long pathId);


    /**
     * 根据路由查询车次信息
     * @param pathId 路由id
     * @return
     */
    List<PathCarEntity> queryByPathId(Long pathId);

    
}
