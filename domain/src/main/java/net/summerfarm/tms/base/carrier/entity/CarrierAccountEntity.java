package net.summerfarm.tms.base.carrier.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: 承运商账户信息
 * date: 2023/10/12 15:13<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CarrierAccountEntity {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 承运商id
     */
    private Long carrierId;

    /**
     * 支付方式 1、银行卡 2、现金
     */
    private Integer payType;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 开户银行
     */
    private String accountBank;

    /**
     * 银行卡归属地
     */
    private String accountAscription;

    /**
     * 账号
     */
    private String account;
}
