package net.summerfarm.tms.base.path.entity;

import lombok.Data;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Date 2023-05-15
 **/
@Data
public class PathCarEntity implements Serializable {
	private static final long serialVersionUID = -6865256950184882443L;

	/**
	 * 主键 单号
	 */
	private Long id;


	/**
	 * 路由id
	 */
	private Long pathId;

	/**
	 * 调度类型
	 */
	private Integer dispatchType;

	/**
	 * 班次类型，0-正班，1-加班
	 */
	private Integer shiftType;


	/**
	 * 开始计算时间
	 */
	private LocalDate startTime;


	/**
	 * 周期类型，0-表示每天，1表示每周
	 */
	private Integer periodType;

	/**
	 * 每周选项下的类型，0-每天，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六，7-周日
	 */
	private String everyWeekType;

	/**
	 * 每天选项下，间隔天数
	 */
	private Integer intervalDays;

	/**
	 * 承运类型，0-前一天，1-当日
	 */
	private Integer carryType;

	/**
	 * 承运时间
	 */
	private String carryTime;

	/**
	 * 承运商id
	 */
	private Long carrierId;

	/**
	 * 承运商名称
	 */
	private String carrierName;

	/**
	 * 司机id
	 */
	private Long driverId;

	/**
	 * 司机名称
	 */
	private String driverName;

	/**
	 * 司机电话
	 */
	private String driverPhone;

	/**
	 * 车辆id
	 */
	private Long carId;

	/**
	 * 车牌号
	 */
	private String carNumber;

	/**
	 * 存储条件
	 */
	private Integer storage;

	/**
	 * 存储条件
	 */
	private String storageDesc;

	/**
	 * 车辆类型
	 */
	private Integer carType;

	/**
	 * 车辆类型
	 */
	private String carTypeDesc;


	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 配送批次类型，-1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车、6提货用车
	 * @see DeliveryBatchTypeEnum
	 */
	private Integer deliveryBatchType;
}
