package net.summerfarm.tms.base.path.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-05-15
 **/
@Data
public class PathQuotationEntity implements Serializable {
    private static final long serialVersionUID = -7954402907904641491L;

    /**
     * 主键 单号
     */
    private Long id;

    /**
     * 路由id
     */
    private Long pathId;

    /**
     * 承运商id
     */
    private Long carrierId;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 车型
     * @see net.summerfarm.tms.enums.CarTypeEnum
     */
    private Integer carType;

    /**
     * 车型描述
     */
    private String carTypeDesc;

    /**
     * 存储条件
     * @see net.summerfarm.tms.enums.CarStorageEnum
     */
    private Integer storage;

    /**
     * 存储条件描述
     */
    private String storageDesc;

    /**
     * 报价费用，单位/元
     */
    private BigDecimal quotationFee;
}
