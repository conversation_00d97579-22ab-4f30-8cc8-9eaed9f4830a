package net.summerfarm.tms.base.distance;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.distance.entity.OutDistanceEntity;
import net.summerfarm.tms.query.base.distance.OutDistanceQuery;

import java.math.BigDecimal;


/**
 * Description: <br/>
 * date: 2023/7/14 14:43<br/>
 *
 * <AUTHOR> />
 */
public interface OutDistanceRepository {
    /**
     * 查询
     * @param outDistanceQuery 查询
     * @return 结果
     */
    PageInfo<OutDistanceEntity> queryPage(OutDistanceQuery outDistanceQuery);

    /**
     * 根据城配仓编号查询
     * @param storeNo 城配仓编号
     * @return 结果
     */
    OutDistanceEntity queryByStoreNo(Integer storeNo);

    /**
     * 保存
     * @param outDistanceEntity 实体
     */
    void save(OutDistanceEntity outDistanceEntity);

    /**
     * 根据ID查询数据
     * @param id 主键
     * @return 结果
     */
    OutDistanceEntity queryById(Long id);

    /**
     * 更新
     * @param outDistanceEntityDb 更新实体
     */
    void update(OutDistanceEntity outDistanceEntityDb);
}
