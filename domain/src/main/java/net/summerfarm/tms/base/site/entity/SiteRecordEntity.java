package net.summerfarm.tms.base.site.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:点位记录实体
 * date: 2024/1/29 18:22
 *
 * <AUTHOR>
 */
@Data
public class SiteRecordEntity implements Serializable {

    private static final long serialVersionUID = -2567148080442428685L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 点位ID
     */
    private Long siteId;

    /**
     * 运输点位ID
     */
    private Long deliverySiteId;

    /**
     * 新省
     */
    private String newProvince;

    /**
     * 新市
     */
    private String newCity;

    /**
     * 新区
     */
    private String newArea;

    /**
     * 新详细地址
     */
    private String newAddress;

    /**
     * 原poi
     */
    private String oldPoi;

    /**
     * 新poi
     */
    private String newPoi;

    /**
     * 原/新poi距离差值，单位m
     */
    private Long distance;

    /**
     * 操作人ID
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private String operator;
}
