package net.summerfarm.tms.base.site.param;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description:点位记录命令参数
 * date: 2024/1/30 14:27
 *
 * <AUTHOR>
 */
@Data
public class SiteRecordCommandParam {

    /**
     * 主键
     */
    private Long id;

    /**
     * 点位ID
     */
    private Long siteId;

    /**
     * 运输点位ID
     */
    private Long deliverySiteId;

    /**
     * 新省
     */
    private String newProvince;

    /**
     * 新市
     */
    private String newCity;

    /**
     * 新区
     */
    private String newArea;

    /**
     * 新详细地址
     */
    private String newAddress;

    /**
     * 原poi
     */
    private String oldPoi;

    /**
     * 新poi
     */
    private String newPoi;

    /**
     * 原/新poi距离差值，单位m
     */
    private Long distance;

    /**
     * 操作人ID
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private String operator;

    public void create(Integer operatorId, String operator) {
        this.operatorId = operatorId;
        this.operator = operator;
        this.createTime = LocalDateTime.now();
    }

    public ContactAdjustCommandParam convertXmParam(){
        ContactAdjustCommandParam param = new ContactAdjustCommandParam();
        param.setNewPoi(this.newPoi);
        param.setNewProvince(this.newProvince);
        param.setNewCity(this.newCity);
        param.setNewArea(this.newArea);
        param.setNewAddress(this.newAddress);
        return param;
    }
}
