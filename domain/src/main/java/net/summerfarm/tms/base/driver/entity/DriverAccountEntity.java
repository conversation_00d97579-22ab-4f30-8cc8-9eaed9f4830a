package net.summerfarm.tms.base.driver.entity;

import lombok.Data;
import net.summerfarm.tms.enums.DriverAccountEnums;

import java.io.Serializable;

/**
 * 司机打款信息
 *
 * <AUTHOR>
 * @Date 2023-07-11
 **/
@Data
public class DriverAccountEntity implements Serializable {
	private static final long serialVersionUID = -5928269075898478998L;


	/**
	 * 支付方式 1、银行卡 2、现金
	 *
	 * @see DriverAccountEnums.DriverAccountTypeEnum
	 */
	private Integer payType;

	/**
	 * 账户名称
	 */
	private String accountName;

	/**
	 * 开户银行
	 */
	private String accountBank;

	/**
	 * 银行卡归属地
	 */
	private String accountAscription;

	/**
	 * 账号
	 */
	private String account;

}
