package net.summerfarm.tms.lack.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/9/8 17:09<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackApprovedResponsibleEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 缺货核准id
     */
    private Long approvedId;

    /**
     * 责任方 1仓库 2城配 3无法判责 4干线
     */
    private Integer responsible;

    /**
     * 是否买赔 0是 1否
     */
    private Integer buyOut;

    /**
     * 买赔金额
     */
    private BigDecimal buyOutMoney;
}
