package net.summerfarm.tms.lack;

import net.summerfarm.tms.lack.entity.LackApprovedResponsibleEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/9/7 16:49<br/>
 *
 * <AUTHOR> />
 */
public interface LackResponsibleRepository {
    /**
     * 判责表的批量保存
     * @param lackApprovedResponsibleEntities 判责信息
     */
    void saveBatch(List<LackApprovedResponsibleEntity> lackApprovedResponsibleEntities);

    /**
     * 根据缺货核准ID查询判责信息
     * @param lackApprovedId 核准ID
     * @return 判责信息
     */
    List<LackApprovedResponsibleEntity> queryListByApprovedId(Long lackApprovedId);

    /**
     * 根据缺货核准ID集合查询判责信息
     * @param approvedIdList 缺货核准ID集合
     * @return 结果
     */
    List<LackApprovedResponsibleEntity> queryListByApprovedIds(List<Long> approvedIdList);

    /**
     * 查询所有的表信息
     * @return 结果
     */
    List<LackApprovedResponsibleEntity> queryAllList();
}
