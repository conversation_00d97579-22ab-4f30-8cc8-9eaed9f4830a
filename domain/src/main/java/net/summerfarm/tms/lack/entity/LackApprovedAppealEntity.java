package net.summerfarm.tms.lack.entity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 缺货核准申诉
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-250 14:11:13
 */
@Data
public class LackApprovedAppealEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 缺货核准id
     */
    private Long approvedId;

    /**
     * 申诉方:1城配、2干线、3仓库
     */
    private Integer side;

    /**
     * 申诉说明
     */
    private String description;

    /**
     * 申诉凭证
     */
    private String certificate;

    /**
     * 申诉人名称
     */
    private String appealPeopleName;


}
