package net.summerfarm.tms.lack;

import net.summerfarm.tms.lack.entity.LackApprovedAppealEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/9/7 16:40<br/>
 *
 * <AUTHOR> />
 */
public interface LackAppealRepository {
    /**
     * 根据缺货核准编号查询申诉信息
     * @param lackGoodsApprovedIds 缺货核准ID集合
     * @return 结果
     */
    List<LackApprovedAppealEntity> queryByApprovedIds(List<Long> lackGoodsApprovedIds);

    /**
     * 保存申诉信息
     * @param lackApprovedAppealEntity 申诉信息
     */
    void appealSave(LackApprovedAppealEntity lackApprovedAppealEntity);
}
