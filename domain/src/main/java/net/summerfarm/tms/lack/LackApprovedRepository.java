package net.summerfarm.tms.lack;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.lack.entity.LackApprovedEntity;
import net.summerfarm.tms.query.delivery.DeliveryLackApprovedQuery;
import net.summerfarm.tms.query.delivery.LackApprovedQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/21 17:04<br/>
 *
 * <AUTHOR> />
 */
public interface LackApprovedRepository {
    /**
     * 生成缺货核准数据
     * @param lackApprovedEntity
     */
    void save(LackApprovedEntity lackApprovedEntity);

    /**
     * 根据id查询数据
     * @param id 主键
     * @return 结果
     */
    LackApprovedEntity query(Long id);

    /**
     * 更新
     * @param lackApprovedEntity
     */
    void update(LackApprovedEntity lackApprovedEntity);

    /**
     * 查询数量
     *
     * @param lackApprovedQuery 查询条件
     * @return 结果数量
     */
    long queryCount(DeliveryLackApprovedQuery lackApprovedQuery);


    /**
     * 查询缺货复核列表
     *
     * @param queryLackApprovedDTO
     * @return
     */
    PageInfo<LackApprovedEntity> queryPageList(LackApprovedQuery queryLackApprovedDTO);

    /**
     * 分页查询缺货核准和申诉信息
     * @param lackApprovedQuery 查询
     * @return 结果
     */
    PageInfo<LackApprovedEntity> queryListWithAppealPage(LackApprovedQuery lackApprovedQuery);

    /**
     * 查询批量信息
     * @param ids 缺货核准ID集合
     * @return 缺货核准集合信息
     */
    List<LackApprovedEntity> queryListByIds(List<Long> ids);

    /**
     * 查询缺货核准信息
     * @param lackApprovedQuery 查询
     * @return 结果
     */
    List<LackApprovedEntity> queryList(LackApprovedQuery lackApprovedQuery);

    /**
     * 批量更新
     * @param lackApprovedEntityList 更新信息
     */
    void updateBatch(List<LackApprovedEntity> lackApprovedEntityList);

    /**
     * 详情信息
     * @param lackApprovedId 缺货核准ID
     * @return 详情信息
     */
    LackApprovedEntity queryDetailWithAppealResponsible(Long lackApprovedId);


    /**
     * 批量查询详情信息
     * @param lackApprovedQuery 查询条件
     * @return 详情信息
     */
    List<LackApprovedEntity> queryListWithAppealResponsible(LackApprovedQuery lackApprovedQuery);
}
