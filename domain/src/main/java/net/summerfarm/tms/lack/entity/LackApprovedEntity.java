package net.summerfarm.tms.lack.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2023-03-15
 **/
@Data
public class LackApprovedEntity implements Serializable {
    private static final long serialVersionUID = 8495450494577657453L;

    /**
     * 任务编号
     */
    private Long id;
    /**
     * 缺货核准ID集合
     */
    private List<Long> ids;
    /**
     * 城配仓
     */
    private Integer storeNo;
    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * 配送司机
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 配送完成时间
     */
    private LocalDateTime finishTime;


    /**
     * 店铺名称
     */
    private String mname;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * sku
     */
    private String sku;

    /**
     * 店铺id
     */
    private Integer mId;
    /**
     * 缺货数量
     */
    private Integer lackNum;

    /**
     * 金额
     */
    private BigDecimal money;

    /**
     * 缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它 8总仓-腐烂
     */
    private String lackType;

    /**
     * 总金额
     */
    private BigDecimal totalMoney;

    private String remark;

    private Integer responsible;

    //状态 1 待核准 2待判责 3 已完成
    private Integer state;

    private String pic;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 缺货核准操作人id
     */
    private Integer approvedAdminId;
    private Integer buyOut;

    private Integer amount;

    private Integer stockLackNum;

    /**
     * 运输单id
     */
    private Long tmsDeliverySiteId;

    private Integer stockTaskId;

    /**
     * 买赔金额
     */
    private BigDecimal buyOutMoney;

    /**
     * 判责意见
     */
    private String judgmentOpinion;

    /**
     * 路线编号
     */
    private String pathName;
    
    /**
     * 配送照片门店照片
     */
    private String deliveryPic;

    private String address;


    private String weight;

    /**
     * 签收面照片
     */
    private String signPic;

    /**
     * 货物照片
     */
    private String productPic;


    private LocalDateTime approvedTime;

    private Integer responsibilityAdminId;

    private LocalDateTime responsibilityTime;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单来源
     */
    private Integer orderSource;

    private String phone;
    
    
    private Long deliveryBatchId;


    /**
     * 缺货核准操作人
     */
    private String approvedName;

    /**
     * 判责操作人
     */
    private String responsibilityName;

    /**
     * 是否申诉 0未申诉 1已申诉
     */
    private Integer appealFlag;

    /**
     * 判责凭证
     */
    private String responsibilityPic;
    /**
     * 申诉信息
     */
    List<LackApprovedAppealEntity> lackApprovedAppealEntities;
    /**
     * 判责信息
     */
    List<LackApprovedResponsibleEntity> lackApprovedResponsibleEntities;

    public String findStoreNo() {
        if (Objects.isNull(this.storeNo)) {
            return null;
        }
        return this.storeNo.toString();
    }

    public String findWarehouseNo() {
        if (Objects.isNull(this.warehouseNo)) {
            return null;
        }
        return this.warehouseNo.toString();
    }

    public String allDeliveryPic(){
        StringJoiner sj = new StringJoiner(",");
        if(StrUtil.isNotBlank(this.getDeliveryPic())){
            sj.add(this.getDeliveryPic());
        }
        if(StrUtil.isNotBlank(this.getProductPic())){
            sj.add(this.getProductPic());
        }
        if(StrUtil.isNotBlank(this.getSignPic())){
            sj.add(this.getSignPic());
        }

        return sj.toString();
    }
}
