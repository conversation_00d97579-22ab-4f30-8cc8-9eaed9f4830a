package net.summerfarm.tms.lack;

import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.summerfarm.model.SummerfarmResult;
import com.cosfo.summerfarm.model.dto.order.AfterOrderItemVO;
import com.cosfo.summerfarm.model.dto.order.OrderItemVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.lack.entity.LackApprovedAppealEntity;
import net.summerfarm.tms.lack.entity.LackApprovedEntity;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.delivery.entity.DeliveryItemEntity;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistClientVO;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.facade.mall.dto.SkuDTO;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.query.delivery.DeliveryLackApprovedQuery;
import net.summerfarm.tms.query.delivery.LackApprovedQuery;
import net.summerfarm.warehouse.model.vo.WarehouseInventoryMappingVO;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/10/21 17:04<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class LackApprovedDomainService {

    @Resource
    private LackApprovedRepository lackApprovedRepository;
    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private LackAppealRepository lackAppealRepository;
    @Resource
    private LackResponsibleRepository lackResponsibleRepository;

    /**
     * 生成缺货核准数据
     *
     * @param deliveryOrderEntityList 配送单id
     * @param deliverySiteId          运输单id
     */
    public void createLackGoodsApproved(List<DeliveryOrderEntity> deliveryOrderEntityList, Long deliverySiteId) {
        //判断是否有缺货
        if (CollectionUtils.isEmpty(deliveryOrderEntityList)) {
            return;
        }

        //获取配送的信息
        List<DeliveryOrderEntity> sendDeliveryOrderList = deliveryOrderEntityList.stream()
                .filter(deliveryOrderEntity -> deliveryOrderEntity.getType() == DeliveryOrderTypeEnum.send.getCode())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sendDeliveryOrderList)) {
            return;
        }
        for (DeliveryOrderEntity deliveryOrderEntity : sendDeliveryOrderList) {
            DistOrderEntity distOrderEntity = distOrderRepository.queryWithSiteWithItem(deliveryOrderEntity.getDistOrderId());
            Map<String, List<DistItemVO>> distItemMap = distOrderEntity.getDistItems().stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));

            List<DeliveryItemEntity> deliveryItemEntityList = deliveryOrderEntity.getDeliveryItemEntityList();
            if (CollectionUtils.isEmpty(deliveryItemEntityList)) {
                continue;
            }
            Map<String, List<DeliveryItemEntity>> skuItemListMap = deliveryItemEntityList.stream().collect(Collectors.groupingBy(DeliveryItemEntity::getOutItemId));
            for (String sku : skuItemListMap.keySet()) {
                List<DeliveryItemEntity> deliveryItemEntities = skuItemListMap.get(sku);
                if(CollectionUtils.isEmpty(deliveryItemEntities)){
                    continue;
                }
                int skuShortCount = deliveryItemEntities.stream().mapToInt(DeliveryItemEntity::getShortCount).sum();
                int skuPlanReceiptCount = deliveryItemEntities.stream().mapToInt(DeliveryItemEntity::getPlanReceiptCount).sum();

                if (skuShortCount > 0) {
                    WarehouseInventoryMappingVO warehouseInventoryMappingVO = warehouseInventoryService.selectVoByUniqueIndex(Integer.parseInt(distOrderEntity.getBeginSite().getOutBusinessNo()),
                            sku);

                    long count = lackApprovedRepository.queryCount(DeliveryLackApprovedQuery.builder()
                            .deliverySiteId(deliverySiteId)
                            .orderNo(distOrderEntity.getDistClientVO().getOutOrderId())
                            .sku(sku)
                            .amount(skuShortCount).build()
                    );
                    if (count > 0) {
                        continue;
                    }
                    LackApprovedEntity lackApprovedEntity = new LackApprovedEntity();
                    lackApprovedEntity.setStoreNo(Integer.parseInt(distOrderEntity.getBeginSite().getOutBusinessNo()));
                    //外单不存在鲜沐的库存仓映射关系
                    if(!Objects.equals(DistOrderSourceEnum.OUTER_CITY,distOrderEntity.getSource())){
                        lackApprovedEntity.setWarehouseNo(warehouseInventoryMappingVO.getWarehouseNo());
                    }
                    lackApprovedEntity.setSku(sku);
                    lackApprovedEntity.setMId(Integer.parseInt(distOrderEntity.getDistClientVO().getOutClientId()));
                    lackApprovedEntity.setLackNum(skuShortCount);
                    lackApprovedEntity.setMoney(distItemMap.get(sku).get(0).getOutItemPrice());
                    lackApprovedEntity.setState(LackGoodsApprovedStateEnum.WAIT_APPROVED.getCode());
                    lackApprovedEntity.setOrderNo(deliveryOrderEntity.getOuterOrderId());
                    lackApprovedEntity.setAmount(skuPlanReceiptCount);
                    lackApprovedEntity.setTmsDeliverySiteId(deliverySiteId);
                    //生成缺货核准数据
                    lackApprovedRepository.save(lackApprovedEntity);
                }
            }
        }
    }

    /**
     * 缺货核准
     *
     * @param lackApprovedEntity
     */
    public void lackApproved(LackApprovedEntity lackApprovedEntity) {
        //判断状态
        LackApprovedEntity lackApprovedData = lackApprovedRepository.query(lackApprovedEntity.getId());
        if(lackApprovedData == null){
            throw new TmsRuntimeException("不存在此缺货核准任务");
        }
        if(!Objects.equals(lackApprovedData.getState(),LackGoodsApprovedStateEnum.WAIT_APPROVED.getCode())){
            throw new TmsRuntimeException("当前状态不为待核准,不能再次核准");
        }
        LocalDateTime now = LocalDateTime.now();
        lackApprovedEntity.setState(LackGoodsApprovedEnum.WAIT_RESPONSIBILITY.getCode());
        lackApprovedEntity.setApprovedTime(now);

        lackApprovedRepository.update(lackApprovedEntity);
    }

    /**
     * 缺货判责
     * @param lackApprovedEntityList 判责对象集合
     */
    public void batchLackToCondemn(List<LackApprovedEntity> lackApprovedEntityList) {
        if(CollectionUtils.isEmpty(lackApprovedEntityList)){
            return;
        }
        List<Long> ids = lackApprovedEntityList.stream().map(LackApprovedEntity::getId).collect(Collectors.toList());
        List<LackApprovedEntity> lackApprovedEntities = lackApprovedRepository.queryList(LackApprovedQuery.builder()
                .state(LackGoodsApprovedStateEnum.WAIT_RESPONSIBILITY.getCode())
                .ids(ids).build());

        if(ids.size() != lackApprovedEntities.size()){
            throw new TmsRuntimeException("存在状态不为带判责的数据,请刷新页面再提交");
        }
        //更新缺货核准相关字段
        lackApprovedRepository.updateBatch(condemnLackGoodApprovedUpdateData(lackApprovedEntityList));
        //判责表保存
        lackResponsibleRepository.saveBatch(lackApprovedEntityList.stream().map(LackApprovedEntity::getLackApprovedResponsibleEntities).flatMap(Collection::stream).collect(Collectors.toList()));
    }

    /**
     * 更新主表缺货核准判责相关的字段
     * @param lackApprovedEntityList 缺货核准集合
     * @return 缺货核准判责更新字段信息
     */
    private ArrayList<LackApprovedEntity> condemnLackGoodApprovedUpdateData(List<LackApprovedEntity> lackApprovedEntityList) {
        ArrayList<LackApprovedEntity> commitApprovedEntityList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (LackApprovedEntity lackApprovedEntity : lackApprovedEntityList) {
            LackApprovedEntity upadteApprovedEntity = new LackApprovedEntity();
            upadteApprovedEntity.setState(LackGoodsApprovedStateEnum.HAVE_FINISH.getCode());
            upadteApprovedEntity.setResponsibilityTime(now);
            upadteApprovedEntity.setJudgmentOpinion(lackApprovedEntity.getJudgmentOpinion());
            upadteApprovedEntity.setResponsibilityName(lackApprovedEntity.getResponsibilityName());
            upadteApprovedEntity.setResponsibilityAdminId(lackApprovedEntity.getResponsibilityAdminId());
            upadteApprovedEntity.setResponsibilityPic(lackApprovedEntity.getResponsibilityPic());
            upadteApprovedEntity.setId(lackApprovedEntity.getId());

            commitApprovedEntityList.add(upadteApprovedEntity);
        }
        return commitApprovedEntityList;
    }

    public void appealSave(LackApprovedAppealEntity lackApprovedAppealEntity) {
        if(lackApprovedAppealEntity == null){
            throw new TmsRuntimeException("创建信息不能为空");
        }
        LackApprovedEntity lackApprovedEntity = lackApprovedRepository.query(lackApprovedAppealEntity.getApprovedId());
        if(lackApprovedEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"缺货核准信息");
        }
        //记录判责凭证
        LackApprovedEntity updateLackApprovedEntity = new LackApprovedEntity();
        updateLackApprovedEntity.setId(lackApprovedEntity.getId());
        updateLackApprovedEntity.setAppealFlag(TmsLackGoodsApprovedEnums.AppealFlag.HAVE.getValue());
        lackApprovedRepository.update(updateLackApprovedEntity);
        //保存申诉信息
        lackAppealRepository.appealSave(lackApprovedAppealEntity);

    }
}
