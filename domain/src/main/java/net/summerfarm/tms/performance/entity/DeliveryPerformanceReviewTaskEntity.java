package net.summerfarm.tms.performance.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/6/30 11:46<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPerformanceReviewTaskEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 审核类型 0城配-出仓、1城配-签收、2干线-签收
     */
    private Integer reviewTaskType;

    /**
     * 名称
     */
    private String name;

    /**
     * 判罚标准
     */
    private BigDecimal penaltyStandards;

    /**
     * 城配仓名称
     */
    private List<String> storeNames;

    /**
     * 城配仓编号
     */
    private List<String> storeNos;
    /**
     * 调度类型名称
     */
    private List<String> batchTypeNames;
    /**
     * 调度类型集合
     */
    private List<Integer> batchTypes;

    /**
     * 开始时间
     */
    private LocalDate beginDeliveryTime;

    /**
     * 结束时间
     */
    private LocalDate endDeliveryTime;

    /**
     * 状态0审核中、1审核完成、2已关闭
     */
    private Integer state;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 超出距离备注
     */
    private String outDistanceReason;

    /**
     * 非水果冷藏数量
     */
    private String nonFruitColdNum;


    /**
     * 非水果冷冻数量
     */
    private String nonFruitFreezeNum;

    /**
     * 详情信息
     */
    private List<DeliveryPerformanceReviewDetailEntity> deliveryPerformanceReviewDetailEntities;

    /**
     * 审核模式
     * 0-人工审核，1-AI审核
     */
    private Integer reviewMode = 0;
}
