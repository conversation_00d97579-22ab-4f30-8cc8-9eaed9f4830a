package net.summerfarm.manage.domain.performance.param.query;

import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryPerformanceReviewDetailAppealQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 履约审核任务ID
	 */
	private Long performanceReviewTaskId;

	/**
	 * 履约审核详情ID
	 */
	private Long delPerfDetailReviewId;

	/**
	 * 配送批次ID
	 */
	private Long deliveryBatchId;

	/**
	 * 状态0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
	 */
	private Integer status;

	/**
	 * 城配仓编号
	 */
	private Integer storeNo;

	/**
	 * 城配仓名称
	 */
	private String storeName;

	

	
}