package net.summerfarm.tms.performance.repository;



import net.summerfarm.manage.domain.performance.param.command.TmsDeliveryPerformanceReviewDetailAppealItemCommandParam;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:07
* @version 1.0
*
*/
public interface DeliveryPerformanceReviewDetailAppealItemCommandRepository {

    /**
     * 申诉项更新
     * @param deliveryPerformanceReviewDetailAppealItemEntities 申诉项实体
     */
    void update(List<DeliveryPerformanceReviewDetailAppealItemEntity> deliveryPerformanceReviewDetailAppealItemEntities);
}