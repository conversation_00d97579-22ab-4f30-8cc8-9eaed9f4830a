package net.summerfarm.tms.performance.domain;


import net.summerfarm.tms.enums.DeliveryPerformanceReviewTaskEnums;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealEntity;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewTaskEntity;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailAppealQueryRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 *
 * @Title: 履约审核申诉领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 *
 */
@Service
public class DeliveryPerformanceReviewDetailAppealQueryDomainService {

    @Resource
    private DeliveryPerformanceReviewDetailAppealQueryRepository deliveryPerformanceReviewDetailAppealQueryRepository;

    /**
     * 根据履约明细ID查询申诉信息
     * @param deliveryPerReviewDetailIds 履约明细ID集合
     * @return delPerfDetailReviewId2AppealMap key->履约明细ID + # +taskId value->申诉信息
     */
    public Map<String, DeliveryPerformanceReviewDetailAppealEntity> queryDelPerReviewId2AppealMapByDelPerReviewIdsAndTaskIds(List<Long> deliveryPerReviewDetailIds, List<Long> performanceReviewTaskIds) {
        if(CollectionUtils.isEmpty(deliveryPerReviewDetailIds)){
            return Collections.emptyMap();
        }
        List<DeliveryPerformanceReviewDetailAppealEntity> appealEntityList = deliveryPerformanceReviewDetailAppealQueryRepository
                .queryAppealListByDeliveryPerReviewDetailIdsAndTaskIds(deliveryPerReviewDetailIds,performanceReviewTaskIds);
        return appealEntityList.stream().collect(Collectors.toMap(appeal -> appeal.getDelPerfDetailReviewId() + "#" + appeal.getPerformanceReviewTaskId(), appeal -> appeal, (o, n) -> o));
    }

    /**
     * 根据批次ID和履约任务ID查询申诉信息
     * @param deliveryBatchIdList 批次ID集合
     * @return map key->批次ID + # +taskId  value->申诉信息
     */
    public Map<String, DeliveryPerformanceReviewDetailAppealEntity> queryDelPerReviewId2AppealMapByBatchIdsAndTaskIds(List<Long> deliveryBatchIdList, List<Long> performanceReviewTaskIds) {
        if(CollectionUtils.isEmpty(deliveryBatchIdList) || CollectionUtils.isEmpty(performanceReviewTaskIds)){
            return Collections.emptyMap();
        }
        List<DeliveryPerformanceReviewDetailAppealEntity> appealEntityList = deliveryPerformanceReviewDetailAppealQueryRepository
                .queryAppealListByBatchIdsAndTaskIds(deliveryBatchIdList,performanceReviewTaskIds);
        return appealEntityList.stream().collect(Collectors.toMap(appeal -> appeal.getDeliveryBatchId() + "#" + appeal.getPerformanceReviewTaskId(), appeal -> appeal, (o, n) -> o));
    }

    /**
     * 设置申诉信息
     * @param taskEntityList 履约审核任务集合
     */
    public void setUpAppealInfo(List<DeliveryPerformanceReviewTaskEntity> taskEntityList) {
        if(CollectionUtils.isEmpty(taskEntityList)){
            return;
        }
        Map<Integer, List<DeliveryPerformanceReviewTaskEntity>> type2TaskMap = taskEntityList.stream().collect(Collectors.groupingBy(DeliveryPerformanceReviewTaskEntity::getReviewTaskType));
        type2TaskMap.keySet().forEach(reviewTaskType -> {
            List<DeliveryPerformanceReviewTaskEntity> taskList = type2TaskMap.get(reviewTaskType);
            List<Long> taskIds = taskList.stream().map(DeliveryPerformanceReviewTaskEntity::getId).distinct().collect(Collectors.toList());

            List<DeliveryPerformanceReviewDetailEntity> detailEntities = taskList.stream().map(DeliveryPerformanceReviewTaskEntity::getDeliveryPerformanceReviewDetailEntities)
                    .flatMap(List::stream).collect(Collectors.toList());
            if(Objects.equals(reviewTaskType, DeliveryPerformanceReviewTaskEnums.ReviewTaskType.TRUNK_IN.getValue())){
                List<Long> batchIds = detailEntities.stream().map(DeliveryPerformanceReviewDetailEntity::getDeliveryBatchId)
                        .collect(Collectors.toList());
                Map<String, DeliveryPerformanceReviewDetailAppealEntity>  batch2AppealMap = this.queryDelPerReviewId2AppealMapByBatchIdsAndTaskIds(batchIds,taskIds);
                // 设置申诉信息
                detailEntities.forEach(detailEntity -> detailEntity.setAppealEntity(batch2AppealMap.get(detailEntity.getDeliveryBatchId() + "#" + detailEntity.getPerformanceReviewTaskId())));
            }{
                List<Long> detailIds = detailEntities.stream().map(DeliveryPerformanceReviewDetailEntity::getId).collect(Collectors.toList());
                Map<String, DeliveryPerformanceReviewDetailAppealEntity>  detailId2AppealMap = this.queryDelPerReviewId2AppealMapByDelPerReviewIdsAndTaskIds(detailIds,taskIds);
                // 设置申诉信息
                detailEntities.forEach(detailEntity -> detailEntity.setAppealEntity(detailId2AppealMap.get(detailEntity.getId() + "#" + detailEntity.getPerformanceReviewTaskId())));
            }
        });
    }
}
