package net.summerfarm.tms.performance.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-09-18 00:16:55
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryPerformanceReviewDetailAiReviewCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 履约审核任务id
	 */
	private Long performanceReviewTaskId;

	/**
	 * 履约审核详情id
	 */
	private Long performanceReviewDetailId;

	/**
	 * 签收照片结果
	 */
	private String citySignPicResults;

	/**
	 * 门店照片结果
	 */
	private String cityDeliveryPicResults;

	/**
	 * 货品照片结果
	 */
	private String cityProductPicResults;

	/**
	 * 签收照片通过,1通过,0不通过
	 */
	private Integer citySignPicPass;

	/**
	 * 门店照片通过,1通过,0不通过
	 */
	private Integer cityDeliveryPicPass;

	/**
	 * 货品照片通过,1通过,0不通过
	 */
	private Integer cityProductPicPass;

	/**
	 * 是否全部通过
	 */
	private Integer allPass;

	

	
}