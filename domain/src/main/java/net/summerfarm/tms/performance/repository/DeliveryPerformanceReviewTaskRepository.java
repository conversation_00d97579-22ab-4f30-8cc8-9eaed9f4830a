package net.summerfarm.tms.performance.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewTaskEntity;
import net.summerfarm.tms.query.delivery.PerformanceReviewTaskQuery;

import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/6/28 16:09<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryPerformanceReviewTaskRepository {
    /**
     * 保存
     * @param deliveryPerformanceReviewTaskEntity 任务实体
     * @return 结果
     */
    Long save(DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity);

    /**
     * 分页查询
     * @param performanceReviewTaskQuery 查询
     * @return 结果
     */
    PageInfo<DeliveryPerformanceReviewTaskEntity> queryPageList(PerformanceReviewTaskQuery performanceReviewTaskQuery);

    /**
     * 根据id查询相关信息
     * @param performanceReviewTaskId id
     * @return 结果
     */
    DeliveryPerformanceReviewTaskEntity queryById(Long performanceReviewTaskId);

    List<DeliveryPerformanceReviewTaskEntity> queryByIdList(List<Long> performanceReviewTaskId);
    Map<Long, DeliveryPerformanceReviewTaskEntity> mapByIdList(List<Long> performanceReviewTaskId);

    /**
     * 更新
     * @param deliveryPerformanceReviewTaskEntity 参数
     */
    void update(DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity);

    /**
     * 根据任务ID集合批量查询任务详情
     * @param performanceReviewTaskIds 任务ID集合
     * @return 结果
     */
    List<DeliveryPerformanceReviewTaskEntity> queryWithDetail(List<Long> performanceReviewTaskIds);
}
