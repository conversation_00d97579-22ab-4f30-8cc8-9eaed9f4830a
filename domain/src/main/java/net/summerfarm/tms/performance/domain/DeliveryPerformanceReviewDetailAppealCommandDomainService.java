package net.summerfarm.tms.performance.domain;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.enums.DeliveryPerformanceReviewDetailEnums;
import net.summerfarm.tms.enums.DeliveryPerformanceReviewTaskEnums;
import net.summerfarm.tms.enums.TmsDeliveryPerformanceReviewDetailAppealEnums;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealEntity;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewTaskEntity;
import net.summerfarm.tms.performance.repository.*;
import net.summerfarm.tms.query.delivery.PerformanceReviewDetailQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 *
 * @Title: 履约审核申诉领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 *
 */
@Service
@Slf4j
public class DeliveryPerformanceReviewDetailAppealCommandDomainService {


    @Resource
    private DeliveryPerformanceReviewDetailAppealCommandRepository deliveryPerformanceReviewDetailAppealCommandRepository;
    @Resource
    private DeliveryPerformanceReviewDetailAppealQueryRepository deliveryPerformanceReviewDetailAppealQueryRepository;
    @Resource
    private DeliveryPerformanceReviewTaskRepository deliveryPerformanceReviewTaskRepository;
    @Resource
    private DeliveryPerformanceReviewDetailRepository deliveryPerformanceReviewDetailRepository;
    @Resource
    private SiteRepository siteRepository;
    @Resource
    private DeliveryPerformanceReviewDetailAppealItemCommandRepository deliveryPerformanceReviewDetailAppealItemCommandRepository;

    @Transactional(rollbackFor = Exception.class)
    public List<DeliveryPerformanceReviewDetailAppealEntity> create(Long performanceReviewTaskId) {
        if(performanceReviewTaskId == null){
            return null;
        }

        // 根据performanceReviewTaskId查询履约审核详情不合规的数据
        DeliveryPerformanceReviewTaskEntity taskEntity = deliveryPerformanceReviewTaskRepository.queryById(performanceReviewTaskId);
        if(taskEntity == null){
            throw new TmsRuntimeException("履约审核任务不存在");
        }
        if(!Objects.equals(taskEntity.getState(), DeliveryPerformanceReviewTaskEnums.State.COMPLETE.getValue())){
            throw new TmsRuntimeException("履约审核任务状态不为审核完成");
        }
        // 不合规的履约数据
        PerformanceReviewDetailQuery query = new PerformanceReviewDetailQuery();
        query.setPerformanceReviewTaskId(performanceReviewTaskId);
        query.setStates(Collections.singletonList(DeliveryPerformanceReviewDetailEnums.State.NO_OK.getValue()));
        if(Objects.equals(taskEntity.getReviewTaskType(), DeliveryPerformanceReviewTaskEnums.ReviewTaskType.CITY_OUT.getValue())){
            query.setBatchGroupBy(true);
        }
        List<DeliveryPerformanceReviewDetailEntity> noOkStateDetailEntityList = deliveryPerformanceReviewDetailRepository.queryList(query);
        if(CollectionUtils.isEmpty(noOkStateDetailEntityList)){
            log.info("该任务没有不合规的履约数据，无需申诉");
            return Collections.emptyList();
        }
        // 数据是否存在
        List<Long> detailIds = noOkStateDetailEntityList.stream().map(DeliveryPerformanceReviewDetailEntity::getId).collect(Collectors.toList());
        List<DeliveryPerformanceReviewDetailAppealEntity> deliveryPerformanceReviewDetailAppealEntities = deliveryPerformanceReviewDetailAppealQueryRepository.queryAppealListByDeliveryPerReviewDetailIdsAndTaskIds(detailIds, Collections.singletonList(performanceReviewTaskId));
        if(!CollectionUtils.isEmpty(deliveryPerformanceReviewDetailAppealEntities)){
            log.info("数据已存在,无需创建:{}", JSON.toJSONString(deliveryPerformanceReviewDetailAppealEntities));
            return Collections.emptyList();
        }
        // 申诉数据创建
        List<Long> beginSiteIds = noOkStateDetailEntityList.stream().map(DeliveryPerformanceReviewDetailEntity::getBeginSiteId).distinct().collect(Collectors.toList());
        Map<Long, SiteEntity> siteEntityMap = siteRepository.queryMapByIds(beginSiteIds);
        List<DeliveryPerformanceReviewDetailAppealEntity> appealSaveList = new ArrayList<>();
        if(!Objects.equals(taskEntity.getReviewTaskType(), DeliveryPerformanceReviewTaskEnums.ReviewTaskType.TRUNK_IN.getValue())){
            // 城配 出仓、签收申诉的创建
            noOkStateDetailEntityList.forEach(detailEntity -> {
                SiteEntity siteEntity = siteEntityMap.get(detailEntity.getBeginSiteId());
                Integer storeNo = siteEntity != null ? Integer.parseInt(siteEntity.getOutBusinessNo()) : null;
                String storeName = siteEntity != null ? siteEntity.getName() : null;

                DeliveryPerformanceReviewDetailAppealEntity appealEntity = new DeliveryPerformanceReviewDetailAppealEntity();
                appealEntity.cityWaitAppealCreate(detailEntity,storeNo,storeName);

                appealSaveList.add(appealEntity);
            });
        }else{
           // 干线签收 申诉的创建
            List<DeliveryPerformanceReviewDetailEntity> trunkNoOkStateDetailEntityList = noOkStateDetailEntityList.stream().filter(detailEntity -> !StringUtils.isEmpty(detailEntity.getSitePicReason())).collect(Collectors.toList());
            // 按照路线分组
            Map<Long, List<DeliveryPerformanceReviewDetailEntity>> trunkBatchDetailMap = trunkNoOkStateDetailEntityList.stream().collect(Collectors.groupingBy(DeliveryPerformanceReviewDetailEntity::getDeliveryBatchId));
            trunkBatchDetailMap.forEach((batchId, performanceReviewDetailEntityList) -> {
                DeliveryPerformanceReviewDetailAppealEntity appealEntity = new DeliveryPerformanceReviewDetailAppealEntity();
                appealEntity.trunkWaitAppealCreate(performanceReviewDetailEntityList);

                appealSaveList.add(appealEntity);
            });
        }

        return deliveryPerformanceReviewDetailAppealCommandRepository.createBatch(appealSaveList);
    }

    /**
     * 提交申诉
     * @param appealEntity 申诉信息
     */
    public void submitAppeal(DeliveryPerformanceReviewDetailAppealEntity appealEntity) {
        if(appealEntity == null){
            throw new TmsRuntimeException("申诉信息为空");
        }
        Long appealId = appealEntity.getId();
        if(appealId == null){
            throw new TmsRuntimeException("申诉ID为空");
        }
        List<DeliveryPerformanceReviewDetailAppealItemEntity> deliveryPerformanceReviewDetailAppealItemEntities = appealEntity.getDeliveryPerformanceReviewDetailAppealItemEntities();
        if(CollectionUtils.isEmpty(deliveryPerformanceReviewDetailAppealItemEntities)){
            throw new TmsRuntimeException("申诉项为空");
        }
        DeliveryPerformanceReviewDetailAppealEntity detailAppealEntity = deliveryPerformanceReviewDetailAppealQueryRepository.selectById(appealId);
        if(detailAppealEntity == null){
            throw new TmsRuntimeException("申诉信息不存在");
        }
        Integer status = detailAppealEntity.getStatus();
        if(status >= TmsDeliveryPerformanceReviewDetailAppealEnums.Status.APPEALING.getValue()){
            throw new TmsRuntimeException("当前状态为不能变更为申诉中");
        }
        // 状态变更
        deliveryPerformanceReviewDetailAppealCommandRepository.updateStatus(detailAppealEntity, TmsDeliveryPerformanceReviewDetailAppealEnums.Status.APPEALING.getValue());

        // 申诉项更新
        deliveryPerformanceReviewDetailAppealItemCommandRepository.update(deliveryPerformanceReviewDetailAppealItemEntities);

    }


    public void updateAppealResult(DeliveryPerformanceReviewDetailAppealEntity appealEntityReq) {
        if(appealEntityReq == null){
            throw new TmsRuntimeException("申诉信息为空");
        }
        Long appealId = appealEntityReq.getId();
        if(appealId == null){
            throw new TmsRuntimeException("申诉ID为空");
        }
        DeliveryPerformanceReviewDetailAppealEntity currentAppealEntity = deliveryPerformanceReviewDetailAppealQueryRepository.selectById(appealId);
        if(currentAppealEntity == null){
            throw new TmsRuntimeException("申诉信息不存在");
        }
        Integer status = currentAppealEntity.getStatus();
        if(status > TmsDeliveryPerformanceReviewDetailAppealEnums.Status.APPEALING.getValue()){
            throw new TmsRuntimeException("当前状态为不能变更");
        }

        currentAppealEntity.setStatus(appealEntityReq.getStatus());
        currentAppealEntity.setAppealFailReason(appealEntityReq.getAppealFailReason());
        // 状态变更
        deliveryPerformanceReviewDetailAppealCommandRepository.update(currentAppealEntity);

    }

    public void batchCloseAppeal(List<Long> appealIds) {
        if(CollectionUtils.isEmpty(appealIds)){
            return;
        }
        List<DeliveryPerformanceReviewDetailAppealEntity> currentAppealList = deliveryPerformanceReviewDetailAppealQueryRepository.queryAppealListByIds(appealIds);
        // 过滤出非待申诉的数据
        List<DeliveryPerformanceReviewDetailAppealEntity> noWaitAppealList = currentAppealList.stream()
                .filter(appealEntity -> !Objects.equals(appealEntity.getStatus(), TmsDeliveryPerformanceReviewDetailAppealEnums.Status.WAIT_APPEAL.getValue())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(noWaitAppealList)){
            throw new TmsRuntimeException("批量关闭的数据中存在申诉状态为非待申诉的数据");
        }
        List<DeliveryPerformanceReviewDetailAppealEntity> appealEntities = new ArrayList<>();
        for (Long appealId : appealIds) {
            DeliveryPerformanceReviewDetailAppealEntity appealEntity = new DeliveryPerformanceReviewDetailAppealEntity();
            appealEntity.setId(appealId);
            appealEntity.setStatus(TmsDeliveryPerformanceReviewDetailAppealEnums.Status.CLOSE.getValue());
            appealEntities.add(appealEntity);
        }
        deliveryPerformanceReviewDetailAppealCommandRepository.batchUpdate(appealEntities);
    }
}
