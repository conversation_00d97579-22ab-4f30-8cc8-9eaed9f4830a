package net.summerfarm.tms.performance;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailAiReviewEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAiReviewQueryParam;

import java.util.List;
import java.util.Map;


/**
*
* <AUTHOR>
* @date 2024-09-18 00:16:55
* @version 1.0
*
*/
public interface TmsDeliveryPerformanceReviewDetailAiReviewQueryRepository {

    PageInfo<TmsDeliveryPerformanceReviewDetailAiReviewEntity> getPage(TmsDeliveryPerformanceReviewDetailAiReviewQueryParam param);

    TmsDeliveryPerformanceReviewDetailAiReviewEntity selectById(Long id);

    List<TmsDeliveryPerformanceReviewDetailAiReviewEntity> selectByCondition(TmsDeliveryPerformanceReviewDetailAiReviewQueryParam param);

    Map<Long, TmsDeliveryPerformanceReviewDetailAiReviewEntity> mapByReviewDetailIdList(List<Long> reviewDetailIdList);
}