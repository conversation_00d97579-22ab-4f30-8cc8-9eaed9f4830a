package net.summerfarm.tms.performance.entity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Data;
import net.summerfarm.tms.enums.DeliveryPerformanceReviewDetailEnums;
import net.summerfarm.tms.enums.TmsDeliveryPerformanceReviewDetailAppealEnums;
import org.springframework.util.CollectionUtils;


/**
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 *
 */
@Data
public class DeliveryPerformanceReviewDetailAppealEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 履约审核任务ID
	 */
	private Long performanceReviewTaskId;

	/**
	 * 履约审核详情ID
	 */
	private Long delPerfDetailReviewId;

	/**
	 * 配送批次ID
	 */
	private Long deliveryBatchId;

	/**
	 * 状态0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
	 */
	private Integer status;

	/**
	 * 城配仓编号
	 */
	private Integer storeNo;

	/**
	 * 城配仓名称
	 */
	private String storeName;

	/**
	 * 申诉失败原因
	 */
	private String appealFailReason;

	/**
	 * 履约审核申诉项
	 */
	private List<DeliveryPerformanceReviewDetailAppealItemEntity> deliveryPerformanceReviewDetailAppealItemEntities;

	/**
	 * 城配-待申诉创建
	 * @param detailEntity 履约审核详情
	 * @param storeNo 城配仓编号
	 * @param storeName 城配仓名称
	 */
	public void cityWaitAppealCreate(DeliveryPerformanceReviewDetailEntity detailEntity, Integer storeNo, String storeName) {
		if(detailEntity == null){
			return;
		}
		this.setPerformanceReviewTaskId(detailEntity.getPerformanceReviewTaskId());
		this.setDelPerfDetailReviewId(detailEntity.getId());
		this.setDeliveryBatchId(detailEntity.getDeliveryBatchId());
		this.setStatus(TmsDeliveryPerformanceReviewDetailAppealEnums.Status.WAIT_APPEAL.getValue());
		this.setStoreName(storeName);
		this.setStoreNo(storeNo);

		// 申诉项
		List<Integer> sitePicReasonList = Arrays.stream(detailEntity.getSitePicReason().split(",")).map(Integer::parseInt).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(sitePicReasonList)){
			return;
		}
		// 把所有的图片原因给到枚举转换为 图片类型 --> 原因集合
		Map<String, String> picReasonType2ReasonMap = DeliveryPerformanceReviewDetailEnums.handleSitePicReasonType(sitePicReasonList);

		List<DeliveryPerformanceReviewDetailAppealItemEntity> itemSaveSet = new ArrayList<>();

		picReasonType2ReasonMap.forEach((picReasonType, reasonStr) -> {
			DeliveryPerformanceReviewDetailAppealItemEntity itemEntity = new DeliveryPerformanceReviewDetailAppealItemEntity();
			itemEntity.setDeliverySiteId(detailEntity.getDeliverySiteId());
			itemEntity.setSiteId(detailEntity.getSiteId());
			itemEntity.setSequence(detailEntity.getSequence());
			itemEntity.setSiteAddress(detailEntity.getSiteAddress());
			itemEntity.setNonComplianceSitePicType(picReasonType);
			itemEntity.setNonCompliancePic(detailEntity.findPicByPicReasons(picReasonType));
			itemEntity.setNonComplianceReason(reasonStr);

			itemSaveSet.add(itemEntity);
		});
		this.setDeliveryPerformanceReviewDetailAppealItemEntities(itemSaveSet);
	}

	public void trunkWaitAppealCreate(List<DeliveryPerformanceReviewDetailEntity> performanceReviewDetailEntityList) {
		if(CollectionUtils.isEmpty(performanceReviewDetailEntityList)){
			return;
		}
		DeliveryPerformanceReviewDetailEntity detailEntity = performanceReviewDetailEntityList.get(0);
		this.setPerformanceReviewTaskId(detailEntity.getPerformanceReviewTaskId());
		this.setDelPerfDetailReviewId(detailEntity.getId());
		this.setDeliveryBatchId(detailEntity.getDeliveryBatchId());
		this.setStatus(TmsDeliveryPerformanceReviewDetailAppealEnums.Status.WAIT_APPEAL.getValue());
		this.setStoreName(storeName);
		this.setStoreNo(storeNo);

		List<DeliveryPerformanceReviewDetailAppealItemEntity> itemSaveSet = new ArrayList<>();
		performanceReviewDetailEntityList.forEach(detail -> {
			// 申诉项
			List<Integer> sitePicReasonList = Arrays.stream(detail.getSitePicReason().split(",")).map(Integer::parseInt).collect(Collectors.toList());
			if(CollectionUtils.isEmpty(sitePicReasonList)){
				return;
			}
			// 把所有的图片原因给到枚举转换为 图片类型 --> 原因集合
			Map<String, String> picReasonType2ReasonMap = DeliveryPerformanceReviewDetailEnums.handleSitePicReasonType(sitePicReasonList);

			picReasonType2ReasonMap.forEach((picReasonType, reasonStr) -> {
				DeliveryPerformanceReviewDetailAppealItemEntity itemEntity = new DeliveryPerformanceReviewDetailAppealItemEntity();
				itemEntity.setDeliverySiteId(detail.getDeliverySiteId());
				itemEntity.setSiteId(detail.getSiteId());
				itemEntity.setSequence(detail.getSequence());
				itemEntity.setSiteAddress(detail.getSiteAddress());
				itemEntity.setNonComplianceSitePicType(picReasonType);
				itemEntity.setNonCompliancePic(detail.findPicByPicReasons(picReasonType));
				itemEntity.setNonComplianceReason(reasonStr);

				itemSaveSet.add(itemEntity);
			});
		});
		this.setDeliveryPerformanceReviewDetailAppealItemEntities(itemSaveSet);
	}
}