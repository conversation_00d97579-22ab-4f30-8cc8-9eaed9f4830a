package net.summerfarm.tms.performance.repository;

import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealEntity;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:06
* @version 1.0
*
*/
public interface DeliveryPerformanceReviewDetailAppealCommandRepository {

    /**
     * 批量创建
     * @param appealSaveList 申诉信息
     * @return
     */
    List<DeliveryPerformanceReviewDetailAppealEntity> createBatch(List<DeliveryPerformanceReviewDetailAppealEntity> appealSaveList);

    /**
     * 更新申诉状态
     * @param appealEntity 申诉信息
     * @param status 申诉状态
     */
    void updateStatus(DeliveryPerformanceReviewDetailAppealEntity appealEntity, Integer status);

    /**
     * 更新
     * @param currentAppealEntity 申诉信息
     */
    void update(DeliveryPerformanceReviewDetailAppealEntity currentAppealEntity);

    /**
     * 批量更新
     * @param appealEntities 申诉集合
     */
    void batchUpdate(List<DeliveryPerformanceReviewDetailAppealEntity> appealEntities);
}