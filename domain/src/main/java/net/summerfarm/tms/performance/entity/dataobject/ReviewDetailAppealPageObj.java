package net.summerfarm.tms.performance.entity.dataobject;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: 申诉分页对象<br/>
 * date: 2024/9/5 18:07<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ReviewDetailAppealPageObj {
    /**
     * primary key
     */
    private Long id;

    /**
     * 履约审核任务ID
     */
    private Long performanceReviewTaskId;

    /**
     * 状态0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
     */
    private Integer status;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 审核类型 0城配-出仓、1城配-签收、2干线-签收
     */
    private Integer reviewTaskType;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆存储条件 0常温、1冷藏
     */
    private String carStorage;

    /**
     * 车型0:小面包车 1:中面包车 2:依维柯 3:小型货车 4: 4米2 5:6米8  6:7米6  7:7 9米6  8 :13米5 9:17米5
     */
    private String carType;

    /**
     * 店铺名称
     */
    private String outerClientName;

    /**
     * 线路编码
     */
    private String pathCode;

    /**
     * 次序
     */
    private Integer sequence;

    /**
     * 线路名称
     */
    private String pathName;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 判罚金额
     */
    private BigDecimal penaltyMoney;

}
