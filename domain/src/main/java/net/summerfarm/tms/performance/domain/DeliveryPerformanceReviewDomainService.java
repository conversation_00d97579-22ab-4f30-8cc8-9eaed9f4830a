package net.summerfarm.tms.performance.domain;

import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.enums.DeliveryPerformanceReviewDetailEnums;
import net.summerfarm.tms.enums.DeliveryPerformanceReviewTaskEnums;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewTaskEntity;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailRepository;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewTaskRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/6/28 16:14<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class DeliveryPerformanceReviewDomainService {

    private final DeliveryPerformanceReviewDetailRepository deliveryPerformanceReviewDetailRepository;
    private final DeliveryPerformanceReviewTaskRepository deliveryPerformanceReviewTaskRepository;


    /**
     * 创建信息
     * @param deliveryPerformanceReviewTaskEntity 实体
     */
    public void createPerformanceTaskWithDetail(DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity) {
        //任务保存
        Long id = deliveryPerformanceReviewTaskRepository.save(deliveryPerformanceReviewTaskEntity);
        //详情保存
        if(CollectionUtils.isNotEmpty(deliveryPerformanceReviewTaskEntity.getDeliveryPerformanceReviewDetailEntities())){
            deliveryPerformanceReviewTaskEntity.getDeliveryPerformanceReviewDetailEntities().forEach(detail ->{
                detail.setPerformanceReviewTaskId(id);
            });
            deliveryPerformanceReviewDetailRepository.saveBatch(deliveryPerformanceReviewTaskEntity.getDeliveryPerformanceReviewDetailEntities());
        }
    }

    /**
     * 任务状态变更
     * @param performanceReviewTaskId 主键
     * @param state 状态
     */
    public void reviewTaskChangeState(Long performanceReviewTaskId, Integer state) {
        DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity = deliveryPerformanceReviewTaskRepository.queryById(performanceReviewTaskId);
        if(deliveryPerformanceReviewTaskEntity == null){
            throw new TmsRuntimeException("任务不存在");
        }
        if(deliveryPerformanceReviewTaskEntity.getState() > state){
            throw new TmsRuntimeException((String.format("变更状态异常,当前任务状态为:%s",
                    DeliveryPerformanceReviewTaskEnums.ReviewTaskType.getContentByValue(deliveryPerformanceReviewTaskEntity.getState()))));
        }
        deliveryPerformanceReviewTaskEntity.setState(state);
        deliveryPerformanceReviewTaskRepository.update(deliveryPerformanceReviewTaskEntity);

    }

    public DeliveryPerformanceReviewTaskEntity cheakDetailAuditState(Long detailId) {
        DeliveryPerformanceReviewDetailEntity reviewDetail = deliveryPerformanceReviewDetailRepository.queryById(detailId);
        if(reviewDetail == null){
            throw new TmsRuntimeException("数据不存在");
        }
        DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity = deliveryPerformanceReviewTaskRepository.queryById(reviewDetail.getPerformanceReviewTaskId());
        if(deliveryPerformanceReviewTaskEntity == null){
            throw new TmsRuntimeException("数据主任务不存在");
        }

        if(!Objects.equals(deliveryPerformanceReviewTaskEntity.getState(),DeliveryPerformanceReviewTaskEnums.State.ING.getValue())){
            throw new TmsRuntimeException("任务状态已变更,当前不允许审核,请刷新页面");
        }

        return deliveryPerformanceReviewTaskEntity;
    }

    public void reviewDetailAudit(List<DeliveryPerformanceReviewDetailEntity> detailEntities) {
        if(CollectionUtils.isEmpty(detailEntities)){
            throw new TmsRuntimeException("审核任务详情不能为空");
        }
        //任务状态校验
        DeliveryPerformanceReviewTaskEntity taskEntity = cheakDetailAuditState(detailEntities.get(0).getId());
        //干线签收处理
        if(Objects.equals(taskEntity.getReviewTaskType(),DeliveryPerformanceReviewTaskEnums.ReviewTaskType.TRUNK_IN.getValue())){
            Optional<DeliveryPerformanceReviewDetailEntity> optional = detailEntities.stream().filter(detail -> Objects.equals(detail.getState(), DeliveryPerformanceReviewDetailEnums.State.NO_OK.getValue())).findFirst();
            List<DeliveryPerformanceReviewDetailEntity> moneyList = detailEntities.stream().filter(detail -> detail.getPenaltyMoney() != null).collect(Collectors.toList());

            if(optional.isPresent()){
                detailEntities.forEach(detail -> detail.setState(DeliveryPerformanceReviewDetailEnums.State.NO_OK.getValue()));
            }
            if(CollectionUtils.isNotEmpty(moneyList)){
                detailEntities.forEach(detail -> detail.setPenaltyMoney(moneyList.get(0).getPenaltyMoney()));
            }
        }
        ArrayList<DeliveryPerformanceReviewDetailEntity> detailEntityList = new ArrayList<>();

        for (DeliveryPerformanceReviewDetailEntity detailEntity : detailEntities) {
            if(detailEntity.getId() == null){
                throw new TmsRuntimeException("详情任务ID不能为空");
            }
            DeliveryPerformanceReviewDetailEntity reviewDetailEntity = deliveryPerformanceReviewDetailRepository.queryById(detailEntity.getId());
            if(reviewDetailEntity == null){
                throw new TmsRuntimeException("审核详情任务不存在");
            }
            reviewDetailEntity.setState(detailEntity.getState());
            if(Objects.equals(reviewDetailEntity.getState(), DeliveryPerformanceReviewDetailEnums.State.OK.getValue())){
                reviewDetailEntity.setSitePicReason("");
            }else{
                reviewDetailEntity.setSitePicReason(StringUtils.isBlank(detailEntity.getSitePicReason()) ? "" : detailEntity.getSitePicReason());
            }
            reviewDetailEntity.setPenaltyMoney(detailEntity.getPenaltyMoney() == null ? new BigDecimal(0) : detailEntity.getPenaltyMoney());

            detailEntityList.add(reviewDetailEntity);
        }
        //状态变更
        deliveryPerformanceReviewDetailRepository.update(detailEntityList);
    }
}
