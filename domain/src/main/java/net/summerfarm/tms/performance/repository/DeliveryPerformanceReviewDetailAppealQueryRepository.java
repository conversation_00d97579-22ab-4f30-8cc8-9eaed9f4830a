package net.summerfarm.tms.performance.repository;



import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealEntity;
import net.summerfarm.tms.performance.entity.dataobject.ReviewDetailAppealPageObj;
import net.summerfarm.tms.query.performance.AppealQuery;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:06
* @version 1.0
*
*/
public interface DeliveryPerformanceReviewDetailAppealQueryRepository {

    DeliveryPerformanceReviewDetailAppealEntity selectById(Long id);

    /**
     * 根据履约明细ID查询申诉信息
     * @param deliveryPerReviewDetailIds 履约明细ID集合
     * @param performanceReviewTaskIds 审核任务ID集合
     */
    List<DeliveryPerformanceReviewDetailAppealEntity> queryAppealListByDeliveryPerReviewDetailIdsAndTaskIds(List<Long> deliveryPerReviewDetailIds, List<Long> performanceReviewTaskIds);

    /**
     * 根据批次ID查询申诉信息
     * @param deliveryBatchIdList 批次ID集合
     * @param performanceReviewTaskIds 审核任务ID集合
     */
    List<DeliveryPerformanceReviewDetailAppealEntity> queryAppealListByBatchIdsAndTaskIds(List<Long> deliveryBatchIdList, List<Long> performanceReviewTaskIds);

    /**
     * 分页查询申诉信息
     * @param query 查询
     * @return 结果
     */
    PageInfo<ReviewDetailAppealPageObj> queryReviewDetailAppealPage(AppealQuery query);

    /**
     * 根据申诉ID查询申诉信息
     * @param id 申诉ID
     * @return 结果
     */
    ReviewDetailAppealPageObj queryDetailById(Long id);

    /**
     * 根据申诉ID集合批量查询
     * @param appealIds 履约申诉ID集合
     */
    List<DeliveryPerformanceReviewDetailAppealEntity> queryAppealListByIds(List<Long> appealIds);
}