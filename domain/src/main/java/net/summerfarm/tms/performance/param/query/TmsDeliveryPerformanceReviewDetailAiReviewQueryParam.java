package net.summerfarm.tms.performance.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-09-18 00:16:55
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryPerformanceReviewDetailAiReviewQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 履约审核任务id
	 */
	private Long performanceReviewTaskId;

	/**
	 * 履约审核详情id
	 */
	private Long performanceReviewDetailId;
	/**
	 * 履约审核详情id
	 */
	private List<Long> performanceReviewDetailIdList;

	/**
	 * 签收照片结果
	 */
	private String citySignPicResults;

	/**
	 * 门店照片结果
	 */
	private String cityDeliveryPicResults;

	/**
	 * 货品照片结果
	 */
	private String cityProductPicResults;

	/**
	 * 签收照片通过,1通过,0不通过
	 */
	private Integer citySignPicPass;

	/**
	 * 门店照片通过,1通过,0不通过
	 */
	private Integer cityDeliveryPicPass;

	/**
	 * 货品照片通过,1通过,0不通过
	 */
	private Integer cityProductPicPass;

	/**
	 * 是否全部通过
	 */
	private Integer allPass;

	/**
	 * 车辆照片结果
	 */
	private String cityVehiclePlatePicResults;

	/**
	 * 车辆照片通过,1通过,0不通过
	 */
	private Integer cityVehiclePlatePicPass;

	/**
	 * 装载照片结果
	 */
	private String cityLoadPicResults;

	/**
	 * 装载照片通过,1通过,0不通过
	 */
	private Integer cityLoadPicPass;
	
}