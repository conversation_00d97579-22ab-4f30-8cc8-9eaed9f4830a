package net.summerfarm.tms.performance.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailQueryParam;
import net.summerfarm.tms.query.delivery.PerformanceReviewDetailQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/6/28 16:13<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryPerformanceReviewDetailRepository {
    /**
     * 批量保存
     * @param deliveryPerformanceReviewDetailEntities 实体
     */
    void saveBatch(List<DeliveryPerformanceReviewDetailEntity> deliveryPerformanceReviewDetailEntities);

    /**
     * 查询数量
     * @param performanceReviewTaskId 主键
     * @param state 状态 0待审核 1无法审核 2合规 3不合规
     * @return 数量
     */
    long queryCountByTaskIdAndState(Long performanceReviewTaskId, Integer state);

    /**
     * 分页查询
     * @param performanceReviewDetailQuery 查询
     * @return 结果
     */
    PageInfo<DeliveryPerformanceReviewDetailEntity> queryListPage(PerformanceReviewDetailQuery performanceReviewDetailQuery);

    /**
     * 根据批次ID分组分页查询
     * @param performanceReviewDetailQuery 查询
     * @return 结果
     */
    PageInfo<DeliveryPerformanceReviewDetailEntity> queryReviewDetailPageGgBatchId(PerformanceReviewDetailQuery performanceReviewDetailQuery);

    /**
     * 根据批次ID集合查询数据
     * @param deliveryBatchIdList 批次ID集合
     * @param taskIds 任务ID集合
     * @return 结果
     */
    List<DeliveryPerformanceReviewDetailEntity> queryByBatchIdsAndTaskIds(List<Long> deliveryBatchIdList,List<Long> taskIds);

    /**
     * 根据主键查询
     * @param performanceReviewDetailId 主键
     * @return 结果
     */
    DeliveryPerformanceReviewDetailEntity queryById(Long performanceReviewDetailId);

    /**
     * 状态变更
     * @param detailEntities 报文
     */
    void update(List<DeliveryPerformanceReviewDetailEntity> detailEntities);

    /**
     * 查询干线数量
     * @param performanceReviewTaskId 主键
     * @param state 状态 0待审核 1无法审核 2合规 3不合规
     * @return 数量
     */
    long queryTrunkCountByTaskIdAndState(Long performanceReviewTaskId, Integer state);

    /**
     * 查询
     * @param query 查询条件
     * @return 结果
     */
    List<DeliveryPerformanceReviewDetailEntity> queryList(PerformanceReviewDetailQuery query);

    PageInfo<TmsDeliveryPerformanceReviewDetailEntity> getPage(TmsDeliveryPerformanceReviewDetailQueryParam param);
}
