package net.summerfarm.tms.performance.repository;

import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAppealItemQueryParam;


/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:07
* @version 1.0
*
*/
public interface DeliveryPerformanceReviewDetailAppealItemQueryRepository {

    DeliveryPerformanceReviewDetailAppealItemEntity selectById(Long id);

    List<DeliveryPerformanceReviewDetailAppealItemEntity> selectByCondition(TmsDeliveryPerformanceReviewDetailAppealItemQueryParam param);


}