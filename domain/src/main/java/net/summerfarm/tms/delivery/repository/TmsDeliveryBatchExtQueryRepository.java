package net.summerfarm.tms.delivery.repository;



import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.delivery.entity.TmsDeliveryBatchExtEntity;
import net.summerfarm.tms.delivery.param.query.TmsDeliveryBatchExtQueryParam;

import java.util.List;
import java.util.Map;


/**
*
* <AUTHOR>
* @date 2025-05-14 15:11:42
* @version 1.0
*
*/
public interface TmsDeliveryBatchExtQueryRepository {

    PageInfo<TmsDeliveryBatchExtEntity> getPage(TmsDeliveryBatchExtQueryParam param);

    TmsDeliveryBatchExtEntity selectById(Long id);

    List<TmsDeliveryBatchExtEntity> selectByCondition(TmsDeliveryBatchExtQueryParam param);

    /**
     * 根据batchId查询保温措施图片
     * @param deliveryBatchId 批次ID
     * @return 保温措施图片
     */
    String queryKeepTemperatureMethodPicsByBatchId(Long deliveryBatchId);

    /**
     * 根据batchIds查询保温措施图片
     * @param batchIds 批次ID
     * @return key 批次ID value 保温措施图片
     */
    Map<Long,  String> queryKeepTemperatureMethodPicsByBatchIds(List<Long> batchIds);
}