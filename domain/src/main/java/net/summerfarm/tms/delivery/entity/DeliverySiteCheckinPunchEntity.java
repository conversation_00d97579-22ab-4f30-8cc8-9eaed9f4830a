package net.summerfarm.tms.delivery.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: 到店打卡
 * date: 2023/10/25 18:38<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteCheckinPunchEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 配送点位Id
     */
    private Long deliverySiteId;

    /**
     * 打卡范围km
     */
    private BigDecimal punchRange;

    /**
     * 打卡详细地址
     */
    private String punchAddress;

    /**
     * 打卡POI
     */
    private String punchPoi;

    /**
     * 打卡到店距离km
     */
    private BigDecimal distanceToSite;

    /**
     * 超区原因
     */
    private String exceedReason;

}
