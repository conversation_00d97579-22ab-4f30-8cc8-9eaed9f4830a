package net.summerfarm.tms.delivery;

import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryBatchFareEntity;

import java.util.List;

/**
 * Description:配送批次费用项 仓库
 * date: 2023/5/30 14:25
 *
 * <AUTHOR>
 */
public interface DeliveryBatchFareRepository {


    /**
     * 查询集合
     * @param deliveryBatchId 配送批次ID
     * @return 结果
     */
    List<DeliveryBatchFareEntity> queryList(Long deliveryBatchId);

    /**
     * 保存配送批次费用项
     * 标准 有则覆盖 没有则新增
     *
     * @param deliveryBatchFareEntities 最新配送批次费用项
     * @param deliveryBatchId 配送批次ID
     */
    void saveOrUpdate(List<DeliveryBatchFareEntity> deliveryBatchFareEntities, Long deliveryBatchId);
}
