package net.summerfarm.tms.delivery.helper;

import lombok.Data;

/**
 * Description: 配送站点标品存储条件<br/>
 * date: 2024/9/2 16:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteStandardTempCondition {

    /**
     * 配送站点ID
     */
    private Long deliverySiteId;

    /**
     * 标品存储条件
     */
    private String standardTempCondition;

    /**
     * 非水果冷藏数量
     */
    private Integer nonFruitColdNum;

    /**
     * 非水果冷冻数量
     */
    private Integer nonFruitFreezeNum;
}
