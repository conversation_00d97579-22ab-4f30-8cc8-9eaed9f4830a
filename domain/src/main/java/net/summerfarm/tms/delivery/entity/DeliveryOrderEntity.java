package net.summerfarm.tms.delivery.entity;

import lombok.Data;
import net.summerfarm.tms.enums.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 配送单
 */
@Data
public class DeliveryOrderEntity {
    private Long id;
    private Long distOrderId;
    private Long deliveryBatchId;
    private DeliveryOrderStatusEnum status;
    /**
     * 外部单号
     */
    private String outerOrderId;

    /**
     * 外部客户号
     */
    private String outerClientId;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 外部联系人ID
     */
    private String outerContactId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 起点
     */
    private Long beginSiteId;

    /**
     * 起点名
     */
    private String beginSiteName;

    /**
     * 起点详细地址
     */
    private String beginSiteFullAddress;

    /**
     * 终点
     */
    private Long endSiteId;

    /**
     * 终点详细地址
     */
    private String endSiteFullAddress;

    /**
     * 终点名
     */
    private String endSiteName;

    /**
     * 委托单来源
     */
    private DistOrderSourceEnum source;

    /**
     * 配送单站点类型枚举
     */
    private DeliveryOrderSiteTypeEnum deliveryOrderSiteType;

    /**
     * 委托单来源描述
     */
    private String sourceDesc;

    /**
     * 履约时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 配送类型 0 配送 1 回收
     *
     * @see DeliveryOrderTypeEnum
     */
    private Integer type;

    private List<DeliveryItemEntity> deliveryItemEntityList;

    /**
     * 拦截编号
     */
    private Integer interceptReasonCode;
    /**
     * 拦截原因
     */
    private String interceptReason;

    private Integer siteType;

    /**
     * 详情地址
     */
    private String addressDetail;

    /**
     * 配送批次信息
     */
    private DeliveryBatchEntity deliveryBatchEntity;

    /**
     * 配送备注
     */
    private String sendRemark;

    /**
     * 拣货类型
     */
    private DistPickTypeEnum pickType;

    /**
     * 外部租户号
     */
    private String outTenantId;

    /**
     * 外部品牌名
     */
    private String outBrandName;


    /**
     * 围栏id
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 区域id
     */
    private Integer adCodeMsgId;

    /**
     * 自定义区域名称
     */
    private String customAreaName;
    public void initSiteType(Long midSiteId) {
        if (midSiteId == null) {
            deliveryOrderSiteType = DeliveryOrderSiteTypeEnum.NO_MID;
        } else if (midSiteId.equals(beginSiteId)) {
            deliveryOrderSiteType = DeliveryOrderSiteTypeEnum.BEGIN_SITE_MID;
        } else {
            deliveryOrderSiteType = DeliveryOrderSiteTypeEnum.END_SITE_MID;
        }
    }

    /**
     * 克隆一个实体
     * @param deliveryOrderEntity
     * @return
     */
    public static DeliveryOrderEntity copy(DeliveryOrderEntity deliveryOrderEntity){
        DeliveryOrderEntity deliveryOrderNew=new DeliveryOrderEntity();
        deliveryOrderNew.setId(deliveryOrderEntity.getId());
        deliveryOrderNew.setDistOrderId(deliveryOrderEntity.getDistOrderId());
        deliveryOrderNew.setDeliveryBatchId(deliveryOrderEntity.getDeliveryBatchId());
        deliveryOrderNew.setStatus(deliveryOrderEntity.getStatus());
        deliveryOrderNew.setOuterOrderId(deliveryOrderEntity.getOuterOrderId());
        deliveryOrderNew.setOuterClientId(deliveryOrderEntity.getOuterClientId());
        deliveryOrderNew.setOuterClientName(deliveryOrderEntity.getOuterClientName());
        deliveryOrderNew.setOuterContactId(deliveryOrderEntity.getOuterContactId());
        deliveryOrderNew.setName(deliveryOrderEntity.getName());
        deliveryOrderNew.setPhone(deliveryOrderEntity.getPhone());
        deliveryOrderNew.setBeginSiteId(deliveryOrderEntity.getBeginSiteId());
        deliveryOrderNew.setBeginSiteName(deliveryOrderEntity.getBeginSiteName());
        deliveryOrderNew.setBeginSiteFullAddress(deliveryOrderEntity.getBeginSiteFullAddress());
        deliveryOrderNew.setEndSiteId(deliveryOrderEntity.getEndSiteId());
        deliveryOrderNew.setEndSiteFullAddress(deliveryOrderEntity.getEndSiteFullAddress());
        deliveryOrderNew.setEndSiteName(deliveryOrderEntity.getEndSiteName());
        deliveryOrderNew.setSource(deliveryOrderEntity.getSource());
        deliveryOrderNew.setDeliveryOrderSiteType(deliveryOrderEntity.getDeliveryOrderSiteType());
        deliveryOrderNew.setSourceDesc(deliveryOrderEntity.getSourceDesc());
        deliveryOrderNew.setDeliveryTime(deliveryOrderEntity.getDeliveryTime());
        deliveryOrderNew.setFinishTime(deliveryOrderEntity.getFinishTime());
        deliveryOrderNew.setType(deliveryOrderEntity.getType());
        deliveryOrderNew.setDeliveryItemEntityList(deliveryOrderEntity.getDeliveryItemEntityList());
        deliveryOrderNew.setInterceptReasonCode(deliveryOrderEntity.getInterceptReasonCode());
        deliveryOrderNew.setInterceptReason(deliveryOrderEntity.getInterceptReason());
        deliveryOrderNew.setSiteType(deliveryOrderEntity.getSiteType());
        deliveryOrderNew.setAddressDetail(deliveryOrderEntity.getAddressDetail());
        deliveryOrderNew.setSendRemark(deliveryOrderEntity.getSendRemark());
        return deliveryOrderNew;
    }

    public DeliveryOrderEntity interceptDeliveryOrder() {
        DeliveryOrderEntity interceptDeliveryOrder = new DeliveryOrderEntity();
        interceptDeliveryOrder.setId(this.getId());
        interceptDeliveryOrder.setStatus(DeliveryOrderStatusEnum.CLOSE);
        interceptDeliveryOrder.setDeliveryBatchId(this.getDeliveryBatchId());
        return interceptDeliveryOrder;
    }
}
