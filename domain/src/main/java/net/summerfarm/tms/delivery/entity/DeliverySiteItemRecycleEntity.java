package net.summerfarm.tms.delivery.entity;

import lombok.Data;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.enums.DeliverySiteItemRecycleEnums;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description:点位物品回收实体
 * date: 2023/12/19 16:25
 *
 * <AUTHOR>
 */
@Data
public class DeliverySiteItemRecycleEntity implements Serializable {

    private static final long serialVersionUID = -6804259429240651782L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配送点位物品ID
     */
    private Long deliverySiteItemId;

    /**
     * 配送点位ID
     */
    private Long deliverySiteId;

    /**
     * 外部货品ID(sku)
     */
    private String outItemId;

    /**
     * 回收照片
     */
    private String recyclePics;

    /**
     * 规格单位数量
     */
    private BigDecimal specificationQuantity;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 基础规格单位数量(小规格单位)
     */
    private BigDecimal basicSpecQuantity;

    /**
     * 基础规格单位(小规格单位)
     */
    private String basicSpecUnit;

    /**
     * 回收原因类型，1：回收数量不符，2：商品已损坏，3：店铺未开门下次回收，4：包装破损，5：客户已用不退了，6：其它
     */
    private DeliverySiteItemRecycleEnums.ReasonType reasonType;

    /**
     * 异常说明备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 温区,1:常温,2:冷藏,3:冷冻
     */
    private Integer temperature;
    public String getRealRecycleQuantityStr() {
        StringBuilder recycleQuantityStr = new StringBuilder();
        recycleQuantityStr.append(this.specificationQuantity);
        recycleQuantityStr.append(this.specificationUnit);
        recycleQuantityStr.append(Constants.Symbol.PLUS);
        recycleQuantityStr.append(this.basicSpecQuantity);
        recycleQuantityStr.append(this.basicSpecUnit);
        return recycleQuantityStr.toString();
    }
}
