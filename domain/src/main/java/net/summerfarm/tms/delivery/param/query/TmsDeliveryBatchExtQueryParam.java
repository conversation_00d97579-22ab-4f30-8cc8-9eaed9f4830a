package net.summerfarm.tms.delivery.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-05-14 15:11:42
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryBatchExtQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 配送批次ID
	 */
	private Long deliveryBatchId;

	/**
	 * 扩展信息key
	 */
	private String extKey;

	/**
	 * 扩展信息value
	 */
	private String extValue;

	/**
	 * 配送批次ID集合
	 */
	private List<Long> batchIds;

	

	
}