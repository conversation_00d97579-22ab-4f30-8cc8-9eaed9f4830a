package net.summerfarm.tms.delivery;

import net.summerfarm.tms.delivery.entity.DeliverySiteCheckinPunchEntity;

/**
 * Description: <br/>
 * date: 2023/10/25 18:35<br/>
 *
 * <AUTHOR> />
 */
public interface DeliverySiteCheckinPunchRepository {

    /**
     * 根据配送点位查询打卡记录信息
     * @param deliverySiteId 配送点位ID
     * @return 到店打卡记录
     */
    DeliverySiteCheckinPunchEntity queryByDeliverySiteId(Long deliverySiteId);

    /**
     * 保存到店打卡信息
     * @param entity 到店打卡信息
     */
    void save(DeliverySiteCheckinPunchEntity entity);
}
