package net.summerfarm.tms.delivery.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/6/28 18:53<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CitySitePerformanceVO {

    /**
     * 配送路线
     */
    private String pathCode;

    /**
     * 路线名称
     */
    private String pathName;

    /**
     * 出仓温度
     */
    private BigDecimal signOutTemperature;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机号码
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车型
     */
    private Integer carType;

    /**
     * 装载照片
     */
    private String loadPics;

    /**
     * 门店抬头
     */
    private String deliveryPic;

    /**
     * json 配送点位属性字段扩展(sealPics-封签照片，vehiclePlatePics-车牌照)
     */
    private String propertyJson;
    /**
     * 车牌照片
     */
    private String vehiclePlatePics;

    /**
     * 点位序号
     */
    private Integer sequence;

    /**
     * 配送方式 0正常配送 1专车配送
     */
    private Integer sendWay;

    /**
     * 地址
     */
    private String address;

    /**
     * 店铺名称
     */
    private String clientName;

    /**
     * 是否超出距离 0 正常 1超出
     */
    private Integer outDistance;

    /**
     * 配送存储条件
     */
    private String temperatureConditions;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 签收面照片
     */
    private String signPic;

    /**
     * 货物照片
     */
    private String productPic;

    /**
     * 10未到站,20已到站,22.已配送,25已拣货30已出发
     */
    private Integer status;

    /**
     * 车辆存储条件 0常温、1冷藏
     */
    private Integer storage;


    /**
     * 冷藏照片
     */
    private String refrigeratePics;

    /**
     * 冷冻照片
     */
    private String freezePics;
}
