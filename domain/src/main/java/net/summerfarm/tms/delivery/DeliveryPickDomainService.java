package net.summerfarm.tms.delivery;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.SiteDomainService;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistClientVO;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.wms.dto.WmsOrderProcessDTO;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.delivery.DeliveryPickQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/11/4 15:01<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeliveryPickDomainService {
    @Lazy
    @Resource
    private DistOrderDomainService distOrderDomainService;
    private final DeliveryPickRepository deliveryPickRepository;
    private final DistOrderRepository distOrderRepository;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final SiteDomainService siteDomainService;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final DeliverySiteRepository deliverySiteRepository;


    /**
     * 订单拦截
     * @param distId 委托单id
     */
    public void intercept(Long distId) {
        DistOrderEntity distOrder = distOrderRepository.query(distId);
        //签发拦截
        if(distOrder.getStatus() == DistOrderStatusEnum.TO_BE_PICKED){
            List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().distOrderId(distOrder.getDistId()).build());
            //只需要点位和批次，所以只取一条
            DeliveryOrderEntity deliveryOrder = deliveryOrderEntities.get(0);
            //订单拦截只会拦截配送的，所以只会有一条
            List<DistOrderEntity> distOrderEntities = distOrderRepository.queryListWithItem(
                    DistOrderQuery.builder().distId(distOrder.getDistId()).type(DistTypeEnum.DELIVERY.getCode()).build());
            DistOrderEntity distOrderDeliveryEntity = distOrderEntities.get(0);
            //sku --数量
            Map<String, List<DistItemVO>> itemKeyMap = distOrderDeliveryEntity.getDistItems().stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));
            String particle;
            if(Objects.equals(distOrderDeliveryEntity.getPickType(), DistPickTypeEnum.BRAND_SINGLE)){
                particle = new StringJoiner("#").add(distOrderDeliveryEntity.getDistClientVO().getOutTenantId())
                        .add(distOrderDeliveryEntity.getDistClientVO().getOutBrandName()).toString();
            }else{
                particle = DeliveryPickTypeEnum.DEFAULT.getName();
            }
            ArrayList<String> outItemList = new ArrayList<>(itemKeyMap.keySet());
            List<DeliveryPickEntity> deliveryPickEntitys = deliveryPickRepository.queryList(DeliveryPickQuery.builder()
                    .siteId(deliveryOrder.getBeginSiteId())
                    .batchId(deliveryOrder.getDeliveryBatchId())
                    .outItemIds(outItemList)
                    .particle(particle)
                    .build());

            //更新拦截数量
            deliveryPickEntitys.forEach(pick ->{
                List<DistItemVO> distItemVOs = itemKeyMap.get(pick.getOutItemId());
                int totalQuality = distItemVOs.stream().mapToInt(DistItemVO::getQuantity).sum();
                if(pick.getQuantity() >= totalQuality + pick.getInterceptQuantity()){
                    pick.setInterceptQuantity(Math.min(pick.getQuantity(),totalQuality + pick.getInterceptQuantity()));
                }else{
                    pick.setInterceptQuantity(pick.getQuantity());
                    DistItemVO distItemVO = DistItemVO.builder().quantity(totalQuality - pick.getQuantity()).build();
                    itemKeyMap.put(pick.getOutItemId(), Arrays.asList(distItemVO));
                }
                deliveryPickRepository.update(pick);
            });
        }
    }

    /**
     * 完成排线
     * @param deliveryBatchEntity 批次
     */
    public void completePath(DeliveryBatchEntity deliveryBatchEntity) {
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryBatchEntity.getDeliveryOrderEntityList();
        //获取批次的起点运输信息
        DeliverySiteEntity beginDeliverySite = deliveryBatchEntity.getBeginDeliverySite();

        Set<Long> distOrderIdSet = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toSet());
        List<DistOrderEntity> distOrderEntities = new ArrayList<>();

        List<DistOrderEntity> distOrderEntitys = distOrderRepository.queryListWithItemWithBeginSite(DistOrderQuery.builder().distIdList(new ArrayList<Long>(distOrderIdSet)).build());
        for (DistOrderEntity distOrderEntity : distOrderEntitys) {
            if(distOrderEntity.getCancelType() != DistOrderCancelTypeEnum.intercept){
                distOrderEntities.add(distOrderEntity);
            }
        }
        this.createPickDetail(deliveryBatchEntity, beginDeliverySite, distOrderEntities);
    }

    /**
     * 生成拣货任务
     * @param deliveryBatchEntity 批次信息
     * @param beginDeliverySite 城配仓配送点位信息
     * @param distOrderEntities 委托单信息
     */
    private void createPickDetail(DeliveryBatchEntity deliveryBatchEntity, DeliverySiteEntity beginDeliverySite, List<DistOrderEntity> distOrderEntities) {
        // POP城配仓标识，POP只有非独立拣货
        boolean popStoreNoFlag = siteDomainService.isPopBySiteIdCache(deliveryBatchEntity.getBeginSiteId());
        if(popStoreNoFlag){
            normalPick(deliveryBatchEntity, beginDeliverySite, distOrderEntities);
        }else{
            // 大客户独立拣货
            this.bigCustomerSinglePick(deliveryBatchEntity, beginDeliverySite, distOrderEntities);

            // 非独立拣货
            List<DistOrderEntity> notAloneDistOrderList = distOrderEntities.stream()
                    .filter(distOrderEntity -> Objects.equals(distOrderEntity.getPickType(), DistPickTypeEnum.DEFAULT)).collect(Collectors.toList());
            normalPick(deliveryBatchEntity, beginDeliverySite, notAloneDistOrderList);

            // 生成门店拣货信息
            this.createMerchantPick(deliveryBatchEntity, beginDeliverySite, distOrderEntities);
        }
    }

    /**
     * 普通拣货
     * @param deliveryBatchEntity 批次信息
     * @param beginDeliverySite 起点信息
     * @param notAloneDistOrderList 非独立拣货委托单
     */
    private void normalPick(DeliveryBatchEntity deliveryBatchEntity, DeliverySiteEntity beginDeliverySite, List<DistOrderEntity> notAloneDistOrderList) {
        ArrayList<DeliveryPickEntity> pick = createPick(deliveryBatchEntity, beginDeliverySite, notAloneDistOrderList, DeliveryPickTypeEnum.DEFAULT);
        pick = (ArrayList<DeliveryPickEntity>) pick.stream().filter(deliveryPickEntity -> deliveryPickEntity.getQuantity() != 0).collect(Collectors.toList());
        deliveryPickRepository.saveBatch(pick);
    }

    /**
     * 大客户独立拣货
     * @param deliveryBatchEntity 批次信息
     * @param beginDeliverySite 起点信息
     * @param distOrderEntities 独立拣货委托单
     */
    private void bigCustomerSinglePick(DeliveryBatchEntity deliveryBatchEntity, DeliverySiteEntity beginDeliverySite, List<DistOrderEntity> distOrderEntities) {
        //独立拣货的委托单
        List<DistOrderEntity> aloneDistOrderList = distOrderEntities.stream()
                .filter(distOrderEntity -> Objects.equals(distOrderEntity.getPickType(), DistPickTypeEnum.BRAND_SINGLE)).collect(Collectors.toList());

        //大客户独立拣货 根据租户id分组
        Map<String, List<DistOrderEntity>> outerTenantIdDistOrderMap = aloneDistOrderList.stream().collect(Collectors.groupingBy(e -> e.getDistClientVO().getOutTenantId()));
        for (String outerTenantId : outerTenantIdDistOrderMap.keySet()) {
            //品牌分组
            List<DistOrderEntity> aloneDistOrderEntities = outerTenantIdDistOrderMap.get(outerTenantId);
            ArrayList<DeliveryPickEntity> pick = createPick(deliveryBatchEntity, beginDeliverySite, aloneDistOrderEntities, DeliveryPickTypeEnum.BRAND_SINGLE);
            pick = (ArrayList<DeliveryPickEntity>) pick.stream().filter(deliveryPickEntity -> deliveryPickEntity.getQuantity() != 0).collect(Collectors.toList());
            deliveryPickRepository.saveBatch(pick);
        }
    }

    /**
     * 生成门店拣货
     * @param batchEntity 批次
     * @param deliverySite 城配仓点位
     * @param distOrders 委托单信息
     */
    private void createMerchantPick(DeliveryBatchEntity batchEntity, DeliverySiteEntity deliverySite, List<DistOrderEntity> distOrders) {
        //按照门店拣货
        Map<String, List<DistOrderEntity>> merchant2DistOrderListMap = distOrders.stream()
                .filter(distOrderEntity -> Objects.equals(distOrderEntity.getPickType(), DistPickTypeEnum.MERCHANT_PICK))
                .collect(Collectors.groupingBy(distOrderEntity -> distOrderEntity.getDistClientVO().getOutClientId()));
        ArrayList<DeliveryPickEntity> merchantPicks = new ArrayList<>();
        for (String outClientId : merchant2DistOrderListMap.keySet()) {
            List<DistOrderEntity> distOrderEntities = merchant2DistOrderListMap.get(outClientId);
            if(CollectionUtils.isEmpty(distOrderEntities)){
                continue;
            }
            List<DistItemVO> distItems = distOrderEntities.stream()
                    .map(DistOrderEntity::getDistItems)
                    .flatMap(Collection::stream)
                    .filter(distItem -> Objects.equals(distItem.getItemDeliveryTypeEnum(), DistItemDeliveryTypeEnum.DELIVERY))
                    .collect(Collectors.toList());

            //按照sku分组
            Map<String, List<DistItemVO>> itemIdDistItemMap = distItems.stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));

            for (String outItemId : itemIdDistItemMap.keySet()) {
                List<DistItemVO> distItemVOList = itemIdDistItemMap.get(outItemId);

                DeliveryPickEntity deliveryPickEntity = new DeliveryPickEntity();
                deliveryPickEntity.setDeliveryBatchId(batchEntity.getId());
                deliveryPickEntity.setSiteId(batchEntity.getBeginSiteId());
                deliveryPickEntity.setDriverId(batchEntity.getDriverId());
                deliveryPickEntity.setOutItemId(outItemId);
                deliveryPickEntity.setCategoryType(distItemVOList.get(0).getType());
                deliveryPickEntity.setItemDesc(distItemVOList.get(0).getOutItemName());

                DistClientVO distClientVO = distOrderEntities.get(0).getDistClientVO();

                deliveryPickEntity.setParticle(distClientVO == null ? "" : distClientVO.getOutBrandName());
                deliveryPickEntity.setType(DeliveryPickTypeEnum.MERCHANT_PICK.getCode());
                deliveryPickEntity.setQuantity(distItemVOList.stream().mapToInt(DistItemVO::getQuantity).sum());
                deliveryPickEntity.setUnit(distItemVOList.get(0).getUnit());
                deliveryPickEntity.setStatus(DeliveryPickStatusEnum.WAIT_PICK.getCode());
                deliveryPickEntity.setTemperature(distItemVOList.get(0).getTemperatureEnum().getCode());
                deliveryPickEntity.setDeliverySiteId(deliverySite.getId());
                deliveryPickEntity.setSpecification(distItemVOList.get(0).getSpecification());
                deliveryPickEntity.setPackType(distItemVOList.get(0).getPackType());
                deliveryPickEntity.setOuterClientName(distClientVO == null ? "" : distClientVO.getOutClientName());
                if(distItemVOList.get(0).getTemperatureEnum() != null){
                    deliveryPickEntity.setTemperature(distItemVOList.get(0).getTemperatureEnum().getCode());
                }

                merchantPicks.add(deliveryPickEntity);
            }
        }
        merchantPicks = (ArrayList<DeliveryPickEntity>) merchantPicks.stream().filter(deliveryPickEntity -> deliveryPickEntity.getQuantity() != 0).collect(Collectors.toList());
        deliveryPickRepository.saveBatch(merchantPicks);
    }

    /**
     * 生成拣货数据
     * @param deliveryBatchEntity 批次
     * @param beginDeliverySite 城配点位
     * @param aloneDistOrderEntities 委托单
     * @param deliveryPickTypeEnum 拣货枚举
     * @return
     */
    private ArrayList<DeliveryPickEntity> createPick(DeliveryBatchEntity deliveryBatchEntity, DeliverySiteEntity beginDeliverySite, List<DistOrderEntity> aloneDistOrderEntities,
                                                     DeliveryPickTypeEnum deliveryPickTypeEnum) {
        //获取下面的item分组
        List<DistItemVO> distItemVOS = aloneDistOrderEntities.stream().map(DistOrderEntity::getDistItems).flatMap(Collection::stream).collect(Collectors.toList());
        //只过滤配送的数据
        distItemVOS = distItemVOS.stream().filter(distItem -> Objects.equals(distItem.getDeliveryType(), DistItemDeliveryTypeEnum.DELIVERY.getCode())).collect(Collectors.toList());

        //按照sku分组
        Map<String, List<DistItemVO>> itemIdDistItemMap = distItemVOS.stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));

        ArrayList<DeliveryPickEntity> deliveryPickEntities = new ArrayList<>();

        for (String outItemId : itemIdDistItemMap.keySet()) {
            List<DistItemVO> distItemVOList = itemIdDistItemMap.get(outItemId);

            DeliveryPickEntity deliveryPickEntity = new DeliveryPickEntity();
            deliveryPickEntity.setDeliveryBatchId(deliveryBatchEntity.getId());
            deliveryPickEntity.setSiteId(deliveryBatchEntity.getBeginSiteId());
            deliveryPickEntity.setDriverId(deliveryBatchEntity.getDriverId());
            deliveryPickEntity.setOutItemId(outItemId);
            deliveryPickEntity.setCategoryType(distItemVOList.get(0).getType());
            deliveryPickEntity.setItemDesc(distItemVOList.get(0).getOutItemName());

            if(!StringUtils.isEmpty(aloneDistOrderEntities.get(0).getDistClientVO().getOutTenantId()) && !StringUtils.isEmpty(aloneDistOrderEntities.get(0).getDistClientVO().getOutBrandName())) {
                deliveryPickEntity.setParticle(new StringJoiner("#").add(aloneDistOrderEntities.get(0).getDistClientVO().getOutTenantId())
                        .add(aloneDistOrderEntities.get(0).getDistClientVO().getOutBrandName()).toString());
            }else{
                deliveryPickEntity.setParticle(deliveryPickTypeEnum.getName());
            }
            deliveryPickEntity.setType(deliveryPickTypeEnum.getCode());
            deliveryPickEntity.setQuantity(distItemVOList.stream().mapToInt(DistItemVO::getQuantity).sum());
            deliveryPickEntity.setUnit(distItemVOList.get(0).getUnit());
            deliveryPickEntity.setStatus(DeliveryPickStatusEnum.WAIT_PICK.getCode());
            deliveryPickEntity.setTemperature(distItemVOList.get(0).getTemperatureEnum().getCode());
            deliveryPickEntity.setDeliverySiteId(beginDeliverySite.getId());
            deliveryPickEntity.setSpecification(distItemVOList.get(0).getSpecification());
            deliveryPickEntity.setPackType(distItemVOList.get(0).getPackType());
            if(distItemVOList.get(0).getTemperatureEnum() != null){
                deliveryPickEntity.setTemperature(distItemVOList.get(0).getTemperatureEnum().getCode());
            }

            deliveryPickEntities.add(deliveryPickEntity);
            //deliveryPickRepository.save(deliveryPickEntity);
        }

        return deliveryPickEntities;
    }

    /**
     * 生成大客户拣货
     * @param deliveryBatchEntity 批次信息
     */
    public void createBigCustomerPick(DeliveryBatchEntity deliveryBatchEntity) {
        if(deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            log.info("大客户生成拣货任务,不存在批次信息");
            return;
        }
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryBatchEntity.getDeliveryOrderEntityList();
        if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
            log.info("大客户生成拣货任务,不存在配送订单配送信息");
            return;
        }
        //获取批次的起点运输信息
        DeliverySiteEntity beginDeliverySite = deliveryBatchEntity.getBeginDeliverySite();
        //委托单集合
        List<Long> distOrderIdList = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
        List<DistOrderEntity> distOrderEntityList = distOrderRepository.queryValidListWithItemByIds(distOrderIdList);
        //查询加工数据并且过滤无效数据
        List<WmsOrderProcessDTO> wmsOrderProcessDTOs = distOrderDomainService.queryWmsOrderProcessDTOS(distOrderEntityList);
        //过滤早于完成排线的时间并且处于加工状态的数据
        wmsOrderProcessDTOs = wmsOrderProcessDTOs.stream()
                .filter(wmsOrderProcessDTO -> wmsOrderProcessDTO.getFinishTime() != null)
                .filter(wmsOrderProcessDTO -> wmsOrderProcessDTO.getFinishTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().isBefore(deliveryBatchEntity.getBePathTime()))
                .filter(wmsOrderProcessDTO -> !Objects.equals(WmsTaskStatusEnum.NO.getCode(), wmsOrderProcessDTO.getTaskStatus()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(wmsOrderProcessDTOs)){
            log.info("大客户生成拣货任务,不存在加工信息或者完成时间在完成排线时间之后");
            return;
        }

        //生成deliveryPickEntities数据
        List<DeliveryPickEntity> deliveryPickEntities = getDeliveryPickEntityList(deliveryBatchEntity, beginDeliverySite, distOrderEntityList, wmsOrderProcessDTOs);
        //移除数据
        deliveryPickRepository.removeByBatchId(deliveryBatchEntity.getId());
        //保存大客户的数据
        deliveryPickEntities.stream()
                .filter(deliveryPickEntity -> deliveryPickEntity.getProcessQuantity() != null && deliveryPickEntity.getProcessQuantity() > 0)
                .forEach(deliveryPickEntity ->  deliveryPickRepository.save(deliveryPickEntity));
        //生成拣货任务
        createPickDetail(deliveryBatchEntity,beginDeliverySite,distOrderEntityList);

    }

    /**
     * 生成大客户拣货数据
     * @param deliveryBatchEntity 批次
     * @param beginDeliverySite 城配点位
     * @param distOrderEntityList 委托单集合
     * @param wmsOrderProcessDTOs 加工数据
     * @return
     */
    private List<DeliveryPickEntity> getDeliveryPickEntityList(DeliveryBatchEntity deliveryBatchEntity, DeliverySiteEntity beginDeliverySite, List<DistOrderEntity> distOrderEntityList, List<WmsOrderProcessDTO> wmsOrderProcessDTOs) {
        log.info("getDeliveryPickEntityList方法订单加工数据wmsOrderProcessDTOs:{}", JSON.toJSONString(wmsOrderProcessDTOs));
        log.info("getDeliveryPickEntityList方法订单数据distOrderEntityList:{}", JSON.toJSONString(distOrderEntityList));
        //订单号和加工数据映射
        Map<String, WmsOrderProcessDTO> orderNoProcessMap = wmsOrderProcessDTOs.stream()
                .collect(Collectors.toMap(WmsOrderProcessDTO::getOrderNo, Function.identity()));

        List<DeliveryPickEntity> deliveryPickEntities = new ArrayList<>();
        distOrderEntityList.forEach(distOrderEntity -> {
            WmsOrderProcessDTO wmsOrderProcessDTO = orderNoProcessMap.get(distOrderEntity.getDistClientVO().getOutOrderId());
            if(wmsOrderProcessDTO == null){
                return;
            }
            Map<String, List<DistItemVO>> skuDistItemMap = distOrderEntity.getDistItems().stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));
            List<DistItemVO> distItems = distOrderEntity.getDistItems();
            //加工数据
            wmsOrderProcessDTO.getOrderProcessDetailDTOList().forEach(orderProcessDetailDTO -> {
                List<DistItemVO> distItemVOList = skuDistItemMap.get(orderProcessDetailDTO.getProductSkuCode());
                if(CollectionUtils.isEmpty(distItemVOList)){
                    return;
                }
                distItemVOList.forEach(distItemVO -> {
                    DistItemVO finalDistItemVO = distItemVO;
                    for (DeliveryPickEntity deliveryPickEntity : deliveryPickEntities) {
                        //sku相同 拣货粒度相同 加工重量相同 加工单位相同 拣货类型相同
                        if(deliveryPickEntity.getOutItemId().equals(distItemVO.getOutItemId()) &&
                                Objects.equals(deliveryPickEntity.getParticle(),new StringJoiner("#")
                                        .add(distOrderEntity.getDistClientVO().getOutTenantId())
                                .add(distOrderEntity.getDistClientVO().getOutBrandName()).toString()) &&
                                Objects.equals(deliveryPickEntity.getProcessWeight(),orderProcessDetailDTO.getProductSkuSpecWeight()) &&
                                Objects.equals(deliveryPickEntity.getProcessUnit(),orderProcessDetailDTO.getProductSkuSpecUnit()) &&
                                Objects.equals(deliveryPickEntity.getType(),distOrderEntity.getPickType().getCode())){
                            //相加
                            deliveryPickEntity.setProcessQuantity(deliveryPickEntity.getProcessQuantity() + orderProcessDetailDTO.getProductSkuSpecSubmitQuantity());
                            deliveryPickEntity.setQuantity(deliveryPickEntity.getQuantity() + orderProcessDetailDTO.getOrigSkuQuantity());
                            //全部加工了
                            if(orderProcessDetailDTO.getNoProductSkuQuantity() == 0){
                                //将item给干掉
                                distItemVO.setQuantity(0);
                            }else{
                                //还剩一部分没有加工
                                int quantity = distItemVO.getQuantity() - orderProcessDetailDTO.getHaveProductSkuQuantity();
                                distItemVO.setQuantity(Math.max(quantity, 0));
                            }
                            return;
                        }
                    }
                    DeliveryPickEntity deliveryPickEntity = new DeliveryPickEntity();
                    deliveryPickEntity.setDeliveryBatchId(deliveryBatchEntity.getId());
                    deliveryPickEntity.setSiteId(deliveryBatchEntity.getBeginSiteId());
                    deliveryPickEntity.setDriverId(deliveryBatchEntity.getDriverId());
                    deliveryPickEntity.setOutItemId(distItemVO.getOutItemId());
                    deliveryPickEntity.setItemDesc(distItemVO.getOutItemName());
                    if(!StringUtils.isEmpty(distOrderEntity.getDistClientVO().getOutTenantId()) && !StringUtils.isEmpty(distOrderEntity.getDistClientVO().getOutBrandName())) {
                        deliveryPickEntity.setParticle(new StringJoiner("#").add(distOrderEntity.getDistClientVO().getOutTenantId())
                                .add(distOrderEntity.getDistClientVO().getOutBrandName()).toString());
                    }else{
                        deliveryPickEntity.setParticle(DeliveryPickTypeEnum.DEFAULT.getName());
                    }
                    //拣货类型
                    if(Objects.equals(distOrderEntity.getPickType(),DistPickTypeEnum.BRAND_SINGLE)){
                        deliveryPickEntity.setType(DeliveryPickTypeEnum.BRAND_SINGLE.getCode());
                    }else{
                        deliveryPickEntity.setType(DeliveryPickTypeEnum.DEFAULT.getCode());
                    }

                    deliveryPickEntity.setUnit(distItemVO.getUnit());
                    deliveryPickEntity.setStatus(DeliveryPickStatusEnum.WAIT_PICK.getCode());
                    deliveryPickEntity.setTemperature(distItemVO.getTemperatureEnum().getCode());
                    deliveryPickEntity.setQuantity(orderProcessDetailDTO.getOrigSkuQuantity());
                    deliveryPickEntity.setDeliverySiteId(beginDeliverySite.getId());
                    deliveryPickEntity.setSpecification(distItemVO.getSpecification());
                    deliveryPickEntity.setProcessFlag(DeliveryPickEnums.ProcessFlag.PROCESS_SKU.getValue());
                    deliveryPickEntity.setProcessQuantity(orderProcessDetailDTO.getProductSkuSpecSubmitQuantity());
                    deliveryPickEntity.setProcessUnit(orderProcessDetailDTO.getProductSkuSpecUnit());
                    deliveryPickEntity.setProcessWeight(orderProcessDetailDTO.getProductSkuSpecWeight());
                    deliveryPickEntity.setProcessConversionRatio(new BigDecimal(orderProcessDetailDTO.getOrigSkuQuantity())
                            .divide(new BigDecimal(orderProcessDetailDTO.getProductSkuSpecSubmitQuantity() == null || orderProcessDetailDTO.getProductSkuSpecSubmitQuantity() == 0
                                    ? 1 : orderProcessDetailDTO.getProductSkuSpecSubmitQuantity()
                                    ), 2, BigDecimal.ROUND_HALF_EVEN));

                    //全部加工了
                    if(orderProcessDetailDTO.getNoProductSkuQuantity() == 0){
                        distItems.forEach(dataItem ->{
                            if(Objects.equals(dataItem.getOutItemId(), finalDistItemVO.getOutItemId())){
                                //将item给干掉
                                //dataItem.setQuantity(0);
                                int quantity = dataItem.getQuantity() - orderProcessDetailDTO.getHaveProductSkuQuantity();
                                dataItem.setQuantity(Math.max(quantity, 0));
                            }
                        });
                    }else{
                        distItems.forEach(dataItem ->{
                            if(Objects.equals(dataItem.getOutItemId(), finalDistItemVO.getOutItemId())){
                                //还剩一部分没有加工
                                int quantity = dataItem.getQuantity() - orderProcessDetailDTO.getHaveProductSkuQuantity();
                                dataItem.setQuantity(Math.max(quantity, 0));
                            }
                        });
                    }
                    deliveryPickEntities.add(deliveryPickEntity);
                });
            });
        });
        return deliveryPickEntities;
    }

    /**
     * 拣货缺货
     * @param deliveryPickId 拣货ID
     * @param shortCnt 缺货数量
     */
    public void pickShort(Long deliveryPickId, Integer shortCnt) {
        if(deliveryPickId == null){
            throw new TmsRuntimeException(ErrorCodeEnum.PARAM_NOT_NULL, "deliveryPickId");
        }

        DeliveryPickEntity dbPickEntity = deliveryPickRepository.query(deliveryPickId);
        if(dbPickEntity == null){
            throw new TmsRuntimeException("拣货ID不存在");
        }
        if(dbPickEntity.getStatus() != DeliveryPickStatusEnum.WAIT_PICK.getCode()){
            throw new TmsRuntimeException("拣货状态异常");
        }
        if(dbPickEntity.getQuantity() < shortCnt){
            throw new TmsRuntimeException("缺货数量不能大于应拣数量");
        }

        dbPickEntity.setShortQuantity(shortCnt);
        deliveryPickRepository.update(dbPickEntity);
    }

    /**
     * 查询干线批次的拣货详情
     * @param batchIds 批次ID
     * @return 站点拣货详情
     */
    public Map<Long, List<DeliveryPickEntity>> queryTrunkPickDetailByBathIds(List<Long> batchIds) {
        if(CollectionUtils.isEmpty(batchIds)){
            return Collections.emptyMap();
        }
        // 全品类提货用车
        List<DeliveryBatchEntity> allCategoryPickTypeBatchList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .batchIds(batchIds)
                .type(DeliveryBatchTypeEnum.all_category_pick.getCode())
                .build());
        if(CollectionUtils.isEmpty(allCategoryPickTypeBatchList)){
            return Collections.emptyMap();
        }
        // 批次ID映射
        Map<Long, DeliveryBatchEntity> batchId2EntityMap = allCategoryPickTypeBatchList.stream()
                .collect(Collectors.toMap(DeliveryBatchEntity::getId, Function.identity()));

        // 配送中、完成配送
        List<DeliveryBatchEntity> deliveryIngAndCompleteBatchList = allCategoryPickTypeBatchList.stream().filter(batch -> Objects.equals(batch.getStatus(), DeliveryBatchStatusEnum.IN_DELIVERY)
                || Objects.equals(batch.getStatus(), DeliveryBatchStatusEnum.COMPLETE_DELIVERY)).collect(Collectors.toList());

        // 非配送中、完成配送状态
        List<DeliveryBatchEntity> noDeliveryIngAndCompleteBatchList = allCategoryPickTypeBatchList.stream().filter(batch -> !Objects.equals(batch.getStatus(), DeliveryBatchStatusEnum.IN_DELIVERY)
                && !Objects.equals(batch.getStatus(), DeliveryBatchStatusEnum.COMPLETE_DELIVERY)).collect(Collectors.toList());

        // key->站点id value->拣货数据
        Map<Long, List<DeliveryPickEntity>> deliverySiteIdDeliveryPickEntityMap = new HashMap<>();

        // 判断batch状态---配送中--查询表
        if(!CollectionUtils.isEmpty(deliveryIngAndCompleteBatchList)){
            List<Long> deliveryIngAndCompleteBatchIds = deliveryIngAndCompleteBatchList.stream()
                    .map(DeliveryBatchEntity::getId)
                    .collect(Collectors.toList());

            if(!CollectionUtils.isEmpty(deliveryIngAndCompleteBatchIds)){
                List<DeliveryPickEntity> deliveryPickEntities = deliveryPickRepository.queryList(DeliveryPickQuery.builder()
                        .batchIdList(deliveryIngAndCompleteBatchIds)
                        .build());
                Map<Long, List<DeliveryPickEntity>> deliveryIngAndCompletePickMap = deliveryPickEntities.stream().collect(Collectors.groupingBy(DeliveryPickEntity::getDeliverySiteId));
                deliverySiteIdDeliveryPickEntityMap.putAll(deliveryIngAndCompletePickMap);
            }
        }

        // 处理非配送中批次
        if(!CollectionUtils.isEmpty(noDeliveryIngAndCompleteBatchList)){
            Map<Long, List<DeliveryPickEntity>> resultMap = this.handleNoIngPickAllCategoryData(batchId2EntityMap, noDeliveryIngAndCompleteBatchList);
            deliverySiteIdDeliveryPickEntityMap.putAll(resultMap);
        }

        return deliverySiteIdDeliveryPickEntityMap;
    }

    /**
     * 处理非配送中批次的全品类拣货信息
     * @param batchId2EntityMap 批次信息
     * @param noDeliveryIngAndCompleteBatchList  非配送中批次数据
     * @return 拣货数据
     */
    private Map<Long, List<DeliveryPickEntity>> handleNoIngPickAllCategoryData(Map<Long, DeliveryBatchEntity> batchId2EntityMap, List<DeliveryBatchEntity> noDeliveryIngAndCompleteBatchList) {
        Map<Long, List<DeliveryPickEntity>> deliverySiteIdDeliveryPickEntityMap = new HashMap<>();
        List<Long> noDeliveryIngAndCompleteBatchIds = noDeliveryIngAndCompleteBatchList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(noDeliveryIngAndCompleteBatchIds)){
            return deliverySiteIdDeliveryPickEntityMap;
        }
        // 查询批次的站点
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchIdList(noDeliveryIngAndCompleteBatchIds).build());
        List<Long> siteIds = deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(siteIds)){
            return deliverySiteIdDeliveryPickEntityMap;
        }

        // 根据站点点位ID 查询全品类承运单起点匹配的，获取配送详情进行聚合展示
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                .batchIds(noDeliveryIngAndCompleteBatchIds)
                .beginSiteIds(siteIds)
                .sourceList(Collections.singletonList(DistOrderSourceEnum.ALL_CATEGORY_PICK.getCode()))
                .build());
        // 查询委托单item信息
        List<Long> distOrderIds = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(distOrderIds)){
            return Collections.emptyMap();
        }
        List<DistOrderEntity> distOrderEntityList = distOrderRepository.queryListWithItem(DistOrderQuery.builder().distIdList(distOrderIds).build());

        // batchId --> deliveryOrderList
        Map<Long, List<DeliveryOrderEntity>> batchId2DeliveryOrderEntityListMap = deliveryOrderEntityList.stream().collect(Collectors.groupingBy(DeliveryOrderEntity::getDeliveryBatchId));

        Map<String, List<DeliveryOrderEntity>> beginSiteIdBatchId2OrderMap = deliveryOrderEntityList.stream().collect(Collectors.groupingBy(deliveryOrder -> deliveryOrder.getBeginSiteId() + "#" + deliveryOrder.getDeliveryBatchId()));
        deliverySiteEntities.forEach(deliverySiteEntity -> {
            List<DeliveryOrderEntity> deliverySiteOrders = beginSiteIdBatchId2OrderMap.get(deliverySiteEntity.buildUk());
            if(CollectionUtils.isEmpty(deliverySiteOrders)){
                return;
            }
            DeliveryBatchEntity deliveryBatchEntity = batchId2EntityMap.get(deliverySiteEntity.getDeliveryBatchId());
            Long batchId = deliveryBatchEntity.getId();
            Long driverId = deliveryBatchEntity.getDriverId();
            Long siteId = deliverySiteEntity.getSiteId();
            Long deliverySiteId = deliverySiteEntity.getId();
            List<DeliveryOrderEntity> currBatchDeliveryOrders = batchId2DeliveryOrderEntityListMap.get(batchId);
            List<Long> currBatchDistOrderIds = currBatchDeliveryOrders.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
            List<DistOrderEntity> currBatchIdDistOrders = distOrderEntityList.stream().filter(distOrder -> currBatchDistOrderIds.contains(distOrder.getDistId())).collect(Collectors.toList());

            // 过滤当前点位的全品类订单且有效
            List<DistOrderEntity> currBatchAllCategoryValidDistOrders = currBatchIdDistOrders.stream().filter(distOrder -> Objects.equals(distOrder.getSource(), DistOrderSourceEnum.ALL_CATEGORY_PICK) &&
                    Objects.equals(distOrder.getBeginSite().getId(), siteId) && DistOrderStatusEnum.isValid(distOrder.getStatus())
            ).collect(Collectors.toList());

            // 组装成Pick数据
            List<DeliveryPickEntity> trunkPick = this.createTrunkPick(batchId, driverId, siteId, deliverySiteId, currBatchAllCategoryValidDistOrders, DeliveryPickTypeEnum.DEFAULT);
            deliverySiteIdDeliveryPickEntityMap.put(deliverySiteId,trunkPick);
        });
        return deliverySiteIdDeliveryPickEntityMap;
    }

    /**
     * 组装干线拣货数据
     * @param batchId 批次ID
     * @param driverId 司机ID
     * @param siteId 点位ID
     * @param deliverySiteId 站点ID
     * @param currBatchAllCategoryValidDistOrders 当前批次的承运单信息
     * @param pickTypeEnum 拣货类型
     * @return 拣货数据
     */
    private List<DeliveryPickEntity> createTrunkPick(Long batchId, Long driverId, Long siteId, Long deliverySiteId, List<DistOrderEntity> currBatchAllCategoryValidDistOrders, DeliveryPickTypeEnum pickTypeEnum) {
        DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();
        deliveryBatchEntity.setId(batchId);
        deliveryBatchEntity.setDriverId(driverId);
        deliveryBatchEntity.setBeginSiteId(siteId);

        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
        deliverySiteEntity.setId(deliverySiteId);

        return this.createPick(deliveryBatchEntity,deliverySiteEntity,currBatchAllCategoryValidDistOrders,pickTypeEnum);
    }

    /**
     * 生成干线拣货任务
     * @param deliveryBatchId 批次ID
     */
    public void createTrunkPickTask(Long deliveryBatchId) {
        if(deliveryBatchId == null){
            return;
        }
        // 查询拣货数据是否存在
        List<BatchPickDetailEntity> batchPickDetailEntities = deliveryPickRepository.queryByBatch(Collections.singletonList(deliveryBatchId));
        if(!CollectionUtils.isEmpty(batchPickDetailEntities)){
            return;
        }
        // 站点拣货数据
        Map<Long, List<DeliveryPickEntity>> deliverySiteIdDeliveryPickEntityMap = this.queryTrunkPickDetailByBathIds(Collections.singletonList(deliveryBatchId));
        if(CollectionUtils.isEmpty(deliverySiteIdDeliveryPickEntityMap)){
            return;
        }

        List<DeliveryPickEntity> allDeliveryPicks = deliverySiteIdDeliveryPickEntityMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        deliveryPickRepository.saveBatch((ArrayList<DeliveryPickEntity>) allDeliveryPicks);

    }

    public void batchIntercept(List<DeliveryPickEntity> deliveryPickEntities, List<DeliveryOrderEntity> haveCompletedDeliveryOrderEntityList) {
        if(CollectionUtils.isEmpty(deliveryPickEntities) || CollectionUtils.isEmpty(haveCompletedDeliveryOrderEntityList)){
            return;
        }
        log.info("deliveryPickDomainService deliveryPickEntities {},haveCompletedDeliveryOrderEntityList :{}",JSON.toJSONString(deliveryPickEntities),
                JSON.toJSONString(haveCompletedDeliveryOrderEntityList));
        // 过滤出【待拣货】的数据
        List<DeliveryPickEntity> waitPickList = deliveryPickEntities.stream().filter(deliveryPickEntity -> deliveryPickEntity.getStatus() == DeliveryPickStatusEnum.WAIT_PICK.getCode()).collect(Collectors.toList());

        // 拣货本身只要【配送】的配送单
        List<DeliveryOrderEntity> sendDeliveryOrderList = haveCompletedDeliveryOrderEntityList.stream()
                .filter(deliveryOrderEntity -> Objects.equals(deliveryOrderEntity.getType(), DistTypeEnum.DELIVERY.getCode()))
                .collect(Collectors.toList());

        // 批次对应的拣货任务 key=batchId value=List<pickEntity>
        Map<Long, List<DeliveryPickEntity>> batchId2PickListMap = waitPickList.stream().collect(Collectors.groupingBy(DeliveryPickEntity::getDeliveryBatchId));

        // 批次对应的配送订单 key=batchId value=List<deliveryOrderEntity>
        Map<Long, List<DeliveryOrderEntity>> batchId2SendDeliveryOrderMap = sendDeliveryOrderList.stream().collect(Collectors.groupingBy(DeliveryOrderEntity::getDeliveryBatchId));

        batchId2PickListMap.forEach((batchId,pickList)->{
            List<DeliveryOrderEntity> pick2DeliveryOrders = batchId2SendDeliveryOrderMap.get(batchId);
            if(CollectionUtils.isEmpty(pick2DeliveryOrders)){
                return;
            }

            // 按照拣货模式来分组
            Map<Integer, List<DeliveryPickEntity>> pickType2PickListMap = pickList.stream().collect(Collectors.groupingBy(DeliveryPickEntity::getType));
            // 配送单按照拣货默认分组
            Map<DistPickTypeEnum, List<DeliveryOrderEntity>> pickType2DeliveryOrderMap = pick2DeliveryOrders.stream().collect(Collectors.groupingBy(DeliveryOrderEntity::getPickType));

            pickType2PickListMap.forEach((pickType, pickType2PickList) -> {
                List<DeliveryOrderEntity> samePickTypeDeliveryOrders = pickType2DeliveryOrderMap.get(DistPickTypeEnum.getDistOrderPickTypeByCode(pickType));
                if(CollectionUtils.isEmpty(samePickTypeDeliveryOrders)){
                    return;
                }

                // sku 对应的拣货任务
                Map<String, List<DeliveryPickEntity>> outItemId2PickList = pickType2PickList.stream().collect(Collectors.groupingBy(DeliveryPickEntity::getOutItemId));

                // 存在相同的拣货类型的
                // 默认拣货类型
                if(DistPickTypeEnum.DEFAULT.getCode() == pickType){
                    // 默认拣货类型的拦截方法
                    this.defaultPickTypeIntercept(samePickTypeDeliveryOrders, outItemId2PickList);
                }else{
                    // 独立拣货类型的拦截方法
                    this.independencePickTypeIntercept(samePickTypeDeliveryOrders, outItemId2PickList);
                }
            });
        });

        // 批量拦截
        List<DeliveryPickEntity> deliveryPickEntityList = waitPickList.stream().map(DeliveryPickEntity::interceptDeliveryPick).collect(Collectors.toList());
        log.info("update deliveryPickEntityList:{}", JSON.toJSONString(deliveryPickEntityList));
        // 批量更新
        deliveryPickRepository.batchUpdate(deliveryPickEntityList);
    }

    /**
     *  独立拣货类型的拦截方法
     * @param samePickTypeDeliveryOrders 相同拣货类型的配送单
     * @param outItemId2PickList sku对应的拣货数据
     */
    private void independencePickTypeIntercept(List<DeliveryOrderEntity> samePickTypeDeliveryOrders, Map<String, List<DeliveryPickEntity>> outItemId2PickList) {
        // key=outItemId + # + OutTenantId + # + outBrandName
        // value=List<DeliveryItemEntity>
        Map<String, List<DeliveryItemEntity>> itemTenantIdOutBrandName2DeliveryItemListMap = samePickTypeDeliveryOrders.stream()
                .flatMap(order -> order.getDeliveryItemEntityList().stream()
                        .map(item -> new AbstractMap.SimpleEntry<>(
                                item.getOutItemId() + "#" + order.getOutTenantId() + "#" + order.getOutBrandName(),
                                item))
                )
                .collect(Collectors.groupingBy(Map.Entry::getKey,Collectors.mapping(Map.Entry::getValue, Collectors.toList())));


        outItemId2PickList.forEach((outItemId, needDistPickInterceptList) -> {
            needDistPickInterceptList.forEach(distPickIntercept -> {
                List<DeliveryItemEntity> deliveryItemEntities = itemTenantIdOutBrandName2DeliveryItemListMap.get(outItemId + "#" + distPickIntercept.getParticle());
                if(CollectionUtils.isEmpty(deliveryItemEntities)){
                    return;
                }
                // 需要把SKU进行聚合处理获取配送数量
                Map<String, Integer> outItemId2DeliveryItemCntMap = deliveryItemEntities.stream().collect(Collectors.toMap(DeliveryItemEntity::getOutItemId, DeliveryItemEntity::getPlanReceiptCount, Integer::sum));
                // 分配逻辑
                int quantity = distPickIntercept.getQuantity();
                int currentPickInterceptQuantity = distPickIntercept.getInterceptQuantity();

                int needDistInterceptCnt = outItemId2DeliveryItemCntMap.getOrDefault(outItemId, 0);
                if(needDistInterceptCnt == 0){
                    return;
                }
                if (quantity >= currentPickInterceptQuantity + needDistInterceptCnt) {
                    distPickIntercept.setInterceptQuantity(distPickIntercept.getInterceptQuantity() + needDistInterceptCnt);
                } else {
                    int shouldInterceptCnt = quantity - currentPickInterceptQuantity;
                    needDistInterceptCnt = Math.max(needDistInterceptCnt - shouldInterceptCnt, 0);
                    distPickIntercept.setInterceptQuantity(distPickIntercept.getInterceptQuantity() + needDistInterceptCnt == 0 ? 0 : shouldInterceptCnt);
                }
            });
        });
    }

    /**
     * 默认拣货类型的拦截方法
     * @param samePickTypeDeliveryOrders 相同拣货类型的配送单
     * @param outItemId2PickList sku对应的拣货数据
     */
    private void defaultPickTypeIntercept(List<DeliveryOrderEntity> samePickTypeDeliveryOrders, Map<String, List<DeliveryPickEntity>> outItemId2PickList) {
        List<DeliveryItemEntity> defaultPickTypeDeliveryItems = samePickTypeDeliveryOrders.stream().map(DeliveryOrderEntity::getDeliveryItemEntityList).flatMap(List::stream).collect(Collectors.toList());
        // 需要把SKU进行聚合处理获取配送数量
        Map<String, Integer> outItemId2DeliveryItemCntMap = defaultPickTypeDeliveryItems.stream().collect(Collectors.toMap(DeliveryItemEntity::getOutItemId, DeliveryItemEntity::getPlanReceiptCount, Integer::sum));

        // pickType2PickList分配拦截的数量
        outItemId2PickList.forEach((outItemId, needDistPickInterceptList) -> {
            if (CollectionUtils.isEmpty(needDistPickInterceptList)) {
                return; // 检查非空
            }
            int needDistInterceptCnt = outItemId2DeliveryItemCntMap.getOrDefault(outItemId, 0);
            if(needDistInterceptCnt == 0){
                return;
            }
            for (DeliveryPickEntity distPickIntercept : needDistPickInterceptList) {
                if (distPickIntercept == null){
                    continue; // 检查非空
                }
                int quantity = distPickIntercept.getQuantity();
                int currentPickInterceptQuantity = distPickIntercept.getInterceptQuantity();

                if (quantity >= currentPickInterceptQuantity + needDistInterceptCnt) {
                    distPickIntercept.setInterceptQuantity(distPickIntercept.getInterceptQuantity() + needDistInterceptCnt);
                } else {
                    int shouldInterceptCnt = quantity - currentPickInterceptQuantity;
                    needDistInterceptCnt = Math.max(needDistInterceptCnt - shouldInterceptCnt, 0);
                    distPickIntercept.setInterceptQuantity(distPickIntercept.getInterceptQuantity() + needDistInterceptCnt == 0 ? 0 : shouldInterceptCnt);
                }
            }
        });
    }

    /**
     * 重新生成拣货任务
     * @param deliveryBatchEntity 批次实体
     */
    public void completePathAgain(DeliveryBatchEntity deliveryBatchEntity) {
        if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null) {
            return;
        }

        //移除数据
        deliveryPickRepository.removeByBatchId(deliveryBatchEntity.getId());
        //生成拣货任务
        this.completePath(deliveryBatchEntity);
    }
}
