package net.summerfarm.tms.delivery.entity;

import lombok.Data;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.DeliveryBatchEnums;
import net.summerfarm.tms.enums.DeliveryBatchStatusEnum;
import net.summerfarm.tms.enums.DeliverySiteEnums;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Date:2022/7/8
 * 配送批次
 */
@Data
public class DeliveryBatchEntity {
    Long id;
    DeliveryBatchStatusEnum status;
    /**
     * 调度单类型
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer type;
    /**
     * 承运商ID
     */
    private Long carrierId;
    private CarrierEntity carrierEntity;

    /**
     * 车辆id
     */
    private Long carId;
    private CarEntity carEntity;

    /**
     * 司机ID
     */
    private Long driverId;
    private DriverEntity driverEntity;
    /**
     * 履约时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 承运时间(开始)
     */
    private LocalDateTime beginTime;

    /**
     *更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 配送单
     */
    List<DeliveryOrderEntity> deliveryOrderEntityList;

    /**
     * 运输路线
     */
    List<DeliverySiteEntity> deliverySiteList;

    /**
     * 开始点位
     */
    Long beginSiteId;

    /**
     * 开始点位
     */
    SiteEntity beginSiteEntity;

    /**
     * 结束点位
     */
    Long endSiteId;

    /**
     * 预估费用
     */
    private BigDecimal estimateFare;

    /**
     * 创建人id
     */
    private Integer createId;

    /**
     * 创建人名称
     */
    private String createName;

    private LocalDateTime createTime;

    /**
     * 关闭原因
     */
    private String closeReason;

    /**
     * 关闭人
     */
    private String closeUser;
    /**
     * 线路编码
     */
    private String pathCode;
    /**
     * 线路名称
     */
    private String pathName;
    /**
     * 线路ID
     */
    private Long pathId;

    /**
     * 完成配送时间
     */
    private LocalDateTime finishDeliveryTime;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;
    /**
     * 总重量
     */
    private BigDecimal totalWeight;
    /**
     * 总价格
     */
    private BigDecimal totalPrice;

    /**
     * 批次对应店铺数
     */
    private Integer shopCount;

    /**
     * 路线满载率
     */
    private BigDecimal pathFullLoadRatio;

    /**
     * 总距离 km
     */
    private BigDecimal planTotalDistance;

    /**
     * 实际总距离 km
     */
    private BigDecimal realTotalDistance;
    /**
     * 完成排线时间
     */
    private LocalDateTime bePathTime;
    /**
     * 完成捡货时间
     */
    private LocalDateTime pickUpTime;
    /**
     * 打车点位数
     */
    private long taxiSize;

    /**
     * 商品数
     */
    private long skuNum;

    /**
     * sku种类数
     */
    private long skuCnt;
    /**
     * 打车费
     */
    private BigDecimal taxiMoney;

    /**
     * 帮采费
     */
    private BigDecimal buyMoney;

    /**
     * 拣货
     */
    private List<DeliveryPickEntity> deliveryPickEntityList;

    /**
     * 智能排线总距离 km
     */
    private BigDecimal intelligenceTotalDistance;

    /**
     * 出发打卡照片
     */
    private String signOutPic;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;
    /**
     * 冷藏照片
     */
    private String refrigeratePics;

    /**
     * 冷冻照片
     */
    private String freezePics;

    /**
     * 保温措施照片
     */
    private String keepTemperatureMethodPics;

    /**
     * 出发温度
     */
    private BigDecimal signOutTemperature;
    /**
     * 实际出发时间
     */
    private LocalDateTime signOutTime;
    /**
     * 出发打卡备注
     */
    private String signOutRemark;

    /**
     * 区域
     */
    private String area;

    /**
     * 班次 0正常 1加班
     */
    private Integer classes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 总数量
     */
    private Integer totalQuantity;

    /**
     * 运费明细项
     */
    private List<DeliveryBatchFareEntity> deliveryBatchFareEntityList;
    /**
     * 体积装载率
     */
    private BigDecimal volumeLoadRatio;

    /**
     * 重量装载率
     */
    private BigDecimal weightLoadRatio;

    /**
     * 件数装载率
     */
    private BigDecimal quantityLoadRatio;
    /**
     * 关联关系
     */
    private List<DeliveryBatchRelationEntity> deliveryBatchRelationEntityList;

    /**
     * 委托单集合
     */
    private List<DistOrderEntity> distOrderEntities;
    /**
     * 车辆体积
     */
    private BigDecimal carLoadVolume;
    /**
     * 车辆重量
     */
    private BigDecimal carLoadWeight;
    /**
     * 车辆件数
     */
    private Integer carLoadQuantity;
    /**
     * 运载体积
     */
    private BigDecimal transportationLoadVolume;
    /**
     * 运载重量,单位:吨
     */
    private BigDecimal transportationLoadWeight;
    /**
     * 运载件数
     */
    private Integer transportationLoadQuantity;
    /**
     * 承运商品类型，0：标品，1：水果
     */
    private DeliveryBatchEnums.CarryType carryType;

    /**
     * 是否需要贪心规划排线 true需要 false或null不需要
     */
    private Boolean needGreedyIntelligentPath;

    /**
     * 总体积、重量、价格、店铺数计算、路线满载率
     */
    public void batchParamCalculate(){
        BigDecimal totalPrice = this.deliverySiteList.stream().map(DeliverySiteEntity::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalVolume = this.deliverySiteList.stream().map(DeliverySiteEntity::getTotalVolume).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalWeight = this.deliverySiteList.stream().map(DeliverySiteEntity::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add);

        this.totalPrice = totalPrice;
        this.totalVolume = totalVolume;
        this.totalWeight = totalWeight;
        this.shopCount = deliverySiteList.size();
        //车辆满载率=商品体积/车辆可装载体积*100%
        this.pathFullLoadRatio = totalVolume.divide(carEntity == null ? new BigDecimal(1): carEntity.getVolume() == null ? new BigDecimal(1) : carEntity.getVolume(),4,BigDecimal.ROUND_HALF_DOWN).multiply(BigDecimal.valueOf(100));
    }

    /**
     * 获取批次的起点运输信息
     */
    public DeliverySiteEntity getBeginDeliverySite(){
        if(CollectionUtils.isEmpty(this.deliverySiteList)){
          return null;
        }
        for (DeliverySiteEntity deliverySiteEntity : this.deliverySiteList) {
            if(Objects.equals(this.beginSiteId,deliverySiteEntity.getSiteId())){
                return deliverySiteEntity;
            }
        }
        return null;
    }

    /**
     * 排除起点的集合
     */
    public List<DeliverySiteEntity> getExcludeBeginDeliverySiteList(){
        List<DeliverySiteEntity> deliverySiteEntities = new ArrayList<>();
        if(CollectionUtils.isEmpty(this.deliverySiteList)){
          return deliverySiteEntities;
        }
        for (DeliverySiteEntity deliverySiteEntity : this.deliverySiteList) {
            if(!Objects.equals(this.beginSiteId,deliverySiteEntity.getSiteId())){
                deliverySiteEntities.add(deliverySiteEntity);
            }
        }
        return deliverySiteEntities;
    }

    /**
     * 获取专车配送的点位
     */
    public List<DeliverySiteEntity> getSpecialSendDeliverySiteList(){
        //排除起点的集合
        List<DeliverySiteEntity> excludeBeginDeliverySiteList = this.getExcludeBeginDeliverySiteList();
        if(!CollectionUtils.isEmpty(excludeBeginDeliverySiteList)){
            //获取专车配送的点位集合
            List<DeliverySiteEntity> specialSendSiteList = excludeBeginDeliverySiteList.stream()
                    .sorted(Comparator.comparing(DeliverySiteEntity::getOuterClientName))
                    .filter(deliverySite -> Objects.equals(deliverySite.getSendWay(), DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue()))
                    .collect(Collectors.toList());
            return specialSendSiteList;
        }
        return Collections.emptyList();
    }

    /**
     * 获取正常配送的点位
     */
    public List<DeliverySiteEntity> getNormalSendDeliverySiteList(){
        //排除起点的集合
        List<DeliverySiteEntity> excludeBeginDeliverySiteList = this.getExcludeBeginDeliverySiteList();
        //获取专车配送的点位集合
        List<DeliverySiteEntity> specialSendSiteList = excludeBeginDeliverySiteList.stream()
                .filter(deliverySite -> Objects.equals(deliverySite.getSendWay(), DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()))
                .collect(Collectors.toList());

        return specialSendSiteList;
    }

    /**
     * 获取正常配送的点位最后的序号
     */
    public Integer getNormalSendLastSiteSequence(){
        //排除起点的集合
        List<DeliverySiteEntity> normalSendDeliverySiteList = this.getNormalSendDeliverySiteList();
        if(CollectionUtils.isEmpty(normalSendDeliverySiteList)){
            return 0;
        }
        //获取专车配送的点位集合
        List<Integer> sequenceList = normalSendDeliverySiteList.stream()
                .sorted(Comparator.comparing(DeliverySiteEntity::getSequence).reversed())
                .map(DeliverySiteEntity::getSequence)
                .limit(1)
                .collect(Collectors.toList());


        return sequenceList.get(0);
    }
    

    /**
     * 获取批次的指定运输节点
     */
    public DeliverySiteEntity getSpecifiedDeliverySite(Long siteId) {
        if (CollectionUtils.isEmpty(this.deliverySiteList)) {
            return null;
        }
        Optional<DeliverySiteEntity> specifiedDeliverySiteOptional = this.deliverySiteList.stream().filter(e -> e.getSiteId().equals(siteId)).findFirst();
        return specifiedDeliverySiteOptional.orElse(null);
    }

    public boolean inDeliveryIng(){
        List<DeliveryBatchStatusEnum> deliveryStatus = Arrays.asList(DeliveryBatchStatusEnum.TO_BE_PICKED, DeliveryBatchStatusEnum.IN_DELIVERY);
        return deliveryStatus.contains(this.status);
    }

    public boolean isCompletePath(){
        List<DeliveryBatchStatusEnum> deliveryStatus = Arrays.asList(DeliveryBatchStatusEnum.TO_BE_PICKED, DeliveryBatchStatusEnum.IN_DELIVERY, DeliveryBatchStatusEnum.COMPLETE_DELIVERY);
        return deliveryStatus.contains(this.status);
    }

    /**
     * 是否关闭
     */
    public boolean isClose() {
        return this.status == DeliveryBatchStatusEnum.DELIVERY_CLOSED;
    }

    public DeliveryBatchEntity cityFinishDelivery() {
        DeliveryBatchEntity batchEntity = new DeliveryBatchEntity();
        batchEntity.setId(this.getId());
        batchEntity.setStatus(DeliveryBatchStatusEnum.COMPLETE_DELIVERY);
        batchEntity.setFinishDeliveryTime(LocalDateTime.now());
        return batchEntity;
    }
}
