package net.summerfarm.tms.delivery.handle;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;

import java.util.Comparator;

@Slf4j
public class DeliveryPickHandle {

    /**
     * 城配拣货处理
     * @param reqDeliverySiteEntity 请求拣货信息
     * @param dbCurrent  当前数据库拣货信息
     * @param isPop 是否是POP城配仓 ture是 false否
     */
    public static void cityPickHandle(DeliverySiteEntity reqDeliverySiteEntity, DeliverySiteEntity dbCurrent, boolean isPop) {
        TmsAssert.isTrue(dbCurrent.getDeliveryPickEntityList().size() == reqDeliverySiteEntity.getDeliveryPickEntityList().size(),
                ErrorCodeEnum.PARAM_ILLEGAL, "拣货项数量不符");
        reqDeliverySiteEntity.getDeliveryPickEntityList().sort(
                Comparator.comparing(DeliveryPickEntity::getId,Comparator.nullsLast(Long::compareTo)));
        dbCurrent.getDeliveryPickEntityList().sort(
                Comparator.comparing(DeliveryPickEntity::getId,Comparator.nullsLast(Long::compareTo)));

        for (int i = 0; i < dbCurrent.getDeliveryPickEntityList().size(); i++) {
            DeliveryPickEntity dbItem = dbCurrent.getDeliveryPickEntityList().get(i);
            DeliveryPickEntity reqItem = reqDeliverySiteEntity.getDeliveryPickEntityList().get(i);
            TmsAssert.isTrue(dbItem.getOutItemId().equals(reqItem.getOutItemId()), ErrorCodeEnum.PARAM_ILLEGAL, "拣货项不符:" + reqItem.getOutItemId());
            Integer shortQuantity = reqItem.getShortQuantity() == null ? 0 : reqItem.getShortQuantity();
           //应拣数量
            Integer quantity = dbItem.getQuantity();

            // POP城配仓拣货
            if(isPop){
                // 缺货数量 + 扫码数量 = 应拣数量
                int sum = shortQuantity + dbItem.getScanCount();
                // 应扫码数量
                int shouldScanNum = quantity - shortQuantity;
                if(shouldScanNum != dbItem.getScanCount()){
                    throw new TmsRuntimeException("缺货数量 + 已扫码数量不等于应拣数量:" + reqItem.getOutItemId());
                }
                TmsAssert.isTrue(dbItem.getQuantity() >= sum, ErrorCodeEnum.PARAM_ILLEGAL, "缺货数量 + 扫码数量不等于应拣数量:" + reqItem.getOutItemId());
                reqItem.setPickQuantity(quantity - shortQuantity);
                reqItem.setShortQuantity(shortQuantity);
            }else{// 非POP城配仓拣货
                // 缺货数量 + 拦截数量 = 应拣数量
                int sum = dbItem.getInterceptQuantity() + shortQuantity;
                TmsAssert.isTrue(dbItem.getQuantity() >= sum, ErrorCodeEnum.PARAM_ILLEGAL, "拣货数量不符:" + reqItem.getOutItemId());
                reqItem.setPickQuantity(quantity - sum);
                reqItem.setShortQuantity(shortQuantity);
            }

            reqItem.setId(dbItem.getId());
            reqItem.setParticle(dbItem.getParticle());
            reqItem.setProcessPickQuantity(dbItem.getProcessQuantity() - (reqItem.getProcessShortQuantity() == null ? 0 : reqItem.getProcessShortQuantity()));
        }
    }


    /**
     * 干线拣货处理
     * @param reqDeliverySiteEntity 请求拣货信息
     * @param dbCurrent  当前数据库拣货信息
     */
    public static void trunkPickHandle(DeliverySiteEntity reqDeliverySiteEntity, DeliverySiteEntity dbCurrent) {
        TmsAssert.isTrue(dbCurrent.getDeliveryPickEntityList().size() == reqDeliverySiteEntity.getDeliveryPickEntityList().size(),
                ErrorCodeEnum.PARAM_ILLEGAL, "拣货项数量不符");
        reqDeliverySiteEntity.getDeliveryPickEntityList().sort(
                Comparator.comparing(DeliveryPickEntity::getId,Comparator.nullsLast(Long::compareTo)));
        dbCurrent.getDeliveryPickEntityList().sort(
                Comparator.comparing(DeliveryPickEntity::getId,Comparator.nullsLast(Long::compareTo)));

        for (int i = 0; i < dbCurrent.getDeliveryPickEntityList().size(); i++) {
            DeliveryPickEntity dbItem = dbCurrent.getDeliveryPickEntityList().get(i);
            DeliveryPickEntity reqItem = reqDeliverySiteEntity.getDeliveryPickEntityList().get(i);
            TmsAssert.isTrue(dbItem.getOutItemId().equals(reqItem.getOutItemId()), ErrorCodeEnum.PARAM_ILLEGAL, "拣货项不符:" + reqItem.getOutItemId());
            Integer shortQuantity = reqItem.getShortQuantity() == null ? 0 : reqItem.getShortQuantity();
            //应拣数量
            Integer quantity = dbItem.getQuantity();

            // 非POP城配仓拣货
            // 缺货数量 + 拦截数量 = 应拣数量
            int sum = dbItem.getInterceptQuantity() + shortQuantity;
            TmsAssert.isTrue(dbItem.getQuantity() >= sum, ErrorCodeEnum.PARAM_ILLEGAL, "拣货数量不符:" + reqItem.getOutItemId());
            reqItem.setPickQuantity(quantity - sum);
            reqItem.setShortQuantity(shortQuantity);

            reqItem.setId(dbItem.getId());
            reqItem.setParticle(dbItem.getParticle());
            reqItem.setProcessPickQuantity(dbItem.getProcessQuantity() - (reqItem.getProcessShortQuantity() == null ? 0 : reqItem.getProcessShortQuantity()));
        }
    }
}
