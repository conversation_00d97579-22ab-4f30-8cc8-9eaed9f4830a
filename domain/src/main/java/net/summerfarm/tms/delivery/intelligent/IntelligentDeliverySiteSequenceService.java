package net.summerfarm.tms.delivery.intelligent;

import com.alibaba.fastjson.JSON;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.util.DistanceUtil;
import net.summerfarm.tms.util.VRPSolverUtil;
import net.xianmu.gaode.support.enums.DriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.service.LbsGaoDeService;
import net.xianmu.gaode.support.service.input.PathSectionInput;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import net.xianmu.gaode.support.service.vo.PathSection;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 智能
 * date: 2025/5/9 17:12<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class IntelligentDeliverySiteSequenceService {

    @Resource
    private VRPSolverUtil vrpsolverUtil;
    @Resource
    private LbsGaoDeService lbsGaoDeService;

    /**
     * ortools算法智能路线
     *
     * @param beginDeliverySite 开始点位
     * @param deliverySiteList        路线点位
     */
    public List<DeliverySiteEntity> ortoolsSolver(DeliverySiteEntity beginDeliverySite,
                              List<DeliverySiteEntity> deliverySiteList) {

        List<DeliverySiteEntity> deliverySiteSortList = new ArrayList<>();
        // 起点点位
        if (beginDeliverySite == null) {
            return deliverySiteSortList;
        }
        List<String> poiList = new ArrayList<>();
        List<Long> deliverySiteIdList = new ArrayList<>();

        poiList.add(beginDeliverySite.getSiteEntity().getPoi());
        deliverySiteIdList.add(beginDeliverySite.getId());

        for (DeliverySiteEntity deliverySiteEntity : deliverySiteList) {
            poiList.add(deliverySiteEntity.getSiteEntity().getPoi());
            deliverySiteIdList.add(deliverySiteEntity.getId());
        }
        List<Long> deliverySiteIdSequenceList = vrpsolverUtil.solve(convertToDoubleArray(poiList), deliverySiteIdList, beginDeliverySite.getId());

        Map<Long, DeliverySiteEntity> id2DeliverySiteMap = deliverySiteList.stream().collect(Collectors.toMap(DeliverySiteEntity::getId, Function.identity()));
        for (Long deliverySiteId : deliverySiteIdSequenceList) {
            DeliverySiteEntity deliverySiteEntity = id2DeliverySiteMap.get(deliverySiteId);
            if(deliverySiteEntity == null){
                continue;
            }

            deliverySiteSortList.add(deliverySiteEntity);
        }

        return deliverySiteSortList;
    }

    private List<double[]> convertToDoubleArray(List<String> poi) {
        return poi.stream()
                .map(s -> {
                    String[] parts = s.split(",");
                    return new double[]{
                            Double.parseDouble(parts[0].trim()), // 经度
                            Double.parseDouble(parts[1].trim())  // 纬度
                    };
                })
                .collect(Collectors.toList());
    }


    /**
     * 贪心算法
     *
     * @param beginDeliverySite           始点
     * @param needIntelligentSortSiteList 点位
     */
    public List<DeliverySiteEntity> greedyAlgorithm(DeliverySiteEntity beginDeliverySite,
                                List<DeliverySiteEntity> needIntelligentSortSiteList){
        List<DeliverySiteEntity> deliverySiteSortList = new ArrayList<>();

        AtomicReference<DeliverySiteEntity> firstNode = getFirstNode(beginDeliverySite, needIntelligentSortSiteList);
        deliverySiteSortList.add(firstNode.get());

        this.getDistanceMethod(deliverySiteSortList, firstNode, needIntelligentSortSiteList);
        return deliverySiteSortList;
    }

    /**
     * 获取路线公里数，16个点位去调用高德接口，避免多次高德。。。省钱
     *
     * @param deliverySiteSortList 公里数返回到这个参数里
     * @param fristNode            开始点位
     * @param deliverySiteList     路线点位
     */
    private void getDistanceMethod(List<DeliverySiteEntity> deliverySiteSortList, AtomicReference<DeliverySiteEntity> fristNode, List<DeliverySiteEntity> deliverySiteList) {
        if (fristNode.get().getId() == null) {
            return;
        }
        Iterator<DeliverySiteEntity> iterator = deliverySiteList.iterator();

        AtomicReference<DeliverySiteEntity> recentNode = new AtomicReference<>(new DeliverySiteEntity());
        AtomicReference<Double> minPoiDistance = new AtomicReference<>();
        minPoiDistance.set(Double.MAX_VALUE);
        while (iterator.hasNext()) {
            DeliverySiteEntity next = iterator.next();
            if (fristNode.get().getId().intValue() == next.getId().intValue()) {
                iterator.remove();
            } else {
                double poiDistance = DistanceUtil.getPoiDistance(fristNode.get().getSiteEntity().getPoi(), next.getSiteEntity().getPoi());
                if (minPoiDistance.get() == null) {
                    minPoiDistance.set(poiDistance);
                } else {
                    if (minPoiDistance.get() > poiDistance) {
                        minPoiDistance.set(poiDistance);
                        recentNode.set(next);
                    }
                }
            }
        }
        if (recentNode.get().getId() != null) {
            deliverySiteSortList.add(recentNode.get());
        }

        if (deliverySiteList.size() > 0) {
            if (recentNode.get().getId() != null) {
                getDistanceMethod(deliverySiteSortList, recentNode, deliverySiteList);
            }
        }
    }

    /**
     * 获取到城配仓最短距离的点位
     *
     * @param beginDeliverySite
     * @param deliverySiteList
     * @return
     */
    private AtomicReference<DeliverySiteEntity> getFirstNode(DeliverySiteEntity beginDeliverySite, List<DeliverySiteEntity> deliverySiteList) {
        AtomicReference<DeliverySiteEntity> fristNode = new AtomicReference<>(new DeliverySiteEntity());
        AtomicReference<Double> minPoiDistance = new AtomicReference<>();
        minPoiDistance.set(Double.MAX_VALUE);
        //获取距离城配仓最近的点位
        deliverySiteList.forEach(deliverySiteEntity -> {
            double poiDistance = DistanceUtil.getPoiDistance(beginDeliverySite.getSiteEntity().getPoi(), deliverySiteEntity.getSiteEntity().getPoi());
            if (minPoiDistance.get() == null) {
                minPoiDistance.set(poiDistance);
            } else {
                if (minPoiDistance.get() > poiDistance) {
                    minPoiDistance.set(poiDistance);
                    fristNode.set(deliverySiteEntity);
                }
            }
        });
        return fristNode;
    }

    /**
     * 获取算法最短距离
     */
    public List<DeliverySiteEntity> algorithmLeastDistance(DeliverySiteEntity beginDeliverySite,List<DeliverySiteEntity> deliverySiteList){
        List<DeliverySiteEntity> ortoolsDeliverySiteSequenceList = this.ortoolsSolver(beginDeliverySite, deliverySiteList);
        List<DeliverySiteEntity> greedyDeliverySiteSequenceList = this.greedyAlgorithm(beginDeliverySite, deliverySiteList);

        // ortools高德距离
        List<DeliverySiteEntity> allOrtoolsDeliverySiteSequenceList = new ArrayList<>();
        allOrtoolsDeliverySiteSequenceList.add(beginDeliverySite);
        allOrtoolsDeliverySiteSequenceList.addAll(ortoolsDeliverySiteSequenceList);
        PathSection ortoolsPathSection = queryGaodePathSection(allOrtoolsDeliverySiteSequenceList);

        // 贪心算法高德距离
        List<DeliverySiteEntity> allGreedyDeliverySiteSequenceList = new ArrayList<>();
        allGreedyDeliverySiteSequenceList.add(beginDeliverySite);
        allGreedyDeliverySiteSequenceList.addAll(greedyDeliverySiteSequenceList);
        PathSection greedyPathSection = queryGaodePathSection(allGreedyDeliverySiteSequenceList);

        // 都为空优先返回贪心算法距离
        if(ortoolsPathSection == null && greedyPathSection == null){
            return greedyDeliverySiteSequenceList;
        }

        if(ortoolsPathSection != null && greedyPathSection != null){
            if(ortoolsPathSection.getTotalDistance().compareTo(greedyPathSection.getTotalDistance()) < 0){
                return ortoolsDeliverySiteSequenceList;
            }else{
                return greedyDeliverySiteSequenceList;
            }
        }

        return greedyDeliverySiteSequenceList;
    }

    /**
     * 高德路径规划
     * @param deliverySiteSequenceList 路线点位
     * @return 结果
     */
    private PathSection queryGaodePathSection(List<DeliverySiteEntity> deliverySiteSequenceList) {
        if(CollectionUtils.isEmpty(deliverySiteSequenceList)){
            return null;
        }

        PathSection pathSection = null;
        try {
            List<WaypointsInput> ortoolsWaypointsInputList = deliverySiteSequenceList.stream()
                    .map(e -> {
                        SiteEntity siteEntity = e.getSiteEntity();
                        if (siteEntity == null) {
                            return null;
                        }
                        WaypointsInput waypointsInput = new WaypointsInput();
                        waypointsInput.setPoi(e.getSiteEntity().getPoi());
                        waypointsInput.setSiteId(e.getSiteEntity().getId());

                        return waypointsInput;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 高德获取距离
            PathSectionInput input = new PathSectionInput();
            input.setStrategyEunm(DriverRoutPlanStrategyEnum.DISTANCE_FIRST);
            input.setWaypointsInputList(ortoolsWaypointsInputList);

            int tryTime = 3;
            while (tryTime > 0){
                try {
                    pathSection = lbsGaoDeService.waypointsPathSection(input);
                } catch (Exception e) {
                    log.error("访问高德调用异常 尝试次数剩余:{} 批次ID:{}",tryTime,deliverySiteSequenceList.get(0).getDeliveryBatchId(),e);
                    try {
                        TimeUnit.MILLISECONDS.sleep(10);
                    } catch (InterruptedException ex) {
                        log.error("中断异常");
                    }
                }
                tryTime--;
            }
        } catch (Exception e) {
            log.error("高德路径规划异常:{}", e.getMessage(),e);
            return null;
        }

        return pathSection;
    }


    /**
     * ortools获取算法直线最短距离的路线
     */
    public List<DeliverySiteEntity> ortoolsSolverStraightLineMinDistanceSequence(List<DeliverySiteEntity> deliverySiteList){
        if (CollectionUtils.isEmpty(deliverySiteList)) {
            return Collections.emptyList();
        }

        HashMap<Double, List<DeliverySiteEntity>> minDistanceDeliverySiteMap = new HashMap<>();

        deliverySiteList.forEach(beginDeliverySite -> {
            List<DeliverySiteEntity> otherDeliverySiteList = deliverySiteList.stream().filter(e -> !Objects.equals(beginDeliverySite.getId(), e.getId())).collect(Collectors.toList());
            List<DeliverySiteEntity> ortoolsDeliverySiteSequenceList = this.ortoolsSolver(beginDeliverySite, otherDeliverySiteList);

            ortoolsDeliverySiteSequenceList.add(0, beginDeliverySite);
            // 计算直线距离公里数取最短
            double minDistance = 0;
            for (int i = 0; i < ortoolsDeliverySiteSequenceList.size(); i++) {
                if (i + 1 >= ortoolsDeliverySiteSequenceList.size()){
                    break;
                }
                String poi = ortoolsDeliverySiteSequenceList.get(i).getSiteEntity().getPoi();
                String nextPoi = ortoolsDeliverySiteSequenceList.get(i + 1).getSiteEntity().getPoi();
                double distance = DistanceUtil.getPoiDistance(poi, nextPoi);
                minDistance = distance + minDistance;
            }

            log.info("路线次序:{},直线最短距离:{}",
                    JSON.toJSONString(ortoolsDeliverySiteSequenceList.stream()
                            .map(DeliverySiteEntity::getOuterClientName)
                            .collect(Collectors.joining( "->"))),
                    minDistance);
            minDistanceDeliverySiteMap.put(minDistance, ortoolsDeliverySiteSequenceList);
        });

        // 比较直线距离最短的站点路线
        return minDistanceDeliverySiteMap.entrySet().stream().min(Map.Entry.comparingByKey()).get().getValue();
    }
}
