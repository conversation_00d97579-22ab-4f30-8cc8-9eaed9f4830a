package net.summerfarm.tms.delivery.entity;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-24 14:21:44
 */
@Data
public class TmsDeliveryPickShortOrderMappingEntity {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 配送批次id
     */
    private Long deliveryBatchId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 外部条目id
     */
    private String outItemId;

    /**
     * 货品描述
     */
    private String itemDesc;

    /**
     * 缺货数量
     */
    private Integer shortQuantity;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 线路编码
     */
    private String pathCode;

    /**
     * 店铺在路线上的顺序
     */
    private Integer pathSequence;

    /**
     * 外部联系人
     */
    private String outContactId;

    /**
     * 外部客户号
     */
    private String outClientId;

}