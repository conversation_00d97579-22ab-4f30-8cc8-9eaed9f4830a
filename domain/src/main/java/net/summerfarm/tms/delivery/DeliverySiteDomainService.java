package net.summerfarm.tms.delivery;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.DeliveryTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.AgentTypeEnum;
import net.summerfarm.tms.alert.DeliveryAlertDomainService;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.site.SiteDomainService;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.config.TmsNacosConfig;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempCondition;
import net.summerfarm.tms.delivery.handle.DeliveryPickHandle;
import net.summerfarm.tms.delivery.intelligent.IntelligentDeliverySiteSequenceService;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.mall.dto.SkuDTO;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.facade.wms.dto.WmsOrderProcessDetailDTO;
import net.summerfarm.tms.facade.wnc.WncQueryFacade;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteItemQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.util.DistanceUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/13 18:24<br/>
 * 通用 支持 城配和干线
 * 查询 尽量走repository, 设计多个聚合的数据接口 在api层组装
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeliverySiteDomainService {

    private final SiteRepository siteRepository;
    private final SiteDomainService siteDomainService;
    private final DeliverySiteRepository deliverySiteRepository;
    private final DeliverySiteItemCodeRepository deliverySiteItemCodeRepository;
    private final DeliverySiteItemRepository deliverySiteItemRepository;
    private final DistOrderDomainService distOrderDomainService;
    private final DistOrderRepository distOrderRepository;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final DeliveryOrderDomainService deliveryOrderDomainService;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final WmsQueryFacade wmsQueryFacade;
    private final DeliveryAlertDomainService deliveryAlertDomainService;
    private final TmsNacosConfig tmsNacosConfig;
    private final DeliverySiteCheckinPunchRepository deliverySiteCheckinPunchRepository;
    private final DeliveryPickRepository deliveryPickRepository;
    private final WncQueryFacade wncQueryFacade;
    private final IntelligentDeliverySiteSequenceService intelligentDeliverySiteSequenceService;

    public void signIn(DeliverySiteEntity deliverySiteEntity) {
        DeliverySiteEntity updateEntity = new DeliverySiteEntity();
        updateEntity.setId(deliverySiteEntity.getId());
        updateEntity.setStatus(DeliverySiteStatusEnum.ALREADY);
        updateEntity.setSignInTime(deliverySiteEntity.getSignInTime());
        updateEntity.setSignInPoi(deliverySiteEntity.getSignInPoi());
        updateEntity.setSignInAddress(deliverySiteEntity.getSignInAddress());
        updateEntity.setSignInDiffMinute(deliverySiteEntity.getSignInDiffMinute());
        updateEntity.setSignInDiffKm(deliverySiteEntity.getSignInDiffKm());
        updateEntity.setSignInPics(deliverySiteEntity.getSignInPics());
        updateEntity.setSignInRemark(deliverySiteEntity.getSignInRemark());
        updateEntity.setSignInErrType(deliverySiteEntity.getSignInErrType());
        updateEntity.setSignInDistance(deliverySiteEntity.getSignInDistance());
        updateEntity.setSignInStatus(deliverySiteEntity.getSignInStatus());
        deliverySiteRepository.update(updateEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void signOut(DeliverySiteEntity deliverySiteEntity) {

        DeliverySiteEntity updateEntity = new DeliverySiteEntity();
        updateEntity.setId(deliverySiteEntity.getId());
        updateEntity.setStatus(DeliverySiteStatusEnum.DEPARTED);
        updateEntity.setSignOutTime(deliverySiteEntity.getSignOutTime());
        updateEntity.setSignOutPoi(deliverySiteEntity.getSignOutPoi());
        updateEntity.setSignOutAddress(deliverySiteEntity.getSignOutAddress());
        updateEntity.setSignOutDiffKm(deliverySiteEntity.getSignOutDiffKm());
        updateEntity.setSignOutPics(deliverySiteEntity.getSignOutPics());
        updateEntity.setVehiclePlatePics(deliverySiteEntity.getVehiclePlatePics());
        updateEntity.setSealPics(deliverySiteEntity.getSealPics());
        updateEntity.setSignOutRemark(deliverySiteEntity.getSignOutRemark());
        updateEntity.setSignOutErrType(deliverySiteEntity.getSignOutErrType());
        updateEntity.setSignOutTemperature(deliverySiteEntity.getSignOutTemperature());
        updateEntity.setSignOutStatus(deliverySiteEntity.getSignOutStatus());
        updateEntity.setSignOutDiffMinute(deliverySiteEntity.getSignOutDiffMinute());
        updateEntity.setOutReason(deliverySiteEntity.getOutReason());
        updateEntity.setFreezePics(deliverySiteEntity.getFreezePics());
        updateEntity.setRefrigeratePics(deliverySiteEntity.getRefrigeratePics());
        deliverySiteRepository.update(updateEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public DeliverySiteEntity finishPick(DeliverySiteEntity deliverySiteEntity) {
        DeliverySiteEntity current = this.cityFinishPick(deliverySiteEntity);
        deliverySiteEntity.setStatus(DeliverySiteStatusEnum.FINISH_PICK);
        deliverySiteEntity.setSignOutTime(LocalDateTime.now());
        deliverySiteEntity.setDeliveryBatchId(current.getDeliveryBatchId());
        if (current.getSignInTime() == null) {
            deliverySiteEntity.setSignInTime(LocalDateTime.now());
        }
        deliverySiteRepository.update(deliverySiteEntity);
        //查询当前批次的点位,并修改点位上面的状态
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(current.getDeliveryBatchId()).build());
        deliverySiteEntities.forEach(site ->{
            //正常和部分拦截
            if(site.getDeliverySiteInterceptState() == DeliverySiteInterceptStateEnum.normal ||
                    site.getDeliverySiteInterceptState() == DeliverySiteInterceptStateEnum.partIntecept){
                site.setStatus(DeliverySiteStatusEnum.FINISH_PICK);
                deliverySiteRepository.update(site);
            }
        });
        return deliverySiteEntity;
    }

    /**
     * 完成拣货逻辑
     * @param deliverySiteEntity
     * @return
     */
    private DeliverySiteEntity cityFinishPick(DeliverySiteEntity deliverySiteEntity) {
        DeliverySiteEntity current = deliverySiteRepository.queryWithPicks(deliverySiteEntity.getId());
        TmsAssert.isTrue(current != null, ErrorCodeEnum.DB_DATA_ERROR, "不存在此拣货任务");
        TmsAssert.isTrue(current.getStatus() != DeliverySiteStatusEnum.FINISH_PICK, ErrorCodeEnum.DB_DATA_ERROR, "已完成拣货,请刷新页面查看");
        TmsAssert.isTrue(current.getStatus() != DeliverySiteStatusEnum.DEPARTED, ErrorCodeEnum.DB_DATA_ERROR, "已完成拣货,请刷新页面查看");
        TmsAssert.isTrue(current.getDeliveryPickEntityList().size() == deliverySiteEntity.getDeliveryPickEntityList().size(),
                ErrorCodeEnum.PARAM_ILLEGAL, "拣货项数量不符");

        // 查询拣货点位是否是POP城配仓
        boolean isPop = siteDomainService.isPopBySiteIdCache(current.getSiteId());

        //拣货处理校验
        DeliveryPickHandle.cityPickHandle(deliverySiteEntity, current,isPop);

        return current;
    }

    /**
     * 干线完成拣货
     * @param deliverySiteId 站点ID
     * @param deliveryPickEntityList 拣货项
     */
    public void trunkFinishPick(Long deliverySiteId, List<DeliveryPickEntity> deliveryPickEntityList) {
        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
        deliverySiteEntity.setId(deliverySiteId);
        deliverySiteEntity.setDeliveryPickEntityList(deliveryPickEntityList);

        DeliverySiteEntity current = deliverySiteRepository.queryWithPicks(deliverySiteEntity.getId());
        TmsAssert.isTrue(current != null, ErrorCodeEnum.DB_DATA_ERROR, "不存在此拣货任务");
        TmsAssert.isTrue(current.getStatus() != DeliverySiteStatusEnum.FINISH_PICK, ErrorCodeEnum.DB_DATA_ERROR, "已完成拣货,请刷新页面查看");
        TmsAssert.isTrue(current.getDeliveryPickEntityList().size() == deliverySiteEntity.getDeliveryPickEntityList().size(),
                ErrorCodeEnum.PARAM_ILLEGAL, "拣货项数量不符");

        //拣货处理校验
        DeliveryPickHandle.trunkPickHandle(deliverySiteEntity, current);

        deliverySiteEntity.setStatus(DeliverySiteStatusEnum.FINISH_PICK);
        deliverySiteEntity.setDeliveryBatchId(current.getDeliveryBatchId());
        if (current.getSignInTime() == null) {
            deliverySiteEntity.setSignInTime(LocalDateTime.now());
        }
        deliverySiteRepository.update(deliverySiteEntity);
    }

    public DeliverySiteEntity finishDelivery(DeliverySiteEntity deliverySiteEntity,Boolean checkScanNum) {
        //校验状态
        DeliverySiteEntity current = deliverySiteRepository.queryWithItems(deliverySiteEntity.getId());
        TmsAssert.isTrue(current.getStatus() != DeliverySiteStatusEnum.FINISH_DELIVERY, ErrorCodeEnum.DB_DATA_ERROR, "已完成配送,请刷新页面");
        TmsAssert.isTrue(current.getDeliverySiteItemEntityList().size() == deliverySiteEntity.getDeliverySiteItemEntityList().size(),
                ErrorCodeEnum.PARAM_ILLEGAL, "配送项数量不符");
        //处理明细
        deliverySiteEntity.getDeliverySiteItemEntityList().sort(Comparator.comparing(DeliverySiteItemEntity::getId));
        List<DeliverySiteItemEntity> currentItemList = current.getDeliverySiteItemEntityList();
        currentItemList.sort(Comparator.comparing(DeliverySiteItemEntity::getId));

        //获取货品属性信息
        List<SkuDTO> skuDTOList = wmsQueryFacade.batchQueryBySkus(currentItemList.stream().map(DeliverySiteItemEntity::getOutItemId).collect(Collectors.toList()));
        Map<String, SkuDTO> skuBaseInfoMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getSku, Function.identity(), (oldData, newData) -> newData));

        //查询订单是否加工过
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                .batchId(current.getDeliveryBatchId())
                .endSiteId(current.getSiteId()).build());
        //委托单集合
        List<Long> distOrderIdList = deliveryOrderEntities.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
        //查询加工数据
        List<DistOrderEntity> distOrderEntityWithItem = distOrderRepository.queryValidListWithItemByIds(distOrderIdList);
        deliverySiteEntity.setSiteDistOrders(distOrderEntityWithItem);

        Map<String, List<WmsOrderProcessDetailDTO>> itemWmsOrderMap = distOrderDomainService.queryProcessOutItemMap(distOrderEntityWithItem);

        // POP城配仓不需要扫码校验
        DeliveryBatchEntity currentDeliveryBatch = deliveryBatchRepository.query(current.getDeliveryBatchId());
        boolean popStoreNoFlag = siteDomainService.isPopBySiteIdCache(currentDeliveryBatch.getBeginSiteId());
        if(popStoreNoFlag){
            checkScanNum = false;
        }

        // 查询扫码数量
        List<Long> deliverySiteItemIds = deliverySiteEntity.getDeliverySiteItemEntityList().stream().map(DeliverySiteItemEntity::getId).collect(Collectors.toList());
        Map<Long,Long> deliverySiteItemId2ScanCountMap = deliverySiteItemCodeRepository.delSiteItemId2ScanCountMapByDelSiteItemIds(deliverySiteItemIds);

        for (int i = 0; i < current.getDeliverySiteItemEntityList().size(); i++) {
            DeliverySiteItemEntity dbItem = current.getDeliverySiteItemEntityList().get(i);
            DeliverySiteItemEntity reqItem = deliverySiteEntity.getDeliverySiteItemEntityList().get(i);
            TmsAssert.isTrue(dbItem.getOutItemId().equals(reqItem.getOutItemId()), ErrorCodeEnum.PARAM_ILLEGAL, "配送项不符:" + reqItem.getOutItemId());
            int sumNoDelivery = dbItem.getInterceptCount() + dbItem.getRejectCount() + (reqItem.getShortCount() == null ? 0 : reqItem.getShortCount());
            reqItem.setRealReceiptCount(dbItem.getPlanReceiptCount() - sumNoDelivery);
            reqItem.setId(dbItem.getId());
            reqItem.setShortCount(reqItem.getShortCount() == null ? 0 : reqItem.getShortCount());

            SkuDTO skuBaseInfo = skuBaseInfoMap.get(dbItem.getOutItemId());
            // 扫码数据校验
            this.scanCodeQuantityCheck(checkScanNum, itemWmsOrderMap, deliverySiteItemId2ScanCountMap,dbItem, reqItem, skuBaseInfo);
            //不更新
            reqItem.setInterceptCount(null);
            reqItem.setRejectCount(null);
            reqItem.setType(dbItem.getType());
            reqItem.setOutItemName(dbItem.getOutItemName());
        }
        //更新
        deliverySiteEntity.setStatus(DeliverySiteStatusEnum.FINISH_DELIVERY);
        if (deliverySiteEntity.getSignInTime() == null) {
            deliverySiteEntity.setSignInTime(LocalDateTime.now());
        }
        deliverySiteRepository.updateAndSaveRecycle(deliverySiteEntity);
        SiteEntity siteEntity = siteRepository.query(deliverySiteEntity.getSiteId());
        deliverySiteEntity.setSiteEntity(siteEntity);
        siteDomainService.updateSitePics(siteEntity, this.querySiteRecentHeadPic(deliverySiteEntity.getSiteId()));

        return deliverySiteEntity;
    }

    private void scanCodeQuantityCheck(Boolean checkScanNum, Map<String, List<WmsOrderProcessDetailDTO>> itemWmsOrderMap,
                                       Map<Long, Long> deliverySiteItemId2ScanCountMap, DeliverySiteItemEntity dbItem,
                                       DeliverySiteItemEntity reqItem, SkuDTO skuBaseInfo) {
        if(checkScanNum == null || BooleanUtils.isFalse(checkScanNum)){
            log.info("skip scanCodeQuantityCheck");
            return;
        }
        int scanCount = deliverySiteItemId2ScanCountMap.getOrDefault(reqItem.getId(), 0L).intValue();
        reqItem.setScanCount(scanCount);
        // 需要扫码标识
        boolean isNeedScanFlag = dbItem.getPlanReceiptCount() < Constants.NEED_SCAN_COUNT_LIMIT;
        // Sku类型扫码标识
        boolean isNeedScanSkuFlag = dbItem.getOutItemType() == DistItemTypeEnum.FRUIT.getCode();

        //POP都需要扫码
        if(skuBaseInfo != null && Objects.equals(skuBaseInfo.getType(), AgentTypeEnum.POP.getValue())){
            isNeedScanFlag = true;
            isNeedScanSkuFlag = true;
        }

        //扫码的校验：是【配送】类型、鲜沐Saas SKU类型【水果】【POP品】、配送数量小于16个需要扫码
        if (isNeedScanSkuFlag &&
                isNeedScanFlag &&
                dbItem.getType() == DeliverySiteItemTypeEnum.DELIVERY.getCode()&&
                CollectionUtils.isEmpty(itemWmsOrderMap.get(dbItem.getOutItemId())) ) {
            //缺货数量 + 拦截数量 + 有货无码数量 + 扫码数量 < 计划数量 需要提醒
            if (reqItem.getShortCount() + dbItem.getInterceptCount() + dbItem.getNoscanCount() + scanCount < dbItem.getPlanReceiptCount()) {
                throw new TmsRuntimeException(ErrorCodeEnum.NEED_SCAN_CODE_OR_WITHOUT_SCAN_ERROR,
                        String.format("商品:%s,扫码数量:%s,拦截数量:%s,缺货数量:%s,无码数量:%s,计划配送数量:%s",
                                dbItem.getOutItemName(), scanCount, dbItem.getInterceptCount(),
                                reqItem.getShortCount(), dbItem.getNoscanCount(), dbItem.getPlanReceiptCount()));
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void scanItemCode(DeliverySiteItemCodeEntity deliverySiteItemCodeEntity) {
        deliverySiteItemCodeRepository.save(deliverySiteItemCodeEntity);
        DeliverySiteItemEntity deliverySiteItemEntity = deliverySiteItemRepository.queryByUk(deliverySiteItemCodeEntity.getDeliverySiteId(),
                deliverySiteItemCodeEntity.getOutItemId(),DeliverySiteItemTypeEnum.DELIVERY.getCode());
        if(deliverySiteItemEntity.getPlanReceiptCount() <= deliverySiteItemEntity.getScanCount()){
            throw new TmsRuntimeException(ErrorCodeEnum.SCAN_CODE_OVER);
        }
        DeliverySiteItemEntity updateEntity = new DeliverySiteItemEntity();
        updateEntity.setId(deliverySiteItemEntity.getId());
        updateEntity.setScanCount(deliverySiteItemEntity.getScanCount() == null ? 1 : deliverySiteItemEntity.getScanCount() + 1);
        deliverySiteItemRepository.update(updateEntity);
    }

    public void noCodeItemSave(DeliverySiteItemEntity deliverySiteItemEntity) {
        DeliverySiteItemEntity update = new DeliverySiteItemEntity();
        update.setId(deliverySiteItemEntity.getId());
        update.setDeliverySiteId(deliverySiteItemEntity.getDeliverySiteId());
        update.setOutItemId(deliverySiteItemEntity.getOutItemId());
        update.setNoscanCount(deliverySiteItemEntity.getNoscanCount());
        update.setNoscanReason(deliverySiteItemEntity.getNoscanReason());
        update.setNoscanPics(deliverySiteItemEntity.getNoscanPics());
        deliverySiteItemRepository.update(update);
    }

    /**
     * 根据批次id查询相关排线点位信息
     *
     * @param batchId 批次id
     * @return 结果
     */
    public List<DeliverySiteEntity> queryDetailByBatchId(Long batchId) {
        TmsAssert.notNull(batchId, ErrorCodeEnum.NOT_FIND, "找不到对应的批次信息");

        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(batchId);
        if(deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            throw new TmsRuntimeException("未找到此配送批次:"+batchId+"，请刷新页面稍后再试");
        }
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder()
                .batchId(batchId)
                .interceptStates(DeliverySiteInterceptStateEnum.validStateList()).build());

        //可以一次查询，点位集合
        List<Long> siteIdList = deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteEntity).map(SiteEntity::getId).collect(Collectors.toList());
        List<SiteEntity> siteEntities = siteRepository.queryPrimaryIdList(siteIdList);
        Map<Long, SiteEntity> siteIdSiteEntityMap = siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));
        //委托单集合
        List<DistOrderEntity> distOrderEntityList = distOrderDomainService.
                queryValidDistOrder(siteIdList, deliveryBatchEntity.getDeliveryTime().toLocalDate(),deliveryBatchEntity.getBeginSiteId());
        Map<Long, List<DistOrderEntity>> siteIdDistOrderEntityMap = distOrderEntityList.stream().collect(Collectors.groupingBy(i -> i.getEndSite().getId()));

        Map<Long, List<DeliverySiteItemEntity>> deliverySiteItemMap = new HashMap<>();
        //不是待排线,需要查询
        if(deliveryBatchEntity.getStatus() != DeliveryBatchStatusEnum.TO_BE_WIRED){
            List<Long> deliverySiteIdList = deliverySiteEntities.stream().map(DeliverySiteEntity::getId).collect(Collectors.toList());
            List<DeliverySiteItemEntity> deliverySiteItemEntities = deliverySiteItemRepository.queryList(DeliverySiteItemQuery.builder().deliverySiteIds(deliverySiteIdList).build());
            deliverySiteItemMap = deliverySiteItemEntities.stream().collect(Collectors.groupingBy(DeliverySiteItemEntity::getDeliverySiteId));
        }

        Map<Long, List<DeliverySiteItemEntity>> finalDeliverySiteItemMap = deliverySiteItemMap;
        deliverySiteEntities.forEach(deliverySiteEntity -> {
            //查询当前配送点位信息
            deliverySiteEntity.setSiteEntity(siteIdSiteEntityMap.get(deliverySiteEntity.getSiteEntity().getId()));

            //根据配送时间和开始点位查询委托单状态有效的状态
            List<DistOrderEntity> distOrderEntities = siteIdDistOrderEntityMap.get(deliverySiteEntity.getSiteEntity().getId()) == null
                    ? new ArrayList<>() : siteIdDistOrderEntityMap.get(deliverySiteEntity.getSiteEntity().getId());

            if(!CollectionUtils.isEmpty(distOrderEntities)){
                deliverySiteEntity.setOuterClientName(distOrderEntities.stream().map(dist -> dist.getDistClientVO().getOutClientName()).distinct().collect(Collectors.joining("、")));
            }
            if(deliveryBatchEntity.getStatus() != DeliveryBatchStatusEnum.TO_BE_WIRED){
                List<DeliverySiteItemEntity> deliverySiteItemEntities = finalDeliverySiteItemMap.get(deliverySiteEntity.getId());
                if(!CollectionUtils.isEmpty(deliverySiteItemEntities)){
                    deliverySiteEntity.setDeliverySiteItemEntityList(deliverySiteItemEntities);
                }
            }
            //获取这个点位的委托单
            deliverySiteEntity.setSiteDistOrders(distOrderEntities);
            //设置该点位配送货物的体积和重量和金额和精准送和配送类型和拦截信息
            deliverySiteEntity.siteDeliveryCalculate();
        });

        return deliverySiteEntities;
    }
    
    

    /**
     * 先删除在新增路线
     */
    public void confirmDeliveryPlan(Long bachId, List<DeliverySiteEntity> saveSiteList, List<DeliverySiteEntity> needDeleteSiteList) {
        DeliveryBatchEntity deliveryBatch = deliveryBatchRepository.query(bachId);
        //配送中、配送完成状态不进行处理
        List<DeliveryBatchStatusEnum> deliveryStatus = Arrays.asList(DeliveryBatchStatusEnum.IN_DELIVERY, DeliveryBatchStatusEnum.COMPLETE_DELIVERY);
        if(deliveryStatus.contains(deliveryBatch.getStatus())){
            return;
        }
        if (!CollectionUtils.isEmpty(needDeleteSiteList)) {
            List<Long> deliverySideIds = needDeleteSiteList.stream().map(DeliverySiteEntity::getId).collect(Collectors.toList());
            //删除批次对应的点位信息
            deliverySiteRepository.batchRemoveByBatchId(bachId, deliverySideIds);
        }
        //新增路线信息
        deliverySiteRepository.batchSave(saveSiteList);
    }

    /**
     * 修改点位上的批次和顺序
     *
     * @param
     * @return
     */
    public DeliverySiteEntity changeSiteBatch(DeliverySiteEntity deliverySite) {
        //修改配送单信息
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySite.getId());
        //专车逻辑，完成排线改为正确的序号
        if(Objects.equals(deliverySite.getSendWay(),DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue())){
            deliverySite.setSequence(-1);
            deliverySiteRepository.update(deliverySite);
            deliverySiteEntity.setSequence(deliverySite.getSequence());
            return deliverySiteEntity;
        }
        Long count = deliverySiteRepository.queryCount(DeliverySiteQuery.builder()
                .batchId(deliverySite.getDeliveryBatchId())
                .sendWay(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())
                .build()
        );
        count = count == 0 ? 1 : count;
        //如果批次不变就表示修改了顺序,也可能是批量排线
        if (Objects.equals(deliverySiteEntity.getDeliveryBatchId(), deliverySite.getDeliveryBatchId())) {
            if (deliverySite.getSequence() != null && (deliverySite.getSequence() > 0 && deliverySite.getSequence() <= count - 1)) {
                //节点后移
                if(deliverySite.getSequence() > deliverySiteEntity.getSequence()){
                    //原位置到新位置需要减法
                    //顺序减
                    deliverySiteRepository.updateSortSub(deliverySiteEntity.getDeliveryBatchId(),deliverySiteEntity.getSequence(),deliverySite.getSequence());
                }
                //节点前移
                if(deliverySite.getSequence() < deliverySiteEntity.getSequence()){
                    //原位置到新位置需要加法法
                    //顺序加
                    deliverySiteRepository.updateSortAdd(deliverySiteEntity.getDeliveryBatchId(),deliverySiteEntity.getSequence(),deliverySite.getSequence());
                }
            }
            if(deliverySite.getSequence() != null && deliverySite.getSequence() > count - 1){
                deliverySite.setSequence(Integer.parseInt(String.valueOf(count - 1)));
                //顺序减
                deliverySiteRepository.updateSortSub(deliverySiteEntity.getDeliveryBatchId(),deliverySiteEntity.getSequence(),Integer.MAX_VALUE);
            }
        } else {
            if (deliverySite.getSequence() == null) {
                deliverySite.setSequence(count.intValue());
            } else {
                if (deliverySite.getSequence() > count) {
                    deliverySite.setSequence(count.intValue());
                } else {
                    deliverySite.setSequence(deliverySite.getSequence());
                    //顺序加
                    deliverySiteRepository.updateSortAdd(deliverySite.getDeliveryBatchId(),Integer.MAX_VALUE,deliverySite.getSequence());
                }
            }
        }
        deliverySiteRepository.update(deliverySite);
        log.info("changeSiteBatch deliverySite deliverySiteId:{},batchId:{}", deliverySite.getId(),deliverySite.getDeliveryBatchId());

        deliverySiteEntity.setSequence(deliverySite.getSequence());
        return deliverySiteEntity;
    }

    public DeliverySiteEntity intercept(Long distId) {
        //一个委托单只会有一条
        DistOrderEntity distOrder = distOrderRepository.queryWithItemWithBeginSite(DistOrderQuery.builder().distId(distId).build());
        Map<String, List<DistItemVO>> skuDistItemMap = distOrder.getDistItems().stream()
                .filter(distItemVO -> Objects.equals(DistItemDeliveryTypeEnum.DELIVERY.getCode(), distItemVO.getDeliveryType()))
                .collect(Collectors.groupingBy(DistItemVO::getOutItemId));

        if (distOrder.getStatus().getCode() < DistOrderStatusEnum.TO_BE_PICKED.getCode() && distOrder.getStatus().getCode() > DistOrderStatusEnum.IN_DELIVERY.getCode()) {
            return null;
        }

        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().distOrderId(distId).build());
        //查询运输单点位
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.query(DeliverySiteQuery.builder()
                .siteId(distOrder.getEndSite().getId())
                .batchId(deliveryOrderEntities.get(0).getDeliveryBatchId())
                .deliveryTime(distOrder.getDistFlowVO().getExpectBeginTime().toLocalDate()).build());

        //查询当前点位存在的委托单的状态是否都是完成排线前拦截
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder()
                .beginSiteId(distOrder.getBeginSite().getId())
                .endSiteId(distOrder.getEndSite().getId())
                .deliveryTime(distOrder.getDistFlowVO().getExpectBeginTime().toLocalDate()).build());

        //筛选排除（关闭）和（完成排线前取消）的状态
        List<DistOrderEntity> distOrderEntityList = distOrderEntities.stream()
                .filter(distOrderEntity -> !Arrays.asList(DistOrderStatusEnum.CLOSED, DistOrderStatusEnum.CANCEL_BEFORE_WIRED)
                        .contains(distOrderEntity.getStatus()))
                .collect(Collectors.toList());

        //取消
        if (CollectionUtils.isEmpty(distOrderEntityList)) {
            //排线点位是取消状态，无需排线
            deliverySiteEntity.setInterceptState(DeliverySiteInterceptStateEnum.cancel.getCode());
            deliverySiteRepository.update(deliverySiteEntity);
            //拦截取消的需要移除到未排线批次里面
            this.siteRemove(deliverySiteEntity);
            //同时更新配送单上批次为未排批次 deliverySiteEntity.getDeliveryBatchId()已处理为未排批次ID
            deliveryOrderDomainService.changeSiteBatch(deliveryOrderEntities, deliverySiteEntity.getDeliveryBatchId());
            return deliverySiteEntity;
        }

        //排除关闭、完成排线前、完成排线后取消
        List<DistOrderEntity> excludeList = distOrderEntityList.stream().filter(distOrderEntity -> !Arrays.asList(DistOrderStatusEnum.CANCEL_AFTER_WIRED)
                        .contains(distOrderEntity.getStatus()))
                .collect(Collectors.toList());
        //全部拦截
        if (CollectionUtils.isEmpty(excludeList)) {
            deliverySiteEntity.setInterceptState(DeliverySiteInterceptStateEnum.allIntecept.getCode());
            //修改item信息
            List<DeliverySiteItemEntity> deliverySiteItemEntities = deliverySiteItemRepository.queryList(DeliverySiteItemQuery.builder().deliverySiteId(deliverySiteEntity.getId()).build());
            deliverySiteItemEntities.forEach(deliverySiteItemEntity -> {
                deliverySiteItemEntity.setInterceptCount(deliverySiteItemEntity.getPlanReceiptCount());
            });
            deliverySiteEntity.setDeliverySiteItemEntityList(deliverySiteItemEntities);
            deliverySiteRepository.update(deliverySiteEntity);

            return deliverySiteEntity;
        }

        //配送类型
        List<Integer> typeList = excludeList.stream().map(e -> e.getDistFlowVO().getType()).collect(Collectors.toList());
        //部分拦截
        if (distOrderEntityList.size() > 0 && (typeList.contains(DistTypeEnum.DELIVERY.getCode()) || typeList.contains(DistTypeEnum.DELIVERY_AND_RECYCLE.getCode()))) {
            deliverySiteEntity.setInterceptState(DeliverySiteInterceptStateEnum.partIntecept.getCode());

            //修改配送的item信息
            List<DeliverySiteItemEntity> deliverySiteItemEntities = deliverySiteItemRepository.queryList(DeliverySiteItemQuery.builder()
                    .deliverySiteId(deliverySiteEntity.getId()).type(DeliverySiteItemTypeEnum.DELIVERY.getCode()).build());

            deliverySiteItemEntities.forEach(deliverySiteItemEntity -> {
                List<DistItemVO> distItemVOS = skuDistItemMap.get(deliverySiteItemEntity.getOutItemId());
                if (!CollectionUtils.isEmpty(distItemVOS)) {
                    int interceptCount = distItemVOS.stream().mapToInt(DistItemVO::getQuantity).sum();
                    deliverySiteItemEntity.setInterceptCount(deliverySiteItemEntity.getInterceptCount() + interceptCount);
                }
            });
            deliverySiteEntity.setDeliverySiteItemEntityList(deliverySiteItemEntities);
            deliverySiteRepository.update(deliverySiteEntity);

            return deliverySiteEntity;
        }

        //拦截关闭
        if (distOrderEntityList.size() > 0 && typeList.size() == 1 && typeList.contains(DistTypeEnum.RECYCLE.getCode())) {
            deliverySiteEntity.setInterceptState(DeliverySiteInterceptStateEnum.closeIntecept.getCode());
            //修改配送的item信息
            List<DeliverySiteItemEntity> deliverySiteItemEntities = deliverySiteItemRepository.queryList(DeliverySiteItemQuery.builder()
                    .deliverySiteId(deliverySiteEntity.getId()).type(DeliverySiteItemTypeEnum.DELIVERY.getCode()).build());
            deliverySiteItemEntities.forEach(deliverySiteItemEntity -> {
                deliverySiteItemEntity.setInterceptCount(deliverySiteItemEntity.getPlanReceiptCount());
            });
            deliverySiteEntity.setDeliverySiteItemEntityList(deliverySiteItemEntities);
            deliverySiteRepository.update(deliverySiteEntity);
        }
        return deliverySiteEntity;
    }

    public void completePath(DeliveryBatchEntity deliveryBatchEntity) {
        List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getExcludeBeginDeliverySiteList();
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryBatchEntity.getDeliveryOrderEntityList();

        //点位
        Map<Long, List<DeliverySiteEntity>> siteDeliveryMap = deliverySiteList.stream().collect(Collectors.groupingBy(DeliverySiteEntity::getSiteId));
        Map<Long, List<DeliveryOrderEntity>> siteDeliveryOrderMap = deliveryOrderEntityList.stream().collect(Collectors.groupingBy(DeliveryOrderEntity::getEndSiteId));
        ArrayList<DeliverySiteItemEntity> siteItemEntities = new ArrayList<>();
        for (Long siteId : siteDeliveryMap.keySet()) {
            List<DeliverySiteEntity> deliverySiteEntities = siteDeliveryMap.get(siteId);

            List<DeliveryOrderEntity> deliveryOrderEntities = siteDeliveryOrderMap.get(siteId);
            if (CollectionUtils.isEmpty(deliveryOrderEntities)) {
                continue;
            }
            //获取点位的配送信息
            List<DeliveryItemEntity> deliveryItemSendEntities = deliveryOrderEntities.stream()
                    .filter(deliveryOrder -> deliveryOrder.getType() == DeliveryOrderTypeEnum.send.getCode())
                    .map(DeliveryOrderEntity::getDeliveryItemEntityList)
                    .flatMap(Collection::stream).collect(Collectors.toList());
            //获取点位的回收信息
            List<DeliveryItemEntity> deliveryItemRecycleEntities = deliveryOrderEntities.stream()
                    .filter(deliveryOrder -> deliveryOrder.getType() == DeliveryOrderTypeEnum.recycle.getCode())
                    .map(DeliveryOrderEntity::getDeliveryItemEntityList)
                    .flatMap(Collection::stream).collect(Collectors.toList());

            //配送 sku 排除完成排线前拦截 的汇总信息
            List<DeliverySiteItemEntity> sendSiteItem = createSiteItem(deliverySiteEntities, deliveryItemSendEntities, DeliverySiteItemTypeEnum.DELIVERY);
            List<DeliverySiteItemEntity> recycleSiteItem = createSiteItem(deliverySiteEntities, deliveryItemRecycleEntities, DeliverySiteItemTypeEnum.RECYCLE);

            siteItemEntities.addAll(sendSiteItem);
            siteItemEntities.addAll(recycleSiteItem);
        }
        //批量保存
        deliverySiteItemRepository.saveBatch(siteItemEntities);

        // 批量更新点位上的归属区域id
        List<DeliverySiteEntity> needUpdateAdCodeMsgIdDeliverySiteList = deliverySiteList.stream().map(deliverySite -> {
            List<DeliveryOrderEntity> siteHaveDeliveryOrders = siteDeliveryOrderMap.get(deliverySite.getSiteId());
            if (CollectionUtils.isEmpty(siteHaveDeliveryOrders)) {
                return null;
            }
            DeliverySiteEntity needUpdateAdCodeMsgIdDeliverySite = new DeliverySiteEntity();
            needUpdateAdCodeMsgIdDeliverySite.setId(deliverySite.getId());
            needUpdateAdCodeMsgIdDeliverySite.setAdCodeMsgId(siteHaveDeliveryOrders.get(0).getAdCodeMsgId());

            return needUpdateAdCodeMsgIdDeliverySite;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        deliverySiteRepository.batchUpdate(needUpdateAdCodeMsgIdDeliverySiteList);
    }

    /**
     * 获取路线公里数，16个点位去调用高德接口，避免多次高德。。。省钱
     *
     * @param deliverySiteSortList 公里数返回到这个参数里
     * @param fristNode            开始点位
     * @param deliverySiteList     路线点位
     */
    private void getDistanceMethod(ArrayList<DeliverySiteEntity> deliverySiteSortList, AtomicReference<DeliverySiteEntity> fristNode, List<DeliverySiteEntity> deliverySiteList) {
        if (fristNode.get().getId() == null) {
            return;
        }
        Iterator<DeliverySiteEntity> iterator = deliverySiteList.iterator();

        AtomicReference<DeliverySiteEntity> recentNode = new AtomicReference<>(new DeliverySiteEntity());
        AtomicReference<Double> minPoiDistance = new AtomicReference<>();
        minPoiDistance.set(Double.MAX_VALUE);
        while (iterator.hasNext()) {
            DeliverySiteEntity next = iterator.next();
            if (fristNode.get().getId().intValue() == next.getId().intValue()) {
                iterator.remove();
            } else {
                double poiDistance = DistanceUtil.getPoiDistance(fristNode.get().getSiteEntity().getPoi(), next.getSiteEntity().getPoi());
                if (minPoiDistance.get() == null) {
                    minPoiDistance.set(poiDistance);
                } else {
                    if (minPoiDistance.get() > poiDistance) {
                        minPoiDistance.set(poiDistance);
                        recentNode.set(next);
                    }
                }
            }
        }
        if (recentNode.get().getId() != null) {
            deliverySiteSortList.add(recentNode.get());
        }

        if (deliverySiteList.size() > 0) {
            if (recentNode.get().getId() != null) {
                getDistanceMethod(deliverySiteSortList, recentNode, deliverySiteList);
            }
        }
    }

    /**
     * 获取到城配仓最短距离的点位
     *
     * @param beginDeliverySite
     * @param deliverySiteList
     * @return
     */
    private AtomicReference<DeliverySiteEntity> getFirstNode(DeliverySiteEntity beginDeliverySite, List<DeliverySiteEntity> deliverySiteList) {
        AtomicReference<DeliverySiteEntity> fristNode = new AtomicReference<>(new DeliverySiteEntity());
        AtomicReference<Double> minPoiDistance = new AtomicReference<>();
        minPoiDistance.set(Double.MAX_VALUE);
        //获取距离城配仓最近的点位
        deliverySiteList.forEach(deliverySiteEntity -> {
            double poiDistance = DistanceUtil.getPoiDistance(beginDeliverySite.getSiteEntity().getPoi(), deliverySiteEntity.getSiteEntity().getPoi());
            if (minPoiDistance.get() == null) {
                minPoiDistance.set(poiDistance);
            } else {
                if (minPoiDistance.get() > poiDistance) {
                    minPoiDistance.set(poiDistance);
                    fristNode.set(deliverySiteEntity);
                }
            }
        });
        return fristNode;
    }

    private ArrayList<DeliverySiteItemEntity> createSiteItem(List<DeliverySiteEntity> deliverySiteEntities, List<DeliveryItemEntity> deliveryItemSendEntities, DeliverySiteItemTypeEnum deliverySiteItemTypeEnum) {
        Map<String, List<DeliveryItemEntity>> itemDeliveryMap = deliveryItemSendEntities.stream().filter(item -> item.getInterceptCount() == 0).collect(Collectors.groupingBy(DeliveryItemEntity::getOutItemId));
        ArrayList<DeliverySiteItemEntity> deliverySiteItemEntities = new ArrayList<>();

        for (String outerItemId : itemDeliveryMap.keySet()) {
            int quality = itemDeliveryMap.get(outerItemId).stream().mapToInt(DeliveryItemEntity::getPlanReceiptCount).sum();
            DeliverySiteItemEntity deliverySiteItemEntity = new DeliverySiteItemEntity();
            deliverySiteItemEntity.setDeliverySiteId(deliverySiteEntities.get(0).getId());
            deliverySiteItemEntity.setOutItemId(outerItemId);
            deliverySiteItemEntity.setPlanReceiptCount(quality);
            deliverySiteItemEntity.setType(deliverySiteItemTypeEnum.getCode());
            deliverySiteItemEntity.setOutItemName(itemDeliveryMap.get(outerItemId).get(0).getOutItemName());
            deliverySiteItemEntity.setOutItemType(itemDeliveryMap.get(outerItemId).get(0).getOutItemType());
            deliverySiteItemEntity.setPackType(itemDeliveryMap.get(outerItemId).get(0).getPackType());
            deliverySiteItemEntity.setTemperature(itemDeliveryMap.get(outerItemId).get(0).getTemperature());

            deliverySiteItemEntities.add(deliverySiteItemEntity);
            //deliverySiteItemRepository.save(deliverySiteItemEntity);

        }
        return deliverySiteItemEntities;
    }

    /**
     * 点位移除
     *
     * @param deliverySiteEntity
     */
    public DeliveryBatchEntity siteRemove(DeliverySiteEntity deliverySiteEntity) {
        //获取未排线批次
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliverySiteEntity.getDeliveryBatchId());
        DeliveryBatchQuery deliveryBatchQuery = DeliveryBatchQuery.builder()
                .beginSiteId(deliveryBatchEntity.getBeginSiteId())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .pathId(Constants.Delivery_Batch.DEF_DELIVERY_PATH_ID)
                .deliveryTime(deliveryBatchEntity.getDeliveryTime()).build();
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryListWithDriver(deliveryBatchQuery);
        //顺序修改
        if(deliverySiteEntity.getSequence() > 0){
            deliverySiteRepository.updateSortSub(deliverySiteEntity.getDeliveryBatchId(), deliverySiteEntity.getSequence(), 100000);
        }
        deliverySiteEntity.setSequence(1);
        deliverySiteEntity.setDeliveryBatchId(deliveryBatchEntityList.get(0).getId());
        deliverySiteEntity.setSendWay(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue());
        //更新方法
        deliverySiteRepository.update(deliverySiteEntity);

        return deliveryBatchEntity;
    }

    /**
     * 点位变成专车配送
     * @param deliverySiteEntity
     */
    public void changeSpecialCarSendWay(DeliverySiteEntity deliverySiteEntity) {
        deliverySiteRepository.update(deliverySiteEntity);
    }

    /**
     * 智能排线
     * @param deliveryBatchEntity 批次信息
     * @param handleIntelligentFlag 手动智能排线标识 true需要智能，false不需要
     */
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void intelligentPath(DeliveryBatchEntity deliveryBatchEntity,boolean handleIntelligentFlag) {
        long currentTimeMillis = System.currentTimeMillis();
        List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getExcludeBeginDeliverySiteList();
        //智能排线
        DeliverySiteEntity beginDeliverySite = deliveryBatchEntity.getBeginDeliverySite();
        if(beginDeliverySite == null){
            return;
        }
        //所有的点位信息查询
        List<Long> allSiteIdList = deliveryBatchEntity.getDeliverySiteList().stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
        List<SiteEntity> allSiteEntities = siteRepository.queryPrimaryIdList(allSiteIdList);
        Map<Long, SiteEntity> idSiteEntityMap = allSiteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));

        //城配仓点位
        SiteEntity beginSite = idSiteEntityMap.get(beginDeliverySite.getSiteId());
        beginDeliverySite.setSiteEntity(beginSite);
        //客户点位
        deliverySiteList.forEach(deliverySiteEntity -> deliverySiteEntity.setSiteEntity(idSiteEntityMap.get(deliverySiteEntity.getSiteId())));

        //排除专车点位
        List<DeliverySiteEntity> normalSiteList = deliverySiteList.stream()
                .filter(site -> Objects.equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue(), site.getSendWay()))
                .collect(Collectors.toList());
        //专车点位
        List<DeliverySiteEntity> specialSiteList = deliverySiteList.stream()
                .filter(site -> Objects.equals(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue(), site.getSendWay()))
                .collect(Collectors.toList());


        if(CollectionUtils.isEmpty(normalSiteList) && !CollectionUtils.isEmpty(specialSiteList)){
            //专线专车 智能排线
            this.intelligentDeliverySiteHandle(deliveryBatchEntity, handleIntelligentFlag, beginDeliverySite, specialSiteList);
        }else if(!CollectionUtils.isEmpty(normalSiteList) && CollectionUtils.isEmpty(specialSiteList)){
            //普线普送
            this.intelligentDeliverySiteHandle(deliveryBatchEntity, handleIntelligentFlag, beginDeliverySite, normalSiteList);
        }else{
            //部分专车 部分配送
            //普送的需要智能排线
            List<DeliverySiteEntity> deliverySiteSortList = this.intelligentDeliverySiteHandle(deliveryBatchEntity, handleIntelligentFlag, beginDeliverySite, normalSiteList);
            //专车的顺序需要在普线之后
            for (int i = 0; i < specialSiteList.size(); i++) {
                specialSiteList.get(i).setSequence(i + 1 + deliverySiteSortList.size());
                specialSiteList.get(i).setIntelligenceSequence(i + 1 + deliverySiteSortList.size());
            }
            //更新
            deliverySiteRepository.batchUpdate(specialSiteList);
        }
        log.info("==========intelligentPath deliveryBatchId:{},耗时:{}=========", deliveryBatchEntity.getId(), (System.currentTimeMillis() - currentTimeMillis));
    }

    /**
     * 智能排线点位顺序处理器
     * @param deliveryBatchEntity 批次
     * @param handleIntelligentFlag 手动智能排线标识
     * @param beginDeliverySite 起点点位
     * @param needIntelligentSortSiteList 需要智能排线的点位
     * @return 排序后的点位信息
     */
    private List<DeliverySiteEntity> intelligentDeliverySiteHandle(DeliveryBatchEntity deliveryBatchEntity, boolean handleIntelligentFlag,
                                                                        DeliverySiteEntity beginDeliverySite, List<DeliverySiteEntity> needIntelligentSortSiteList) {
        //城配仓点位信息
        SiteEntity beginSite = beginDeliverySite.getSiteEntity();

        //按照距离存放的集合
        List<DeliverySiteEntity> deliverySiteSortList = new ArrayList<>();
        List<String> ortoolsStoreNos = tmsNacosConfig.queryOrtoolsStoreNos();
        Boolean needGreedyIntelligentPath = deliveryBatchEntity.getNeedGreedyIntelligentPath();
        boolean needGreedy= needGreedyIntelligentPath == null ? false : needGreedyIntelligentPath;
        if(ortoolsStoreNos.contains(beginSite.getOutBusinessNo()) && !needGreedy){
            //按照距离存放的集合
            deliverySiteSortList = intelligentDeliverySiteSequenceService.algorithmLeastDistance(beginDeliverySite, needIntelligentSortSiteList);
        }else{
            deliverySiteSortList = intelligentDeliverySiteSequenceService.greedyAlgorithm(beginDeliverySite, needIntelligentSortSiteList);
        }

        log.info("智能排线顺序如下:批次id:{},排线顺序:{}", deliveryBatchEntity.getId(), JSON.toJSONString(deliverySiteSortList));
        List<DeliverySiteEntity> batchDeliverySites = deliveryBatchEntity.getDeliverySiteList();
        //更新点位排序信息
        for (int i = 0; i < deliverySiteSortList.size(); i++) {
            //如果是手动排线要更新智能排线顺序
            if(Objects.equals(beginSite.getIntelligencePath(),DistSiteEnums.IntelligencePath.HAND_PATH.getValue()) && !handleIntelligentFlag){
                deliverySiteSortList.get(i).setIntelligenceSequence(i + 1);
            }else{
                deliverySiteSortList.get(i).setSequence(i + 1);
                deliverySiteSortList.get(i).setIntelligenceSequence(i + 1);
            }

            for (DeliverySiteEntity batchDeliverySite : batchDeliverySites) {
                if(Objects.equals(deliverySiteSortList.get(i).getId(),batchDeliverySite.getId())){
                    batchDeliverySite.setSequence(deliverySiteSortList.get(i).getSequence());
                    batchDeliverySite.setIntelligenceSequence(deliverySiteSortList.get(i).getIntelligenceSequence());
                }
            }
        }
        //更新
        deliverySiteRepository.batchUpdate(deliverySiteSortList);
        return deliverySiteSortList;
    }

    public void siteShort(DeliverySiteEntity deliverySiteEntity) {
        List<DeliverySiteItemEntity> deliverySiteItemEntityList = deliverySiteEntity.getDeliverySiteItemEntityList();
        for (DeliverySiteItemEntity deliverySiteItemEntity : deliverySiteItemEntityList) {
            deliverySiteItemRepository.update(deliverySiteItemEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void siteTemConditionByBatchId(Long deliveryBatchId) {
        if(deliveryBatchId == null){
            return;
        }
        //根据批次查询点位信息
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryListWithItemsByBatchIds(Collections.singletonList(deliveryBatchId));
        deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySite -> !CollectionUtils.isEmpty(deliverySite.getDeliverySiteItemEntityList())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(deliverySiteEntities)){
            return;
        }
        List<DeliverySiteItemEntity> siteItemEntities = deliverySiteEntities.stream()
                .map(DeliverySiteEntity::getDeliverySiteItemEntityList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        //过滤出配送的数据
        Set<String> skuList = siteItemEntities.stream()
                .filter(item -> Objects.equals(item.getType(),DistItemDeliveryTypeEnum.DELIVERY.getCode()))
                .map(DeliverySiteItemEntity::getOutItemId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(skuList)){
            return;
        }
        //查询sku信息
        List<SkuDTO> skuDTOS = wmsQueryFacade.batchQueryBySkus(new ArrayList(skuList));
        Map<String, Integer> skuTemperatureMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getStorageArea, (oldValue, newValue) -> newValue));

        //需要更新的数据
        List<DeliverySiteEntity> updateDeliverySiteList = new ArrayList<>();
        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            List<DeliverySiteItemEntity> deliverySiteItemEntityList = deliverySiteEntity.getDeliverySiteItemEntityList();
            //过滤出配送数据
            List<DeliverySiteItemEntity> deliveryItems = deliverySiteItemEntityList.stream().filter(item -> Objects.equals(item.getType(), DistItemDeliveryTypeEnum.DELIVERY.getCode())).collect(Collectors.toList());
            Set<String> skus = deliveryItems.stream().map(DeliverySiteItemEntity::getOutItemId).collect(Collectors.toSet());
            HashSet<String> temConditionSet = new HashSet<>();

            for (String sku : skus) {
                Integer temperature = skuTemperatureMap.get(sku);
                if(temperature == null || TmsTemperatureEnum.map.get(temperature) == null){
                    continue;
                }
                temConditionSet.add(TmsTemperatureEnum.map.get(temperature));
            }
            DeliverySiteEntity updateDeliverySite = new DeliverySiteEntity();
            updateDeliverySite.setId(deliverySiteEntity.getId());
            updateDeliverySite.setTemperatureConditions(temConditionSet.stream().collect(Collectors.joining("|")));

            updateDeliverySiteList.add(updateDeliverySite);
        }

        deliverySiteRepository.batchUpdate(updateDeliverySiteList);
    }


    public List<DeliverySiteEntity> queryDetail(List<Long> batchIds, LocalDateTime deliveryTime,Long beginSiteId) {
        TmsAssert.notEmpty(batchIds, ErrorCodeEnum.PARAM_NOT_NULL, "批次信息");
        TmsAssert.notNull(deliveryTime, ErrorCodeEnum.PARAM_NOT_NULL, "配送日期");
        TmsAssert.notNull(beginSiteId, ErrorCodeEnum.PARAM_NOT_NULL, "城配仓点位不存在");

        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder()
                .batchIdList(batchIds)
                .interceptStates(DeliverySiteInterceptStateEnum.validStateList()).build());
        if(CollectionUtils.isEmpty(deliverySiteEntities)){
            return Collections.emptyList();
        }
        deliverySiteEntities = deliverySiteEntities.stream().filter(e -> !Objects.equals(e.getType(),DeliverySiteTypeEnum.begin.getCode())).collect(Collectors.toList());
        List<Long> siteIdList = deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteEntity).map(SiteEntity::getId).collect(Collectors.toList());
        //委托单集合
        List<DistOrderEntity> distOrderEntityList = distOrderDomainService.queryValidDistOrder(siteIdList, deliveryTime.toLocalDate(),beginSiteId);
        Map<Long, List<DistOrderEntity>> siteIdDistOrderEntityMap = distOrderEntityList.stream().collect(Collectors.groupingBy(i -> i.getEndSite().getId()));
        deliverySiteEntities.forEach(deliverySiteEntity -> {
            //根据配送时间和开始点位查询委托单状态有效的状态
            List<DistOrderEntity> distOrderEntities = siteIdDistOrderEntityMap.get(deliverySiteEntity.getSiteEntity().getId()) == null
                    ? new ArrayList<>() : siteIdDistOrderEntityMap.get(deliverySiteEntity.getSiteEntity().getId());

            if(!CollectionUtils.isEmpty(distOrderEntities)){
                deliverySiteEntity.setOuterClientName(distOrderEntities.stream().map(dist -> dist.getDistClientVO().getOutClientName()).distinct().collect(Collectors.joining("、")));
            }
            //获取这个点位的委托单
            deliverySiteEntity.setSiteDistOrders(distOrderEntities);
            //设置该点位配送货物的体积和重量和金额和精准送和配送类型和拦截信息
            deliverySiteEntity.siteDeliveryCalculate();
        });

        //设置配送时效
        deliveryAlertDomainService.installDeliveryAlertTimeFrameList(deliverySiteEntities,beginSiteId);

        return deliverySiteEntities;

    }

    /**
     * 是否需要扫码判断
     * @param deliverySiteEntity 点位实体
     * @param distOrderEntityList 点位订单实体
     */
    public void isNeedScanCode(DeliverySiteEntity deliverySiteEntity, List<DistOrderEntity> distOrderEntityList) {
        if(deliverySiteEntity == null || CollectionUtils.isEmpty(distOrderEntityList)){
            return;
        }
        deliverySiteEntity.setScanCodeFlag(true);
        DistOrderSourceEnum distSource = distOrderEntityList.get(0).getSource();
        if(DistOrderSourceEnum.getXmOrderTypeEnum().contains(distSource)){
            //鲜沐
            List<String> xmOutBrandIds = tmsNacosConfig.queryNoScanXianMuBrandIds();
            if(xmOutBrandIds.contains(deliverySiteEntity.getOuterBrandId())){
                deliverySiteEntity.setScanCodeFlag(false);
                return;
            }
            SiteEntity beginSite = distOrderEntityList.get(0).getBeginSite();
            if(beginSite != null){
                Long beginSiteId = beginSite.getId();
                // 如果是POP城配仓不需要扫码标识
                List<String> popStoreNosList = wncQueryFacade.queryPopStoreNosList();
                Map<Long, SiteEntity> siteId2SiteMap = siteRepository.querySiteStoreMapByOutBusinessNos(popStoreNosList);
                SiteEntity siteEntity = siteId2SiteMap.get(beginSiteId);
                if(siteEntity == null){
                    return;
                }
                deliverySiteEntity.setScanCodeFlag(false);
            }
        }else if(DistOrderSourceEnum.getSaasOrderTypeEnum().contains(distSource)){
            //Saas
            List<String> saasOutBrandIds = tmsNacosConfig.queryNoScanSaasBrandIds();
            if(saasOutBrandIds.contains(deliverySiteEntity.getOuterBrandId())){
                deliverySiteEntity.setScanCodeFlag(false);
            }
        }
    }

    /**
     * 根据订单信息设置到店打卡标识
     * @param deliverySiteEntities 配送点位订单信息
     */
    public void checkinPunchByDistOrders(List<DeliverySiteEntity> deliverySiteEntities) {
        if(CollectionUtils.isEmpty(deliverySiteEntities)){
            return;
        }
        //查询鲜沐到店打卡品牌id
        List<String> cheakInPunchXmBrandIdList = tmsNacosConfig.queryCheakInPunchXmBrandIds();

        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            List<DistOrderEntity> distOrderEntityList = deliverySiteEntity.getSiteDistOrders();
            if(CollectionUtils.isEmpty(distOrderEntityList)){
                continue;
            }
            deliverySiteEntity.setCheckinPunchFlag(false);
            //如果是鲜沐点位并且有配置则需要到店打卡
            if(DistOrderSourceEnum.getXmOrderTypeEnum().contains(distOrderEntityList.get(0).getSource()) && cheakInPunchXmBrandIdList.contains(deliverySiteEntity.getOuterBrandId())){
                deliverySiteEntity.setCheckinPunchFlag(true);
            }
        }
    }


    /**
     * 根据点位信息设置到店打卡标识
     * @param deliverySite 配送点位订单信息
     */
    public void checkinPunchByDistSite(DeliverySiteEntity deliverySite) {
        if(deliverySite == null || deliverySite.getId() == null){
            return;
        }
        //查询鲜沐到店打卡品牌id
        List<String> cheakInPunchXmBrandIdList = tmsNacosConfig.queryCheakInPunchXmBrandIds();
        SiteEntity siteEntity = siteRepository.query(deliverySite.getSiteId());
        deliverySite.setSiteEntity(siteEntity);

        //点位不为空、鲜沐点位、有配置信息
        deliverySite.setCheckinPunchFlag(siteEntity != null && Objects.equals(siteEntity.getType(), SiteTypeEnum.cust.getCode())
                && cheakInPunchXmBrandIdList.contains(deliverySite.getOuterBrandId()));
    }

    /**
     * 设置到店打卡信息记录等
     * @param deliverySiteEntity 配送点位订单信息
     */
    public void checkinPunchRecord(DeliverySiteEntity deliverySiteEntity) {
        if(deliverySiteEntity == null){
            return;
        }
        //设置到店打卡信息
        this.checkinPunchByDistOrders(Collections.singletonList(deliverySiteEntity));
        if(deliverySiteEntity.getCheckinPunchFlag() != null && deliverySiteEntity.getCheckinPunchFlag()){
            //查询是否有打卡记录
            DeliverySiteCheckinPunchEntity punchEntity = deliverySiteCheckinPunchRepository.queryByDeliverySiteId(deliverySiteEntity.getId());
            deliverySiteEntity.setCheckinPunchEntity(punchEntity);
            deliverySiteEntity.setPunchRange(tmsNacosConfig.getPunchRange());
        }
    }

    /**
     * 到店打卡
     * @param entity 到点打卡信息
     */
    public void cheakinPunch(DeliverySiteCheckinPunchEntity entity) {
        //查询是否已经存在打卡
        DeliverySiteCheckinPunchEntity punchEntity = deliverySiteCheckinPunchRepository.queryByDeliverySiteId(entity.getDeliverySiteId());
        if(punchEntity != null){
            throw new TmsRuntimeException("当前店铺已经存在打卡信息,无需再次打卡!");
        }
        deliverySiteCheckinPunchRepository.save(entity);
    }

    /**
     * 是否需要到店打卡校验
     * @param deliverySiteEntityReq 配送点位实体
     */
    public void checkIsHaveinPunch(DeliverySiteEntity deliverySiteEntityReq) {
        if(deliverySiteEntityReq == null || deliverySiteEntityReq.getId() == null){
            return;
        }
        DeliverySiteEntity deliverySiteData = deliverySiteRepository.queryById(deliverySiteEntityReq.getId());
        if(deliverySiteData == null){
            throw new TmsRuntimeException("配送点位不存在");
        }
        //判断是否需要打卡
        this.checkinPunchByDistSite(deliverySiteData);

        if(!deliverySiteData.getCheckinPunchFlag()){
            return;
        }
        //如果需要判断是否已经打卡了
        DeliverySiteCheckinPunchEntity punchEntity = deliverySiteCheckinPunchRepository.queryByDeliverySiteId(deliverySiteData.getId());
        if(punchEntity == null){
            throw new TmsRuntimeException("此店铺需要到店打卡才能完成配送");
        }
    }

    public String querySiteRecentHeadPic(Long siteId) {
        if (siteId == null){
            return null;
        }
        DeliverySiteEntity deliverySiteEntity = this.queryRecentDeliveryFinishSite(siteId);
        if (deliverySiteEntity == null){
            return null;
        }
        return deliverySiteEntity.getSignInPics();
    }

    public DeliverySiteEntity queryRecentDeliveryFinishSite(Long siteId) {
        if (siteId == null){
            return null;
        }
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryRecentDeliveryFinishSite(siteId);
        if (deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            return null;
        }
        return deliverySiteEntity;
    }

    /**
     * 根据配送点位ID查询配送点位订单信息
     * @param tmsDeliverySiteId 配送点位ID
     * @return 配送点位信息
     */
    public DeliverySiteEntity querySiteOrderById(Long tmsDeliverySiteId) {
        if(tmsDeliverySiteId == null){
            return null;
        }
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(tmsDeliverySiteId);
        if(deliverySiteEntity == null){
            return null;
        }
        //查询配送点位订单信息
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                .endSiteId(deliverySiteEntity.getSiteId())
                .batchId(deliverySiteEntity.getDeliveryBatchId())
                .build());
        if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
            return deliverySiteEntity;
        }
        List<Long> distIds = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(distIds)){
            return deliverySiteEntity;
        }
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder().distIdList(distIds).build());
        deliverySiteEntity.setSiteDistOrders(distOrderEntities);
        return deliverySiteEntity;
    }

    /**
     * 根据干线配送点位ID查询配送点位订单信息
     * @param tmsDeliverySiteId 配送点位ID
     * @return 配送点位信息
     */
    public DeliverySiteEntity queryTrunkSiteOrderById(Long tmsDeliverySiteId) {
        if(tmsDeliverySiteId == null){
            return null;
        }
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(tmsDeliverySiteId);
        if(deliverySiteEntity == null){
            return null;
        }
        //查询配送点位订单信息
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                .beginSiteId(deliverySiteEntity.getSiteId())
                .batchId(deliverySiteEntity.getDeliveryBatchId())
                .build());
        if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
            return deliverySiteEntity;
        }
        List<Long> distIds = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(distIds)){
            return deliverySiteEntity;
        }
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder().distIdList(distIds).build());
        deliverySiteEntity.setSiteDistOrders(distOrderEntities);
        return deliverySiteEntity;
    }

    /**
     * 查询配送点位标品温度条件
     * @param deliverySiteIds 站点ID集合
     * @return 配送站点对应标品的数据信息
     */
    public Map<Long, DeliverySiteStandardTempCondition> queryStandardItemTemperatureConditionsByIds(List<Long> deliverySiteIds) {
        if(CollectionUtils.isEmpty(deliverySiteIds)){
            return Collections.emptyMap();
        }
        List<DeliverySiteStandardTempCondition> deliverySiteStandardTempConditions = deliverySiteRepository.queryStandardItemTemperatureConditionsByIds(deliverySiteIds);
        if(CollectionUtils.isEmpty(deliverySiteStandardTempConditions)){
            return Collections.emptyMap();
        }
        // 转换为Map
        return deliverySiteStandardTempConditions.stream().collect(Collectors.toMap(DeliverySiteStandardTempCondition::getDeliverySiteId,Function.identity()));
    }

    /**
     * 根据拣货站点信息查询标品温度条件
     * @param deliverySiteIds 站点ID集合
     * @return 配送站点对应拣货标品的数据信息
     */
    public Map<Long, DeliverySiteStandardTempCondition> queryPickStandardItemTemperatureConditionsByDelSiteIds(List<Long> deliverySiteIds) {
        if(CollectionUtils.isEmpty(deliverySiteIds)){
            return Collections.emptyMap();
        }
        List<DeliverySiteStandardTempCondition> deliverySiteStandardTempConditions = deliveryPickRepository.queryPickStandardItemTemperatureConditionsByDelSiteIds(deliverySiteIds);
        if(CollectionUtils.isEmpty(deliverySiteStandardTempConditions)){
            return Collections.emptyMap();
        }
        // 转换为Map
        return deliverySiteStandardTempConditions.stream().collect(Collectors.toMap(DeliverySiteStandardTempCondition::getDeliverySiteId,Function.identity()));
    }

    /**
     * 点位拦截批量处理（延期配送场景使用）
     * @param deliverySiteEntities 店铺点位配送信息
     * @param haveCompletedDeliveryOrderEntityList 配送单集合
     */
    public void batchInterceptToDelayedDelivery(List<DeliverySiteEntity> deliverySiteEntities, List<DeliveryOrderEntity> haveCompletedDeliveryOrderEntityList) {
        if(CollectionUtils.isEmpty(deliverySiteEntities) || CollectionUtils.isEmpty(haveCompletedDeliveryOrderEntityList)){
            return;
        }
        log.info("deliverySiteDomainService batchInterceptToDelayedDelivery deliverySiteEntities:{},haveCompletedDeliveryOrderEntityList:{}",
                JSON.toJSONString(deliverySiteEntities),JSON.toJSONString(haveCompletedDeliveryOrderEntityList));
        // 过滤出【未签收】状态的配送单数据
        List<DeliveryOrderEntity> couldInterceptOrderEntityList = haveCompletedDeliveryOrderEntityList.stream().filter(deliveryOrderEntity -> Objects.equals( DeliveryOrderStatusEnum.NO_SIGN, deliveryOrderEntity.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(couldInterceptOrderEntityList)){
            return;
        }

        // 批次ID集合
        List<Long> batchIds = deliverySiteEntities.stream().map(DeliverySiteEntity::getDeliveryBatchId).distinct().collect(Collectors.toList());
        // 点位集合
        List<Long> siteIds = deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteId).distinct().collect(Collectors.toList());

        // 查询配送站点对应的配送单数据
        List<DeliveryOrderEntity> deliverySiteHaveOrders = deliveryOrderRepository.queryListWithItem(DeliveryOrderQuery.builder().batchIds(batchIds).endSiteIds(siteIds).build());

        // 按照批次ID、站点ID、配送时间进行分组
        Map<String, List<DeliveryOrderEntity>> batchSiteDeliveryTime2AllOrderMap = deliverySiteHaveOrders.stream().collect(Collectors.groupingBy(d -> d.getDeliveryBatchId() + "_" + d.getEndSiteId() + "_" + d.getDeliveryTime()));

        // 对应可拦截的配送单数据
        Map<String, List<DeliveryOrderEntity>> couldInterceptBatchSiteDeliveryTime2OrderMap = couldInterceptOrderEntityList.stream().collect(Collectors.groupingBy(d -> d.getDeliveryBatchId() + "_" + d.getEndSiteId() + "_" + d.getDeliveryTime()));

        // 设置当前站点拥有的配送单数据
        deliverySiteEntities.forEach(deliverySiteEntity -> {
            String onlyKey = deliverySiteEntity.getDeliveryBatchId() + "_" + deliverySiteEntity.getSiteId() + "_" + deliverySiteEntity.getPlanArriveTime();
            // 站点需要拦截的配送单数据
            List<DeliveryOrderEntity> couldInterceptOrders = couldInterceptBatchSiteDeliveryTime2OrderMap.getOrDefault(onlyKey,Collections.emptyList());
            // 站点拥有的所有的配送单数据
            List<DeliveryOrderEntity> siteHaveAllOrders = batchSiteDeliveryTime2AllOrderMap.getOrDefault(onlyKey,Collections.emptyList());

            List<DeliveryOrderEntity> siteHaveNotCloseOrders = siteHaveAllOrders.stream().filter(order -> !Objects.equals(DeliveryOrderStatusEnum.CLOSE, order.getStatus())).collect(Collectors.toList());
            // 如果站点拥有的配送单状态都是关闭状态，则直接全部拦截
            if(CollectionUtils.isEmpty(siteHaveNotCloseOrders)){
                // 直接全部拦截
                deliverySiteEntity.setInterceptState(DeliverySiteInterceptStateEnum.allIntecept.getCode());
                List<DeliverySiteItemEntity> deliverySiteItemEntityList = deliverySiteEntity.getDeliverySiteItemEntityList();
                deliverySiteItemEntityList.forEach(deliverySiteItemEntity -> {
                    deliverySiteItemEntity.setInterceptCount(deliverySiteItemEntity.getPlanReceiptCount());
                });
                return;
            }
            // 比对数据，是否全部拦截了
            Map<Long, DeliveryOrderEntity> couldInterceptDeliveryIdsMap = couldInterceptOrders.stream().collect(Collectors.toMap(DeliveryOrderEntity::getId, Function.identity()));
            Map<Long, DeliveryOrderEntity> siteHaveAllDeliveryIdsMap = siteHaveAllOrders.stream().collect(Collectors.toMap(DeliveryOrderEntity::getId, Function.identity()));

            if(couldInterceptDeliveryIdsMap.keySet().containsAll(siteHaveAllDeliveryIdsMap.keySet())){
                // 全部拦截
                deliverySiteEntity.setInterceptState(DeliverySiteInterceptStateEnum.allIntecept.getCode());
                List<DeliverySiteItemEntity> deliverySiteItemEntityList = deliverySiteEntity.getDeliverySiteItemEntityList();
                deliverySiteItemEntityList.forEach(deliverySiteItemEntity -> {
                    deliverySiteItemEntity.setInterceptCount(deliverySiteItemEntity.getPlanReceiptCount());
                });
            }else{
                // 找出在 siteHaveAllDeliveryIdsMap 中但不在 couldInterceptDeliveryIdsMap 中的部分
                List<DeliveryOrderEntity> missingDeliveryOrderList = new ArrayList<>();
                siteHaveAllDeliveryIdsMap.entrySet().stream()
                        .filter(entry -> !couldInterceptDeliveryIdsMap.containsKey(entry.getKey()))
                        .forEach(entry -> {
                            missingDeliveryOrderList.add(entry.getValue());
                        });

                Set<Integer> deliveryTypeSet = missingDeliveryOrderList.stream()
                        .filter(o -> !Objects.equals(o.getStatus(),DeliveryOrderStatusEnum.CLOSE))
                        .map(DeliveryOrderEntity::getType)
                        .collect(Collectors.toSet());

                Map<String, List<DeliveryItemEntity>> groupedByTypeAndOutItemIdCouldInterceptOrderMap = couldInterceptOrders.stream()
                        .flatMap(order -> order.getDeliveryItemEntityList().stream()
                                .map(item -> new AbstractMap.SimpleEntry<>(order.getType(), item))) // 将type和item组合成Entry
                        .collect(Collectors.groupingBy(entry -> entry.getKey() + "#" + entry.getValue().getOutItemId(),
                                Collectors.mapping(AbstractMap.SimpleEntry::getValue, Collectors.toList())));

                if(deliveryTypeSet.size() == 1 && deliveryTypeSet.contains(DeliveryTypeEnum.RECYCLE.getType())){
                    // 只存在回收（拦截关闭）
                    deliverySiteEntity.setInterceptState(DeliverySiteInterceptStateEnum.closeIntecept.getCode());
                }else{
                    // 部分拦截
                    deliverySiteEntity.setInterceptState(DeliverySiteInterceptStateEnum.partIntecept.getCode());
                }

                List<DeliverySiteItemEntity> closeInterceptItemList = deliverySiteEntity.getDeliverySiteItemEntityList();
                closeInterceptItemList.forEach(item ->{
                    List<DeliveryItemEntity> itemNeedInterceptList = groupedByTypeAndOutItemIdCouldInterceptOrderMap.get(item.getType() + "#" + item.getOutItemId());
                    if(CollectionUtils.isEmpty(itemNeedInterceptList)){
                        return;
                    }
                    int needInterceptNum = itemNeedInterceptList.stream().mapToInt(DeliveryItemEntity::getPlanReceiptCount).sum();
                    item.setInterceptCount(Math.min(needInterceptNum + item.getInterceptCount(), item.getPlanReceiptCount()));
                });
            }
        });
        List<DeliverySiteEntity> needUpdateDeliverySiteList = deliverySiteEntities.stream().map(DeliverySiteEntity::interceptDeliverySiteEntity).collect(Collectors.toList());
        // 更新
        deliverySiteRepository.batchUpdate(needUpdateDeliverySiteList);

        List<DeliverySiteItemEntity> needUpdateItemEntityList = deliverySiteEntities.stream()
                .map(DeliverySiteEntity::getDeliverySiteItemEntityList)
                .flatMap(Collection::stream)
                .map(DeliverySiteItemEntity::interceptDeliverySiteItem)
                .collect(Collectors.toList());
        deliverySiteItemRepository.batchUpdate(needUpdateItemEntityList);

    }
}
