package net.summerfarm.tms.delivery.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 配送单详情表
 */
@Data
public class DeliveryItemEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 配送单ID
     */
    private Long deliveryOrderId;

    /**
     * 委托单id
     */
    private Long distOrderId;

    /**
     * 外部订单号
     */
    private String outOrderId;

    /**
     * 外部条目id
     */
    private String outItemId;

    /**
     * 计划签收数量
     */
    private Integer planReceiptCount;

    /**
     * 实际签收数量
     */
    private Integer realReceiptCount;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 拦截数量
     */
    private Integer interceptCount;

    /**
     * 拒收数量
     */
    private Integer rejectCount;

    /**
     * 拒收原因
     */
    private String rejectRemark;

    /**
     * 扫码数量
     */
    private Integer scanCount;

    /**
     * 无码数量
     */
    private Integer noscanCount;

    /**
     * 无码原因
     */
    private String noscanReason;

    /**
     * 无码货物照片
     */
    private String noscanPics;

    /**
     * 商品名称
     */
    private String outItemName;

    /**
     * 类型 0普通 1水果
     */
    private Integer outItemType;

    /**
     * 包装类型：0单品 1包裹
     */
    private Integer packType;

    /**
     * 温区,1:常温,2:冷藏,3:冷冻
     */
    private Integer temperature;

    public DeliveryItemEntity interceptItem() {
        DeliveryItemEntity interceptItem = new DeliveryItemEntity();
        interceptItem.setId(this.getId());
        interceptItem.setInterceptCount(this.getPlanReceiptCount());
        return interceptItem;
    }
}
