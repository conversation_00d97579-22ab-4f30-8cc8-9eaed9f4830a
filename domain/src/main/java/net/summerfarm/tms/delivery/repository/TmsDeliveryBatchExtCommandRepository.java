package net.summerfarm.tms.delivery.repository;


import net.summerfarm.tms.delivery.entity.TmsDeliveryBatchExtEntity;
import net.summerfarm.tms.delivery.param.command.TmsDeliveryBatchExtCommandParam;

/**
*
* <AUTHOR>
* @date 2025-05-14 15:11:42
* @version 1.0
*
*/
public interface TmsDeliveryBatchExtCommandRepository {

    TmsDeliveryBatchExtEntity insertSelective(TmsDeliveryBatchExtCommandParam param);

    int updateSelectiveById(TmsDeliveryBatchExtCommandParam param);

    int remove(Long id);

}