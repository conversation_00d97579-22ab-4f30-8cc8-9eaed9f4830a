package net.summerfarm.tms.delivery;


import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.SiteDomainService;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.delivery.repository.TmsDeliveryPickShortOrderMappingCommandRepository;
import net.summerfarm.tms.delivery.repository.TmsDeliveryPickShortOrderMappingQueryRepository;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.facade.wms.dto.SkuBatchCodeTraceDTO;
import net.summerfarm.tms.facade.wms.input.QueryBatchTraceCodeByOrderNoInput;
import net.summerfarm.tms.pick.repository.DeliveryPickScanCodeQueryRepository;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.wms.skucodetrace.enums.SkuBatchCodeTraceStateEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 拣货缺货订单映射领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TmsDeliveryPickShortOrderMappingCommandDomainService {

    @Autowired
    private DeliverySiteRepository deliverySiteRepository;
    @Autowired
    private SiteDomainService siteDomainService;
    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;
    @Autowired
    private DeliveryPickScanCodeQueryRepository deliveryPickScanCodeQueryRepository;
    @Autowired
    private WmsQueryFacade wmsQueryFacade;
    @Autowired
    private DeliveryBatchRepository deliveryBatchRepository;
    @Autowired
    private TmsDeliveryPickShortOrderMappingCommandRepository tmsDeliveryPickShortOrderMappingCommandRepository;
    @Autowired
    private TmsDeliveryPickShortOrderMappingQueryRepository tmsDeliveryPickShortOrderMappingQueryRepository;

    public String validateFinishPick(DeliverySiteEntity deliverySiteEntity) {
        if (deliverySiteEntity == null) {
            return null;
        }
        boolean isPop = siteDomainService.isPopBySiteIdCache(deliverySiteEntity.getSiteId());
        // 非POP城配仓不做校验
        if (!isPop) {
            log.info("非POP城配仓不做校验, siteId:{}", deliverySiteEntity.getSiteId());
            return null;
        }

        // 查询拣货唯一码
        List<DeliveryPickScanCodeEntity> deliveryPickScanCodeEntities = deliveryPickScanCodeQueryRepository.queryByDeliveryBatchId(deliverySiteEntity.getDeliveryBatchId());
        List<String> pickScanOnlyCodeList = deliveryPickScanCodeEntities.stream().map(DeliveryPickScanCodeEntity::getOnlyCode).distinct().collect(Collectors.toList());

        // 查询溯源码
        List<SkuBatchCodeTraceDTO> skuBatchCodeTraceDTOList = querySkuBatchCodeTrade(deliverySiteEntity.getDeliveryBatchId());

        // 如果存在已称重的溯源码但未拣货，则返回校验提示信息
        Set<String> noPickPdNames = new HashSet<>();
        for (SkuBatchCodeTraceDTO skuBatchCodeTraceDTO : skuBatchCodeTraceDTOList) {
            // 过滤掉未称重的溯源码
            if (!SkuBatchCodeTraceStateEnum.HAVE_WEIGHT.getValue().equals(skuBatchCodeTraceDTO.getState())) {
                continue;
            }
            if (!pickScanOnlyCodeList.contains(skuBatchCodeTraceDTO.getSkuBatchTraceCode())) {
                noPickPdNames.add(skuBatchCodeTraceDTO.getPdName());
            }
        }
        if (CollectionUtils.isEmpty(noPickPdNames)) {
            return null;
        } else {
            return String.join("、", noPickPdNames) + "已完成称重，请仔细核对是否缺货";
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void finishPick(Long deliverySiteId) {
        if (deliverySiteId == null) {
            return;
        }
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteId);
        boolean isPop = siteDomainService.isPopBySiteIdCache(deliverySiteEntity.getSiteId());
        // 非POP城配仓不做处理
        if (!isPop) {
            log.info("非POP城配仓不做处理, deliverySiteId:{}", deliverySiteId);
            return;
        }

        // 查询拣货唯一码
        List<DeliveryPickScanCodeEntity> deliveryPickScanCodeEntities = deliveryPickScanCodeQueryRepository.queryByDeliveryBatchId(deliverySiteEntity.getDeliveryBatchId());
        List<String> pickScanOnlyCodeList = deliveryPickScanCodeEntities.stream().map(DeliveryPickScanCodeEntity::getOnlyCode).distinct().collect(Collectors.toList());

        // 查询溯源码
        List<SkuBatchCodeTraceDTO> skuBatchCodeTraceDTOList = querySkuBatchCodeTrade(deliverySiteEntity.getDeliveryBatchId());

        // 查询司机信息
        DeliveryBatchEntity deliveryBatchDetail = deliveryBatchRepository.getBatchDetail(deliverySiteEntity.getDeliveryBatchId());

        // 未拣货的溯源码则认为拣货缺货
        Map<String, TmsDeliveryPickShortOrderMappingEntity> deliveryPickShortOrderMappingEntityMap = new HashMap<>();
        for (SkuBatchCodeTraceDTO skuBatchCodeTraceDTO : skuBatchCodeTraceDTOList) {
            // 过滤掉待分配的溯源码
            if (SkuBatchCodeTraceStateEnum.ORDER_WAIT_ALLOCATION.getValue().equals(skuBatchCodeTraceDTO.getState())
                    || SkuBatchCodeTraceStateEnum.PATH_WAIT_ALLOCATION.getValue().equals(skuBatchCodeTraceDTO.getState())) {
                continue;
            }
            if (!pickScanOnlyCodeList.contains(skuBatchCodeTraceDTO.getSkuBatchTraceCode())) {
                String key = skuBatchCodeTraceDTO.getOrderNo() + "_" + skuBatchCodeTraceDTO.getSku();
                TmsDeliveryPickShortOrderMappingEntity entity = deliveryPickShortOrderMappingEntityMap.computeIfAbsent(
                        key, (k) -> buildTmsDeliveryPickShortOrderMappingEntity(skuBatchCodeTraceDTO, deliveryBatchDetail));
                entity.setShortQuantity(entity.getShortQuantity() + 1);
            }
        }
        // 保存拣货缺货信息
        if (MapUtils.isNotEmpty(deliveryPickShortOrderMappingEntityMap)) {
            tmsDeliveryPickShortOrderMappingCommandRepository.batchInsert(Lists.newArrayList(deliveryPickShortOrderMappingEntityMap.values()));
        }
    }

    public void installDeliverySitePickLackInfo(DeliveryBatchEntity deliveryBatchEntity, List<DeliverySiteEntity> deliverySiteEntities) {
        if (deliveryBatchEntity == null || CollectionUtils.isEmpty(deliverySiteEntities)) {
            return;
        }
        List<TmsDeliveryPickShortOrderMappingEntity> deliveryPickShortOrderMappingEntities = tmsDeliveryPickShortOrderMappingQueryRepository.listByDeliveryBatchId(deliveryBatchEntity.getId());
        if (CollectionUtils.isEmpty(deliveryPickShortOrderMappingEntities)) {
            return;
        }
        Map<Integer, List<TmsDeliveryPickShortOrderMappingEntity>> pickShortEntityMap = deliveryPickShortOrderMappingEntities.stream()
                .collect(Collectors.groupingBy(TmsDeliveryPickShortOrderMappingEntity::getPathSequence));
        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            // 设置配送站点拣货缺货标识
            List<TmsDeliveryPickShortOrderMappingEntity> pickShortEntityList = pickShortEntityMap.get(deliverySiteEntity.getSequence());
            if (CollectionUtils.isEmpty(pickShortEntityList)) {
                deliverySiteEntity.setPickLackFlag(false);
                continue;
            }
            deliverySiteEntity.setPickLackFlag(true);

            // 设置拣货缺货数量
            if (CollectionUtils.isEmpty(deliverySiteEntity.getDeliverySiteItemEntityList())) {
                continue;
            }
            Map<String, Integer> pickShortCountMap = pickShortEntityList.stream().collect(Collectors.toMap(
                    TmsDeliveryPickShortOrderMappingEntity::getOutItemId, TmsDeliveryPickShortOrderMappingEntity::getShortQuantity, Integer::sum));
            for (DeliverySiteItemEntity deliverySiteItemEntity : deliverySiteEntity.getDeliverySiteItemEntityList()) {
                deliverySiteItemEntity.setPickShortCount(pickShortCountMap.get(deliverySiteItemEntity.getOutItemId()));
            }
        }
    }

    private List<SkuBatchCodeTraceDTO> querySkuBatchCodeTrade(Long deliveryBatchId) {
        if (deliveryBatchId == null) {
            return Collections.emptyList();
        }
        DeliveryOrderQuery deliveryOrderQuery = new DeliveryOrderQuery();
        deliveryOrderQuery.setBatchId(deliveryBatchId);
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(deliveryOrderQuery);
        if (CollectionUtils.isEmpty(deliveryOrderEntities)) {
            throw new TmsRuntimeException("根据配送批次找不到对应的配送单，配送批次id：" + deliveryBatchId);
        }
        List<String> outerOrderIds = deliveryOrderEntities.stream().map(DeliveryOrderEntity::getOuterOrderId).distinct().collect(Collectors.toList());
        QueryBatchTraceCodeByOrderNoInput input = new QueryBatchTraceCodeByOrderNoInput();
        input.setOrderNos(outerOrderIds);
        LocalDateTime deliveryTime = deliveryOrderEntities.get(0).getDeliveryTime();
        if (deliveryTime != null) {
            input.setDeliveryDate(deliveryTime.toLocalDate());
        }
        return wmsQueryFacade.queryBatchTraceCodeByOrderNo(input);
    }

    private TmsDeliveryPickShortOrderMappingEntity buildTmsDeliveryPickShortOrderMappingEntity(SkuBatchCodeTraceDTO skuBatchCodeTraceDTO, DeliveryBatchEntity deliveryBatchEntity) {
        TmsDeliveryPickShortOrderMappingEntity entity = new TmsDeliveryPickShortOrderMappingEntity();
        entity.setDeliveryBatchId(deliveryBatchEntity.getId());
        entity.setOrderNo(skuBatchCodeTraceDTO.getOrderNo());
        entity.setOutItemId(skuBatchCodeTraceDTO.getSku());
        entity.setItemDesc(skuBatchCodeTraceDTO.getPdName());
        entity.setShortQuantity(0);
        entity.setDriverName(deliveryBatchEntity.getDriverEntity().getName());
        entity.setDriverPhone(deliveryBatchEntity.getDriverEntity().getPhone());
        entity.setPathCode(skuBatchCodeTraceDTO.getPathCode());
        entity.setPathSequence(skuBatchCodeTraceDTO.getPathSequence());
        entity.setOutContactId(skuBatchCodeTraceDTO.getContactId());
        entity.setOutClientId(skuBatchCodeTraceDTO.getMerchantId());
        return entity;
    }

}
