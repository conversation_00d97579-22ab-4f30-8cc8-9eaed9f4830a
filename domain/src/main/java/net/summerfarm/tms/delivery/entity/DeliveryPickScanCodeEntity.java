package net.summerfarm.tms.delivery.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Description: 拣货扫码<br/>
 * date: 2024/8/14 15:07<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPickScanCodeEntity {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 拣货任务ID
     */
    private Long pickId;

    /**
     * 配送批次id
     */
    private Long deliveryBatchId;

    /**
     * 点位ID
     */
    private Long siteId;

    /**
     * 外部条目id
     */
    private String outItemId;

    /**
     * 货品描述
     */
    private String itemDesc;

    /**
     * 唯一码
     */
    private String onlyCode;
}
