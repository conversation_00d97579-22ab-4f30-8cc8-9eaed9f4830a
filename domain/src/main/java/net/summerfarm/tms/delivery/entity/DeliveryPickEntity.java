package net.summerfarm.tms.delivery.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeliveryPickEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 配送批次id
     */
    private Long deliveryBatchId;

    /**
     * 点位ID
     */
    private Long siteId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 外部条目id
     */
    private String outItemId;

    /**
     * 货品描述
     */
    private String itemDesc;

    /**
     * 拣货粒度,eg:品牌名
     */
    private String particle;

    /**
     * 拣货类型：0默认， 1独立拣货(按品牌捡货)
     */
    private Integer type;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 拣货数量
     */
    private Integer pickQuantity;

    /**
     * 缺货数量
     */
    private Integer shortQuantity;

    /**
     * 拦截数量
     */
    private Integer interceptQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 状态 20 待捡货 25 捡货完成 26 捡货异常
     */
    private Integer status;

    /**
     * 捡货 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 温区
     */
    private Integer temperature;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 配送点位
     */
    private Long deliverySiteId;

    /**
     * 规格
     */
    private String specification;
    /**
     * 商品图片
     */
    private String skuPic;
    /**
     * 类型 0 自营 1 代仓
     */
    private Integer skuType;

    /**
     * 大客户名称
     */
    private String nameRemakes;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 商品类型，0:普通，1:水果
     */
    private Integer categoryType;
    /**
     * 加工标识 0非加工商品 1加工商品
     */
    private Integer processFlag;

    /**
     * 加工后商品数量
     */
    private Integer processQuantity;

    /**
     * 加工后重量
     */
    private BigDecimal processWeight;

    /**
     * 加工转化比(加工前数量/加工后数量)
     */
    private BigDecimal processConversionRatio;

    /**
     * 加工拣货数量
     */
    private Integer processPickQuantity;

    /**
     * 加工缺货数量
     */
    private Integer processShortQuantity;

    /**
     * 加工后单位
     */
    private String processUnit;

    /**
     * 存储条件
     * 0, "未分类"
     * 1, "冷冻"
     * 2, "冷藏"
     * 3, "常温"
     * 4, "顶汇大流通"
     */
    private Integer storageArea;

    /**
     * 条码信息
     */
    private List<String> barcodes;


    /**
     * 包装类型：0单品 1包裹
     */
    private Integer packType;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 扫码数量
     */
    private Integer scanCount;

    public DeliveryPickEntity interceptDeliveryPick() {
        DeliveryPickEntity deliveryPickEntity = new DeliveryPickEntity();
        deliveryPickEntity.setId(this.getId());
        deliveryPickEntity.setInterceptQuantity(this.getInterceptQuantity());
        return deliveryPickEntity;
    }
}
