package net.summerfarm.tms.delivery.helper;

import lombok.Data;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;

@Data
public class DeliverySiteWithDistance {
    /**
     * 站点
     */
    private DeliverySiteEntity deliverySiteEntity;

    /**
     * 距离
     */
    private double distance;

    public DeliverySiteWithDistance(DeliverySiteEntity deliverySiteEntity, double distance) {
        this.deliverySiteEntity = deliverySiteEntity;
        this.distance = distance;
    }

    public DeliverySiteEntity getDeliverySite() {
        return deliverySiteEntity;
    }

    public double getDistance() {
        return distance;
    }
}