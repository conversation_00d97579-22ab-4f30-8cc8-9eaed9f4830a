package net.summerfarm.tms.delivery.entity;

import lombok.Data;

import java.time.LocalDate;

/**
 * Description: <br/>
 * date: 2023/5/17 16:44<br/>
 *
 * <AUTHOR> />
 */
@Data
public class BoardEntity {
    /**
     * 司机Id
     */
    private Integer deliveryCarId;
    /**
     * 司机姓名
     */
    private String driver;
    /**
     * 车牌号
     */
    private String carNumber;
    /**
     * 配送路径
     */
    private String path;
    /**
     * 已配送的总店铺数
     */
    private Integer deliveredCount;
    /**
     * 未配送的店铺数
     */
    private Integer unDeliveredCount;
    /**
     * 司机配送的总店铺数
     */
    private Integer deliveryAmount;
    /**
     * 已配送完成订单中缺货的店铺数
     */
    private Integer shortCount;
    /**
     * 对应的仓库编码
     */
    private String storeNo;
    /**
     * 完成配送时间
     */
    private LocalDate deliveryTime;
    /**
     * 超距离签收店铺数
     */
    private int outDistanceCount;

    /**
     * 车辆id
     */
    private Long tmsCarId;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 司机电话
     */
    private String phone;
}
