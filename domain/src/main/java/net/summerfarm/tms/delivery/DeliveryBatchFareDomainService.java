package net.summerfarm.tms.delivery;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.delivery.entity.DeliveryBatchFareEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Description:配送批次费用项 domain服务
 * date: 2023/5/30 14:21
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryBatchFareDomainService {

    private final DeliveryBatchFareRepository deliveryBatchFareRepository;

    public void deliveryBatchFareSave(List<DeliveryBatchFareEntity> deliveryBatchFareEntityList, Long deliveryBatchId,String operator) {
        if (deliveryBatchFareEntityList == null){
            return;
        }
        for (DeliveryBatchFareEntity deliveryBatchFareEntity : deliveryBatchFareEntityList) {
            deliveryBatchFareEntity.setDeliveryBatchId(deliveryBatchId);
            deliveryBatchFareEntity.setCreator(operator);
        }
        deliveryBatchFareRepository.saveOrUpdate(deliveryBatchFareEntityList, deliveryBatchId);
    }
}
