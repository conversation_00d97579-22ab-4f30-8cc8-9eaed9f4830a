package net.summerfarm.tms.delivery.entity.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @Description
 * @Date 2025/2/25 15:19
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOrderRelatedQuery {

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 外部单据类型
     */
    private Integer outOrderSource;

}
