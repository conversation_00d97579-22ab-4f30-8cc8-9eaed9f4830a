package net.summerfarm.tms.delivery.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.tms.after.entity.ExpenseEntity;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistFlowVO;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.enums.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/10/18 14:49<br/>
 *
 * <AUTHOR> />
 */
@Data
@NoArgsConstructor
public class DeliverySiteEntity {
    private Long id;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    private Long deliveryBatchId;
    private SiteEntity siteEntity;

    /**
     * @see DeliverySiteTypeEnum
     */
    private Integer type;

    private Integer sequence;

    private DeliverySiteStatusEnum status;

    /**
     * 计划到站时间
     */
    private LocalDateTime planArriveTime;

    /**
     * 计划出发时间
     */
    private LocalDateTime planOutTime;

    /**
     *出发时间和要求时间的差值，单位/分钟
     */
    private Integer signOutDiffMinute;

    /**
     * 实际到达时间
     */
    private LocalDateTime signInTime;

    /**
     * 实际到达地点
     */
    private String signInPoi;


    /**
     * 实际打卡和预计到达时间的差值
     */
    private Integer signInDiffMinute;

    /**
     * 实际打卡地和点位的差值
     */
    private BigDecimal signInDiffKm;

    /**
     * 签到打卡照片(门店抬头)
     */
    private String signInPics;

    /**
     * 签到备注
     */
    private String signInRemark;

    /**
     * 超出距离备注
     */
    private String outReason;

    /**
     * 是否超出距离
     */
    private Integer outDistance;

    /**
     * 出发打卡时间
     */
    private LocalDateTime signOutTime;

    /**
     * 实际出发打卡点和点位的差值 km
     */
    private BigDecimal signOutDiffKm;

    /**
     * 出发打卡拍照
     */
    private String signOutPics;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;

    /**
     * 封签照片
     */
    private String sealPics;

    /**
     * 出发打卡备注
     */
    private String signOutRemark;

    /**
     * 出发温度
     */
    private BigDecimal signOutTemperature;

    /**
     * 0正常 2部分拦截 3全部拦截
     */
    private DeliverySiteInterceptStateEnum deliverySiteInterceptState;

    /**
     * 到仓距离
     */
    private BigDecimal distance;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 外部客户号
     */
    private String outerClientId;

    /**
     * 当前点位的有效的委托单
     */
    private List<DistOrderEntity> siteDistOrders;

    /**
     * 配送类型
     */
    private DistTypeEnum deliveryType;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;
    /**
     * 总重量
     */
    private BigDecimal totalWeight;
    /**
     * 总价格
     */
    private BigDecimal totalPrice;
    /**
     * 精准送
     */
    private String timeFrame;

    /**
     * 站点名称
     */
    private String siteName;
    /**
     * 站点ID
     */
    private Long siteId;
    /**
     * 点位类型
     */
    private Integer siteType;
    /**
     * 签到距离
     */
    private BigDecimal signInDistance;

    /**
     * 是否正常签收，0：正常，1：不正常
     */
    private Integer signInStatus;

    /**
     * 签发状态 0：正常，1：不正常
     */
    private Integer signOutStatus;
    /**
     * 拣货列表
     */
    private List<DeliveryPickEntity> deliveryPickEntityList;
    /**
     * sku配送列表
     */
    private List<DeliverySiteItemEntity> deliverySiteItemEntityList;
    /**
     * 状态，0：正常，1：取消，2：部分拦截，3：全部拦截 4：拦截关闭
     */
    private Integer interceptState;
    /**
     * 签到异常类型
     */
    private String signInErrType;
    /**
     * 出发异常类型
     */
    private String signOutErrType;
    /**
     * 签到点位地址
     */
    private String signInAddress;
    /**
     * 实际出发点位
     */
    private String signOutPoi;
    /**
     * 实际出发点位地址
     */
    private String signOutAddress;
    /**
     * 配送方式 0正常配送 1专车配送
     */
    private Integer sendWay;
    /**
     * 批次信息
     */
    private DeliveryBatchEntity deliveryBatchEntity;
    /**
     * 地址状态
     */
    private Integer contactStatus;
    /**
     * 报销单
     */
    private ExpenseEntity expenseEntity;

    /**
     * 智能排线顺序
     */
    private Integer intelligenceSequence;

    /**
     * 配送照片签收面单
     */
    private String signInSignPic;

    /**
     * 配送照片货物照片
     */
    private String signInProductPic;

    /**
     * 超区异常打卡原因类型
     */
    private String outReasonType;

    /**
     * 超区异常打卡图片
     */
    private String outPic;

    /**
     * 外部品牌号
     */
    private String outerBrandId;

    /**
     * 外部品牌名
     */
    private String outerBrandName;

    /**
     * 配送存储条件
     */
    private String temperatureConditions;
    

    /**
     * 配送备注
     */
    private String sendRemark;

    /**
     *是否需要扫码 false 不需要 true 需要
     */
    private Boolean scanCodeFlag;
    /**
     * 到店打卡标识 true 需要打卡 false不需要打卡
     */
    private Boolean checkinPunchFlag;
    /**
     * 拣货缺货标识 true 缺货 false 不缺货
     */
    private Boolean pickLackFlag;
    /**
     * 打卡范围 km
     */
    private BigDecimal punchRange;
    /**
     * 到店打卡信息
     */
    private DeliverySiteCheckinPunchEntity checkinPunchEntity;

    /**
     * 蚁群算法顺序
     */
    private Integer antSequence;

    /**
     * 订单来源
     */
    private String orderSourceInfo;

    /**
     * 当前点位的配送单
     */
    private List<DeliveryOrderEntity> deliverySiteHaveDeliveryOrderEntities;

    /**
     * 扫码信息
     */
    private List<DeliverySiteItemCodeEntity> deliverySiteItemCodeEntityList;

    /**
     * 冷藏照片
     */
    private String refrigeratePics;
    /**
     * 冷冻照片
     */
    private String freezePics;
    /**
     * 区域id
     */
    private Integer adCodeMsgId;
    /**
     * 设置该点位配送货物的体积和重量和金额和精准送和配送类型和拦截信息
     */
    public void siteDeliveryCalculate() {
        BigDecimal totalVolume = BigDecimal.ZERO;
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalPrice = BigDecimal.ZERO;

        //配送类型
        HashSet<Integer> typeSet = new HashSet<>();
        //精准送
        HashSet<String> timeFrameSet = new HashSet<>();

        for (DistOrderEntity distOrderEntity : this.siteDistOrders) {
            List<DistItemVO> distItems = distOrderEntity.getDistItems();
            DistFlowVO distFlowVO = distOrderEntity.getDistFlowVO();
            typeSet.add(distFlowVO.getType());
            timeFrameSet.add(distFlowVO.getTimeFrame());

            for (DistItemVO distItem : distItems) {
                BigDecimal quantity = new BigDecimal(distItem.getQuantity());
                BigDecimal volume = distItem.getVolume();
                BigDecimal weight = distItem.getWeight();
                BigDecimal outItemPrice = distItem.getOutItemPrice();

                totalPrice = totalPrice.add(outItemPrice.multiply(quantity).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                totalVolume = totalVolume.add(volume.multiply(quantity).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                totalWeight = totalWeight.add(weight.multiply(quantity).setScale(2, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        //点位配送的价格、体积、重量
        this.totalPrice = totalPrice;
        this.totalVolume = totalVolume;
        this.totalWeight = totalWeight;

        //配送类型
        if (typeSet.size() == 0) {
            this.deliveryType = DistTypeEnum.DELIVERY;
        }else if (typeSet.size() > 1) {
            this.deliveryType = DistTypeEnum.DELIVERY_AND_RECYCLE;
        }else {
            this.deliveryType = DistTypeEnum.getDistTypeByCode(new ArrayList<>(typeSet).get(0));
        }

        //精准送
        StringJoiner timeFrameJoiner = new StringJoiner(",");
        if (timeFrameSet.size() > 0) {
            for (String timeFrame : timeFrameSet) {
                if(StringUtils.isNotBlank(timeFrame)){
                    timeFrameJoiner.add(timeFrame);
                }
            }
            this.timeFrame = timeFrameJoiner.toString();
        }
    }

    public boolean begin(){
        return Objects.equals(this.type, DeliverySiteTypeEnum.begin.getCode());
    }

    public boolean end(){
        return Objects.equals(this.type, DeliverySiteTypeEnum.end.getCode());
    }

    public boolean isSiteItemRecycleAbnormal() {
        if (CollectionUtils.isEmpty(this.deliverySiteItemEntityList)){
            return false;
        }
        List<DeliverySiteItemEntity> recycleAbnormalSkus = this.querySiteItemRecycleAbnormalItems();
        return !CollectionUtils.isEmpty(recycleAbnormalSkus);
    }

    public List<DeliverySiteItemEntity> querySiteItemRecycleAbnormalItems() {
        if (CollectionUtils.isEmpty(this.deliverySiteItemEntityList)){
            return Collections.emptyList();
        }
        return this.deliverySiteItemEntityList.stream().filter(e -> Objects.equals(e.getType(), DeliverySiteItemTypeEnum.RECYCLE.getCode()))
                .filter(e -> Objects.equals(e.getStatus(), DeliverySiteItemEnums.Status.ABNORMAL.getValue()))
                .collect(Collectors.toList());
    }

    public DeliverySiteEntity(Long id,SiteEntity siteEntity, Integer antSequence) {
        this.id = id;
        this.siteEntity = siteEntity;
        this.antSequence = antSequence;
    }

    /**
     * 获取点位订单来源类型
     * @return
     */
    public String findOrderSource(){
        if(!CollectionUtils.isEmpty(this.siteDistOrders)){
            Set<DistOrderSourceEnum> sourceEnums = this.siteDistOrders.stream().map(DistOrderEntity::getSource).collect(Collectors.toSet());
            Set<String> orderSourceInfoSet = new HashSet<>();
            for (DistOrderSourceEnum sourceEnum : sourceEnums) {
                if(DistOrderSourceEnum.getXmSldOrderType().contains(sourceEnum)){
                    orderSourceInfoSet.add(OrderSourceEnum.SLD.getDesc());
                    continue;
                }
                if(DistOrderSourceEnum.getXmOrderTypeEnum().contains(sourceEnum)){
                    orderSourceInfoSet.add(OrderSourceEnum.XM.getDesc());
                    continue;
                }
                if(DistOrderSourceEnum.getSaasOrderTypeEnum().contains(sourceEnum)){
                    orderSourceInfoSet.add(OrderSourceEnum.SAAS.getDesc());
                    continue;
                }
                if(Objects.equals(DistOrderSourceEnum.OUTER_CITY,sourceEnum)){
                    orderSourceInfoSet.add(OrderSourceEnum.OUTER.getDesc());
                    continue;
                }
            }
            return String.join(",",orderSourceInfoSet);
        }

        return "";
    }

    public DeliverySiteEntity finishPick(Long deliveryBatchId,LocalDateTime signInTime){
        LocalDateTime now = LocalDateTime.now();

        this.setStatus(DeliverySiteStatusEnum.FINISH_PICK);
        this.setSignOutTime(now);
        this.setDeliveryBatchId(deliveryBatchId);
        if (signInTime == null) {
            this.setSignInTime(now);
        }

        return this;
    }

    public String buildUk() {
        return this.siteId + "#" + this.deliveryBatchId;
    }

    public DeliverySiteEntity interceptDeliverySiteEntity(){
        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();

        deliverySiteEntity.setId(this.getId());
        deliverySiteEntity.setInterceptState(this.getInterceptState());

        return deliverySiteEntity;
    }
}

