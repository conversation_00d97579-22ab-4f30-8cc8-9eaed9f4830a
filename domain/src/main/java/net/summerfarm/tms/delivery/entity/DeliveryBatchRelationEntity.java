package net.summerfarm.tms.delivery.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.enums.DeliveryBatchEnums;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Description:配送批次关联关系
 * date: 2023/8/8 10:41
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DeliveryBatchRelationEntity {

    /**
     * 主键
     */
    private Long id;
    /**
     * 真正关联配送批次ID
     */
    private Long deliveryBatchId;
    /**
     * 配送批次ID
     */
    private Long batchId;
    /**
     * 关联配送批次ID
     */
    private Long relateBatchId;
    /**
     * @see DeliveryBatchTypeEnum
     * 调度单类型
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private DeliveryBatchTypeEnum type;
    /**
     * 班次 0正常 1加班
     */
    private DeliveryBatchEnums.Classes classes;
    /**
     * 线路名称
     */
    private String pathName;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 车牌号
     */
    private CarEntity carEntity;

    public DeliveryBatchRelationEntity(Long batchId, Long relateBatchId, String creator){
        this.batchId = batchId;
        this.relateBatchId = relateBatchId;
        this.creator = creator;
    }

    public Long buildUk(){
        if (this.batchId == null || this.relateBatchId == null){
            return -1L;
        }
        return this.batchId ^ this.relateBatchId;
    }

    public Long findRelateBatchId(Long batchId){
        if (batchId == null){
            return null;
        }
        if (Objects.equals(batchId, this.batchId)){
            return this.relateBatchId;
        }
        if (Objects.equals(batchId, this.relateBatchId)){
            return this.batchId;
        }
        return null;
    }

    public void resetRelateBatchId(Long batchId){
        this.deliveryBatchId = this.findRelateBatchId(batchId);
    }

}
