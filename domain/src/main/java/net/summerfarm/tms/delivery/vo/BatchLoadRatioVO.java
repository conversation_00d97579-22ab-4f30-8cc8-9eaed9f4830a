package net.summerfarm.tms.delivery.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Description:批次装载率值对象
 * date: 2023/8/9 16:30
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class BatchLoadRatioVO implements Serializable {

    private static final long serialVersionUID = 3387760792492387061L;

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 批次装载明细统计
     */
    private BatchDetailLoadStaticVO batchDetailLoadStaticVO;

    /**
     * 批次车辆装载统计
     */
    private BatchCarLoadStaticVO batchCarLoadStaticVO;

    /**
     * 体积装载率
     */
    private BigDecimal volumeLoadRatio;

    /**
     * 重量装载率
     */
    private BigDecimal weightLoadRatio;

    /**
     * 件数装载率
     */
    private BigDecimal quantityLoadRatio;

    public BatchLoadRatioVO(Long batchId, BatchDetailLoadStaticVO batchDetailLoadStaticVO, BatchCarLoadStaticVO batchCarLoadStaticVO){
        this.batchId = batchId;
        this.batchDetailLoadStaticVO = batchDetailLoadStaticVO;
        this.batchCarLoadStaticVO = batchCarLoadStaticVO;
    }

    /**
     * 计算装载率信息
     */
    public void calculate(){
        if (this.batchDetailLoadStaticVO == null || this.batchCarLoadStaticVO == null){
            return;
        }
        if (batchCarLoadStaticVO.getLoadVolume() != null && batchCarLoadStaticVO.getLoadVolume().compareTo(BigDecimal.ZERO) != 0){
            this.volumeLoadRatio = batchDetailLoadStaticVO.getLoadVolume().divide(batchCarLoadStaticVO.getLoadVolume(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        BigDecimal loadWeight = batchCarLoadStaticVO.getLoadWeight();
        if (loadWeight != null && loadWeight.compareTo(BigDecimal.ZERO) != 0){
            loadWeight = loadWeight.multiply(BigDecimal.valueOf(1000));
            this.weightLoadRatio = batchDetailLoadStaticVO.getLoadWeight().divide(loadWeight, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        if (batchCarLoadStaticVO != null && batchCarLoadStaticVO.getLoadQuantity() != 0){
            this.quantityLoadRatio = new BigDecimal(batchDetailLoadStaticVO.getLoadQuantity()).divide(new BigDecimal(batchCarLoadStaticVO.getLoadQuantity()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }
    }
}
