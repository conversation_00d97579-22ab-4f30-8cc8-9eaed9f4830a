package net.summerfarm.tms.delivery.entity;

import lombok.Data;
import net.summerfarm.tms.enums.DeliveryBatchEnums;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description:配送批次费用明细项
 * date: 2023/5/30 11:56
 *
 * <AUTHOR>
 */
@Data
public class DeliveryBatchFareEntity {

    /**
     * 运费ID
     */
    private Long id;

    /**
     * 配送批次ID
     */
    private Long deliveryBatchId;

    /**
     * 费用类型，10：喜茶费用，20：益禾堂费用，30：美团费用，40：大客户费用，50：调拨费用，60：采购费用
     */
    private DeliveryBatchEnums.FareType fareType;

    /**
     * 费用金额
     */
    private BigDecimal amount;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
