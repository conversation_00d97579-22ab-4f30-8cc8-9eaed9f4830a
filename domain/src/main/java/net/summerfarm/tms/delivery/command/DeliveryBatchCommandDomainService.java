package net.summerfarm.tms.delivery.command;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.config.TmsNacosConfig;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliverySiteRepository;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.delivery.intelligent.IntelligentDeliverySiteSequenceService;
import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.util.AntColonyUtil;
import net.summerfarm.tms.util.PoiInfo;
import net.summerfarm.tms.util.VRPSolverUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.gaode.support.enums.XMDriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 批次写操作<br/>
 * date: 2024/1/11 16:45<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class DeliveryBatchCommandDomainService {

    @Resource
    private DeliverySiteRepository deliverySiteRepository;
    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;
    @Resource
    private TmsNacosConfig tmsNacosConfig;
    @Resource
    private VRPSolverUtil vrpsolverUtil;
    @Resource
    private IntelligentDeliverySiteSequenceService intelligentDeliverySiteSequenceService;

    /**
     * 蚁群算法计算路径距离
     *
     * @param batchId 批次ID
     * @param siteIdList 点位ID集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void antAlgorithmCalcDistance(Long batchId, List<Long> siteIdList) {
        if(batchId == null || CollectionUtils.isEmpty(siteIdList)){
            return;
        }
        //查询批次对应的点位信息
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryListWithSite(DeliverySiteQuery.builder().batchId(batchId).build());
        if(CollectionUtils.isEmpty(deliverySiteEntities)){
            return;
        }
        List<DeliverySiteEntity> siteNeedAntSequences = deliverySiteEntities.stream().filter(deliverySite -> siteIdList.contains(deliverySite.getSiteId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(siteNeedAntSequences)){
            return;
        }
        for (DeliverySiteEntity siteNeedAntSequence : siteNeedAntSequences) {
            //城配仓顺序应该为零
            if(siteNeedAntSequence.getSiteEntity() != null && Objects.equals(siteNeedAntSequence.getSiteEntity().getType(), SiteTypeEnum.store.getCode())){
                siteNeedAntSequence.setSequence(0);
            }
        }
        //按照顺序排序
        siteNeedAntSequences.sort(Comparator.comparing(DeliverySiteEntity::getSequence));
        //蚁群算法获得排序信息
        antSequenceHandle(siteNeedAntSequences);
        //更新顺序信息
        List<DeliverySiteEntity> deliverySiteAntSequences = siteNeedAntSequences.stream()
                .map(deliverySite -> new DeliverySiteEntity(deliverySite.getId(),deliverySite.getSiteEntity(), deliverySite.getAntSequence()))
                .sorted(Comparator.comparing(DeliverySiteEntity::getId)).collect(Collectors.toList());
        for (DeliverySiteEntity deliverySiteAntSequence : deliverySiteAntSequences) {
            deliverySiteRepository.update(deliverySiteAntSequence);
        }
        //更新批次蚁群距离和路段信息
        deliverySiteAntSequences.sort(Comparator.comparing(DeliverySiteEntity::getAntSequence));
        //计算路段信息
        List<WaypointsInput> waypointsInputList = deliverySiteAntSequences.stream().map(deliverySite -> new WaypointsInput(deliverySite.getSiteEntity().getId(), deliverySite.getSiteEntity().getPoi())).collect(Collectors.toList());
        deliveryBatchRepository.calculateDistance(waypointsInputList,batchId, DeliverySectionEnums.Type.ANT_ALGORITHM, null);
    }

    /**
     * 蚁群算法计算配送点位顺序
     *
     * @param siteNeedAntSequences 需要计算的点位顺序集合
     */
    private void antSequenceHandle(List<DeliverySiteEntity> siteNeedAntSequences) {
        //设置POI信息
        List<PoiInfo> poiInfos = new ArrayList<>();
        for (int i = 0; i < siteNeedAntSequences.size(); i++) {
            DeliverySiteEntity deliverySiteEntity = siteNeedAntSequences.get(i);
            SiteEntity siteEntity = deliverySiteEntity.getSiteEntity();
            if(siteEntity == null){
                throw new BizException("蚁群算法计算配送点位顺序点位信息为空");
            }
            poiInfos.add(new PoiInfo(siteEntity.getId(), siteEntity.getPoi(), siteEntity.getName(),i));
        }
        //蚁群算法获取配送顺序
        Map<Long, Integer> map = AntColonyUtil.antColonyPathSequence(poiInfos);

        for (DeliverySiteEntity siteNeedAntSequence : siteNeedAntSequences) {
            Integer seq = map.get(siteNeedAntSequence.getSiteId());
            if(seq != null){
                siteNeedAntSequence.setAntSequence(seq-1);
            }
        }
    }

    /**
     * 路径规划计算配送点位顺序
     *
     * @param batchId 批次ID
     * @param siteIdList 点位ID集合
     */
    public void orToolsCalcDistance(Long batchId, List<Long> siteIdList) {
        if(batchId == null || CollectionUtils.isEmpty(siteIdList)){
            return;
        }
        //查询批次对应的点位信息
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryListWithSite(DeliverySiteQuery.builder().batchId(batchId).build());
        if(CollectionUtils.isEmpty(deliverySiteEntities)){
            return;
        }
        List<DeliverySiteEntity> siteNeedOrToolsSequences = deliverySiteEntities.stream().filter(deliverySite -> siteIdList.contains(deliverySite.getSiteId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(siteNeedOrToolsSequences)){
            return;
        }

        SiteEntity beginSiteEntity = null;
        for (DeliverySiteEntity siteNeedAntSequence : siteNeedOrToolsSequences) {
            //城配仓顺序应该为零
            if(siteNeedAntSequence.getSiteEntity() != null && Objects.equals(siteNeedAntSequence.getSiteEntity().getType(), SiteTypeEnum.store.getCode())){
                siteNeedAntSequence.setSequence(0);
                beginSiteEntity = siteNeedAntSequence.getSiteEntity();
                break;
            }
        }
        // 按照顺序排序
        siteNeedOrToolsSequences.sort(Comparator.comparing(DeliverySiteEntity::getSequence));
        // 算法获得排序信息
        List<String> poiList = new ArrayList<>();
        List<Long> deliverySiteIdList = new ArrayList<>();
        for (DeliverySiteEntity deliverySite : siteNeedOrToolsSequences) {
            deliverySiteIdList.add(deliverySite.getId());
            poiList.add(deliverySite.getSiteEntity().getPoi());
        }
        List<DeliverySiteEntity> needUpdateDeliverySitesSequenceList = new ArrayList<>();

        Map<Long, DeliverySiteEntity> id2DeliverySiteMap = siteNeedOrToolsSequences.stream().collect(Collectors.toMap(DeliverySiteEntity::getId, Function.identity()));
        List<Long> deliverySiteIdSequenceList = vrpsolverUtil.solve(convertToDoubleArray(poiList), deliverySiteIdList, deliverySiteIdList.get(0));
        for (int i = 0; i < deliverySiteIdSequenceList.size(); i++) {
            DeliverySiteEntity deliverySiteEntity = id2DeliverySiteMap.get(deliverySiteIdSequenceList.get(i));
            if(deliverySiteEntity == null){
                continue;
            }
            deliverySiteEntity.setAntSequence(i);
            needUpdateDeliverySitesSequenceList.add(deliverySiteEntity);
        }

        // 批量更新顺序信息
        deliverySiteRepository.batchUpdateAntSequences(needUpdateDeliverySitesSequenceList);

        // 更新批次蚁群距离和路段信息
        needUpdateDeliverySitesSequenceList.sort(Comparator.comparing(DeliverySiteEntity::getAntSequence));

        // 计算路段信息
        List<WaypointsInput> waypointsInputList = needUpdateDeliverySitesSequenceList.stream().map(deliverySite -> new WaypointsInput(deliverySite.getSiteEntity().getId(), deliverySite.getSiteEntity().getPoi())).collect(Collectors.toList());

        if(beginSiteEntity != null && tmsNacosConfig.queryGaodeMinDistanceStoreNos().contains(beginSiteEntity.getOutBusinessNo())){
            deliveryBatchRepository.calculateDistance(waypointsInputList,batchId, DeliverySectionEnums.Type.ANT_ALGORITHM, XMDriverRoutPlanStrategyEnum.COMBINATION_STRATEGY);
        }else{
            deliveryBatchRepository.calculateDistance(waypointsInputList,batchId, DeliverySectionEnums.Type.ANT_ALGORITHM, null);
        }
    }

    private List<double[]> convertToDoubleArray(List<String> poi) {
        return poi.stream()
                .map(s -> {
                    String[] parts = s.split(",");
                    return new double[]{
                            Double.parseDouble(parts[0].trim()), // 经度
                            Double.parseDouble(parts[1].trim())  // 纬度
                    };
                })
                .collect(Collectors.toList());
    }

    /**
     * 贪心算法计算配送点位顺序
     *
     * @param deliveryBatchIds 批次ID集合
     */
    public void greedyCalcDistance(List<Long> deliveryBatchIds) {
        log.info("greedyCalcDistance:{}", JSON.toJSONString(deliveryBatchIds));
        if(CollectionUtils.isEmpty(deliveryBatchIds)){
            return;
        }

        List<String> storeNos = tmsNacosConfig.queryOrtoolsStoreNos();

        for (Long deliveryBatchId : deliveryBatchIds) {
            //查询批次对应的点位信息
            List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryListWithSite(DeliverySiteQuery.builder().batchId(deliveryBatchId).build());
            if(CollectionUtils.isEmpty(deliverySiteEntities)){
                continue;
            }

            List<DeliverySiteEntity> storeNoDeliverySites = deliverySiteEntities.stream()
                    .filter(e -> e.getSiteEntity() != null)
                    .filter(e -> Objects.equals(e.getSiteEntity().getType(), SiteTypeEnum.store.getCode()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(storeNoDeliverySites)){
                log.info("批次{}没有城配仓", deliveryBatchId);
                continue;
            }

            SiteEntity siteEntity = storeNoDeliverySites.get(0).getSiteEntity();
            if(siteEntity != null && !storeNos.contains(siteEntity.getOutBusinessNo())){
                log.info("非灰度城配仓:{},无需处理", siteEntity.getOutBusinessNo());
                continue;
            }

            List<DeliverySiteEntity> merchantDeliverySites = deliverySiteEntities.stream()
                    .filter(e -> e.getSiteEntity() != null)
                    .filter(e -> !Objects.equals(e.getSiteEntity().getType(), SiteTypeEnum.store.getCode()))
                    .collect(Collectors.toList());

            DeliverySiteEntity storeNoDeliverySite = storeNoDeliverySites.get(0);

            List<DeliverySiteEntity> deliverySiteSortList = intelligentDeliverySiteSequenceService.greedyAlgorithm(storeNoDeliverySite, merchantDeliverySites);
            if(CollectionUtils.isEmpty(deliverySiteSortList)){
                log.info("批次{}没有配送点", deliveryBatchId);
                continue;
            }
            List<WaypointsInput> allWaypointsInputs = new ArrayList<>();
            WaypointsInput storeWaypointsInput = new WaypointsInput();
            storeWaypointsInput.setPoi(storeNoDeliverySite.getSiteEntity().getPoi());
            storeWaypointsInput.setSiteId(storeNoDeliverySite.getSiteEntity().getId());

            allWaypointsInputs.add(storeWaypointsInput);

            List<WaypointsInput> merchantWaypointsInputList = deliverySiteSortList.stream().map(e -> {
                if (e.getSiteEntity() == null) {
                    return null;
                }
                WaypointsInput waypointsInput = new WaypointsInput();
                waypointsInput.setPoi(e.getSiteEntity().getPoi());
                waypointsInput.setSiteId(e.getSiteEntity().getId());

                return waypointsInput;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            allWaypointsInputs.addAll(merchantWaypointsInputList);
            deliveryBatchRepository.calculateDistance(allWaypointsInputs,deliveryBatchId, DeliverySectionEnums.Type.GREEDY_ALGORITHM, null);
        }
    }
}
