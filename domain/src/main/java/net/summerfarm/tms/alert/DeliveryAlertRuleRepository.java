package net.summerfarm.tms.alert;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleItemVO;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleEntity;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleGroupEntity;
import net.summerfarm.tms.enums.DeliveryAlertEnums;
import net.summerfarm.tms.query.alert.DeliveryAlertRuleQuery;

import java.util.List;

/**
 * Description:配送提醒仓库接口
 * date: 2023/3/21 15:09
 *
 * <AUTHOR>
 */
public interface DeliveryAlertRuleRepository {

    /**
     * 查询集合
     * @param deliveryAlertRuleQuery 配送提醒规则查询
     * @return 结果
     */
    List<DeliveryAlertRuleEntity> queryList(DeliveryAlertRuleQuery deliveryAlertRuleQuery);

    /**
     * 查询集合
     * @param deliveryAlertRuleQuery 配送提醒规则查询
     * @return 结果
     */
    List<DeliveryAlertRuleEntity> queryListWithItems(DeliveryAlertRuleQuery deliveryAlertRuleQuery);

    /**
     * 分页查询配送提醒特殊规则组-城配仓维度
     * @param deliveryAlertRuleQuery 配送提醒规则查询
     * @return 分页实体
     */
    PageInfo<DeliveryAlertRuleGroupEntity> queryPage(DeliveryAlertRuleQuery deliveryAlertRuleQuery);

    /**
     * 保存配送提醒特殊规则组-城配仓维度
     * 标准 有则覆盖 没有则新增
     * @param deliveryAlertRuleGroupEntity 配送提醒规则组实体
     */
    void saveOrUpdate(DeliveryAlertRuleGroupEntity deliveryAlertRuleGroupEntity);

    /**
     * 删除配送提醒特殊规则组-城配仓维度
     * @param storeNo 配送仓编号
     */
    void remove(Integer storeNo);

    /**
     * 根据UK查询 主表数据
     * @param storeNo 配送仓编号
     * @param channel 渠道
     * @param type 类型
     * @param bizNo 业务编号
     * @return 配送提醒规则项
     */
    DeliveryAlertRuleItemVO queryByUk(Integer storeNo, DeliveryAlertEnums.Channel channel, DeliveryAlertEnums.Type type, String bizNo);

    /**
     * 新增配送提醒特殊规则-规则维度
     * @param deliveryAlertRuleEntity 配送提醒规则实体
     */
    void save(DeliveryAlertRuleEntity deliveryAlertRuleEntity);

    /**
     * 保存配送提醒特殊规则-规则维度
     * 标准 有则覆盖 没有则新增
     * @param deliveryAlertRuleEntity 配送提醒规则实体
     */
    void update(DeliveryAlertRuleEntity deliveryAlertRuleEntity);

    /**
     * 删除配送提醒特殊规则组-规则维度
     * @param ruleId 配送仓编号
     */
    void removeById(Long ruleId);

    /**
     * 根据主键查询主表数据
     * @param id 主键
     * @return 配送提醒规则
     */
    DeliveryAlertRuleEntity query(Long id);

    /**
     * 查询集合 主表数据
     * @param storeNo 配送仓编号
     * @param type 类型
     * @param bizNos 业务编号集合
     * @return 配送提醒规则项
     */
    List<DeliveryAlertRuleItemVO> queryList(Integer storeNo, DeliveryAlertEnums.Type type, List<String> bizNos);
}
