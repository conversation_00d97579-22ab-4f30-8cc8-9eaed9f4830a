package net.summerfarm.tms.alert.entity;

import lombok.Data;

import java.util.List;

/**
 * Description:配送提醒规则组实体
 * date: 2023/3/21 16:42
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertRuleGroupEntity {

    /**
     * 配送仓编号
     */
    private Integer storeNo;

    /**
     * 配送仓名称
     */
    private String storeName;

    /**
     * 配送提醒规则实体集合
     */
    List<DeliveryAlertRuleEntity> deliveryAlertRules;
}
