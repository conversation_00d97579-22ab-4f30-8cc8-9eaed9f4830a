package net.summerfarm.tms.alert;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.alert.entity.DeliveryAlertEntity;
import net.summerfarm.tms.query.alert.DeliveryAlertQuery;

import java.util.List;

/**
 * Description:配送提醒仓库接口
 * date: 2023/3/21 15:09
 *
 * <AUTHOR>
 */
public interface DeliveryAlertRepository {

    /**
     * 分页查询配送提醒特殊规则-规则维度
     * @param deliveryAlertQuery 配送提醒查询
     * @return 分页实体
     */
    PageInfo<DeliveryAlertEntity> queryPage(DeliveryAlertQuery deliveryAlertQuery);

    /**
     * 保存配送提醒记录
     * 标准 有则覆盖 没有则新增
     * @param deliveryAlertEntity 配送提醒规则实体
     */
    void saveOrUpdate(DeliveryAlertEntity deliveryAlertEntity);

    /**
     * 查询配送提醒
     * @param deliveryAlertQuery 查询实体
     * @return 结果实体
     */
    List<DeliveryAlertEntity> queryList(DeliveryAlertQuery deliveryAlertQuery);

    /**
     * 查询配送提醒
     * @param deliveryAlertQuery 查询实体
     * @return 结果实体
     */
    List<DeliveryAlertEntity> queryListWithAreas(DeliveryAlertQuery deliveryAlertQuery);

    /**
     * 查询需要配送提醒的城配仓编号集合
     * @return 城配仓编号集合
     */
    List<Integer> queryAlertStoreList();
}
