package net.summerfarm.tms.alert.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:配送提醒实体
 * date: 2023/3/21 16:42
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 区域
     */
    private String region;

    /**
     * 配送仓编号
     */
    private Integer storeNo;

    /**
     * 配送仓名称
     */
    private Integer storeName;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 区域集合
     */
    List<AreaAdCode> areaAdCodes;

    /**
     * 配送完成时间
     */
    private String completeDeliveryTime;

    /**
     * 状态 0 正常 1 暂停
     */
    private Integer status;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
