package net.summerfarm.tms.alert.entity;

import lombok.Data;
import net.summerfarm.tms.enums.DeliveryAlertEnums;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:配送提醒规则实体
 * date: 2023/3/21 16:42
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertRuleEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 配送仓编号
     */
    private Integer storeNo;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 品牌规则文件oss标识
     */
    private String brandRuleObjectOssKey;

    /**
     * 门店规则文件oss标识
     */
    private String merchantRuleObjectOssKey;

    /**
     * 品牌规则项实体集合
     */
    List<DeliveryAlertRuleItemVO> brandAlertRuleItems;

    /**
     * 门店规则项实体集合
     */
    List<DeliveryAlertRuleItemVO> merchantAlertRuleItems;

    /**
     * 配送提醒规则项实体集合
     */
    List<DeliveryAlertRuleItemVO> deliveryAlertRuleItems;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public void deliveryAlertRuleItemsInit(List<DeliveryAlertRuleItemVO> deliveryAlertRuleItems){
        this.deliveryAlertRuleItems = deliveryAlertRuleItems;
        this.brandAlertRuleItems = deliveryAlertRuleItems.stream().filter(e -> DeliveryAlertEnums.Type.BRAND == e.getType()).collect(Collectors.toList());
        this.merchantAlertRuleItems = deliveryAlertRuleItems.stream().filter(e -> DeliveryAlertEnums.Type.MERCHANT == e.getType()).collect(Collectors.toList());
    }

}
