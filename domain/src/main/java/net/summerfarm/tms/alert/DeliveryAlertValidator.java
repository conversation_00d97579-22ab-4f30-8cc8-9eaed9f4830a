package net.summerfarm.tms.alert;

import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleGroupEntity;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleItemVO;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleEntity;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.query.alert.DeliveryAlertRuleQuery;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:配送提醒校验器
 * date: 2023/3/22 16:40
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DeliveryAlertValidator {

    private final SiteRepository siteRepository;
    private final DeliveryAlertRuleRepository deliveryAlertRuleRepository;

    public void validateDeliveryWarehouse(Integer storeNo){
        SiteEntity siteEntity = siteRepository.query(SiteQuery.builder()
                .outBusinessNo(String.valueOf(storeNo))
                .type(TmsSiteTypeEnum.STORE.getCode()).build());
        if (siteEntity == null){
            throw new TmsRuntimeException("无效配送仓编号");
        }
    }

    public void validateDeliveryRuleGroup(DeliveryAlertRuleGroupEntity deliveryAlertRuleGroupEntity){
        List<DeliveryAlertRuleEntity> deliveryAlertRules = deliveryAlertRuleGroupEntity.getDeliveryAlertRules();
        List<DeliveryAlertRuleItemVO> allDeliveryAlertRuleItems = new ArrayList<>();
        for (DeliveryAlertRuleEntity deliveryAlertRule : deliveryAlertRules) {
            //同一规则组同一渠道同一品牌编号，不可存在重复数据
            this.validateDeliveryRule(deliveryAlertRule);
            allDeliveryAlertRuleItems.addAll(deliveryAlertRule.getDeliveryAlertRuleItems());
        }
        //不同规则组下不可存在相同渠道相同品牌/门店编号
        this.validateDeliveryRule(allDeliveryAlertRuleItems);
    }

    public void validateSimpleDeliveryRule(DeliveryAlertRuleEntity deliveryAlertRule) {
        //同一规则组同一渠道同一品牌编号，不可存在重复数据
        this.validateDeliveryRule(deliveryAlertRule);
        //不同规则组下不可存在相同渠道相同品牌/门店编号
        List<DeliveryAlertRuleEntity> existedDeliveryAlertRules = deliveryAlertRuleRepository.queryListWithItems(DeliveryAlertRuleQuery.builder().storeNo(deliveryAlertRule.getStoreNo()).notEqualId(deliveryAlertRule.getId()).build());
        List<DeliveryAlertRuleItemVO> existedDeliveryAlertRuleItems = existedDeliveryAlertRules.stream().map(DeliveryAlertRuleEntity::getDeliveryAlertRuleItems).flatMap(Collection::stream).collect(Collectors.toList());
        Map<String, DeliveryAlertRuleItemVO> existedDeliveryAlertRuleItemMap = existedDeliveryAlertRuleItems.stream().collect(Collectors.toMap(DeliveryAlertRuleItemVO::buildUk, Function.identity(), (oldDate,newData) -> newData));
        for (DeliveryAlertRuleItemVO deliveryAlertRuleItem : deliveryAlertRule.getDeliveryAlertRuleItems()) {
            if (existedDeliveryAlertRuleItemMap.containsKey(deliveryAlertRuleItem.buildUk())){
                throw new TmsRuntimeException(this.getMsgDataIdentify(deliveryAlertRuleItem) + "存在重复数据");

            }
        }
    }

    private void validateDeliveryRule(DeliveryAlertRuleEntity deliveryAlertRule) {
        //同一规则组同一渠道同一品牌编号，不可存在重复数据
        List<DeliveryAlertRuleItemVO> deliveryAlertRuleItems = deliveryAlertRule.getDeliveryAlertRuleItems();
        if (!CollectionUtils.isEmpty(deliveryAlertRuleItems)){
            this.validateDeliveryRule(deliveryAlertRuleItems);
        }
    }

    public void validateDeliveryRule(List<DeliveryAlertRuleItemVO> deliveryAlertRuleItems){
        //同一渠道同一品牌/门店编号，不可存在重复数据
        Map<String, Long> ukCountMap = deliveryAlertRuleItems.stream().collect(Collectors.groupingBy(DeliveryAlertRuleItemVO::buildUk, Collectors.counting()));
        for (DeliveryAlertRuleItemVO deliveryAlertRuleItem : deliveryAlertRuleItems) {
            this.validateDeliveryRuleItem(deliveryAlertRuleItem);
            if (ukCountMap.get(deliveryAlertRuleItem.buildUk()) > 1){
                throw new TmsRuntimeException(this.getMsgDataIdentify(deliveryAlertRuleItem) + "存在重复数据");
            }
        }
    }

    private String getMsgDataIdentify(DeliveryAlertRuleItemVO deliveryAlertRuleItem) {
        return String.format("%s规则%s渠道业务编号:[%s]", deliveryAlertRuleItem.getChannel().getContent(),
                deliveryAlertRuleItem.getType().getContent(), deliveryAlertRuleItem.getBizNo());
    }

    public void validateDeliveryRuleItem(DeliveryAlertRuleItemVO deliveryAlertRuleItemVO){
        //校验字段合规性
        TmsAssert.notNull(deliveryAlertRuleItemVO.getChannel(), ErrorCodeEnum.PARAM_NOT_NULL, "Channel");
        TmsAssert.notNull(deliveryAlertRuleItemVO.getBizNo(), ErrorCodeEnum.PARAM_NOT_NULL, "BizNo");
        LocalTime beginTime = deliveryAlertRuleItemVO.getBeginTime();
        TmsAssert.notNull(beginTime, ErrorCodeEnum.PARAM_ILLEGAL, "BeginTime");
        LocalTime endTime = deliveryAlertRuleItemVO.getEndTime();
        TmsAssert.notNull(endTime, ErrorCodeEnum.PARAM_ILLEGAL, "EndTime");
        //时间格式 xx:00、xx:30的校验
        TmsAssert.isTrue(beginTime.getMinute() == 0 || beginTime.getMinute() == 30, ErrorCodeEnum.PARAM_ILLEGAL, String.format(this.getMsgDataIdentify(deliveryAlertRuleItemVO) + "开始时间[%s]格式不符合要求", beginTime));
        TmsAssert.isTrue(endTime.getMinute() == 0 || endTime.getMinute() == 30, ErrorCodeEnum.PARAM_ILLEGAL, String.format(this.getMsgDataIdentify(deliveryAlertRuleItemVO) + "结束时间[%s]格式不符合要求", endTime));
        //开始时间早于结束时间
        TmsAssert.isTrue(beginTime.isBefore(endTime), ErrorCodeEnum.PARAM_ILLEGAL, String.format(this.getMsgDataIdentify(deliveryAlertRuleItemVO) + "开始时间[%s]晚于结束时间[%s]", beginTime, endTime));

    }

}
