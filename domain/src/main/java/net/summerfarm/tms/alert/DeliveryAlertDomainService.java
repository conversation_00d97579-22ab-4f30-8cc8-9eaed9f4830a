package net.summerfarm.tms.alert;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleItemVO;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.config.TmsNacosConfig;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistFlowVO;
import net.summerfarm.tms.enums.DeliveryAlertEnums;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.query.alert.CompleteDeliveryTimeQueryParam;
import net.summerfarm.tms.query.alert.LastDeliveryTimeQueryParam;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:配送提醒 domain服务
 * date: 2023/3/21 15:07
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DeliveryAlertDomainService {

    private final DeliveryAlertRuleRepository deliveryAlertRuleRepository;
    private final SiteRepository siteRepository;
    private final DistOrderDomainService distOrderDomainService;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final CompleteDeliveryQueryRepository completeDeliveryQueryRepository;
    private final TmsNacosConfig tmsNacosConfig;

    public String queryDeliveryAlertTimeFrame(DeliverySiteEntity deliverySiteEntity){
        //查询批次信息
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliverySiteEntity.getDeliveryBatchId());
        //查询该点位信息的委托单集合
        List<DistOrderEntity> distOrderEntityList = distOrderDomainService.queryValidDistOrder(Collections.singletonList(deliverySiteEntity.getSiteId()), deliverySiteEntity.getPlanArriveTime().toLocalDate(),deliveryBatchEntity.getBeginSiteId());
        if (CollectionUtils.isEmpty(distOrderEntityList)){
            return null;
        }
        deliverySiteEntity.setSiteDistOrders(distOrderEntityList);
        //判断这批委托单是否需要精准送
        String timeFrames = distOrderEntityList.stream().map(DistOrderEntity::getDistFlowVO).map(DistFlowVO::getTimeFrame).filter(StrUtil::isNotBlank).distinct().collect(Collectors.joining(","));
        if (StrUtil.isNotBlank(timeFrames)){
            return timeFrames;
        }
        DistOrderEntity distOrderEntity = distOrderEntityList.get(0);
        Long beginSiteId = distOrderEntity.getBeginSite().getId();
        //查询配送仓信息
        SiteEntity beginSiteEntity = siteRepository.query(beginSiteId);
        if (beginSiteEntity == null){
            throw new ProviderException("起始城配仓点位信息异常");
        }
        DeliveryAlertEnums.Channel channel = DistOrderSourceEnum.getXmOrderTypeEnum().contains(distOrderEntity.getSource())
                ? DeliveryAlertEnums.Channel.XM_MALL : DeliveryAlertEnums.Channel.SAAS;
        //查询该点位是否配置了门店特殊规则
        Integer storeNo = Integer.valueOf(beginSiteEntity.getOutBusinessNo());
        DeliveryAlertRuleItemVO merchantAlertRuleItemVO = deliveryAlertRuleRepository.queryByUk(storeNo,
                channel, DeliveryAlertEnums.Type.MERCHANT, deliverySiteEntity.getOuterClientId());
        if (merchantAlertRuleItemVO != null){
            return merchantAlertRuleItemVO.findTimeFrame();
        }
        String outTenantId = distOrderEntity.getDistClientVO().getOutTenantId();
        if (StrUtil.isBlank(outTenantId)){
            return null;
        }
        //查询该点位是否配置了品牌特殊规则
        DeliveryAlertRuleItemVO brandAlertRuleItemVO = deliveryAlertRuleRepository.queryByUk(storeNo,
                channel, DeliveryAlertEnums.Type.BRAND, outTenantId);
        if (brandAlertRuleItemVO != null){
            return brandAlertRuleItemVO.findTimeFrame();
        }
        return null;
    }

    /**
     * 设置配送时效
     * @param deliverySiteEntities 点位集合信息
     * @param beginSiteId 城配点位ID
     */
    public void installDeliveryAlertTimeFrameList(List<DeliverySiteEntity> deliverySiteEntities, Long beginSiteId) {
        if(CollectionUtils.isEmpty(deliverySiteEntities)){
            return;
        }
        //查询配送仓信息
        SiteEntity beginSiteEntity = siteRepository.query(beginSiteId);
        if (beginSiteEntity == null){
            throw new ProviderException("起始城配仓点位信息异常");
        }
        List<String> outClientIdList = deliverySiteEntities.stream().map(DeliverySiteEntity::getOuterClientId).collect(Collectors.toList());
        List<String> outTenantIdList = deliverySiteEntities.stream().map(DeliverySiteEntity::getOuterBrandId).collect(Collectors.toList());

        Integer storeNo = NumberUtils.toInt(beginSiteEntity.getOutBusinessNo());
        //查询门店配送时效规则
        List<DeliveryAlertRuleItemVO> merchantAlertRuleEntities = deliveryAlertRuleRepository.queryList(storeNo, DeliveryAlertEnums.Type.MERCHANT, outClientIdList);
        Map<String, String> merchantTimeMap = merchantAlertRuleEntities.stream().collect(Collectors.toMap(DeliveryAlertRuleItemVO::buildUk, DeliveryAlertRuleItemVO::findTimeFrame));
        //查询品牌配送时效规则
        List<DeliveryAlertRuleItemVO> brandAlertRuleEntities = deliveryAlertRuleRepository.queryList(storeNo, DeliveryAlertEnums.Type.BRAND, outTenantIdList);
        Map<String, String> brandTimeMap = brandAlertRuleEntities.stream().collect(Collectors.toMap(DeliveryAlertRuleItemVO::buildUk, DeliveryAlertRuleItemVO::findTimeFrame));

        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            List<DistOrderEntity> distOrderEntityList = deliverySiteEntity.getSiteDistOrders();
            if(CollectionUtils.isEmpty(distOrderEntityList)){
                continue;
            }
            //判断这批委托单是否需要精准送
            String timeFrames = distOrderEntityList.stream().map(DistOrderEntity::getDistFlowVO).map(DistFlowVO::getTimeFrame).filter(StrUtil::isNotBlank).distinct().collect(Collectors.joining(","));
            if (StrUtil.isNotBlank(timeFrames)){
                deliverySiteEntity.setTimeFrame(timeFrames);
                continue;
            }

            DeliveryAlertEnums.Channel channel = DistOrderSourceEnum.getXmOrderTypeEnum().contains(distOrderEntityList.get(0).getSource())
                    ? DeliveryAlertEnums.Channel.XM_MALL : DeliveryAlertEnums.Channel.SAAS;

            StringJoiner merchantUkSj = new StringJoiner("#");
            //查询该点位是否配置了门店特殊规则
            String merchantTimeFrame = merchantTimeMap.get(merchantUkSj.add(storeNo.toString()).add(channel.getValue().toString()).add(DeliveryAlertEnums.Type.MERCHANT.getValue().toString()).add(deliverySiteEntity.getOuterClientId()).toString());

            if (StrUtil.isNotBlank(merchantTimeFrame)){
                deliverySiteEntity.setTimeFrame(merchantTimeFrame);
                continue;
            }
            StringJoiner brandUkSj = new StringJoiner("#");
            String brandTimeFrame = brandTimeMap.get(brandUkSj.add(storeNo.toString()).add(channel.getValue().toString()).add(DeliveryAlertEnums.Type.BRAND.getValue().toString()).add(deliverySiteEntity.getOuterBrandId()).toString());
            if (StrUtil.isNotBlank(brandTimeFrame)){
                deliverySiteEntity.setTimeFrame(brandTimeFrame);
            }
        }
    }

    /**
     * 查询最晚配送时效
     * @param queryParam 查询
     * @return 时间
     */
    public LocalTime queryDeliveryAlertTimeFrameList(LastDeliveryTimeQueryParam queryParam) {
        DeliveryAlertEnums.Channel channel = queryParam.getChannel();
        Integer storeNo = queryParam.getStoreNo();
        String city = queryParam.getCity();

        if(channel == null){
            throw new TmsRuntimeException("渠道来源不能为空");
        }
        if(StringUtils.isBlank(city)){
            throw new TmsRuntimeException("城市不能为空");
        }
        if(storeNo == null){
            throw new TmsRuntimeException("城配仓编号不能为空");
        }


        //先获取门店规则配送时效
        if(StringUtils.isNotBlank(queryParam.getOuterClientId())){
            DeliveryAlertRuleItemVO rule = deliveryAlertRuleRepository.queryByUk(storeNo,channel, DeliveryAlertEnums.Type.MERCHANT, queryParam.getOuterClientId());
            if(rule != null){
                return rule.getEndTime();
            }
        }
        //获取品牌规则配送时效
        if(StringUtils.isNotBlank(queryParam.getOuterTenantId())){
            DeliveryAlertRuleItemVO rule = deliveryAlertRuleRepository.queryByUk(storeNo,channel, DeliveryAlertEnums.Type.BRAND, queryParam.getOuterTenantId());
            if(rule != null){
                return rule.getEndTime();
            }
        }

        //获取地区规则配送时效
        if(StringUtils.isNotBlank(city)){
            CompleteDeliveryTimeQueryParam query = CompleteDeliveryTimeQueryParam.builder()
                    .storeNo(storeNo)
                    .city(city)
                    .area(queryParam.getArea())
                    .build();
            List<LocalTime> lastTimes = completeDeliveryQueryRepository.queryListAreaLastTime(query);
            if(!CollectionUtils.isEmpty(lastTimes)){
                return lastTimes.get(0);
            }
        }
        //默认配送规则
        return tmsNacosConfig.queryDefaultLastDelivertTime();
    }
}
