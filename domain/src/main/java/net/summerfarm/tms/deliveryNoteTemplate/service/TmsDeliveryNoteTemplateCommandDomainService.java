package net.summerfarm.tms.deliveryNoteTemplate.service;


import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateBelongCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateBelongCommandRepository;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateCommandRepository;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateQueryRepository;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 *
 * @Title: 配送单模版领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Service
public class TmsDeliveryNoteTemplateCommandDomainService {


    @Autowired
    private TmsDeliveryNoteTemplateCommandRepository tmsDeliveryNoteTemplateCommandRepository;
    @Autowired
    private TmsDeliveryNoteTemplateQueryRepository tmsDeliveryNoteTemplateQueryRepository;
    @Autowired
    private TmsDeliveryNoteTemplateBelongCommandRepository tmsDeliveryNoteTemplateBelongCommandRepository;


    public void insert(TmsDeliveryNoteTemplateCommandParam param) {
        if(param == null){
            return;
        }
        TmsDeliveryNoteTemplateEntity deliveryNoteTemplateEntity = tmsDeliveryNoteTemplateCommandRepository.insertSelective(param);
        // 新增配送单归属
        List<TmsDeliveryNoteTemplateBelongCommandParam> belongCommandParamList = param.getBelongCommandParamList();
        if(!CollectionUtils.isEmpty(belongCommandParamList) && deliveryNoteTemplateEntity != null){
            belongCommandParamList.forEach(belongCommandParam -> belongCommandParam.setDeliveryNoteTemplateId(deliveryNoteTemplateEntity.getId()));
            tmsDeliveryNoteTemplateBelongCommandRepository.batchInsert(belongCommandParamList);
        }
    }


    public void update(TmsDeliveryNoteTemplateCommandParam param) {
        if(param == null || param.getId() == null){
            return;
        }
        // 更新配送单配置
        tmsDeliveryNoteTemplateCommandRepository.updateSelectiveById(param);
        // 删除配送单归属
        tmsDeliveryNoteTemplateBelongCommandRepository.removeByTemplateId(param.getId());

        List<TmsDeliveryNoteTemplateBelongCommandParam> belongCommandParamList = param.getBelongCommandParamList();
        if(!CollectionUtils.isEmpty(belongCommandParamList)){
            belongCommandParamList.forEach(belongCommandParam -> belongCommandParam.setDeliveryNoteTemplateId(param.getId()));
            // 新增配送单归属
            tmsDeliveryNoteTemplateBelongCommandRepository.batchInsert(belongCommandParamList);
        }
    }


    public int delete(Long id) {
        return tmsDeliveryNoteTemplateCommandRepository.remove(id);
    }
}
