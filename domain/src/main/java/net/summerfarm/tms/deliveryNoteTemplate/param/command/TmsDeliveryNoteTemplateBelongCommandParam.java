package net.summerfarm.tms.deliveryNoteTemplate.param.command;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TmsDeliveryNoteTemplateBelongCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 配送单模版ID
	 */
	private Long deliveryNoteTemplateId;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 作用域 0默认 1租户 2大客户
	 */
	private Integer scopeType;

	/**
	 * 作用域下的业务ID 0默认
	 */
	private String scopeBusinessId;

	/**
	 * 业务方名称
	 */
	private String scopeBusinessName;

	/**
	 * 应用来源 1顺路达 2鲜沐 3saas 4外单
	 */
	private Integer appSource;


	

	
}