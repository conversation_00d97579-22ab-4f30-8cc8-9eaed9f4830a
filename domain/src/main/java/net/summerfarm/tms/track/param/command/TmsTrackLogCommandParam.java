package net.summerfarm.tms.track.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-08-30 14:11:07
 * @version 1.0
 *
 */
@Data
public class TmsTrackLogCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 业务类型 0履约申诉
	 */
	private Integer bizType;

	/**
	 * 业务ID
	 */
	private Long bizNo;

	/**
	 * 动作名称
	 */
	private String actionName;

	/**
	 * 操作人
	 */
	private String operater;

	/**
	 * 备注
	 */
	private String remark;

	

	
}