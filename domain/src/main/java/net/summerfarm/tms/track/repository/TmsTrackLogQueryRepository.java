package net.summerfarm.tms.track.repository;




import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.track.entity.TmsTrackLogEntity;
import net.summerfarm.tms.track.param.query.TmsTrackLogQueryParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:07
* @version 1.0
*
*/
public interface TmsTrackLogQueryRepository {

    PageInfo<TmsTrackLogEntity> getPage(TmsTrackLogQueryParam param);

    TmsTrackLogEntity selectById(Long id);

    List<TmsTrackLogEntity> selectByCondition(TmsTrackLogQueryParam param);

}