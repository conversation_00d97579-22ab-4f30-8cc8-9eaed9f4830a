package net.summerfarm.tms.track.repository;


import net.summerfarm.tms.track.entity.TmsTrackLogEntity;
import net.summerfarm.tms.track.param.command.TmsTrackLogCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:07
* @version 1.0
*
*/
public interface TmsTrackLogCommandRepository {

    TmsTrackLogEntity insertSelective(TmsTrackLogCommandParam param);

    int updateSelectiveById(TmsTrackLogCommandParam param);

    int remove(Long id);

    /**
     * 批量保存
     * @param tmsTrackLogCommandParams 参数
     */
    void batchInsert(List<TmsTrackLogCommandParam> tmsTrackLogCommandParams);
}