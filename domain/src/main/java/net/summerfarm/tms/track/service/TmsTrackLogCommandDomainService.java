package net.summerfarm.tms.track.service;

import net.summerfarm.tms.track.entity.TmsTrackLogEntity;
import net.summerfarm.tms.track.param.command.TmsTrackLogCommandParam;
import net.summerfarm.tms.track.repository.TmsTrackLogCommandRepository;
import net.summerfarm.tms.track.repository.TmsTrackLogQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 *
 * @Title: 轨迹日志表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-08-30 14:11:07
 * @version 1.0
 *
 */
@Service
public class TmsTrackLogCommandDomainService {


    @Autowired
    private TmsTrackLogCommandRepository tmsTrackLogCommandRepository;
    @Autowired
    private TmsTrackLogQueryRepository tmsTrackLogQueryRepository;



    public TmsTrackLogEntity insert(TmsTrackLogCommandParam param) {
        return tmsTrackLogCommandRepository.insertSelective(param);
    }


    public int update(TmsTrackLogCommandParam param) {
        return tmsTrackLogCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return tmsTrackLogCommandRepository.remove(id);
    }

    /**
     * 批量插入
     * @param tmsTrackLogCommandParams 参数
     */
    public void batchInsert(List<TmsTrackLogCommandParam> tmsTrackLogCommandParams) {
        tmsTrackLogCommandRepository.batchInsert(tmsTrackLogCommandParams);
    }
}
