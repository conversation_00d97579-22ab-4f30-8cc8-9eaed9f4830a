package net.summerfarm.tms.dist;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.delivery.*;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.entity.TrunkCityDistRelationshipEntity;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.facade.wms.dto.WmsOrderProcessDTO;
import net.summerfarm.tms.facade.wms.dto.WmsOrderProcessDetailDTO;
import net.summerfarm.tms.facade.wms.input.OrderProcessingQueryInput;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.dist.DistOrderMark;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.xianmu.common.exception.BizException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DistOrderDomainService {

    private final DistOrderRepository distOrderRepository;
    private final SiteRepository siteRepository;
    private final DistConfigRepository distConfigRepository;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final WmsQueryFacade wmsQueryFacade;
    private final TrunkCityDistRelationshipCommandRepository trunkCityDistRelationshipCommandRepository;

    @Lazy
    @Resource
    private DeliveryBatchDomainService deliveryBatchDomainService;
    @Lazy
    @Resource
    private DeliveryOrderDomainService deliveryOrderDomainService;
    @Resource
    private DeliveryPickDomainService deliveryPickDomainService;
    @Lazy
    @Resource
    private DeliverySiteDomainService deliverySiteDomainService;

    @Transactional
    public List<DistOrderEntity> finishDelivery(List<Long> distIdList) {
        List<DistOrderEntity> activeDistOrderList = new ArrayList<>(distIdList.size());
        for (Long distId : distIdList) {
            activeDistOrderList.add(finishDelivery(distId));
        }
        return activeDistOrderList;
    }

    public DistOrderEntity finishDelivery(Long distId) {
        DistOrderEntity distOrderEntity = getDistOrderDetail(distId);
        if(distOrderEntity.getStatus() != DistOrderStatusEnum.CANCEL_BEFORE_WIRED &&
                distOrderEntity.getStatus() != DistOrderStatusEnum.CANCEL_AFTER_WIRED){
            distOrderEntity.setStatus(DistOrderStatusEnum.COMPLETE_DELIVERY);
            distOrderEntity.getDistFlowVO().setRealArrivalTime(LocalDateTime.now());
        }
        distOrderRepository.update(distOrderEntity);
        return distOrderRepository.query(distId);
    }

    /**
     * 新增委托单
     *
     * @param distOrderEntity 委托单实体
     */
    public Long createDistOrder(DistOrderEntity distOrderEntity) {
        SiteEntity beginSite = handleSiteInfo(distOrderEntity.getBeginSite());
        SiteEntity endSite = handleSiteInfo(distOrderEntity.getEndSite());
        //查询起点终点是否配置自动生成中转站
        String midSiteIdStr = distConfigRepository.findConfigValue(beginSite.getId(), endSite.getId(), DistConfigTypeEnum.TRANSFER_SITE);
        distOrderEntity.autoGenerateMidSite(midSiteIdStr);
        distOrderEntity.create();
        //保存委托单信息
        Long distId = distOrderRepository.saveOrUpdate(distOrderEntity);
        //保存配送单信息
        for (DeliveryOrderEntity deliveryOrderEntity : distOrderEntity.getDeliveryOrders()) {
            deliveryOrderEntity.setDistOrderId(distId);
            deliveryOrderRepository.saveOrUpdate(deliveryOrderEntity);
        }
        return distId;
    }

    /**
     * 处理点位信息
     *
     * @param siteEntity 点位实体
     * @return 点位实体
     */
    private SiteEntity handleSiteInfo(SiteEntity siteEntity) {
        Long siteId = siteRepository.siteAdd(siteEntity);
        siteEntity.setId(siteId);
        return siteEntity;
    }

    /**
     * 校验委托单
     *
     * @param distId 委托单ID
     */
    public void verifyDistOrder(Long distId) {
        Long count = distOrderRepository.queryCount(distId);
        if (count == 0) {
            throw new TmsRuntimeException("该委托单不存在");
        }
    }

    /**
     * 查询委托单详情
     *
     * @param distOrderMark 委托单标记
     * @return 委托单详情
     */
    public DistOrderEntity getDistOrderDetail(DistOrderMark distOrderMark) {
        DistOrderEntity distOrderEntity;
        if (distOrderMark.getDistId() == null) {
            String outerOrderId = distOrderMark.getOuterOrderId();
            DistOrderSourceEnum source = distOrderMark.getSource();
            LocalDateTime expectBeginTime = distOrderMark.getExpectBeginTime();
            String outerContactId = distOrderMark.getOuterContactId();
            TmsAssert.notNull(outerOrderId, ErrorCodeEnum.PARAM_NOT_NULL, "outerOrderId");
            TmsAssert.notNull(source, ErrorCodeEnum.PARAM_NOT_NULL, "source");
            distOrderEntity = getDistOrderDetail(outerOrderId, source, expectBeginTime, outerContactId);
        } else {
            distOrderEntity = getDistOrderDetail(distOrderMark.getDistId());
        }
        if (distOrderEntity != null) {
            List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryListWithSiteName(
                    DeliveryOrderQuery.builder().distOrderId(distOrderEntity.getDistId()).build());
            distOrderEntity.setDeliveryOrders(deliveryOrderEntityList);
        }
        return distOrderEntity;
    }

    /**
     * 查询委托单详情
     *
     * @param outOrderId      外部订单号
     * @param source          来源
     * @param expectBeginTime 期待开始时间
     * @param outerContactId
     * @return 委托单详情
     */
    public DistOrderEntity getDistOrderDetail(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        DistOrderEntity distOrderEntity = distOrderRepository.queryWithItemByUk(outOrderId, source, expectBeginTime, outerContactId);
        if (distOrderEntity == null || !distOrderEntity.isValid()) {
            throw new TmsRuntimeException(ErrorCodeEnum.DIST_ORDER_NOT_FIND);
        }
        return getSiteInfo(distOrderEntity);
    }

    /**
     * 获取点位信息
     *
     * @param distOrderEntity 点位实体
     * @return 点位实体
     */
    private DistOrderEntity getSiteInfo(DistOrderEntity distOrderEntity) {
        //获取点位信息
        SiteEntity beginSite = siteRepository.query(distOrderEntity.getBeginSite().getId());
        distOrderEntity.setBeginSite(beginSite);
        SiteEntity midSite = siteRepository.query(distOrderEntity.getMidSite().getId());
        distOrderEntity.setMidSite(midSite);
        SiteEntity endSite = siteRepository.query(distOrderEntity.getEndSite().getId());
        distOrderEntity.setEndSite(endSite);
        return distOrderEntity;
    }

    /**
     * 查询委托单详情
     *
     * @param distId 委托单ID
     * @return 委托单详情
     */
    public DistOrderEntity getDistOrderDetail(Long distId) {
        verifyDistOrder(distId);
        DistOrderEntity distOrderEntity = distOrderRepository.queryWithItem(distId);
        return getSiteInfo(distOrderEntity);
    }

    /**
     * 状态变更
     *
     * @param distId 委托单ID
     */
    public void bindDeliveryBatchSuc(Long distId) {
        DistOrderEntity distOrderEntity = queryDistOrderById(distId);
        TmsAssert.notNull(distOrderEntity, ErrorCodeEnum.DB_DATA_ERROR, "委托单没找到");
        switch (distOrderEntity.getStatus()) {
            case IN_WIRED:
            case TO_BE_WIRED:
            case TO_BE_PICKED:
                distOrderEntity.setStatus(DistOrderStatusEnum.IN_DELIVERY);
                distOrderRepository.modifyStatus(distOrderEntity);
                return;
            case IN_DELIVERY:
                return;
            //不能改
            case CLOSED:
            case CANCEL_BEFORE_WIRED:
            case CANCEL_AFTER_WIRED:
            case COMPLETE_DELIVERY:
            case REJECT:
            case COMPLETE_DELIVERY_SHORT:
            default:
                throw new TmsRuntimeException(ErrorCodeEnum.DIST_STATE_ERROR, distOrderEntity.getStatus().getName());

        }

    }

    /**
     * 再次初始化状态
     *
     * @param distId 委托单ID
     */
    public DistOrderEntity resetInitState(Long distId) {
        DistOrderEntity distOrderEntity = queryDistOrderById(distId);
        TmsAssert.notNull(distOrderEntity, ErrorCodeEnum.DB_DATA_ERROR, "委托单没找到");
        switch (distOrderEntity.getStatus()) {
            case IN_WIRED:
            case TO_BE_WIRED:
            case TO_BE_PICKED:
            case IN_DELIVERY:
                distOrderEntity.setStatus(DistOrderStatusEnum.TO_BE_WIRED);
                distOrderRepository.modifyStatus(distOrderEntity);
                return distOrderEntity;
            //不能改
            case CLOSED:
            case CANCEL_BEFORE_WIRED:
            case CANCEL_AFTER_WIRED:
            case COMPLETE_DELIVERY:
            case REJECT:
            case COMPLETE_DELIVERY_SHORT:
            default:
                throw new TmsRuntimeException(ErrorCodeEnum.DIST_STATE_ERROR, distOrderEntity.getStatus().getName());

        }

    }

    /**
     * 根据id查询委托单信息
     *
     * @param distOrderId
     * @return
     */
    public DistOrderEntity queryDistOrderById(Long distOrderId) {
        return distOrderRepository.query(distOrderId);
    }

    /**
     * 编辑委托单
     *
     * @param distOrderEntity 委托单实体
     */
    public void editDistOrder(DistOrderEntity distOrderEntity) {
        distOrderRepository.update(distOrderEntity);
    }

    /**
     * 编辑委托单点位
     *
     * @param distOrderEntity 委托单实体
     * @param siteId          点位ID
     */
    public void editDistOrderSite(DistOrderEntity distOrderEntity, Long siteId) {
        //校验中转站有效性
        SiteEntity siteEntity = siteRepository.query(siteId);
        if (siteEntity == null) {
            throw new TmsRuntimeException("该中转站点位不存在");
        }
        distOrderEntity.modifyMidSite(siteEntity.getId());
        //更新委托单信息
        distOrderRepository.update(distOrderEntity);
        //删除原配送单信息
        deliveryOrderRepository.remove(distOrderEntity.getDistId());
        //新增配送单信息
        distOrderEntity.getDeliveryOrders().forEach(deliveryOrderRepository::saveOrUpdate);
    }

    /**
     * 关闭委托单
     *
     * @param distOrderEntity 委托单实体
     * @param closeReason     关闭原因
     */
    public void closeDistOrder(DistOrderEntity distOrderEntity, String closeReason) {
        distOrderEntity.close(closeReason);
        //更新委托单信息
        distOrderRepository.update(distOrderEntity);
        //删除原配送单信息
        deliveryOrderRepository.remove(distOrderEntity.getDistId());
    }

    /**
     * 取消委托单
     *
     * @param distOrderEntity 委托单实体
     */
    public void cancelDistOrder(DistOrderEntity distOrderEntity) {
        distOrderEntity.cancel();
        //更新委托单信息
        distOrderRepository.update(distOrderEntity);
        //删除原配送单信息
        deliveryOrderRepository.remove(distOrderEntity.getDistId());
        
    }

    /**
     * 查询是否已配置
     *
     * @param beginSiteId        起点ID
     * @param endSiteId          终点ID
     * @param distConfigTypeEnum 委托单配置类型枚举
     * @return 是否已配置, true:是,false:否
     */
    public Boolean queryIfConfigured(Long beginSiteId, Long endSiteId, DistConfigTypeEnum distConfigTypeEnum) {
        Long count = distConfigRepository.queryCount(beginSiteId, endSiteId, distConfigTypeEnum);
        return count != 0;
    }

    /**
     * 根据状态查询委托单信息
     *
     * @param statusList 状态
     * @return 结果
     */
    public List<DistOrderEntity> queryDistOrderByState(List<DistOrderStatusEnum> statusList) {
        return distOrderRepository.queryByStatus(statusList);
    }

    /**
     * 根据开始点位和期望开始时间查询有效的配送单
     *
     * @param endSiteIds    配送点位id集合
     * @param deliveryTime 期望开始时间
     * @param beginSiteId 开始配送点位
     * @return 结果
     */
    public List<DistOrderEntity> queryValidDistOrder(List<Long> endSiteIds, LocalDate deliveryTime,Long beginSiteId) {
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setExpectBeginTime(deliveryTime.atStartOfDay());
        distOrderQuery.setEndSiteIds(endSiteIds);
        distOrderQuery.setBeginSiteId(beginSiteId);
        distOrderQuery.setSources(DistOrderSourceEnum.getCityCode());
        distOrderQuery.setStateList(Arrays.asList(
                DistOrderStatusEnum.TO_BE_WIRED.getCode(),
                DistOrderStatusEnum.IN_WIRED.getCode(),
                DistOrderStatusEnum.TO_BE_PICKED.getCode(),
                DistOrderStatusEnum.IN_DELIVERY.getCode(),
                DistOrderStatusEnum.DELIVERY_ERROR.getCode(),
                DistOrderStatusEnum.CANCEL_AFTER_WIRED.getCode(),
                DistOrderStatusEnum.COMPLETE_DELIVERY.getCode(),
                DistOrderStatusEnum.REJECT.getCode(),
                DistOrderStatusEnum.COMPLETE_DELIVERY_SHORT.getCode()
        ));
        return distOrderRepository.queryListWithItem(distOrderQuery);
    }

    public void confirmDeliveryPlan(List<DistOrderEntity> distOrderEntities, DistOrderStatusEnum distOrderStatusEnum) {
        if (CollectionUtils.isEmpty(distOrderEntities)) {
            return;
        }
        for (DistOrderEntity distOrderEntity : distOrderEntities) {
            if(distOrderEntity == null){
                continue;
            }
            distOrderEntity.setStatus(distOrderStatusEnum);
            distOrderRepository.modifyStatus(distOrderEntity);
        }
    }

    /**
     * 委托单进行订单拦截，并返回未能处理的
     *
     * @param distOrder 委托单
     */
    public void intercept(DistOrderEntity distOrder) {
        if(distOrder == null){
            return;
        }
        DistOrderEntity interceptDistOrder = distOrder.interceptDistOrder();
        distOrderRepository.update(interceptDistOrder);
    }

    /**
     * 拦截委托单
     *
     * @param distOrderEntity 委托单实体
     */
    public void interceptDistOrder(DistOrderEntity distOrderEntity) {
        if (distOrderEntity.isIntercept()) {
            return;
        }
        //签发拦截
        deliveryPickDomainService.intercept(distOrderEntity.getDistId());
        //委托单拦截
        this.intercept(distOrderEntity);
        //配送单拦截
        deliveryOrderDomainService.intercept(distOrderEntity.getDistId());
        //配送点位拦截
        deliverySiteDomainService.intercept(distOrderEntity.getDistId());
    }

    /**
     * 删除委托单
     *
     * @param distOrderEntity 委托单实体
     */
    public void removeDistOrder(DistOrderEntity distOrderEntity) {
        if (!CollectionUtils.isEmpty(distOrderEntity.getDeliveryOrders())) {
            List<Long> deliveryBatchIds = distOrderEntity.getDeliveryOrders().stream()
                    .filter(Objects::nonNull)
                    .map(DeliveryOrderEntity::getDeliveryBatchId)
                    .collect(Collectors.toList());
            deliveryBatchDomainService.removeDeliveryBatch(deliveryBatchIds, distOrderEntity.getDistId());
        }
        distOrderRepository.remove(distOrderEntity.getDistId());
        deliveryOrderRepository.remove(distOrderEntity.getDistId());
    }

    /**
     * 点位移除，修改委托单的状态
     *
     * @param distOrderEntities
     */
    public void siteRemove(List<DistOrderEntity> distOrderEntities) {
        if (CollectionUtils.isEmpty(distOrderEntities)) {
            return;
        }
        for (DistOrderEntity distOrderEntity : distOrderEntities) {
            distOrderEntity.setStatus(DistOrderStatusEnum.TO_BE_WIRED);
            distOrderRepository.update(distOrderEntity);

            //根据distId获取配送单
            List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                    .distOrderId(distOrderEntity.getDistId()).build());

            for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntities) {
                deliveryOrderRepository.update(deliveryOrderEntity);
            }

        }
    }

    /**
     * 完成排线
     * @param updateDistOrderEntityList 委托单集合
     * @param statusEnum 状态
     */
    public void completePath(ArrayList<DistOrderEntity> updateDistOrderEntityList, DistOrderStatusEnum statusEnum) {
        distOrderRepository.updateStatus(updateDistOrderEntityList,statusEnum);
    }

    /**
     * 完成拣货
     * @param distOrderIdList 委托单id集合
     */
    public void finishPick(List<Long> distOrderIdList) {
        distOrderIdList = distOrderIdList.stream().sorted(Long::compareTo).collect(Collectors.toList());
        ArrayList<DistOrderEntity> distOrderEntities = new ArrayList<>();
        for (Long distId : distOrderIdList) {
            DistOrderEntity distOrderEntity = new DistOrderEntity();
            distOrderEntity.setDistId(distId);

            distOrderEntities.add(distOrderEntity);
        }
        distOrderRepository.updateStatus(distOrderEntities,DistOrderStatusEnum.IN_DELIVERY);
    }

    public void removeDistOrderItem(Long distItemId) {
        distOrderRepository.removeDistItem(distItemId);
    }

    /**查询有效的加工信息
     * @param distOrderEntityList 配送单
     * @return
     */
    public List<WmsOrderProcessDTO> queryWmsOrderProcessDTOS(List<DistOrderEntity> distOrderEntityList) {
        //查询订单信息
        List<String> outOrderIdList = distOrderEntityList.stream().map(e -> e.getDistClientVO().getOutOrderId()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(outOrderIdList)){
            return Collections.EMPTY_LIST;
        }
        //获取加工信息
        List<WmsOrderProcessDTO> wmsOrderProcessDTOs = wmsQueryFacade.queryOrderProcessingInfo(OrderProcessingQueryInput.builder()
                .orderNoList(outOrderIdList)
                .build());
        //过滤有问题的加工数据
        List<WmsOrderProcessDTO> wmsOrderProcessDTOList = new ArrayList<>();
        //过滤有问题的数据
        //订单号和加工数据映射
        Map<String, WmsOrderProcessDTO> orderNoProcessMap = wmsOrderProcessDTOs.stream()
                .collect(Collectors.toMap(WmsOrderProcessDTO::getOrderNo, Function.identity()));
        for (DistOrderEntity distOrderEntity : distOrderEntityList) {
            WmsOrderProcessDTO wmsOrderProcessDTO = orderNoProcessMap.get(distOrderEntity.getDistClientVO().getOutOrderId());
            if(wmsOrderProcessDTO != null){
                List<DistItemVO> distItems = distOrderEntity.getDistItems();
                List<WmsOrderProcessDetailDTO> orderProcessDetailDTOList = wmsOrderProcessDTO.getOrderProcessDetailDTOList();
                List<WmsOrderProcessDetailDTO> newOrderProcessDetailDTOList = new ArrayList<>();

                Map<String, List<DistItemVO>> skuItemMapList = distItems.stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));
                Map<String, List<WmsOrderProcessDetailDTO>> processSkuItemMapList = orderProcessDetailDTOList.stream().collect(Collectors.groupingBy(WmsOrderProcessDetailDTO::getProductSkuCode));
                for (String sku : skuItemMapList.keySet()) {
                    List<WmsOrderProcessDetailDTO> wmsOrderProcessDetailDTOS = processSkuItemMapList.get(sku);
                    if(CollectionUtils.isEmpty(wmsOrderProcessDetailDTOS)){
                        continue;
                    }
                    //加工原sku数量
                    int origSkuQuantity = wmsOrderProcessDetailDTOS.stream().mapToInt(WmsOrderProcessDetailDTO::getOrigSkuQuantity).sum();
                    //加工原sku数量 不等于 大于订单的数量 是加工异常数据
                    if(origSkuQuantity != skuItemMapList.get(sku).stream().mapToInt(DistItemVO::getQuantity).sum()){
                        continue;
                    }
                    newOrderProcessDetailDTOList.addAll(wmsOrderProcessDetailDTOS);
                }
                wmsOrderProcessDTO.setOrderProcessDetailDTOList(newOrderProcessDetailDTOList);
                wmsOrderProcessDTOList.add(wmsOrderProcessDTO);
            }
        }
        return wmsOrderProcessDTOList;
    }

    /**
     * 查询订单加工信息
     * @param distOrderEntityList 委托单信息
     * @return sku加工信息
     */
    public Map<String, List<WmsOrderProcessDetailDTO>> queryProcessOutItemMap(List<DistOrderEntity> distOrderEntityList) {
        //获取加工信息
        List<WmsOrderProcessDTO> wmsOrderProcessDTOs = queryWmsOrderProcessDTOS(distOrderEntityList);
        //处于加工状态的数据
        wmsOrderProcessDTOs = wmsOrderProcessDTOs.stream()
                .filter(wmsOrderProcessDTO -> !Objects.equals(WmsTaskStatusEnum.NO.getCode(), wmsOrderProcessDTO.getTaskStatus()))
                .collect(Collectors.toList());
        if(org.apache.commons.collections.CollectionUtils.isEmpty(wmsOrderProcessDTOs)){
            log.info("查询配送详情,不存在加工信息");
        }
        Map<String, List<WmsOrderProcessDetailDTO>> resMap = new HashMap<>();
        try {
            List<WmsOrderProcessDetailDTO> wmsOrderProcessDetailDTOs = wmsOrderProcessDTOs.stream()
                    .map(WmsOrderProcessDTO::getOrderProcessDetailDTOList).flatMap(Collection::stream)
                    .collect(Collectors.toList());
            Map<String, List<WmsOrderProcessDetailDTO>> skuProcessDetailMap = wmsOrderProcessDetailDTOs.stream().collect(Collectors.groupingBy(WmsOrderProcessDetailDTO::getProductSkuCode));

            for (String outItemId : skuProcessDetailMap.keySet()) {
                List<WmsOrderProcessDetailDTO> resProcessList = new ArrayList<>();
                for (WmsOrderProcessDetailDTO wmsOrderProcessDetailDTO : skuProcessDetailMap.get(outItemId)) {
                    if(Objects.equals(wmsOrderProcessDetailDTO.getProductSkuSpecSubmitQuantity(),0)){
                        continue;
                    }
                    for (WmsOrderProcessDetailDTO resDetail : resProcessList) {
                        //相同sku下的单位和重量一致
                        if(Objects.equals(resDetail.getProductSkuSpecUnit(),wmsOrderProcessDetailDTO.getProductSkuSpecUnit()) &&
                                Objects.equals(resDetail.getProductSkuSpecWeight(),wmsOrderProcessDetailDTO.getProductSkuSpecWeight())){
                            resDetail.setNoProductSkuQuantity(resDetail.getNoProductSkuQuantity() + wmsOrderProcessDetailDTO.getNoProductSkuQuantity());
                            resDetail.setProductSkuSpecSubmitQuantity(resDetail.getProductSkuSpecSubmitQuantity() + wmsOrderProcessDetailDTO.getProductSkuSpecSubmitQuantity());
                            resDetail.setProductSkuNeedQuantity(resDetail.getProductSkuNeedQuantity() + wmsOrderProcessDetailDTO.getProductSkuNeedQuantity());
                            resDetail.setProductSkuSpecNeedQuantity(resDetail.getProductSkuSpecNeedQuantity() + wmsOrderProcessDetailDTO.getProductSkuSpecNeedQuantity());
                            resDetail.setProductSkuSpecSubmitWeight(resDetail.getProductSkuSpecSubmitWeight().add(wmsOrderProcessDetailDTO.getProductSkuSpecSubmitWeight()));
                            resDetail.setHaveProductSkuQuantity(resDetail.getHaveProductSkuQuantity() + wmsOrderProcessDetailDTO.getHaveProductSkuQuantity());
                            resDetail.setOrigSkuQuantity(resDetail.getOrigSkuQuantity() + wmsOrderProcessDetailDTO.getOrigSkuQuantity());

                            wmsOrderProcessDetailDTO.setProductSkuSpecSubmitQuantity(0);
                        }
                    }
                    if(wmsOrderProcessDetailDTO.getProductSkuSpecSubmitQuantity() != 0){
                        resProcessList.add(wmsOrderProcessDetailDTO);
                    }
                }
                resMap.put(outItemId,resProcessList);
            }
        } catch (Exception e) {
            log.error("tms处理加工数据异常",e);
        }

        log.info("获取订单WMS加工{}", JSON.toJSONString(resMap));
        return resMap;
    }

    public List<Long> autoMatchBatch(DistOrderEntity distOrderEntity) {
        //销售出库、出样出库、补货出库支持自动匹配
        if (!DistOrderSourceEnum.isAuto(distOrderEntity.getSource())){
            return Collections.emptyList();
        }

        List<DeliveryOrderEntity> needHandleDeliveryOrders = distOrderEntity.getDeliveryOrders();
        if (CollectionUtils.isEmpty(needHandleDeliveryOrders)){
            return Collections.emptyList();
        }
        //获取对应调度单业务类型
        Integer deliveryBatchType = DeliveryBatchTypeSourceEnum.getBySource(distOrderEntity.getSource().getCode());
        if (deliveryBatchType == null){
            return Collections.emptyList();
        }
        //根据承运单履约时间、业务类型、起始点位终止点位查找对应可匹配的调度单[待配送、配送中、配送完成]
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryWithSite(DeliveryBatchQuery.builder().deliveryTime(distOrderEntity.getDistFlowVO().getExpectBeginTime())
                .type(deliveryBatchType)
                .deliveryBatchStatusList(Arrays.asList(DeliveryBatchStatusEnum.TO_BE_PICKED.getCode(), DeliveryBatchStatusEnum.IN_DELIVERY.getCode(), DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode())).build());
        //数据源无调度单可匹配
        if (CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return Collections.emptyList();
        }
        // 全品类用车 仅待配送才能自动匹配
        if(DistOrderSourceEnum.ALL_CATEGORY_PICK == distOrderEntity.getSource()){
            deliveryBatchEntityList = deliveryBatchEntityList.stream().filter(deliveryBatchEntity -> DeliveryBatchStatusEnum.TO_BE_PICKED == deliveryBatchEntity.getStatus()).collect(Collectors.toList());
        }
        //构建可匹配的路段对应调度单Map
        Map<String, List<DeliveryBatchEntity>> beginEndSiteMap = new HashMap<>();
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            List<Long> deliverySiteIds = deliveryBatchEntity.getDeliverySiteList().stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence))
                    .map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
            for (int i = 0; i < deliverySiteIds.size(); i++) {
                for (int j = i + 1; j < deliverySiteIds.size(); j++) {
                    String beginEndSiteStr = deliverySiteIds.get(i) + "#" + deliverySiteIds.get(j);
                    if (beginEndSiteMap.containsKey(beginEndSiteStr)){
                        List<DeliveryBatchEntity> existedDeliveryBatchList = beginEndSiteMap.get(beginEndSiteStr);
                        existedDeliveryBatchList.add(deliveryBatchEntity);
                    }else {
                        beginEndSiteMap.put(beginEndSiteStr, Lists.newArrayList(deliveryBatchEntity));
                    }
                }
            }
        }

        List<DeliveryOrderEntity> needAddDeliveryOrders = new ArrayList<>();
        for (DeliveryOrderEntity deliveryOrder : needHandleDeliveryOrders) {
            String beginEndSiteStr = deliveryOrder.getBeginSiteId() + "#" + deliveryOrder.getEndSiteId();
            List<DeliveryBatchEntity> matchedDeliveryBatchList = beginEndSiteMap.get(beginEndSiteStr);
            //承运单无调度单可匹配
            if (CollectionUtils.isEmpty(matchedDeliveryBatchList)){
                continue;
            }
            //匹配一条或多条的情况下默认当前配送单先绑定第一条调度单
            DeliveryBatchEntity firstDeliveryBatch = matchedDeliveryBatchList.remove(0);
            deliveryOrder.setDeliveryBatchId(firstDeliveryBatch.getId());
            if (matchedDeliveryBatchList.isEmpty()) {
                continue;
            }
            //匹配多条的情况下剩余调度单需要全部进行绑定
            for (DeliveryBatchEntity matchedDeliveryBatch : matchedDeliveryBatchList) {
                //新增
                DeliveryOrderEntity deepCopyDeliveryOrder = DeliveryOrderEntity.copy(deliveryOrder);
                deepCopyDeliveryOrder.setId(null);
                deepCopyDeliveryOrder.setDeliveryBatchId(matchedDeliveryBatch.getId());
                needAddDeliveryOrders.add(deepCopyDeliveryOrder);
            }
        }
        List<Long> matchBatchIds = new ArrayList<>();
        if (!needAddDeliveryOrders.isEmpty()){
            List<Long> batchIds = needAddDeliveryOrders.stream().map(DeliveryOrderEntity::getDeliveryBatchId).collect(Collectors.toList());
            matchBatchIds.addAll(batchIds);
            deliveryOrderRepository.saveBatch(needAddDeliveryOrders);
        }
        //过滤出按规则需自动匹配的单据进行更新处理
        List<DeliveryOrderEntity> needUpdateDeliveryOrders = needHandleDeliveryOrders.stream().filter(e -> e.getDeliveryBatchId() != null)
                .sorted(Comparator.comparing(DeliveryOrderEntity::getId)).collect(Collectors.toList());
        if (!needUpdateDeliveryOrders.isEmpty()){
            List<Long> batchIds = needUpdateDeliveryOrders.stream().map(DeliveryOrderEntity::getDeliveryBatchId).collect(Collectors.toList());
            matchBatchIds.addAll(batchIds);
            needUpdateDeliveryOrders.forEach(deliveryOrderRepository::update);
            //匹配成功更新承运单状态为承运中
            this.confirmDeliveryPlan(Collections.singletonList(distOrderEntity),DistOrderStatusEnum.IN_DELIVERY);
        }
        return matchBatchIds;
    }

    /**
     * 承运单与调度单关系新增
     * @param trunkDistId 干线承运单Id
     * @param cityDistId 城配承运单Id
     */
    public void distRelationshipAdd(Long trunkDistId, Long cityDistId) {
        if(trunkDistId == null || cityDistId == null){
            throw new BizException("承运单与调度单关系新增干线承运单Id或城配承运单Id不能为空");
        }
        DistOrderEntity distOrderEntity = distOrderRepository.queryById(trunkDistId);
        if (distOrderEntity == null){
            throw new BizException("未查询到对应的承运单信息");
        }
        TrunkCityDistRelationshipEntity relationshipEntity = TrunkCityDistRelationshipEntity.builder()
                .trunkDistId(trunkDistId)
                .cityDistId(cityDistId)
                .outerOrderId(distOrderEntity.getDistClientVO().getOutOrderId())
                .build();

        trunkCityDistRelationshipCommandRepository.save(relationshipEntity);
    }

    /**
     * 外单关联关系变更
     * @param outerOrderId 外单编号
     * @param trunkDistOrderId 干线承运单Id
     * @param cityDistOrderId 城配承运单Id
     */
    public void distRelationshipChange(String outerOrderId, Long trunkDistOrderId, Long cityDistOrderId) {
        if(StringUtils.isEmpty(outerOrderId) || trunkDistOrderId == null || cityDistOrderId == null){
            throw new BizException("变更外单关联关系的外部单号、干线承运单Id和城配承运单Id不能为空");
        }
        trunkCityDistRelationshipCommandRepository.deleteByOuterOrderId(outerOrderId);

        TrunkCityDistRelationshipEntity relationshipEntity = TrunkCityDistRelationshipEntity.builder()
                .trunkDistId(trunkDistOrderId)
                .cityDistId(cityDistOrderId)
                .outerOrderId(outerOrderId)
                .build();

        trunkCityDistRelationshipCommandRepository.save(relationshipEntity);
    }

    /**
     * 校验外单取消承运单
     * @param outerOrderIds 外单编号
     * @return 校验结果
     */
    public HashMap<String, String> outerCancelDistOrderValidate(List<String> outerOrderIds) {
        if(CollectionUtils.isEmpty(outerOrderIds)){
            throw new BizException("取消外单承运单的外单编号集合不能为空");
        }
        HashMap<String, String> restultMap = new HashMap<>();
        //根据委托单查询单据信息
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder()
                .outerOrderIds(outerOrderIds).build());
        for (DistOrderEntity distOrder : distOrderEntities) {
            if(DistOrderSourceEnum.getTrunkCode().contains(distOrder.getSource().getCode())){
                if(!DistOrderStatusEnum.isTrunkCancelValid(distOrder.getStatus())){
                    restultMap.put(distOrder.getDistClientVO().getOutOrderId(),"该外单已被干线承运不能取消");
                }
            }else{
                if(!DistOrderStatusEnum.isCityCancelValid(distOrder.getStatus().getCode())){
                    restultMap.put(distOrder.getDistClientVO().getOutOrderId(),"该外单城配状态不能被取消");
                }
            }
        }

        return restultMap;
    }

    /**
     * 批量拦截处理
     * @param haveCompletedDistOrderList 已完成排线委托单
     */
    public void batchIntercept(List<DistOrderEntity> haveCompletedDistOrderList) {
        if(CollectionUtils.isEmpty(haveCompletedDistOrderList)){
            return;
        }
        log.info("DistOrderDomainService batchIntercept req data {}",JSON.toJSONString(haveCompletedDistOrderList));

        List<DistOrderEntity> distOrderEntities = new ArrayList<>();

        haveCompletedDistOrderList.forEach(distOrderEntity -> {
            distOrderEntities.add(distOrderEntity.interceptDistOrder());
        });

        log.info("DistOrderDomainService batchIntercept update data {}",JSON.toJSONString(haveCompletedDistOrderList));
        distOrderRepository.batchUpdate(distOrderEntities);
    }
}
