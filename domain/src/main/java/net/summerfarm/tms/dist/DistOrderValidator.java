package net.summerfarm.tms.dist;

import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.DistPickTypeEnum;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.client.message.in.*;
import net.summerfarm.tms.message.in.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Description:委托单校验器
 * date: 2023/2/9 15:31
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DistOrderValidator {

    public void validateDistOrderUk(String outOrderId, Integer source, LocalDateTime expectBeginTime, String outerContactId){
        TmsAssert.notEmpty(outOrderId, ErrorCodeEnum.PARAM_NOT_NULL, "OuterOrderId");
        TmsAssert.notNull(source, ErrorCodeEnum.PARAM_NOT_NULL, "Source");
        TmsAssert.notNull(expectBeginTime, ErrorCodeEnum.PARAM_NOT_NULL, "ExpectBeginTime");
        TmsAssert.notEmpty(outerContactId, ErrorCodeEnum.PARAM_NOT_NULL, "OuterContactId");
    }

    public void validateDistOrderCreate(DistOrderCreateMessage distOrderCreateMessage){
        this.validateDistOrder(distOrderCreateMessage);
        this.validateDistOrderItem(distOrderCreateMessage.getDistOrderItemList());
        this.validateDistSite(distOrderCreateMessage.getBeginSite());
        this.validateDistSite(distOrderCreateMessage.getEndSite());
        this.validateOperator(distOrderCreateMessage.getCreatorId(),distOrderCreateMessage.getCreator());
    }

    public void validateDistOrderChange(DistOrderChangeMessage distOrderChangeMessage){
        String outerOrderId = distOrderChangeMessage.getOuterOrderId();
        Integer source = distOrderChangeMessage.getSource();
        LocalDateTime expectBeginTime = distOrderChangeMessage.getExpectBeginTime();
        String outerContactId = distOrderChangeMessage.getOuterContactId();
        this.validateDistOrderUk(outerOrderId, source, expectBeginTime, outerContactId);
        if (distOrderChangeMessage.getNewExpectBeginTime() != null){
            if (!distOrderChangeMessage.getNewExpectBeginTime().toLocalDate().isAfter(LocalDate.now())){
                throw new TmsRuntimeException("不支持的NewExpectBeginTime");
            }
        }
        if (distOrderChangeMessage.getNewBeginSite() != null){
            this.validateDistSite(distOrderChangeMessage.getNewBeginSite());
        }
        if (distOrderChangeMessage.getNewEndSite() != null){
            this.validateDistSite(distOrderChangeMessage.getNewEndSite());
        }

        List<DistOrderItemChangeMessage> newDistOrderItemChangeMessages = distOrderChangeMessage.getNewDistOrderItemChangeMessages();
        if (!CollectionUtils.isEmpty(newDistOrderItemChangeMessages)){
            for (DistOrderItemChangeMessage newDistOrderItemChangeMessage : newDistOrderItemChangeMessages) {
                TmsAssert.notEmpty(newDistOrderItemChangeMessage.getOuterItemId(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemChangeMessage.OuterItemId");
                TmsAssert.notNull(newDistOrderItemChangeMessage.getQuantity(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemChangeMessage.Quantity");
                TmsAssert.notNull(newDistOrderItemChangeMessage.getNewQuantity(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemChangeMessage.NewQuantity");
            }
        }
        this.validateOperator(distOrderChangeMessage.getUpdaterId(),distOrderChangeMessage.getUpdater());
    }
    public void validateDistOrder(DistOrderCreateMessage distOrderCreateMessage){
        String outerOrderId = distOrderCreateMessage.getOuterOrderId();
        Integer source = distOrderCreateMessage.getSource();
        LocalDateTime expectBeginTime = distOrderCreateMessage.getExpectBeginTime();
        String outerContactId = distOrderCreateMessage.getOuterContactId();
        this.validateDistOrderUk(outerOrderId, source, expectBeginTime, outerContactId);
        TmsAssert.notEmpty(distOrderCreateMessage.getOuterId(), ErrorCodeEnum.PARAM_NOT_NULL, "OuterId");
        TmsAssert.notNull(distOrderCreateMessage.getType(), ErrorCodeEnum.PARAM_NOT_NULL, "Type");
        TmsAssert.notNull(distOrderCreateMessage.getPickType(), ErrorCodeEnum.PARAM_NOT_NULL, "PickType");
        TmsAssert.notEmpty(distOrderCreateMessage.getOuterClientId(), ErrorCodeEnum.PARAM_NOT_NULL, "OuterClientId");
        TmsAssert.notEmpty(distOrderCreateMessage.getOuterClientName(), ErrorCodeEnum.PARAM_NOT_NULL, "OuterClientName");
        TmsAssert.notEmpty(distOrderCreateMessage.getOuterContactId(), ErrorCodeEnum.PARAM_NOT_NULL, "OuterContactId");
        //独立拣货属性校验
        if (Objects.equals(DistPickTypeEnum.BRAND_SINGLE.getCode(), distOrderCreateMessage.getPickType())){
            TmsAssert.notEmpty(distOrderCreateMessage.getOuterTenantId(), ErrorCodeEnum.PARAM_NOT_NULL, "OuterTenantId");
            TmsAssert.notEmpty(distOrderCreateMessage.getOuterBrandName(), ErrorCodeEnum.PARAM_NOT_NULL, "OuterBrandName");
        }
        //干配需要指定租户
        if(Objects.equals(DistOrderSourceEnum.OUTER_TRUNK_CITY.getCode(),source)){
            TmsAssert.notEmpty(distOrderCreateMessage.getOuterTenantId(), ErrorCodeEnum.PARAM_NOT_NULL, "OuterTenantId");
        }

    }
    public void validateDistOrderItem(List<DistOrderItemMessage> distOrderItemList){
        TmsAssert.notEmpty(distOrderItemList, ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemList");
        for (DistOrderItemMessage distOrderItemMessage : distOrderItemList) {
            TmsAssert.notEmpty(distOrderItemMessage.getOuterItemId(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.OuterItemId");
            //TmsAssert.notEmpty(distOrderItemMessage.getOuterItemType(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.OuterItemType");
            TmsAssert.notEmpty(distOrderItemMessage.getOuterItemName(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.OuterItemName");
            TmsAssert.notNull(distOrderItemMessage.getOuterItemPrice(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.OuterItemPrice");
            TmsAssert.notNull(distOrderItemMessage.getVolume(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.Volume");
            TmsAssert.notNull(distOrderItemMessage.getWeight(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.Weight");
            TmsAssert.notNull(distOrderItemMessage.getQuantity(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.Quantity");
            TmsAssert.notNull(distOrderItemMessage.getTemperature(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.Temperature");
            //TmsAssert.notEmpty(distOrderItemMessage.getSpecification(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.Specification");
            TmsAssert.notEmpty(distOrderItemMessage.getUnit(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.Unit");
            TmsAssert.notNull(distOrderItemMessage.getType(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.Type");
            TmsAssert.notNull(distOrderItemMessage.getDeliveryType(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderItemMessage.DeliveryType");
        }
    }

    public void validateDistSite(DistSiteMessage distSiteMessage){
        TmsAssert.notNull(distSiteMessage, ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage");
        TmsAssert.notNull(distSiteMessage.getType(), ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage.Type");
        TmsAssert.notEmpty(distSiteMessage.getOutBusinessNo(), ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage.OutBusinessNo");
        //展示地址全称类型集合
        List<Integer> types = Arrays.asList(TmsSiteTypeEnum.CUSTOMER.getCode(), TmsSiteTypeEnum.SAAS.getCode());
        List<Integer> poiTypes = Collections.singletonList(TmsSiteTypeEnum.CUSTOMER.getCode());
        if (types.contains(distSiteMessage.getType())){
            TmsAssert.notEmpty(distSiteMessage.getProvince(), ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage.Province");
            TmsAssert.notEmpty(distSiteMessage.getCity(), ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage.City");
            TmsAssert.notEmpty(distSiteMessage.getAddress(), ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage.Address");
            TmsAssert.notEmpty(distSiteMessage.getPhone(), ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage.Phone");
            TmsAssert.notEmpty(distSiteMessage.getName(), ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage.Name");
        }
        if (poiTypes.contains(distSiteMessage.getType())){
            TmsAssert.notEmpty(distSiteMessage.getPoi(), ErrorCodeEnum.PARAM_NOT_NULL, "DistSiteMessage.Poi");
        }
    }

    public void validateOperator(String operatorId, String operator) {
        TmsAssert.notEmpty(operatorId, ErrorCodeEnum.PARAM_NOT_NULL, "OperatorId");
        TmsAssert.notEmpty(operator, ErrorCodeEnum.PARAM_NOT_NULL, "Operator");
    }
}
