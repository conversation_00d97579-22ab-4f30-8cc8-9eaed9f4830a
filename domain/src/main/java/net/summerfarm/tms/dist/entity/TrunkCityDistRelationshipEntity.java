package net.summerfarm.tms.dist.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Description: 干线城配单绑定关系<br/>
 * date: 2024/4/15 10:46<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrunkCityDistRelationshipEntity {


    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 干线委托单ID
     */
    private Long trunkDistId;

    /**
     * 城配委托单ID
     */
    private Long cityDistId;

    /**
     * 外部单号
     */
    private String outerOrderId;

}
