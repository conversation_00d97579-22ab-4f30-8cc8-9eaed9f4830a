package net.summerfarm.tms.dist.flatObject;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: 配送单宽表<br/>
 * date: 2025/1/17 14:24<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryNoteOrderFlatObject {
    /**
     * 委托单ID
     */
    private Long distId;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 配送站点ID
     */
    private Long deliverySiteId;

    /**
     * 品牌名称
     */
    private String outerBrandName;

    /**
     * 配送备注
     */
    private String sendRemark;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 店铺名称
     */
    private String merchantName;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 外部联系人ID
     */
    private String outContactId;

    /**
     * 外部租户ID（鲜沐为大客户ID，Saas为租户ID）
     */
    private String outerTenantId;

    /**
     * 店铺ID
     */
    private String merchantId;

    /**
     * 门店 1大客户\2大连锁3\小连锁\4单点
     */
    private String merchantSize;

    /**
     * 外部联系人名称
     */
    private String contactName;

    /**
     * 外部联系人电话
     */
    private String contactPhone;

    /**
     * 外部联系人地址
     */
    private String deliveryAddress;

    /**
     * 外部联系人地址Id
     */
    private Long endSiteId;

    /**
     * 路线编码
     */
    private String pathCode;

    /**
     * 路线序列
     */
    private String pathSequence;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 配送单明细
     */
    private List<DeliveryNoteOrderItemFlatObject> items;


    // ===============外部属性====================
    /**
     * 是否显示价格 ture 展示 false 不展示
     */
    private Boolean showPriceFlag;

    /**
     * 是否打印配送单 ture 需要 false 不需要
     */
    private Boolean deliveryNotePrintFlag;

    /**
     * 客户经理名称
     */
    private String bdName;

    /**
     * 客户经理电话
     */
    private String bdPhone;

    /**
     * 订单备注
     */
    private String orderRemark;

    /**
     * 大客户ID
     */
    private Long bigCustomerId;

    /**
     * 销售主体名称
     */
    private String sellingEntityName;

    /**
     * 履约配送方式  0：干配，1：自提，2：干线 ，3：快递 ，4：干线转运
     */
    private Integer fulfillmentDeliveryWay;
}
