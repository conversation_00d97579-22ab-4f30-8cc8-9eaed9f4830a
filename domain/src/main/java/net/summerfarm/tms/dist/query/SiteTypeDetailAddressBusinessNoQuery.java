package net.summerfarm.tms.dist.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 点位类型详情地址业务号查询
 * date: 2025/7/3 16:34<br/>
 *
 * <AUTHOR> />
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SiteTypeDetailAddressBusinessNoQuery {

    /**
     * 省
     */
    private String provice;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 地址
     */
    private String address;

    /**
     * 点位
     */
    private String poi;

    /**
     * 电话
     */
    private String phone;

    /**
     * 名称
     */
    private String name;

    /**
     * 点位类型，0：客户，1：城配仓，2：库存仓，3：监管仓，4：采购地址，5：Saas，6：指定地址
     */
    private Integer type;

    private String outBusinessNo;
}
