package net.summerfarm.tms.dist.flatObject;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Description: 配送单明细宽表<br/>
 * date: 2025/1/17 14:24<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryNoteOrderItemFlatObject {

    /**
     * 委托单明细ID
     */
    private String distItemId;

    /**
     * SKU
     */
    private String sku;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 规格
     */
    private String specification;

    /**
     * 重量
     */
    private String weight;

    /**
     * 体积
     */
    private String volume;

    /**
     * 温区,0:未分类,1:冷冻,2:冷藏,3:常温,4:顶汇大流通
     */
    private Integer temperature;

    /**
     * 单价
     */
    private BigDecimal price;

    //------------其他属性------------------

    /**
     * 货品名称
     */
    private String productName;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer skuExtType;

    /**
     * 温区,0:未分类,1:冷冻,2:冷藏,3:常温,4:顶汇大流通
     */
    private String temperatureStr;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

}
