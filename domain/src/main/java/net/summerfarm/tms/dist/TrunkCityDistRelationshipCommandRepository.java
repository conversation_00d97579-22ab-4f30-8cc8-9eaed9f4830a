package net.summerfarm.tms.dist;

import net.summerfarm.tms.dist.entity.TrunkCityDistRelationshipEntity;

/**
 * Description: 干线城配委托单操作类<br/>
 * date: 2024/4/12 18:52<br/>
 *
 * <AUTHOR> />
 */
public interface TrunkCityDistRelationshipCommandRepository {
    /**
     * 新增数据
     * @param relationshipEntity 干线城配关系实体
     */
    void save(TrunkCityDistRelationshipEntity relationshipEntity);

    /**
     * 根据外单Id删除外单数据
     * @param outerOrderId 外单Id
     */
    void deleteByOuterOrderId(String outerOrderId);
}
