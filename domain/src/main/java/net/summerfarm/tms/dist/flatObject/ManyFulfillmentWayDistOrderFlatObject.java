package net.summerfarm.tms.dist.flatObject;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 多种履约方式委托单信息
 * date: 2025/7/18 15:55<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ManyFulfillmentWayDistOrderFlatObject {
    /**
     * 期望配送日期
     */
    private LocalDateTime expectBeginTime;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 订单号和配送方式
     */
    private String orderNoWithFulfillmentWay;

}
