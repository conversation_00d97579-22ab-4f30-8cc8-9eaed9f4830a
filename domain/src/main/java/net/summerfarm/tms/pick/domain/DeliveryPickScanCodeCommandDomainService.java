package net.summerfarm.tms.pick.domain;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliveryPickRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;
import net.summerfarm.tms.delivery.entity.DeliveryPickScanCodeEntity;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.facade.wms.dto.SkuBatchCodeTraceDTO;
import net.summerfarm.tms.pick.repository.DeliveryPickScanCodeCommandRepository;
import net.summerfarm.tms.pick.repository.DeliveryPickScanCodeQueryRepository;
import net.summerfarm.wms.skucodetrace.enums.SkuBatchCodeTraceStateEnum;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2024/8/14 15:16<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryPickScanCodeCommandDomainService {

    private final DeliveryPickScanCodeCommandRepository deliveryPickScanCodeCommandRepository;
    private final DeliveryPickScanCodeQueryRepository deliveryPickScanCodeQueryRepository;
    private final WmsQueryFacade wmsQueryFacade;
    private final DeliveryPickRepository deliveryPickRepository;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final SiteRepository siteRepository;

    /**
     * 拣货扫码
     * @param entity 拣货扫码
     * @return 拣货扫码信息
     */
    public DeliveryPickScanCodeEntity pickScan(DeliveryPickScanCodeEntity entity) {
        String onlyCode = entity.getOnlyCode();
        Long batchId = entity.getDeliveryBatchId();

        // 拣货扫码校验
        if(batchId == null){
            throw new TmsRuntimeException("批次ID不能为空");
        }

        if(StringUtils.isBlank(onlyCode)){
            throw new TmsRuntimeException("唯一码不能为空");
        }

        // 查询WMS获取唯一溯源码信息
        SkuBatchCodeTraceDTO skuBatchCodeTraceDTO = wmsQueryFacade.queryBatchTraceCode(onlyCode);
        if(skuBatchCodeTraceDTO == null){
            throw new TmsRuntimeException("唯一溯源码码不存在");
        }

        // 是否称重
        Integer state = skuBatchCodeTraceDTO.getState();
        if(!Objects.equals(SkuBatchCodeTraceStateEnum.HAVE_WEIGHT.getValue(),state)){
            throw new TmsRuntimeException("该溯源码未称重");

        }

        String pathCode = skuBatchCodeTraceDTO.getPathCode();
        Integer storeNo = skuBatchCodeTraceDTO.getStoreNo();
        if(StringUtils.isBlank(pathCode) || storeNo == null){
            throw new TmsRuntimeException("溯源码不存在路线信息和城配仓信息");
        }

        // 是否已经被扫过
        DeliveryPickScanCodeEntity deliveryPickScanCodeEntity = deliveryPickScanCodeQueryRepository.queryByOnlyCode(onlyCode);
        if(deliveryPickScanCodeEntity != null){
            throw new TmsRuntimeException("该溯源码已经扫过");
        }

        // 是否此路线
        List<DeliveryPickEntity> deliveryPickEntities = deliveryPickRepository.queryByBatchIdSku(batchId, skuBatchCodeTraceDTO.getSku());

        if(CollectionUtils.isEmpty(deliveryPickEntities)){
            throw new TmsRuntimeException("此拣货明细不存在");
        }
        if(deliveryPickEntities.size() > 1){
            throw new BizException("此"+skuBatchCodeTraceDTO.getSku()+"拣货明细存在多条,请联系管理员");
        }

        DeliveryBatchEntity batchEntity = deliveryBatchRepository.query(batchId);
        if(batchEntity == null || batchEntity.getId() == null){
            throw new TmsRuntimeException("配送批次不存在");
        }
        // 城配仓校验
        Map<Long, SiteEntity> siteEntityMap = siteRepository.querySiteStoreMapByOutBusinessNos(Collections.singletonList(String.valueOf(storeNo)));
        if(siteEntityMap.get(batchEntity.getBeginSiteId()) == null){
            throw new TmsRuntimeException("此商品不归属当前司机的城配仓");
        }
        // 路线校验
        if(!Objects.equals(pathCode,batchEntity.getPathCode())){
            throw new TmsRuntimeException("此商品路线不归属当前司机");
        }

        DeliveryPickEntity deliveryPickEntity = deliveryPickEntities.get(0);
        // 保存
        entity.setDeliveryBatchId(batchEntity.getId());
        entity.setSiteId(deliveryPickEntity.getSiteId());
        entity.setOutItemId(deliveryPickEntity.getOutItemId());
        entity.setItemDesc(deliveryPickEntity.getItemDesc());
        entity.setPickId(deliveryPickEntity.getId());

        DeliveryPickScanCodeEntity deliveryPickScanCodeSave = deliveryPickScanCodeCommandRepository.save(entity);
        // 更新拣货扫码明细
        deliveryPickRepository.scanCodeAdd(deliveryPickEntity);

        return deliveryPickScanCodeSave;
    }

}
