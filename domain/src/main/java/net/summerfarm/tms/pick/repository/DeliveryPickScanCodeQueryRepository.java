package net.summerfarm.tms.pick.repository;

import net.summerfarm.tms.delivery.entity.DeliveryPickScanCodeEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2024/8/14 15:11<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryPickScanCodeQueryRepository {
    /**
     * 根据溯源码查询是否存在
     * @param onlyCode 溯源码
     * @return 结果
     */
    DeliveryPickScanCodeEntity queryByOnlyCode(String onlyCode);

    /**
     * 根据配送批次id查询拣货扫码信息
     *
     * @param deliveryBatchId
     * @return
     */
    List<DeliveryPickScanCodeEntity> queryByDeliveryBatchId(Long deliveryBatchId);
}
