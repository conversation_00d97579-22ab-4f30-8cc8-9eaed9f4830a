package net.summerfarm.tms.path;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.enums.TmsPathStatusEnum;
import net.summerfarm.tms.path.dto.*;
import net.summerfarm.tms.query.path.PathQuery;
import net.summerfarm.tms.query.path.QueryPathFilterDTO;

import java.time.LocalDate;

/**
 * 线路api
 */
public interface TmsPathService {

    /**
     * 查询线路列表
     *
     * @param pathQuery
     * @return
     */
    TmsResult<PageInfo<TmsPathDTO>> queryList(PathQuery pathQuery);

    /**
     * 关闭线路
     *
     * @param pathId
     * @return
     */
    TmsResult<Void> closePath(Long pathId);

    /**
     * 删除线路
     *
     * @param pathId
     * @return
     */
    TmsResult<Void> deletePath(Long pathId);


    /**
     * 查询线路详情
     *
     * @param pathId
     * @return
     */
    TmsResult<TmsPathDTO> queryDetail(Long pathId);


    /**
     * 保存线路
     *
     * @param tmsPathDTO
     * @return
     */
    TmsResult<Void> temporarySave(TmsPathDTO tmsPathDTO);

    /**
     * 保存线路
     *
     * @param tmsPathDTO 路由信息
     * @param bizDate    履约时间
     * @param pathCarDTO 车次信息
     * @return
     */
    TmsResult<DeliveryBatchEntity> createDeliveryBatchByPath(TmsPathDTO tmsPathDTO, LocalDate bizDate, PathCarDTO pathCarDTO);

    /**
     * 保存路由信息
     *
     * @param createPathDTO 创建路由请求参数
     * @return
     */
    TmsResult<Void> save(CreatePathCommand createPathDTO);


    /**
     * 更新路由信息
     *
     * @param updatePathDTO
     * @return
     */
    TmsResult<Void> update(UpdatePathCommand updatePathDTO);

    /**
     * 查询路由详情
     *
     * @param pathId 路由id
     * @return
     */
    PathDTO queryNewDetail(Long pathId);

    /**
     * 查询路由列表接口
     *
     * @param filterDTO
     * @return
     */
    PageInfo<PathListDTO> queryNewList(QueryPathFilterDTO filterDTO);


    /**
     * 更新路由状态
     *
     * @param pathId
     * @param statusEnum
     */
    void updateStatus(Long pathId, TmsPathStatusEnum statusEnum);


}
