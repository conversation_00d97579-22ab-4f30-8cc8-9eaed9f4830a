package net.summerfarm.tms.path.dto;

import lombok.Data;
import net.summerfarm.tms.base.site.dto.SiteDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TmsPathDTO {
    /**
     * 路线ID
     */
    Long pathId;
    /**
     * 路线名称
     */
    String pathName;

    /**
     * 周期 每X周
     */
    Integer periodWeek;

    /**
     * 周期 星期X
     */
    List<Integer> periodWeekday;

    /**
     * 承运日期,当天0,前一天 -1
     */
    Integer deliveryDayDiff;
    /**
     * 承运时间 hh:mm
     */
    String deliveryDayTime;
    /**
     * 承运商ID
     */
    Long carrierId;
    /**
     * 承运商名称
     */
    String carrierName;
    /**
     * 司机ID
     */
    Long driverId;
    /**
     * 司机名称
     */
    String driverName;
    /**
     * 司机手机号
     */
    String driverPhone;
    /**
     * 车辆ID
     */
    Long carId;
    /**
     * 车牌号
     */
    String carNum;
    /**
     * 车型
     */
    Integer carType;
    /**
     * 车型描述
     */
    String carTypeDesc;
    /**
     * 状态
     */
    Integer status;
    /**
     * 状态描述
     */
    String statusDesc;

    /**
     * 预估费用
     */
    BigDecimal estimateFare;
    /**
     * 创建时间
     */
    LocalDateTime createTime;
    /**
     * 创建人
     */
    String creator;
    /**
     * 是否自动生成调度单
     */
    Integer autoSwitch;
    /**
     * 类型
     *
     * @see net.summerfarm.tms.enums.TmsPathStatusEnum
     */
    private Integer type;
    /**
     * 路线节点列表
     */
    List<SiteDTO> siteList;
    /**
     * 路线 边信息
     */
    List<TmsPathSectionDTO> pathSectionList;

    /**
     * 报价信息
     */
    List<PathQuotationDTO> pathQuotationList;

    /**
     * 车次信息
     */
    List<PathCarDTO> pathCarList;
    /**
     * 路线编码
     */
    private String pathCode;

    /**
     * 总距离
     */
    private BigDecimal distance;
}
