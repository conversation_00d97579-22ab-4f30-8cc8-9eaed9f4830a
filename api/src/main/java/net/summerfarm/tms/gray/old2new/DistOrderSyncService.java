package net.summerfarm.tms.gray.old2new;

import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.client.message.in.*;
import net.summerfarm.tms.gray.dto.OldDistOrderDTO;

import java.time.LocalDate;
import java.util.List;

/**
 * Description:委托单同步接口
 * date: 2022/11/7 14:41
 *
 * <AUTHOR>
 */
public interface DistOrderSyncService {

    /**
     * 数据同步结果比对
     * @param beginDate 开始日期
     * @param noSyncDataFlag 是否需要同步未同步数据标识
     * @param storeNo 城配仓编号
     * @return  处理结果
     */
    TmsResult<Void> dataSyncResultCompare(LocalDate beginDate, Boolean noSyncDataFlag, Integer storeNo);

    /**
     * 删除重复委托单
     * @param distIds 委托单IDs
     * @return 处理结果
     */
    TmsResult<Void> removeRepeatDistOrder(List<Long> distIds);

    /**
     * 同步委托单
     * @param oldDistOrderDTO 老模型委托单DTO
     * @return 同步结果,true:已同步,false:无需同步
     */
    boolean syncDistOrder(OldDistOrderDTO oldDistOrderDTO);

    /**
     * 创建城配委托单
     * @param distOrderCreateMessage
     */
    void createDistOrder(DistOrderCreateMessage distOrderCreateMessage);

    /**
     * 变更城配委托单
     * @param distOrderChangeMessage
     */
    void changeDistOrder(DistOrderChangeMessage distOrderChangeMessage);

    /**
     * 取消城配委托单
     * @param distOrderCancelMessage
     */
    void cancelDistOrder(DistOrderCancelMessage distOrderCancelMessage);

    /**
     * 取消城配委托单明细
     * @param distOrderDetailCancelMessage
     */
    void cancelDistOrderDetail(DistOrderDetailCancelMessage distOrderDetailCancelMessage);

    /**
     * 配送单-配送点位 数据同步结果比对
     * @param beginDate 开始日期
     * @param noSyncDataFlag 是否需要同步未同步数据标识
     * @return  处理结果
     */
    TmsResult<Void> deliverySiteSyncCompare(LocalDate beginDate, Boolean noSyncDataFlag);
}
