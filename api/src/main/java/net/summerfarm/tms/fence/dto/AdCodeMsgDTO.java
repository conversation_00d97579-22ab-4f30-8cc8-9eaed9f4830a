package net.summerfarm.tms.fence.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/3/16 15:50<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AdCodeMsgDTO {
    private Integer id;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 等级
     */
    private String level;

    /**
     * 高德id
     */
    private String gdId;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    /**
     * 状态 0 正常 1 失效
     */
    private Integer status;

    /**
     * 围栏id
     */
    private Integer fenceId;

}
