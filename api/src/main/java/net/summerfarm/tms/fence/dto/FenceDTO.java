package net.summerfarm.tms.fence.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/16 15:17<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FenceDTO {

    private Integer id;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 运营范围
     */
    private Integer areaNo;

    /**
     * 状态 0正常 1失效
     */
    private Integer status;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private Integer adminId;

    /**
     * 打包id
     */
    private Integer packId;

    /**
     * 0 新建 1 拆分
     */
    private Integer type;

    /**
     * 省市区信息
     */
    private List<AdCodeMsgDTO> adCodeMsgDTOS;

}
