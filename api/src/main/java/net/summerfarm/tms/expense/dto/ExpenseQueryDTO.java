package net.summerfarm.tms.expense.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.tms.anno.CityStoreNo;
import net.summerfarm.tms.base.AbstractPageQuery;
import net.summerfarm.tms.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 费用查询请求参数
 *
 * <AUTHOR>
 * @Date 2023-02-24
 **/
@Data
public class ExpenseQueryDTO extends AbstractPageQuery implements Serializable {
    private static final long serialVersionUID = 5410281439790542760L;
    /**
     * id
     */
    private Integer id;

    @ApiModelProperty(value = "是否复核")
    private Integer isReview;

    @ApiModelProperty(value = "城配仓")
    @CityStoreNo
    private Integer storeNo;

    @ApiModelProperty("配送时间")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryTime;

    @ApiModelProperty(value = "配送时间(支持配送时间段搜索)")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryEndTime;

    @ApiModelProperty(value = "审核状态")
    private Integer status;

    @ApiModelProperty(value = "店铺名称")
    private String mname;

    @ApiModelProperty(value = "接收报销类型(多选筛选)")
    private String types;

    @ApiModelProperty(value = "报销类型(多选筛选)")
    private List<String> typeList;

    @ApiModelProperty(value = "创建时间")
    private LocalDate createTime;

    @CityStoreNo(needPerStoreNos = true)
    private List<String> storeNos;
}
