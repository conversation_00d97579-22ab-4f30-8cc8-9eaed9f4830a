package net.summerfarm.tms.expense.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.tms.anno.CityStoreNo;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-11
 */
@Data
public class Expense implements Serializable {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "司机id")
    private Integer driverId;

    @ApiModelProperty(value = "配送信息id")
    private Integer deliveryPathId;

    @ApiModelProperty("配送时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryTime;

    @ApiModelProperty(value = "状态")
    private Integer state;

    @ApiModelProperty(value = "报销类型")
    private Integer type;

    @ApiModelProperty(value = "城配仓")
    @CityStoreNo
    private Integer storeNo;

    @ApiModelProperty(value = "是否复核")
    private Integer isReview;

    @ApiModelProperty(value = "店铺id")
    private Long mId;

    @ApiModelProperty(value = "店铺名称")
    private String mname;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "修改时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 状态意义
     * @see 
     */
    @ApiModelProperty(value = "审核状态")
    private Integer status;
    @ApiModelProperty("审核失败原因")
    private String reason;

    /**
     * 城配仓编号
     */
    @CityStoreNo(needPerStoreNos = true)
    private List<String> storeNos;

    public Expense() {
    }

    public Expense(Integer deliveryPathId, LocalDate deliveryTime, Integer driverId, Integer state, Integer storeNo, Integer isaReview, Long mId, String mname, LocalDateTime createTime,Integer status) {
        this.deliveryPathId = deliveryPathId;
        this.driverId = driverId;
        this.storeNo = storeNo;
        this.state = state;
        this.isReview = isaReview;
        this.mId = mId;
        this.createTime = createTime;
        this.mname = mname;
        this.deliveryTime = deliveryTime;
        this.status = status;
    }
}
