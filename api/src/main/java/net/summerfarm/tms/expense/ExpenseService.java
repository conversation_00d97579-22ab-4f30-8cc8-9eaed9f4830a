package net.summerfarm.tms.expense;

import com.github.pagehelper.PageInfo;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.tms.expense.dto.DeliveryExpenseRecordVO;
import net.summerfarm.tms.expense.dto.ExpenseQueryDTO;
import net.summerfarm.tms.expense.dto.ExpenseVO;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-11
 */
public interface ExpenseService {
    /**
     * 提交报销单
     * @param expense
     * @return
     */
    AjaxResult insertExpenseDetail(ExpenseVO expense);

    /**
     * 查询报销明细
     * @param id
     * @return
     */
    AjaxResult selectExpenseDetail(Integer id);

    /**
     * 计算里程
     * @param startAddress
     * @param endAddress
     * @return
     */
    AjaxResult getMileage(String startAddress, String endAddress);

    /**
     * 修改报销明细
     * @param expense
     * @return
     */
    AjaxResult updateExpenseDetail(ExpenseVO expense);

    /**
     * 查询审核信息
     *
     * @return 费用报销审核信息
     */
    AjaxResult selectAuditExpense();

    /**
     * 查询费用列表信息
     *
     * @param expenseQueryDTO
     * @return
     */
    PageInfo<ExpenseVO> query(ExpenseQueryDTO expenseQueryDTO);

    /**
     * 查看费用报销审核详情
     *
     * @param expenseId 费用id
     * @return
     */
    DeliveryExpenseRecordVO  getAuditDetail(Integer expenseId);


    /**
     * 报销查询导出
     * @param selectKeys
     * @return
     */
    void selectExpenseExport(ExpenseVO selectKeys);

}
