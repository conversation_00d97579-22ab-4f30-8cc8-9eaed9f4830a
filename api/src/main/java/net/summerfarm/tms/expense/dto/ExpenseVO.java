package net.summerfarm.tms.expense.dto;


import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.tms.anno.CityStoreNo;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 费用对象参数
 *
 * <AUTHOR> xiang
 * create at:  2021-11-12
 */
@Data
public class ExpenseVO extends Expense {
    /**
     * 报销明细
     */
    private List<ExpenseDetailVO> expenseDetails;

    /**
     * 报销明细
     */
    private List<ExpenseDetail> expenseDetailList;

    /**
     * 城配仓名称
     */
    private String storeName;

    @ApiModelProperty(value = "配送时间(支持配送时间段搜索)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryEndTime;

    /**
     * 审批时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvalTime;

    @ApiModelProperty(value = "接收报销类型(多选筛选)")
    private String types;

    @ApiModelProperty(value = "报销类型(多选筛选)")
    private List<String> typeList;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 配送方式 0-正常配送 1-专车配送
     */
    private Integer sendWay;

    /**
     * 审核记录
     */
    private DeliveryExpenseRecordVO auditRecord;

    @CityStoreNo(needPerStoreNos = true)
    private List<String> storeNos;
}
