package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.DeliveryBatchStatusEnum;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 配送批次(调度单)
 */
@Data
public class DeliveryBatchDTO extends BaseObject {

    private static final long serialVersionUID = 6365812400800468160L;

    /**
     * 批次号(调度单号)
     */
    private Long deliveryBatchId;
    /**
     * @see DeliveryBatchTypeEnum
     * 调度单类型
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer type;
    /**
     * 履约时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 承运时间(开始)
     */
    private LocalDateTime beginTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 预估费用
     */
    private BigDecimal estimateFare;
    /**
     * 承运商ID
     */
    private Long carrierId;
    /**
     * 承运商
     */
    private String carrierName;
    /**
     * 司机ID
     */
    private Long driverId;
    /**
     * 司机名称
     */
    private String driver;
    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 车辆id
     */
    private Long carId;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车型描述
     */
    private String carType;
    /**
     * 车型
     */
    private Integer carTypeCode;

    /**
     * 车辆体积
     */
    private BigDecimal carVolume;

    /**
     * 车辆存储条件
     */
    private String storageName;

    /**
     * 车辆存储条件
     */
    private Integer storageCode;


    /**
     * @see DeliveryBatchStatusEnum
     * 状态
     */
    private Integer status;
    /**
     * 状态描述
     */
    private String statusDesc;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建人id
     */
    private Integer createId;
    /**
     * 创建时间
     */
    private LocalDateTime creatTime;
    /**
     * 关闭原因
     */
    private String closeReason;

    /**
     * 关闭人
     */
    private String closeUser;
    /**
     * 线路名称
     */
    private String pathName;
    /**
     * 路线编码
     */
    private String pathCode;
    /**
     * 运输路线/点位
     */
    private List<DeliverySiteDTO> deliverySiteDTOList;

    /**
     * 配送单信息
     */
    private List<DeliveryOrderDTO> deliveryOrderDTOList;
    /**
     * 关联的承运单
     */
    private List<TrunkDeliveryDistOrderDTO> trunkDeliveryDistOrderDTOS;
    /**
     * 委托单信息
     */
    private List<DistOrderDTO> distOrderDTOList;

    /**
     * 多个批次号(调度单号)
     */
    private List<Long> deliveryBatchIds;

    /**
     * 承运单id
     */
    private Long distId;

    /**
     * 总冷冻重量
     */
    private BigDecimal freezeWeightTotal;

    /**
     * 总冷冻体积
     */
    private BigDecimal freezeVolumeTotal;

    /**
     * 总冷冻件数
     */
    private BigDecimal freezeQuantityTotal;

    /**
     * 总冷藏重量
     */
    private BigDecimal coldWeightTotal;

    /**
     * 总冷藏体积
     */
    private BigDecimal coldVolumeTotal;

    /**
     * 总冷藏件数
     */
    private BigDecimal coldQuantityTotal;

    /**
     * 总常温重量
     */
    private BigDecimal normalWeightTotal;

    /**
     * 总常温体积
     */
    private BigDecimal normalVolumeTotal;

    /**
     * 总常温件数
     */
    private BigDecimal normalQuantityTotal;
    /**
     * 总重量
     */
    private BigDecimal weightTotal;

    /**
     * 总体积
     */
    private BigDecimal volumeTotal;

    /**
     * 总件数
     */
    private BigDecimal quantityTotal;

    /**
     * 起点点位名称
     */
    private String beginSiteName;
    /**
     * 起点点位ID
     */
    private Long beginSiteId;

    /**
     * 终点点位名称
     */
    private String endSiteName;
    /**
     * 终点点位ID
     */
    private Long endSiteId;

    /**
     * 批次排线点位数
     */
    private Integer totalSiteNum;

    /**
     * 批次排线获取价格
     */
    private BigDecimal priceTotal;

    /**
     * 签发数据
     */
    private List<DeliveryPickDTO> deliveryPickDTOS;

    /**
     * 开始点位信息
     */
    private SiteDTO beginSiteDto;
    /**
     * 线路ID
     */
    private Long pathId;

    /**
     * 路线满载率
     */
    private BigDecimal pathFullLoadRatio;

    /**
     * 总距离 km
     */
    private BigDecimal planTotalDistance;

    /**
     * 实际总距离 km
     */
    private BigDecimal realTotalDistance;

    /**
     * 打车点位数
     */
    private long taxiSize;

    /**
     * 商品数
     */
    private long skuNum;

    /**
     * 打车费
     */
    private BigDecimal taxiMoney;

    /**
     * 帮采费
     */
    private BigDecimal buyMoney;

    /**
     * sku数
     */
    private long skuCnt;

    /**
     * 拦截标识
     * 0 无回收拦截关闭  1 有回收拦截关闭
     */
    private Integer interceptFlag;
    /**
     * 完成捡货时间
     */
    private LocalDateTime pickUpTime;
    /**
     * 完成排线时间
     */
    private LocalDateTime bePathTime;
    /**
     * 智能排线总距离 km
     */
    private BigDecimal intelligenceTotalDistance;

    /**
     * 出发打卡照片
     */
    private String signOutPic;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;
    /**
     * 冷藏照片
     */
    private String refrigeratePics;

    /**
     * 冷冻照片
     */
    private String freezePics;

    /**
     * 保温措施图片
     */
    private String keepTemperatureMethodPics;

    /**
     * 出发温度
     */
    private BigDecimal signOutTemperature;
    /**
     * 实际出发时间
     */
    private LocalDateTime signOutTime;
    /**
     * 出发打卡备注
     */
    private String signOutRemark;

    /**
     * 区域
     */
    private String area;

    /**
     * 班次 0正常 1加班
     */
    private Integer classes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 运费明细项集合
     */
    private List<DeliveryBatchFareDTO> deliveryBatchFareDTOList;

    /**
     * 体积装载率
     */
    private BigDecimal volumeLoadRatio ;

    /**
     * 重量装载率
     */
    private BigDecimal weightLoadRatio ;

    /**
     * 件数装载率
     */
    private BigDecimal quantityLoadRatio;

    /**
     * 关联批次集合
     */
    private List<DeliveryBatchRelationDTO> deliveryBatchRelationDTOList;

    /**
     * 完成配送时间
     */
    private LocalDateTime finishDeliveryTime;
    /**
     * 城配仓编号
     */
    private Integer storeNo;
    /**
     * 承运商品类型，0：标品，1：水果
     */
    private Integer carryType;
    /**
     * 承运商品类型描述
     */
    private String carryTypeDesc;

    /**
     * 是否需要扫码标识
     * true 需要扫码，false无需扫码
     */
    private Boolean needScanCodeFlag;
}
