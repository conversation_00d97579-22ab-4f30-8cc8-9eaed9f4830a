package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

@Data
public class DeliveryOrderDTO extends BaseObject {

    private static final long serialVersionUID = 8946208695208317120L;

    /**
     * 配送状态，10：未签收，20：已签收，30：签收异常
     */
    private Integer status;
    /**
     * 状态描述
     */
    private String statusDesc;
    /**
     * 签收照片
     */
    private String signInPic;
    /**
     * 配送单id
     */
    Long id;
    /**
     * 承运单id
     */
    Long distOrderId;
    /**
     * 批次id
     */
    Long deliveryBatchId;

    /**
     * 起点Id
     */
    private Long beginSiteId;
    /**
     * 起点名称
     */
    private String beginSiteName;
    /**
     * 终点Id
     */
    private Long endSiteId;
    /**
     * 终点名称
     */
    private String endSiteName;
    /**
     * 外部单号
     */
    private String outerOrderId;
}
