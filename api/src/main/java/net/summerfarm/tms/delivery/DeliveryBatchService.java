package net.summerfarm.tms.delivery;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.summerfarm.tms.message.out.CompletePathMessage;
import net.summerfarm.tms.path.dto.TmsPathDTO;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryBatchRelateQuery;
import net.summerfarm.tms.query.delivery.DeliveryPathQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public interface DeliveryBatchService {

    /**
     * 新增调度单
     *
     * @param deliveryOrderCommond 保存实体类
     * @return 无返回
     */
    TmsResult<Void> deliveryBatchSave(DeliveryOrderSaveCommond deliveryOrderCommond);

    /**
     * 查询调度单详情
     *
     * @param deliveryBatchId
     * @return
     */
    TmsResult<DeliveryBatchDTO> deliveryBatchDetail(Long deliveryBatchId);

    /**
     * 更新调度单
     *
     * @param deliveryOrderCommond
     * @return
     */
    TmsResult<Void> deliveryBatchUpdate(DeliveryOrderUpdateCommand deliveryOrderCommond);

    /**
     * 关闭调度单
     *
     * @param deliveryBatchId 调度单id
     * @param closeReason     关闭原因
     * @return 无返回
     */
    TmsResult<Void> deliveryBatchClose(Long deliveryBatchId, String closeReason);

    /**
     * 分页查询调度单
     *
     * @param deliveryBatchQuery 查询
     * @return 分页数据
     */
    TmsResult<PageInfo<DeliveryBatchDTO>> list(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 按条件匹配配送批次,无法分页,全量计算
     *
     * @param deliveryBatchQuery 查询
     * @return 分页数据
     */
    TmsResult<List<DeliveryBatchDTO>> matchDeliveryBatch(List<DeliveryBatchDTO> deliveryBatchList, List<Long> deliverySiteIds);

    /**
     * 承运单智能推荐分页
     *
     * @param deliveryBatchQuery 查询
     * @return 返回结果
     */
    TmsResult<PageInfo<TrunkDeliveryDistOrderDTO>> intelligentDistPage(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 完成配送
     *
     * @param deliveryBatchId 配送批次id
     * @return 返回结果
     */
    TmsResult<Void> finishDelivery(Long deliveryBatchId);

    /**
     * 新增路线批次信息
     *
     * @param deliveryBatchDTO 请求数据
     * @return 返回结果
     */
    TmsResult<TmsPathDTO> addSiteBatch(DeliveryBatchDTO deliveryBatchDTO);

    /**
     * 完成排线
     *
     * @param deliveryBatchDTOS 完成排线请求数据
     * @return 返回结果
     */
    TmsResult<Void> completePath(ArrayList<DeliveryBatchDTO> deliveryBatchDTOS);

    /**
     * 路线配送师傅修改
     *
     * @param deliveryBatchDTO 批次信息
     * @return 结果
     */
    TmsResult<Void> updatePathDriver(DeliveryBatchDTO deliveryBatchDTO);

    /**
     * 修改路线名称
     *
     * @param deliveryBatchDTO 批次信息
     * @return 结果
     */
    TmsResult<Void> updatePathName(DeliveryBatchDTO deliveryBatchDTO);

    /**
     * 查询排线信息
     * @param deliveryPathQuery 查询
     * @return
     */
    TmsResult<List<DeliveryBatchDTO>> getBatchDetailDelivery(DeliveryPathQuery deliveryPathQuery);

    /**
     * 查询批次
     * @param deliveryBatchQuery 查询
     * @return 结果
     */
    TmsResult<DeliveryBatchDTO> query(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 查询是否有未排线店铺
     * @param beginSiteId 开始点位id
     * @param deliveryTime 配送日期
     * @return
     */
    TmsResult<Boolean> isHaveNoPath(Long beginSiteId, LocalDate deliveryTime);

    /**
     * 更新调度单错误状态
     * @return
     */
    TmsResult<Void> initBatchStatus();

    /**
     * 完成排线通知商城侧
     * @param batchId 批次ID
     */
    void notifyCompletePathDeliveryEvents(Long batchId);

    /**
     * 上传预排名单
     * @param file 文件
     * @return
     */
    TmsResult<Void> prePathUpload(MultipartFile file);

    /**
     * 更新预计公里数
     * @param deliveryBatchQuery 查询
     * @return
     */
    TmsResult<Void> updateBatchDistance(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 获取当前司机批次信息
     * @return 结果
     */
    TmsResult<DeliveryBatchDTO> queryCurrentDriverBatch(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 获取当前司机批次信息
     * @param deliveryBatchQuery 查询
     * @return 结果
     */
    TmsResult<PageInfo<DeliveryBatchDTO>> queryCurrentDriverBatchPage(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 查询批次信息
     * @param deliveryBatchQuery
     */
    TmsResult<DeliveryBatchDTO> queryPickDetail(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 查询批次的配送信息
     * @param deliveryBatchQuery 条件
     * @return 结果
     */
    TmsResult<DeliveryBatchDTO> queryDeliverySiteList(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 分页查询运输任务
     * @param deliveryBatchQuery 查询
     * @return 分页数据
     */
    TmsResult<PageInfo<DeliveryBatchDTO>> queryTaskPage(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 查询运输任务详情
     * @param deliveryBatchId 配送批次ID
     * @return 运输任务详情
     */
    TmsResult<DeliveryBatchDTO> queryTaskDetail(Long deliveryBatchId);

    /**
     * 查看打卡记录
     * @param deliveryBatchId 配送批次ID
     * @return 打卡记录详情
     */
    TmsResult<List<DeliverySiteDTO>> punchDetail(Long deliveryBatchId);

    /**
     * 批量查询批次信息
     * @param distOrderList 委托单信息
     */
    void queryDistOrdersBatch(List<DistOrderDTO> distOrderList);

    /**
     * 城配智能排线
     * @param command 操作
     * @return 结果
     */
    TmsResult<Void> intelligentPath(IntelligentPathUpdateCommand command);

    /**
     * 查询批次信息
     * @param deliveryBatchQuery 查询
     * @return 结果
     */
    TmsResult<List<DeliveryBatchDTO>> queryList(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 完成排线通知Tms
     * @param batchId 批次id
     */
    void finishCompletePathNotifyTms(Long batchId);

    /**
     * 生成大客户拣货任务
     * @param batchId 批次id
     */
    void createBigCustomerPick(Long batchId);

    /**
     * 手动生成拣货任务
     *
     * @param batchId 批次id
     */
    void completePathBySelf(Long batchId);

    /**
     *发送符合条件的调度单钉钉消息提示
     */
    void sendUnSignBatchDeliverySiteMsg();

    /**
     * 批次校验
     * @param deliveryBatchId 批次id
     * @return 结果
     */
    Boolean deliveryBatchValidator(Long deliveryBatchId);

    /**
     * 不同批次处理
     */
    void diffBatchHandle();

    /**
     * 查询城配干线信息
     * @param batchId 批次Id
     * @return 结果
     */
    List<DeliveryBatchDTO> queryCityTrunkBatch(Long batchId);

    /**
     * 查询
     * @param deliveryBatchQuery 查询
     * @return 结果
     */
    DeliveryBatchDTO queryDriverCurrentInfo(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 完成排线通知总线
     *
     * @param completePathMessage 消息
     */
    void finishCompletePathAllMessageSend(CompletePathMessage completePathMessage);


    /**
     * 计算路径公里数
     *
     * @param calcTmsPathDistanceMessage
     */
    void calcPathDistance(CalcTmsPathDistanceMessage calcTmsPathDistanceMessage);

    /**
     * 出仓晚点监控
     * @param postDate 监控日期
     */
    void batchOutTimeMonitor(String postDate);

    /**
     * 出仓晚点监控数据下载
     * @param postDate 监控日期
     * @param response 响应对象
     */
    void batchOutTimeMonitorDown(String postDate, HttpServletResponse response);

    /**
     * 配送批次关联校验
     * @param deliveryBatchRelateValidateQuery 关联校验查询参数
     */
    void deliveryBatchRelateValidate(DeliveryBatchRelateValidateQuery deliveryBatchRelateValidateQuery);

    /**
     * 执行批次装载率计算
     * @param deliveryBatchId 配送批次ID
     */
    void executeBatchLoadRatioCalc(Long deliveryBatchId);
    /**
     * 调度单关联查询
     * @param deliveryBatchRelateQuery 查询
     * @return 结果
     */
    List<DeliveryBatchDTO> queryRelateBatchList(DeliveryBatchRelateQuery deliveryBatchRelateQuery);

    /**
     * 查询干线详情信息
     * @param deliveryBatchQuery 查询
     */
    List<DeliveryBatchDTO> queryTrunkDeliveryDetail(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 查询配送城配仓配送点位的时效
     * @param deliveryPathQuery 查询
     * @return
     */
    List<DeliverySiteDTO> queryDeliveryAgeing(DeliveryPathQuery deliveryPathQuery);

    /**
     * 查询配送批次详情
     * @param deliveryBatchId 配送批次ID
     * @return 结果
     */
    DeliveryBatchDTO queryDetail(Long deliveryBatchId);

    /**
     * 下载批次记录
     * @param deliveryBatchQuery 查询
     * @return 资源ID
     */
    Long downloadBatchRecord(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 下载预排名单
     * @param storeNo 城配仓编号
     * @return 资源ID
     */
    Long downloadPrePath(String storeNo);

    /**
     * 下载路线
     * @param storeNo 城配仓
     * @param time 配送日期
     * @return 资源ID
     */
    Long downloadPath(String storeNo, LocalDate time);

    /**
     * 下载路线商品
     * @param storeNo 城配仓
     * @param deliveryTime 配送日期
     * @return 资源ID
     */
    Long downloadPathProduct(String storeNo, LocalDate deliveryTime);

    /**
     * 根据批次ID获取城配仓
     * @param batchId 批次ID
     * @return 城配仓编号
     */
    Integer queryStoreNoById(Long batchId);

    /**
     * 后门批量更新城配仓智能排线配送距离
     * @param storeNo 城配仓编号
     * @param deliveryTime 配送日期
     * @param batchIds 批次ID
     */
    void backDoorUpdateBatchIntelligentDistance(Integer storeNo, LocalDate deliveryTime, List<Long> batchIds);
}
