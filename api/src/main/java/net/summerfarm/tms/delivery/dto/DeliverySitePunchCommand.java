package net.summerfarm.tms.delivery.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: 配送点位打卡参数类
 * date: 2022/12/2 16:11
 *
 * <AUTHOR>
 */
@Data
public class DeliverySitePunchCommand {

    /**
     * 运输点位ID集合
     */
    @NotEmpty(message = "deliverySiteIds不能为空")
    private List<Long> deliverySiteIds;
    /**
     * 签到距离
     */
    private BigDecimal signDistance;
    /**
     * 打卡地址Poi
     */
    @NotBlank(message = "signPoi不能为空")
    private String signPoi;
    /**
     * 打卡详细地址
     */
    @NotBlank(message = "signAddress不能为空")
    private String signAddress;
    /**
     * 打卡异常类型
     */
    private String signErrType;
    /**
     * 打卡照片
     */
    @NotBlank(message = "signPics不能为空")
    private String signPics;


//    @NotBlank(message = "封签照片不能为空")
    private String sealPics;
    
    /**
     * 打卡备注
     */
    private String signRemark;
    /**
     * 打卡温度
     */
    private BigDecimal signTemperature;

}
