package net.summerfarm.tms.delivery.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DeliverySiteItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 运输单id
     */
    private Long deliverySiteId;

    /**
     * 外部条目id
     */
    private String outItemId;

    /**
     * 商品名称
     */
    private String outItemName;

    /**
     * 计划签收数量
     */
    private Integer planReceiptCount;

    /**
     * 实际签收数量
     */
    private Integer realReceiptCount;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 拣货缺货数量
     */
    private Integer pickShortCount;

    /**
     * 拦截数量
     */
    private Integer interceptCount;

    /**
     * 拒收数量
     */
    private Integer rejectCount;

    /**
     * 拒收原因
     */
    private String rejectReason;

    /**
     * 扫码数量
     */
    private Integer scanCount;

    /**
     * 无码数量
     */
    private Integer noscanCount;

    /**
     * 无码原因
     */
    private String noscanReason;

    /**
     * 无码货物照片
     */
    private String noscanPics;

    /**
     * 备注
     */
    private String remark;

    /**
     * 配送类型 0配送1回收
     */
    private Integer type;

    /**
     * 商品图片
     */
    private String skuPic;
    /**
     * 类型 0 自营 1 代仓
     */
    private Integer skuType;

    /**
     * 类型 0普通 1水果
     */
    private Integer outItemType;

    /**
     * 扫码信息
     */
    List<DeliverySiteItemCodeDTO> deliverySiteItemCodeDTOS;
    /**
     * 配送商品批次
     */
    private String batchs;
    /**
     * 大客户名称
     */
    private String nameRemakes;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 存储条件
     * 0, "未分类"
     * 1, "冷冻"
     * 2, "冷藏"
     * 3, "常温"
     * 4, "顶汇大流通"
     */
    private Integer storageArea;


    /**
     * 加工信息
     */
    List<DeliverySiteProcessItemDTO> deliverySiteProcessItemDTOList;

    /**
     * 回收信息
     */
    private DeliverySiteItemRecycleDTO deliverySiteRecycleDTO;

    /**
     * 配送/回收物品状态，0：正常，1：异常
     */
    private Integer status;

    /**
     * 条码信息
     */
    private List<String> barcodes;

    /**
     * 包装类型：0单品 1包裹
     */
    private Integer packType;
}
