package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/9/13 17:42<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryOrderUpdateCommand extends BaseObject {

    @NotNull(message = "调度单ID不能为空")
    private Long deliveryBatchId;
    /**
     * 调度单类型
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer type;

    /**
     * 履约时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 承运时间(开始)
     */
    private LocalDateTime beginTime;

    /**
     * 承运商ID
     */
    private Long carrierId;

    /**
     * 车辆id
     */
    private Long carId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 运输路线
     */
    private List<DeliverySiteDTO> deliverySiteList;

    /**
     * 配送单id
     */
    private List<Long> orderIdList;

    /**
     * 预估费用
     */
    @NotNull(message = "预估费用不能为空")
    @DecimalMin(value="0", message="预估费用不符合规范")
    @Digits(integer=10, fraction=2, message="预估费用不符合规范")
    private BigDecimal estimateFare;

    /**
     * 区域
     */
    private String area;

    /**
     * 班次 0正常 1加班
     */
    private Integer classes;

    /**
     * 备注
     */
    private String remarkInfo;

    /**
     * 运费明细
     */
    @Valid
    private List<DeliveryBatchFareCommand> deliveryBatchFareCommandList;

    /**
     * 批次关联关系
     */
    @Valid
    private List<DeliveryBatchRelationCommand> deliveryBatchRelationCommandList;

    /**
     * 承运商品类型，0：标品，1：水果
     */
    private Integer carryType;
}
