package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.site.dto.SiteDTO;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2022/12/7 10:39<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySectionDTO implements Serializable {

    private static final long serialVersionUID = -7042542841736623749L;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 开始点位信息
     */
    private SiteDTO beginSite;

    /**
     * 开始点位区域id
     */
    private Integer adCodeMsgIdForBeginSite;

    /**
     * 结束点位信息
     */
    private SiteDTO endSite;

    /**
     * 结束点位区域id
     */
    private Integer adCodeMsgIdForEndSite;

    /**
     * 距离米
     */
    private BigDecimal distance;
}
