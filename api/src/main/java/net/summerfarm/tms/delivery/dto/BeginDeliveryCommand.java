package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/2/3 11:07<br/>
 *
 * <AUTHOR> />
 */
@Data
public class BeginDeliveryCommand extends BaseObject {
    private static final long serialVersionUID = -1556658351003531121L;
    /**
     * 开始配送id
     */
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;
    /**
     * 装车照片
     */
    private String loadingPhotos;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;
    /**
     * 出仓温度
     */
    private BigDecimal outWarehouseTemperature;
    /**
     * 出仓晚点原因
     */
    private String overOutTimeReason;
    /**
     * 冷藏照片
     */
    private String refrigeratePics;
    /**
     * 冷冻照片
     */
    private String freezePics;
}
