package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/1/11 16:53<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackApprovedCommand extends BaseObject {
    private static final long serialVersionUID = -7786586009300658088L;

    @NotNull(message = "id不能为空")
    private Long id;

    /**
     *缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它 8.总仓-腐烂
     */
    @NotNull(message = "lackTypes不能为空")
    private List<Integer> lackTypes;

    private Integer stockLackNum;

    private String pic;

    private String remark;
}
