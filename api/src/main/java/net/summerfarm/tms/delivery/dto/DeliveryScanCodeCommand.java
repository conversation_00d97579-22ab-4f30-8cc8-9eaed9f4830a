package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Description: <br/>
 * date: 2023/1/10 18:38<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryScanCodeCommand extends BaseObject {
    /**
     * 条形码信息
     */
    @NotBlank(message = "onlyCode不能为空")
    private String onlyCode;
    /**
     * 配送点位信息
     */
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;

    /**
     * 是否是溯源码 true是 false否
     */
    private Boolean batchCodeTraceFlag;

}
