package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.delivery.group.ValidationGroups;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Description: <br/>
 * date: 2022/12/27 18:18<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteUpdateCommand {

    /**
     * 配送点位id
     */
    @NotNull(groups = {ValidationGroups.UpdateSiteBatch.class},message = "id.not.null")
    private Long id;

    /**
     * 城配仓
     */
    @NotBlank(groups = {ValidationGroups.UpdateSiteBatch.class},message = "storeNo.not.null")
    private String storeNo;

    /**
     * 路线
     */
    private String path;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 配送方式 0正常配送 1专车配送
     */
    @NotNull(groups = {ValidationGroups.UpdateSiteBatch.class},message = "sendWay.not.null")
    private Integer sendWay;
}
