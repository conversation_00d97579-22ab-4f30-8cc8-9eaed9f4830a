package net.summerfarm.tms.delivery.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.base.BaseObject;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.delivery.dto.cheakinpunch.DeliverySiteCheckinPunchDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.DistTypeEnum;
import net.summerfarm.tms.util.DateUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 运输路线节点
 */
@Data
public class DeliverySiteDTO extends BaseObject {

    private static final long serialVersionUID = -4711064018153911837L;

    /**
     * 运输节点ID
     */
    private Long id;
    /**
     * 运输节点类型(起点/途径点/终点)
     * @see net.summerfarm.tms.enums.DeliverySiteTypeEnum
     */
    private Integer type;
    /**
     * 运输路线序号
     */
    private Integer sequence;
    /**
     * 运输节点配送状态
     * @see net.summerfarm.tms.enums.DeliverySiteStatusEnum
     */
    private Integer status;
    /**
     * 运输节点配送状态描述
     */
    private String statusDesc;
    /**
     * 点位名称
     */
    private String siteName;
    /**
     * 点位ID
     */
    private Long siteId;
    /**
     * 点位类型
     * @see net.summerfarm.tms.enums.TmsSiteTypeEnum
     */
    private Integer siteType;
    /**
     * 点位类型描述
     */
    private String siteTypeDesc;
    /**
     * 计划达到时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planArriveTime;

    /**
     * 计划出发时间
     */
    @JSONField(format = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime planOutTime;
    /**
     * 实际到达时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signInTime;
    /**
     * 到达打卡时间差值/分
     */
    private Integer signInDiffMinute;
    /**
     * 签到距离
     */
    private BigDecimal signInDistance;
    /**
     * 签到(签收)状态，0：正常，1：不正常
     */
    private Integer signInStatus;
    /**
     * 签到点位
     */
    private String signInPoi;
    /**
     * 到达打卡距离差值/千米
     */
    private BigDecimal signInDiffKm;
    /**
     * 到达打卡照片1(门店抬头)
     */
    private String signInPic1;
    /**
     * 到达打卡照片（签收面单）
     */
    private String signInPic2;

    /**
     * 到达打卡备注
     */
    private String signInRemark;
    /**
     * 实际出发时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signOutTime;
    /**
     * 出发打卡时间差值/分
     */
    private Integer signOutDiffMinute;
    /**
     * 出发打卡距离差值/千米
     */
    private BigDecimal signOutDiffKm;
    /**
     * 出发打卡照片1
     */
    private String signOutPic1;
    /**
     * 出发打卡照片2
     */
    private String signOutPic2;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;
    
    

    /**
     * 封签照片
     */
    private String sealPics;
    /**
     * 出发打卡备注
     */
    private String signOutRemark;
    /**
     * 备用点ID(监管仓ID)
     */
    private Long backSiteId;
    /**
     * 备用点名称(监管仓名称)
     */
    private String backSiteName;
    /**
     * 备用点类型
     */
    private Integer backSiteType;
    /**
     * 备用点类型描述
     */
    private String backSiteTypeDesc;
    /**
     * 是否卸货到备用点
     */
    private Boolean signInBack;

    /**
     * 配送类型
     */
    private DistTypeEnum deliveryType;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;
    /**
     * 总重量
     */
    private BigDecimal totalWeight;
    /**
     * 总价格
     */
    private BigDecimal totalPrice;
    /**
     * 精准送
     */
    private String timeFrame;

    /**
     * 点位信息
     */
    private SiteDTO siteDTO;

    /**
     * 出发温度
     */
    private BigDecimal signOutTemperature;

    /**
     * 配送批次id
     */
    private Long deliveryBatchId;

    /**
     * 点位对应的委托单
     */
    private List<DistOrderDTO> distOrderDTOS;

    /**
     * 到仓距离
     */
    private BigDecimal distance;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 外部客户号
     */
    private String outerClientId;

    /**
     * 0正常 1部分拦截 2全部拦截
     * @see net.summerfarm.tms.enums.DeliverySiteInterceptStateEnum
     */
    private Integer interceptState;

    /**
     * 点位配送详情
     */
    private List<DeliverySiteItemDTO> deliverySiteItemDTOList;

    /**
     * 签发数据
     */
    private List<DeliveryPickDTO> deliveryPickDTOS;

    /**
     * 超出距离原因
     */
    private String outReason;

    /**
     * 是否超出距离 0 正常 1超出
     */
    private Integer outDistance;

    /**
     * 距离下一个运输点位之间的距离
     */
    private BigDecimal nextSiteDistanceKm;

    /**
     * 签到异常类型
     */
    private String signInErrType;
    /**
     * 出发异常类型
     */
    private String signOutErrType;
    /**
     * 签发状态 0：正常，1：不正常
     */
    private Integer signOutStatus;
    /**
     * 签到点位地址
     */
    private String signInAddress;
    /**
     * 实际出发点位
     */
    private String signOutPoi;
    /**
     * 实际出发点位地址
     */
    private String signOutAddress;
    /**
     * 实际出发点位地址
     */
    private boolean curSignFlag;
    /**
     * 配送方式 0正常配送 1专车配送
     */
    private Integer sendWay;

    /**
     * 是否需要打卡
     */
    private boolean needPunch;

    /**
     * 实际需要打卡距离
     */
    private BigDecimal shouldPunchDistance;

    /**
     * 实际需要打卡poi
     */
    private String shouldPunchPoi;

    /**
     * 批次信息
     */
    private DeliveryBatchDTO deliveryBatchDTO;

    /**
     * bd名称
     */
    private String bdName;

    /**
     * bd手机号
     */
    private String bdPhone;

    /**
     * 地址状态
     */
    private Integer contactStatus;

    /**
     * 报销信息
     */
    private ExpenseDTO expenseDTO;

    /**
     * 智能排线顺序
     */
    private Integer intelligenceSequence;
    /**
     * 城配仓点位信息
     */
    private DeliverySiteDTO storeSiteDTO;

    /**
     * 配送照片3（货物照片）
     */
    private String signInPic3;

    /**
     * 超出异常备注
     */
    private String outReasonType;

    /**
     * 超出距离照片凭证
     */
    private String outPic;
    /**
     * 配送单
     */
    private List<DeliveryOrderDTO> deliveryOrders;
    /**
     * 配送备注
     */
    private String sendRemark;
    /**
     *是否需要扫码 false 不需要 true 需要
     */
    private Boolean scanCodeFlag;
    /**
     * 到店打卡标识 true 需要打卡 false不需要打卡
     */
    private Boolean checkinPunchFlag;

    /**
     * 打卡范围 km
     */
    private BigDecimal punchRange;

    /**
     * 到店打卡信息
     */
    private DeliverySiteCheckinPunchDTO checkinPunchDTO;

    /**
     * 点位最近完成配送门店抬头照片
     */
    private String recentHeadPic;

    /**
     * 外部品牌号
     */
    private String outerBrandId;

    /**
     * 外部品牌名
     */
    private String outerBrandName;

    /**
     * 订单来源
     */
    private String orderSourceInfo;

    /**
     * 需要提货标识 true需要提货 false不需要提货
     */
    private boolean needPickUpFlag;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 拣货缺货标识 true 缺货 false 不缺货
     */
    private Boolean pickLackFlag;

    /**
     * 冷藏照片
     */
    private String refrigeratePics;
    /**
     * 冷冻照片
     */
    private String freezePics;

    /**
     * 点位用途
     */
    private Integer siteUse;

    private String outBusinessNo;

    /**
     * 仓网区域Id
     */
    private Integer adCodeMsgId;
}
