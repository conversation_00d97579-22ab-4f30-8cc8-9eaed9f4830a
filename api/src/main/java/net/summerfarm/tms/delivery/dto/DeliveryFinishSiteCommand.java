package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/1/10 18:45<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryFinishSiteCommand extends BaseObject {
    private static final long serialVersionUID = -3237254483059707285L;

    /**
     * 门店抬头
     */
    private String deliveryPic;

    /**
     * 签收面照片
     */
    private String signPic;

    /**
     * 货物照片
     */
    private String productPic;
    /**
     * 配送点位id
     */
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;
    /**
     * 完成配送poi
     */
    @NotBlank(message = "完成配送poi")
    private String finishPoi;
    /**
     * 签收备注
     */
    private String signForRemarks;
    /**
     * 是否是正常签收 0 正常 1 不正常
     */
    private Integer signForStatus;

    /**
     * 完成配送地点poi与地址poi的距离
     */
    @NotNull(message = "finishDistance不能为空")
    private BigDecimal finishDistance;

    /**
     * 超出距离说明
     */
    private String outRemark;

    /**
     * 超出异常原因类型
     */
    private String outReasonType;

    /**
     * 超出距离照片凭证
     */
    private String outPic;

    /**
     * 是否超出距离 0 正常 1超出
     */
    private Integer outDistance;
    /**
     * 是否校验扫码商品数量
     */
    private Boolean checkScanNum;
    /**
     * 缺货信息
     */
    private List<DeliveryFinishSiteShortCommand> deliveryPathShortSkuList;

    /**
     * 回收信息
     */
    @Valid
    private List<DeliveryFinishSiteRecycleCommand> deliveryFinishSiteRecycleList;
}
