package net.summerfarm.tms.facade.common.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.common.client.enums.DownloadCenterEnum;

/**
 * Description:
 * date: 2024/2/20 16:19
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DownloadCenterUploadInput {

    /**
     * 资源ID
     */
    private Long resId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 状态
     */
    private DownloadCenterEnum.Status status;

    /**
     * 业务状态;
     */
    private DownloadCenterEnum.BizStatusEnum bizStatus;
}
