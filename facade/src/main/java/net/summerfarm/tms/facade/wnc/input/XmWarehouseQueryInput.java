package net.summerfarm.tms.facade.wnc.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/10/20 15:28<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XmWarehouseQueryInput {
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    private Integer type;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 库存仓编号集合
     */
    private List<Integer> warehouseNos;
}
