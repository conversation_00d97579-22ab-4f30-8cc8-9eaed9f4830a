package net.summerfarm.tms.facade.auth;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.auth.converter.AuthUserDTOConverter;
import net.summerfarm.tms.facade.auth.dto.AuthUserDTO;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.input.permission.PermissionQueryVO;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.client.provider.AuthUserQueryProvider;
import net.xianmu.authentication.client.provider.PermissionQueryProvider;
import net.xianmu.authentication.client.resp.AuthDatePermissionResp;
import net.xianmu.authentication.client.resp.AuthUserBaseResp;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/5/30 16:55<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class AuthQueryFacade {

    @DubboReference
    private AuthBaseUserProvider authBaseUserProvider;
    @DubboReference
    private PermissionQueryProvider permissionQueryProvider;
    @DubboReference
    private AuthUserQueryProvider authUserQueryProvider;

    public String queryAdminRealName(Long adminId){
        AuthUserQueryInput authUserQueryInput = new AuthUserQueryInput();
        authUserQueryInput.setBizId(adminId);
        DubboResponse<AuthUserBaseResp> authUserBaseRespDubboResponse = null;
        try {
            authUserBaseRespDubboResponse = authBaseUserProvider.queryRealName(SystemOriginEnum.ADMIN, authUserQueryInput);
        } catch (Exception e) {
            log.error("dubbo请求查询Auth操作人名称异常adminId:{}",adminId,e);
            return "";
        }
        if(authUserBaseRespDubboResponse == null || !authUserBaseRespDubboResponse.isSuccess() || authUserBaseRespDubboResponse.getData() == null){
            log.error("dubbo请求查询Auth操作人名称异常,adminId为:{}，返回信息{}",adminId, JSON.toJSONString(authUserBaseRespDubboResponse));
            return "";
        }
        return authUserBaseRespDubboResponse.getData() == null ? "" : authUserBaseRespDubboResponse.getData().getName();
    }

    public String queryDriverPassword(Long driverId){
        AuthUserQueryInput authUserQueryInput = new AuthUserQueryInput();
        authUserQueryInput.setBizId(driverId);
        DubboResponse<AuthUserBaseResp> authUserBaseRespDubboResponse = null;
        try {
            authUserBaseRespDubboResponse = authBaseUserProvider.queryPassword(SystemOriginEnum.TMS, authUserQueryInput);
        } catch (Exception e) {
            log.error("dubbo请求Auth获取密码异常driverId:{}",driverId,e);
            throw new TmsRuntimeException("请求Auth查询数据密码异常");
        }
        if(authUserBaseRespDubboResponse == null || !authUserBaseRespDubboResponse.isSuccess() || authUserBaseRespDubboResponse.getData() == null){
            log.error("dubbo请求Auth获取密码数据异常,driverId为:{}，返回信息{}",driverId, JSON.toJSONString(authUserBaseRespDubboResponse));
            throw new TmsRuntimeException("请求Auth查询数据密码异常");
        }
        return authUserBaseRespDubboResponse.getData() == null ? "" : authUserBaseRespDubboResponse.getData().getPassword();
    }

    /**
     * 查询城配仓数据权限
     * @param authUserId 登录人ID
     * @return 数据结果
     */
    public List<String> queryCityStoreUserPermission(Long authUserId){

        if(authUserId == null){
            return Collections.emptyList();
        }
        PermissionQueryVO permissionQueryVO = new PermissionQueryVO();
        //城配仓
        permissionQueryVO.setType(2);
        permissionQueryVO.setAuthUserId(authUserId);
        DubboResponse<List<AuthDatePermissionResp>> listDubboResponse = null;
        try {
            listDubboResponse = permissionQueryProvider.queryUserPermission(permissionQueryVO);
        } catch (Exception e) {
            log.error("dubbo请求Auth获取城配仓异常:{}",e.getMessage(),e);
            throw new TmsRuntimeException("请求Auth获取数据权限异常");
        }
        if(listDubboResponse == null || !listDubboResponse.isSuccess() || listDubboResponse.getData() == null){
            log.error("dubbo请求Auth获取城配仓异常,authUserId为:{}，返回信息{}",authUserId, JSON.toJSONString(listDubboResponse));
            throw new TmsRuntimeException("请求Auth获取数据权限异常");
        }
        //城配仓编号
        List<AuthDatePermissionResp> cityPermissionList = listDubboResponse.getData();
        return cityPermissionList.stream().map(AuthDatePermissionResp::getPermissionValue).collect(Collectors.toList());
    }

    /**
     * 根据用户ID列表查询用户信息
     * @param bizIds 用户ID列表
     * @return  用户信息
     */
    public List<AuthUserDTO> queryAuthUserList(List<Long> bizIds){
        if(CollectionUtils.isEmpty(bizIds)){
            return Collections.emptyList();
        }
        AuthUserQueryInput queryInput = new AuthUserQueryInput();
        queryInput.setTenantId(Constants.Tenant.XM_TENANT_ID);
        queryInput.setBizIds(bizIds);
        DubboResponse<List<AuthUserResp>> listDubboResponse = null;
        try {
            listDubboResponse = authUserQueryProvider.queryAuthUserList(net.xianmu.authentication.client.input.SystemOriginEnum.ADMIN, queryInput);
        } catch (Exception e) {
            log.error("req Auth authUserQueryProvider.queryAuthUserList error:{}",e.getMessage(),e);
            return Collections.emptyList();
        }
        if(listDubboResponse == null || !listDubboResponse.isSuccess() || listDubboResponse.getData() == null){
            log.error("dubbo请求Auth批量查询用户信息异常,返回信息{}", JSON.toJSONString(listDubboResponse));
            return Collections.emptyList();
        }
        List<AuthUserResp> authUserResps = listDubboResponse.getData();
        return authUserResps.stream().map(AuthUserDTOConverter::resp2DTO).collect(Collectors.toList());
    }

    /**
     * 根据用户ID列表查询用户名称
     * @param bizIds 用户ID列表
     * @return 结果集
     */
    public Map<Long,String> queryNameByIdsMap(List<Long> bizIds){
        if(CollectionUtils.isEmpty(bizIds)){
            return Collections.emptyMap();
        }
        List<AuthUserDTO> authUserDTOS = this.queryAuthUserList(bizIds);
        if(CollectionUtils.isEmpty(authUserDTOS)){
            return Collections.emptyMap();
        }
        authUserDTOS = authUserDTOS.stream()
                .filter(user-> user.getBizUserId() != null)
                .filter(user-> !StringUtils.isEmpty(user.getName())).collect(Collectors.toList());

        return authUserDTOS.stream().collect(Collectors.toMap(AuthUserDTO::getBizUserId,AuthUserDTO::getName));
    }

}
