package net.summerfarm.tms.facade.wnc.converter;

import net.summerfarm.tms.facade.wnc.dto.AdCodeMsgDTO;
import net.summerfarm.wnc.client.resp.fence.AdCodeMsgResp;

/**
 * Description: 转换类<br/>
 * date: 2024/11/15 14:17<br/>
 *
 * <AUTHOR> />
 */
public class AdCodeMsgConverter {

    public static AdCodeMsgDTO dto2Resp(AdCodeMsgResp resp){
        if(resp == null){
            return null;
        }

        AdCodeMsgDTO dto = new AdCodeMsgDTO();

        dto.setId(resp.getId());
        dto.setAdCode(resp.getAdCode());
        dto.setProvince(resp.getProvince());
        dto.setCity(resp.getCity());
        dto.setArea(resp.getArea());
        dto.setLevel(resp.getLevel());
        dto.setGdId(resp.getGdId());
        dto.setAddTime(resp.getAddTime());
        dto.setUpdateTime(resp.getUpdateTime());
        dto.setStatus(resp.getStatus());
        dto.setFenceId(resp.getFenceId());
        dto.setFenceName(resp.getFenceName());
        dto.setFenceStatus(resp.getFenceStatus());
        dto.setStoreName(resp.getStoreName());
        dto.setStoreNo(resp.getStoreNo());
        dto.setStoreStatus(resp.getStoreStatus());

        return dto;
    }
}
