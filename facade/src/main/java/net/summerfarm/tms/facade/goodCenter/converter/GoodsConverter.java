package net.summerfarm.tms.facade.goodCenter.converter;

import com.aliyun.odps.Volume;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.tms.facade.goodCenter.dto.GoodsDTO;
import net.summerfarm.tms.facade.goodCenter.dto.GoodsDetailDTO;

/**
 * Description:货品Converter
 * date: 2023/12/21 16:57
 *
 * <AUTHOR>
 */
public class GoodsConverter {

    public static GoodsDTO res2dto(ProductSkuDetailResp productSkuDetailResp) {
        if (productSkuDetailResp == null){
            return null;
        }
        GoodsDTO goodsDTO = new GoodsDTO();
        goodsDTO.setSku(productSkuDetailResp.getSku());
        goodsDTO.setSpecificationUnit(productSkuDetailResp.getSpecificationUnit());
        goodsDTO.setBasicSpecUnit(productSkuDetailResp.getBasicSpecUnit());
        return goodsDTO;
    }

    public static GoodsDetailDTO res2DetailDto(ProductSkuDetailResp productSkuDetailResp) {
        if (productSkuDetailResp == null){
            return null;
        }
        GoodsDetailDTO goodsDetailDTO = new GoodsDetailDTO();
        goodsDetailDTO.setSkuId(productSkuDetailResp.getSkuId());
        goodsDetailDTO.setTenantId(productSkuDetailResp.getTenantId());
        goodsDetailDTO.setSpuId(productSkuDetailResp.getSpuId());
        goodsDetailDTO.setSku(productSkuDetailResp.getSku());
        goodsDetailDTO.setAgentType(productSkuDetailResp.getAgentType());
        goodsDetailDTO.setSubAgentType(productSkuDetailResp.getSubAgentType());
        goodsDetailDTO.setPlaceType(productSkuDetailResp.getPlaceType());
        goodsDetailDTO.setVolumeUnit(productSkuDetailResp.getVolumeUnit());
        goodsDetailDTO.setVolume(productSkuDetailResp.getVolume());
        goodsDetailDTO.setSpecification(productSkuDetailResp.getSpecification());
        goodsDetailDTO.setAssociated(productSkuDetailResp.getAssociated());
        goodsDetailDTO.setWeightNotes(productSkuDetailResp.getWeightNotes());
        goodsDetailDTO.setWeight(productSkuDetailResp.getWeight());
        goodsDetailDTO.setSpecificationUnit(productSkuDetailResp.getSpecificationUnit());
        goodsDetailDTO.setCreateTime(productSkuDetailResp.getCreateTime());
        goodsDetailDTO.setUpdateTime(productSkuDetailResp.getUpdateTime());
        goodsDetailDTO.setTaxRateValue(productSkuDetailResp.getTaxRateValue());
        goodsDetailDTO.setCustomSkuCode(productSkuDetailResp.getCustomSkuCode());
        goodsDetailDTO.setUseFlag(productSkuDetailResp.getUseFlag());
        goodsDetailDTO.setCreateType(productSkuDetailResp.getCreateType());
        goodsDetailDTO.setSkuPicture(productSkuDetailResp.getSkuPicture());
        goodsDetailDTO.setSkuTitle(productSkuDetailResp.getSkuTitle());
        goodsDetailDTO.setOwnerId(productSkuDetailResp.getOwnerId());
        goodsDetailDTO.setFirstCategory(productSkuDetailResp.getFirstCategory());
        goodsDetailDTO.setFirstCategoryId(productSkuDetailResp.getFirstCategoryId());
        goodsDetailDTO.setSecondCategory(productSkuDetailResp.getSecondCategory());
        goodsDetailDTO.setSecondCategoryId(productSkuDetailResp.getSecondCategoryId());
        goodsDetailDTO.setCategoryId(productSkuDetailResp.getCategoryId());
        goodsDetailDTO.setCategoryName(productSkuDetailResp.getCategoryName());
        goodsDetailDTO.setCategoryType(productSkuDetailResp.getCategoryType());
        goodsDetailDTO.setTitle(productSkuDetailResp.getTitle());
        goodsDetailDTO.setSubTitle(productSkuDetailResp.getSubTitle());
        goodsDetailDTO.setMainPicture(productSkuDetailResp.getMainPicture());
        goodsDetailDTO.setDetailPicture(productSkuDetailResp.getDetailPicture());
        goodsDetailDTO.setStorageLocation(productSkuDetailResp.getStorageLocation());
        goodsDetailDTO.setStorageTemperature(productSkuDetailResp.getStorageTemperature());
        goodsDetailDTO.setGuaranteePeriod(productSkuDetailResp.getGuaranteePeriod());
        goodsDetailDTO.setGuaranteeUnit(productSkuDetailResp.getGuaranteeUnit());
        goodsDetailDTO.setOrigin(productSkuDetailResp.getOrigin());
        goodsDetailDTO.setBrandName(productSkuDetailResp.getBrandName());
        goodsDetailDTO.setCustomSpuCode(productSkuDetailResp.getCustomSpuCode());
        goodsDetailDTO.setExtType(productSkuDetailResp.getExtType());

        return goodsDetailDTO;

    }
}
