package net.summerfarm.tms.facade.mall.converter;

import net.summerfarm.client.resp.admin.AdminResp;
import net.summerfarm.tms.facade.mall.dto.AdminDTO;

/**
 * Description:Admin转换器
 * date: 2023/3/23 13:50
 *
 * <AUTHOR>
 */
public class AdminConverter {

    public static AdminDTO resp2dto(AdminResp adminResp){
        if (adminResp == null){
            return null;
        }
        AdminDTO adminDTO = new AdminDTO();
        adminDTO.setAdminId(adminResp.getAdminId());
        adminDTO.setNameRemakes(adminResp.getNameRemark());
        return adminDTO;
    }
}
