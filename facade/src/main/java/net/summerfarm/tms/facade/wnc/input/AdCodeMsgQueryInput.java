package net.summerfarm.tms.facade.wnc.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: 查询<br/>
 * date: 2024/11/15 17:54<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdCodeMsgQueryInput {

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 状态 0 正常 1 失效 3停用
     */
    private Integer status;

    /**
     * 围栏id
     */
    private Integer fenceId;

    /**
     * 区域编码集合
     */
    private List<String> adCodeList;

    /**
     * 城配仓编号
     */
    private List<Integer> storeNos;

    /**
     * 城配仓状态
     */
    private Integer storeStatus;

    /**
     * 围栏状态
     */
    private Integer fenceStatus;

}
