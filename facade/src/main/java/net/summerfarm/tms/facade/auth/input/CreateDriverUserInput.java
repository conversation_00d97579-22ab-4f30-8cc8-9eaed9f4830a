package net.summerfarm.tms.facade.auth.input;

/**
 * Description: <br/>
 * date: 2023/5/31 11:49<br/>
 *
 * <AUTHOR> />
 */

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class CreateDriverUserInput implements Serializable {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 密码
     */
    private String password;

    /**
     * 状态
     */
    private Integer status;
}
