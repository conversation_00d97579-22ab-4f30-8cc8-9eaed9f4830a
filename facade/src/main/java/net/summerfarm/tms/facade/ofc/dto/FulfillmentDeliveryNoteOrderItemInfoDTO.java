package net.summerfarm.tms.facade.ofc.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: 订单项<br/>
 * date: 2025/1/17 17:57<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FulfillmentDeliveryNoteOrderItemInfoDTO {

    /**
     * 商品类型：0、普通商品 1、赠品
     */
    private Integer productType;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productSpecifications;

    /**
     * 商品数量
     */
    private Integer productAmount;

    /**
     * 商品单价
     */
    private BigDecimal productUnitPrice;

    /**
     * 商品实付总价
     */
    private BigDecimal productActualTotalPrice;

    /**
     * 商品单位
     */
    private String productUnit;

    /**
     * sku
     */
    private String sku;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 货品规格
     */
    private String skuSpecifications;

    /**
     * 商品属性: 10自营，11代仓
     */
    private String skuAttribute;

    /**
     * 货品存储区域，1:冷冻,2:冷藏,3:常温
     */
    private Integer skuStorageLocation;

    /**
     * 货品货品重量
     */
    private BigDecimal skuWeightNum;

    /**
     * 货品体积
     */
    private String skuVolume;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;
}
