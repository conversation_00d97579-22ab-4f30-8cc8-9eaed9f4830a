package net.summerfarm.tms.facade.crm.converter;

import net.summerfarm.crm.client.dto.ContactBdDTO;
import net.summerfarm.crm.client.dto.CrmBdInfoDTO;
import net.summerfarm.tms.facade.crm.dto.BdDTO;
import net.summerfarm.tms.facade.crm.dto.MerchantBdDTO;

/**
 * Description: <br/>
 * date: 2023/7/18 15:23<br/>
 *
 * <AUTHOR> />
 */
public class ContactBdConverter {

    public static BdDTO resp2DTO(ContactBdDTO contactBdDTO){
        if(contactBdDTO == null){
            return null;
        }
        BdDTO bdDTO = new BdDTO();

        bdDTO.setBdPhone(contactBdDTO.getBdPhone());
        bdDTO.setBdName(contactBdDTO.getBdName());

        return bdDTO;
    }

    public static MerchantBdDTO resp2DTO(CrmBdInfoDTO crmBdInfoDTO){
        if(crmBdInfoDTO == null){
            return null;
        }

        MerchantBdDTO merchantBdDTO = new MerchantBdDTO();
        merchantBdDTO.setMid(crmBdInfoDTO.getMid());
        merchantBdDTO.setBdName(crmBdInfoDTO.getBdName());
        merchantBdDTO.setBdPhone(crmBdInfoDTO.getPhone());

        return merchantBdDTO;
    }
}
