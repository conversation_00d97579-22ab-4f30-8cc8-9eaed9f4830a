package net.summerfarm.tms.facade.pms;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.pms.client.provider.ArrangeProvider;
import net.summerfarm.pms.client.req.PurchasesArrangeQueryReq;
import net.summerfarm.pms.client.resp.PurchasesArrangeBatchResp;
import net.summerfarm.pms.client.resp.PurchasesArrangeResp;
import net.summerfarm.pms.client.resp.PurchasesArrangeSkuResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: PMS查询服务<br/>
 * date: 2024/8/27 17:23<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class PmsQueryFacade {

    @DubboReference
    private ArrangeProvider arrangeProvider;

    /**
     * 根据预约id查询供应商名称
     * @param arrangeIds 预约单id
     * @return key 预约单id，value 供应商名称集合
     */
    public Map<Long, List<String>> querySupplierNameByArrangeIds(List<String> arrangeIds){
        if(CollectionUtils.isEmpty(arrangeIds)){
            return Collections.emptyMap();
        }
        PurchasesArrangeQueryReq req = new PurchasesArrangeQueryReq();
        req.setArrangeIdList(arrangeIds.stream().map(Long::parseLong).collect(Collectors.toList()));
        req.setQueryPurchasePlanInfo(true);
        DubboResponse<PurchasesArrangeBatchResp> resp = arrangeProvider.queryPurchasesArrangeByIdList(req);
        if (resp == null || !resp.isSuccess()){
            throw new ProviderException("调用通过预约id查询预约单异常");
        }

        PurchasesArrangeBatchResp purchasesArrangeBatchResp = resp.getData();
        if(purchasesArrangeBatchResp == null){
            return Collections.emptyMap();
        }

        List<PurchasesArrangeResp> arrangeRespList = purchasesArrangeBatchResp.getArrangeRespList();

        return arrangeRespList.stream().collect(Collectors.toMap(PurchasesArrangeResp::getId,
                res -> res.getPurchasesArrangeSkuRespList().stream().map(PurchasesArrangeSkuResp::getSupplierName).distinct().collect(Collectors.toList())));
    }
}
