package net.summerfarm.tms.facade.wnc.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/10/20 15:20<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseLogisticsQueryInput {

    /**
     * 物流中心编号（配送仓编号）
     */
    private Integer storeNo;

    /**
     * 名称
     */
    private String storeName;

    /**
     * 配送中心状态：0、失效 1、有效
     */
    private Integer status;

    /**
     * 城配仓编号集合
     */
    private List<Integer> storeNos;
}
