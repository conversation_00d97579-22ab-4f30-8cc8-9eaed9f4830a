package net.summerfarm.tms.facade.mall.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description:鲜沐大客户查询
 * date: 2024/2/6 10:32
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminQueryInput implements Serializable {

    private static final long serialVersionUID = -1489424691095268002L;

    /**
     * 大客户ID集合
     */
    private List<Integer> adminIds;
}
