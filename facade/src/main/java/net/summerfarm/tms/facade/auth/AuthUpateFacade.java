package net.summerfarm.tms.facade.auth;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.auth.converter.UserBaseDTOConverter;
import net.summerfarm.tms.facade.auth.dto.UserBaseDTO;
import net.summerfarm.tms.facade.auth.input.CreateDriverUserInput;
import net.summerfarm.tms.facade.auth.input.UpdateDriverUserInput;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.BaseUserUpdateInput;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * Description: <br/>
 * date: 2023/5/30 17:46<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class AuthUpateFacade {

    @DubboReference
    private AuthUserProvider authUserProvider;

    @DubboReference
    private AuthBaseUserProvider authBaseUserProvider;

    public UserBaseDTO createDriverUser(CreateDriverUserInput createDriverUserInput){
        UserBase userBase = new UserBase();
        userBase.setPhone(createDriverUserInput.getPhone());
        userBase.setUsername(createDriverUserInput.getPhone());
        userBase.setStatus(createDriverUserInput.getStatus());
        userBase.setPassword(createDriverUserInput.getPassword());
        log.info("AuthUpateFacade.createDriverUser userBase={}",JSON.toJSONString(userBase));
        DubboResponse<UserBase> userBaseDubboResponse = authUserProvider.createUser(SystemOriginEnum.TMS, userBase);
        log.info("AuthUpateFacade.createDriverUser response={}",JSON.toJSONString(userBaseDubboResponse));
        if(userBaseDubboResponse == null || !userBaseDubboResponse.isSuccess() || userBaseDubboResponse.getData() == null){
            log.error("dubbo请求Auth创建司机用户数据异常,手机号:{},返回信息{}",createDriverUserInput.getPhone(), JSON.toJSONString(userBaseDubboResponse));
            if(userBaseDubboResponse != null){
                throw new TmsRuntimeException(StringUtils.isNotBlank(userBaseDubboResponse.getMsg()) ? userBaseDubboResponse.getMsg() :  "auth创建账号信息失败");
            }else{
                throw new TmsRuntimeException("auth创建账号信息失败");
            }
        }

        return UserBaseDTOConverter.resp2DTO(userBaseDubboResponse.getData());
    }

    public void updateUserBase(UpdateDriverUserInput updateDriverUserInput){
        BaseUserUpdateInput userBase = new BaseUserUpdateInput();
        userBase.setStatus(updateDriverUserInput.getStatus());
        userBase.setNikeName(updateDriverUserInput.getName());
        userBase.setPassword(updateDriverUserInput.getPassword());
        userBase.setBaseUserId(updateDriverUserInput.getBaseUserId());
        userBase.setBizUserId(updateDriverUserInput.getBizUserId());
        if(StringUtils.isNotBlank(updateDriverUserInput.getPhone())){
            userBase.setPhone(updateDriverUserInput.getPhone());
        }
        log.info("AuthUpateFacade.updateUserBase userBase={}",JSON.toJSONString(userBase));
        DubboResponse<Boolean> booleanDubboResponse = authBaseUserProvider.updateUserBase(net.xianmu.common.enums.base.auth.SystemOriginEnum.TMS, userBase);
        log.info("AuthUpateFacade.updateUserBase response={}",JSON.toJSONString(booleanDubboResponse));
        if(booleanDubboResponse == null || !booleanDubboResponse.isSuccess() || booleanDubboResponse.getData() == null){
            log.error("dubbo请求Auth更新机用户数据异常,手机号:{},返回信息{}",updateDriverUserInput.getBaseUserId(), JSON.toJSONString(booleanDubboResponse));
            if(booleanDubboResponse != null){
                throw new TmsRuntimeException(StringUtils.isNotBlank(booleanDubboResponse.getMsg()) ? booleanDubboResponse.getMsg() :  "auth更新账号信息失败");
            }else{
                throw new TmsRuntimeException("auth更新账号信息失败");
            }
        }
        if(!booleanDubboResponse.getData()){
            log.error("auth更新账号信息失败");
            throw new TmsRuntimeException("auth更新账号信息失败");
        }
    }
}
