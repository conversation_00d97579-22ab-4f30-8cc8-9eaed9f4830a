package net.summerfarm.tms.facade.goodCenter;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.req.ProductSkuPageQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.facade.goodCenter.converter.GoodsConverter;
import net.summerfarm.tms.facade.goodCenter.dto.GoodsDTO;
import net.summerfarm.tms.facade.goodCenter.dto.GoodsDetailDTO;
import net.summerfarm.tms.facade.goodCenter.input.GoodsInput;
import net.summerfarm.tms.facade.mall.dto.SkuDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:货品中心查询Facade
 * date: 2023/12/21 14:32
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GoodCenterQueryFacade {

    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;

    /**
     * 查询货品sku集合
     * @param goodsInput sku集合信息
     */
    public List<GoodsDTO> querySkuList(GoodsInput goodsInput){
        List<String> skus = goodsInput.getSkus();
        if (CollectionUtils.isEmpty(skus)){
            return Collections.emptyList();
        }
        List<ProductSkuDetailResp> results = Lists.newArrayList();
        List<List<String>> partList = Lists.partition(skus, 100);
        for (List<String> skuList : partList) {
            ProductSkuPageQueryReq req = new ProductSkuPageQueryReq();
            req.setTenantId(Constants.Tenant.XM_TENANT_ID);
            req.setIsSearchXmAgent(Boolean.TRUE);
            req.setSkuList(skuList);
            req.setPageSize(100);
            DubboResponse<PageInfo<ProductSkuDetailResp>> response = productsSkuQueryProvider.selectSkuPage(req);
            if (response == null || !response.isSuccess()){
                throw new ProviderException("调用货品中心查询货品数据异常");
            }
            PageInfo<ProductSkuDetailResp> pageInfo = response.getData();
            if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())){
                continue;
            }
            results.addAll(pageInfo.getList());
        }
        return results.stream().map(GoodsConverter::res2dto).collect(Collectors.toList());
    }

    public Map<String,GoodsDetailDTO> querySkuListBySku(List<String> skus,Long tenantId){
        if (CollectionUtils.isEmpty(skus)){
            return Collections.emptyMap();
        }
        if(tenantId == null){
            throw new BizException("租户ID不能为空");
        }
        List<ProductSkuDetailResp> results = Lists.newArrayList();
        List<List<String>> partList = Lists.partition(skus, 100);
        for (List<String> skuList : partList) {
            ProductSkuPageQueryReq req = new ProductSkuPageQueryReq();
            req.setTenantId(tenantId);
            req.setIsSearchXmAgent(Boolean.TRUE);
            req.setSkuList(skuList);
            req.setPageSize(100);
            DubboResponse<PageInfo<ProductSkuDetailResp>> response = productsSkuQueryProvider.selectSkuPage(req);
            if (response == null || !response.isSuccess()){
                throw new ProviderException("调用货品中心查询货品数据异常");
            }
            PageInfo<ProductSkuDetailResp> pageInfo = response.getData();
            if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())){
                continue;
            }
            results.addAll(pageInfo.getList());
        }
        List<GoodsDetailDTO> goodsDetailDTOS = results.stream().map(GoodsConverter::res2DetailDto).collect(Collectors.toList());

        return goodsDetailDTOS.stream().collect(Collectors.toMap(GoodsDetailDTO::getSku, Function.identity(), (oldData, newData) -> newData));
    }
}
