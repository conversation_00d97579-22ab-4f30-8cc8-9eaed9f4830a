package net.summerfarm.tms.facade.saas.converter;

import com.cosfo.oms.client.req.AddressAuditCreateReq;
import com.cosfo.oms.client.resp.StoreAddressAuditResp;
import net.summerfarm.tms.facade.saas.dto.StoreAddressAuditDTO;
import net.summerfarm.tms.facade.saas.input.AddressAuditCreateCommandInput;

/**
 * Description:
 * date: 2024/1/29 16:38
 *
 * <AUTHOR>
 */
public class StoreAddressAuditConverter {

    public static StoreAddressAuditDTO resp2Dto(StoreAddressAuditResp resp){
        if (resp == null){
            return null;
        }
        StoreAddressAuditDTO storeAddressAuditDTO = new StoreAddressAuditDTO();
        storeAddressAuditDTO.setId(resp.getId());
        storeAddressAuditDTO.setAuditRemark(resp.getAuditRemark());
        storeAddressAuditDTO.setAuditStatus(resp.getAuditStatus());
        return storeAddressAuditDTO;
    }

    public static AddressAuditCreateReq input2Req(AddressAuditCreateCommandInput input){
        if (input == null){
            return null;
        }
        AddressAuditCreateReq addressAuditCreateReq = new AddressAuditCreateReq();
        addressAuditCreateReq.setTenantId(input.getTenantId());
        addressAuditCreateReq.setStoreId(input.getStoreId());
        addressAuditCreateReq.setProvince(input.getProvince());
        addressAuditCreateReq.setCity(input.getCity());
        addressAuditCreateReq.setArea(input.getArea());
        addressAuditCreateReq.setAddress(input.getAddress());
        addressAuditCreateReq.setHouseNumber(input.getHouseNumber());
        addressAuditCreateReq.setPoi(input.getPoi());
        addressAuditCreateReq.setDistance(input.getDistance());
        addressAuditCreateReq.setContactName(input.getContactName());
        addressAuditCreateReq.setContactPhone(input.getContactPhone());
        addressAuditCreateReq.setAuditNo(input.getAuditNo());
        return addressAuditCreateReq;
    }
}
