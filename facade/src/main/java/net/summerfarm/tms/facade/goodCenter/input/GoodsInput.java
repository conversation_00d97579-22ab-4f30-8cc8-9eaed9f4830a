package net.summerfarm.tms.facade.goodCenter.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description:货品Input
 * date: 2023/12/21 16:51
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsInput implements Serializable {

    private static final long serialVersionUID = 4455931741556958241L;

    /**
     * 货品sku集合
     */
    private List<String> skus;
}
