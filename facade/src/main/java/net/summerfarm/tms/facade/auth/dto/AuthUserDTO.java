package net.summerfarm.tms.facade.auth.dto;

import lombok.Data;

import java.util.Date;

/**
 * Description: auth用户数据<br/>
 * date: 2024/10/23 14:27<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AuthUserDTO {
    /**
     * id 自增
     */
    private Long id;

    /**
     * 基础表的id
     */
    private Long userBaseId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态，0：有效，1：失效
     */
    private Byte status;

    /**
     * 系统来源
     */
    private Integer systemOrigin;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 用户业务id
     */
    private Long bizUserId;
    /**
     * 密码
     */
    private String password;
    /**
     * 上次登陆时间
     */
    private Date lastLoginTime;
    /**
     * 审核状态
     */
    private Byte auditStatus;

    /**
     * openid
     */
    private String openid;


    /**
     * 小程序openid
     */
    private String mpOpenid;

    /**
     * unionId
     */
    private String unionid;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;
}
