package net.summerfarm.tms.facade.mallmanage;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.merchant.MerchantInfoQueryProvider;
import net.summerfarm.client.resp.merchant.MerchantShowPriceResultResp;
import net.summerfarm.tms.facade.mallmanage.converter.MerchantConverter;
import net.summerfarm.tms.facade.wms.converter.SkuBarcodeConverter;
import net.summerfarm.wms.sku.req.SkuBarcodeQueryReq;
import net.summerfarm.wms.sku.resp.SkuBarcodeResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 门店信息查询<br/>
 * date: 2025/1/20 11:51<br/>
 *
 * <AUTHOR> />
 *
 */
@Component
@Slf4j
public class MerchantInfoQueryFacade {

    @DubboReference
    private MerchantInfoQueryProvider merchantInfoQueryProvider;

    /**
     * 查询门店配送单是否展示价格列表
     * @param mids 门店id列表
     * @return 门店配送单是否展示价格列表
     */
    public List<MerchantShowPriceResultDTO> queryMerchantShowPrice(List<Long> mids) {
        if(CollectionUtils.isEmpty(mids)){
            return Collections.emptyList();
        }
        List<List<Long>> mIdLists = Lists.partition(mids, 200);

        List<MerchantShowPriceResultResp> merchantShowPriceResultResps = new ArrayList<>();
        for (List<Long> mIdList : mIdLists) {
            DubboResponse<List<MerchantShowPriceResultResp>> listDubboResponse = merchantInfoQueryProvider.queryMerchantShowPriceList(mIdList);
            if (listDubboResponse == null || !listDubboResponse.isSuccess()){
                log.error("merchantInfoQueryProvider queryMerchantShowPriceList,return{}", JSON.toJSONString(listDubboResponse),new ProviderException("查询门店配送单是否展示价格列表异常"));
                continue;
            }
            List<MerchantShowPriceResultResp> data = listDubboResponse.getData();
            if (CollectionUtils.isEmpty(data)){
                continue;
            }
            merchantShowPriceResultResps.addAll(data);
        }

        return merchantShowPriceResultResps.stream().map(MerchantConverter::resp2DTOConvert).collect(Collectors.toList());
    }

    /**
     * 查询门店配送单是否展示价格列表
     * @param mids 门店id列表
     * @return 门店配送单是否展示价格列表
     */
    public Map<Long,Boolean> queryMerchantShowPriceMap(List<Long> mids) {
        List<MerchantShowPriceResultDTO> merchantShowPriceResultDTOS = queryMerchantShowPrice(mids);
        return merchantShowPriceResultDTOS.stream().collect(Collectors.toMap(MerchantShowPriceResultDTO::getMId, MerchantShowPriceResultDTO::getShowPrice));
    }
}
