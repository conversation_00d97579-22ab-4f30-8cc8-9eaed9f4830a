package net.summerfarm.tms.facade.ofc.dto;

import lombok.Data;
import net.summerfarm.ofc.client.resp.fulfillment.FulfillmentDeliveryNoteOrderItemInfoResp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: 履约配送单实体类<br/>
 * date: 2025/1/17 17:56<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FulfillmentDeliveryNoteOrderInfoDTO {

    //==================订单基本信息=======================
    /**
     * 外部关联订单号
     */
    private String outOrderNo;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 履约时间
     */
    private LocalDate fulfillmentTime;

    /**
     * 城配仓no
     */
    private Integer storeNo;

    /**
     * 城配仓
     */
    private String storeName;

    /**
     * 门店名称
     */
    private String outClientName;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店 1大客户\2大连锁3\小连锁\4单点
     */
    private String storeSize;

    /**
     * 配送地址id
     */
    private Long contactId;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 履约单状态: 10待排线，20待拣货，30配送中，40配送完成，44已自提，50已取消，51已关闭，52已拦截
     */
    private Integer fulfillmentStatus;

    /**
     * 履约单来源：100SaaS订单，200鲜沐订单，300运配外单
     */
    private Integer orderSource;

    /**
     * 履约单类型: 10普通订单，11直发采购订单，20售后回收单，21售后补发单，30 样品订单
     */
    private Integer fulfillmentType;

    /**
     * 销售经理
     */
    private String salesManager;

    /**
     * 销售经理电话
     */
    private String salesManagerPhone;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalPrice;

    /**
     * 订单配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 订单超时加单费用
     */
    private BigDecimal outTimesFee;

    /**
     * 地址备注
     */
    private String addressRemark;

    /**
     * 订单备注
     */
    private String orderRemark;

    /**
     * 大客户ID
     */
    private Long bigCustomerId;

    /**
     * 销售主体名称
     */
    private String sellingEntityName;

    /**
     * 履约配送单物品明细
     */
    private List<FulfillmentDeliveryNoteOrderItemInfoDTO> fulfillmentOrderItemList;
}
