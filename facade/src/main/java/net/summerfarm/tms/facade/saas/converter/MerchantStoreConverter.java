package net.summerfarm.tms.facade.saas.converter;

import com.cosfo.manage.client.merchant.resp.MerchantStoreResp;
import net.summerfarm.tms.facade.saas.dto.MerchantStoreDTO;

/**
 * Description:Tenant转换器
 * date: 2023/3/23 13:50
 *
 * <AUTHOR>
 */
public class MerchantStoreConverter {

    public static MerchantStoreDTO resp2dto(MerchantStoreResp merchantStoreResp){
        if (merchantStoreResp == null){
            return null;
        }
        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
        merchantStoreDTO.setId(merchantStoreResp.getId());
        merchantStoreDTO.setTenantId(merchantStoreResp.getTenantId());
        merchantStoreDTO.setStoreName(merchantStoreResp.getStoreName());
        return merchantStoreDTO;
    }
}
