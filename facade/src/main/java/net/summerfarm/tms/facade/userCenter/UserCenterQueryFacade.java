package net.summerfarm.tms.facade.userCenter;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.facade.userCenter.converter.StoreConverter;
import net.summerfarm.tms.facade.userCenter.dto.StoreDTO;
import net.summerfarm.tms.facade.userCenter.input.MerchantInput;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:用户中心查询
 * date: 2023/12/1 15:09
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserCenterQueryFacade {

    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;

    public List<StoreDTO> queryMerchantStores(MerchantInput merchantInput) {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setTenantId(merchantInput.getTenantId());
        req.setMIds(merchantInput.getMIds());
        DubboResponse<List<MerchantStoreResultResp>> storeResponse = merchantStoreQueryProvider.getMerchantStoresByPrimaryKeys(req);
        if (storeResponse == null || !storeResponse.isSuccess()){
            throw new ProviderException("调用用户中心获取门店信息异常");
        }
        return storeResponse.getData().stream().map(StoreConverter::resp2dto).collect(Collectors.toList());
    }
}
