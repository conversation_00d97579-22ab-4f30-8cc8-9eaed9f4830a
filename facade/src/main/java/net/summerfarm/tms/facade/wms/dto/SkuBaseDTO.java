package net.summerfarm.tms.facade.wms.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/6/5 16:39<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SkuBaseDTO {

    private Long skuId;

    private String sku;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 规格
     */
    private String weight;

    /**
     * 类型 0 自营 1 代仓
     */
    private Integer type;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 图片地址
     */
    private String picturePath;

    /**
     * 体积
     */
    private String volume;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    /**
     * 所属大客户id
     */
    private Integer adminId;

    /**
     * 所属大客户名称
     */
    private String adminName;

    /**
     * SKU生命周期：-1、上新处理中 0、使用中 1、已删除
     */
    private Integer outdated;

    private Long pdId;

    /**
     * 类目id
     */
    private Integer categoryId;
}
