package net.summerfarm.tms.facade.userCenter;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.facade.userCenter.enums.MerchantPropertiesExtEnum;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreExtQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreExtQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreExtResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 用户中心模块-门店打印配送单查询<br/>
 * date: 2024/4/7 15:23<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class MerchantStoreExtQueryFacade {

    @DubboReference
    private MerchantStoreExtQueryProvider merchantStoreExtQueryProvider;

    /**
     * 查询店铺不需要打印配送单的门店
     * @param summerFarmMIds
     * @return
     */
    public List<Long> queryBatchNoNeedPrintOutConfig(List<Long> summerFarmMIds){
        if(CollectionUtils.isEmpty(summerFarmMIds)){
            return Collections.emptyList();
        }
        MerchantStoreExtQueryReq req = new MerchantStoreExtQueryReq();
        req.setMIds(summerFarmMIds);
        req.setProKey(MerchantPropertiesExtEnum.TMS_PRINT_OUT_CONFIG.name());
        try {
            DubboResponse<List<MerchantStoreExtResp>> resp = merchantStoreExtQueryProvider.getMerchantStoreExtByQuery(req);
            if(Objects.isNull(resp)){
                throw new ProviderException("merchantStoreExtQueryProvider getMerchantStoreExtByQuery error");
            }
            if(!DubboResponse.COMMON_SUCCESS_CODE.equals(resp.getCode())){
                throw new ProviderException(resp.getMsg());
            }
            return resp.getData().stream().filter(config -> Objects.equals(config.getProValue(),"1")).map(MerchantStoreExtResp::getMId).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询店铺不需要打印配送单的门店失败", e);
            return Collections.emptyList();
        }
    }

}
