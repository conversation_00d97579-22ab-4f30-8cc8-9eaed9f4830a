package net.summerfarm.tms.facade.saas.dto;

import lombok.Data;

/**
 * Description:SAAS门店地址审核详情
 * date: 2024/1/29 15:01
 *
 * <AUTHOR>
 */
@Data
public class StoreAddressAuditDTO {

    /**
     * 审批单ID
     */
    private Long id;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核状态 1:待审核;2:审核成功;3:审核失败
     */
    private Integer auditStatus;

    public Integer getUnifiedStatus(){
        if (this.auditStatus == null){
            return null;
        }
        //鲜沐地址审核状态 0 待审核, 1 审核通过 ,2 拒绝重新交, 3 审核失败
        return this.auditStatus - 1;
    }
}
