package net.summerfarm.tms.facade.saas;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.merchant.MerchantStoreProvider;
import com.cosfo.manage.client.merchant.req.MerchantStoreQueryReq;
import com.cosfo.manage.client.merchant.resp.MerchantStoreResp;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.cosfo.oms.client.provider.merchant.StoreAddressAuditProvider;
import com.cosfo.oms.client.req.AddressAuditQueryReq;
import com.cosfo.oms.client.resp.StoreAddressAuditResp;
import com.cosfo.ordercenter.client.req.QueryResentOrderAfterSaleReq;
import com.cosfo.ordercenter.client.resp.QueryResentOrderAfterSaleDTO;
import com.cosfo.ordercenter.client.service.OrderAfterSaleQueryService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.facade.saas.converter.MerchantStoreConverter;
import net.summerfarm.tms.facade.saas.converter.SaasAfterSaleConverter;
import net.summerfarm.tms.facade.saas.converter.StoreAddressAuditConverter;
import net.summerfarm.tms.facade.saas.converter.TenantConverter;
import net.summerfarm.tms.facade.saas.dto.MerchantStoreDTO;
import net.summerfarm.tms.facade.saas.dto.AfterSaleDTO;
import net.summerfarm.tms.facade.saas.dto.StoreAddressAuditDTO;
import net.summerfarm.tms.facade.saas.dto.TenantDTO;
import net.summerfarm.tms.facade.saas.input.SaasAfterSaleOrderInput;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:Saas查询Facade
 * date: 2023/3/22 18:48
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SaasQueryFacade {

    @DubboReference
    private MerchantStoreProvider merchantStoreProvider;
    @DubboReference
    private TenantProvider tenantProvider;
    @DubboReference
    private OrderAfterSaleQueryService orderAfterSaleQueryService;
    @DubboReference
    private StoreAddressAuditProvider storeAddressAuditProvider;

    public List<TenantDTO> queryTenants(List<Long> tenantIds){
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setTenantIds(tenantIds);
        DubboResponse<List<TenantResp>> response = tenantProvider.list(tenantQueryReq);
        if (response == null || !response.isSuccess()){
            throw new ProviderException("调用Saas接口获取品牌信息异常");
        }
        List<TenantResp> data = response.getData();
        if (CollectionUtils.isEmpty(data)){
            return new ArrayList<>();
        }
        return data.stream().map(TenantConverter::resp2dto).collect(Collectors.toList());
    }

    public List<MerchantStoreDTO> queryMerchantStores(List<Long> storeIds){
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setStoreIds(storeIds);
        DubboResponse<List<MerchantStoreResp>> response = merchantStoreProvider.batchQueryStoreInfo(merchantStoreQueryReq);
        if (response == null || !response.isSuccess()){
            throw new ProviderException("调用Saas接口获取门店信息异常");
        }
        List<MerchantStoreResp> data = response.getData();
        if (CollectionUtils.isEmpty(data)){
            return new ArrayList<>();
        }
        return data.stream().map(MerchantStoreConverter::resp2dto).collect(Collectors.toList());
    }

    public List<AfterSaleDTO> querySaasAfterSale(SaasAfterSaleOrderInput saasAfterSaleOrderInput){
        QueryResentOrderAfterSaleReq req = new QueryResentOrderAfterSaleReq();
        req.setCreateDate(saasAfterSaleOrderInput.getCreateDate());
        req.setDeliveryDate(saasAfterSaleOrderInput.getDeliveryDate());
        req.setSku(saasAfterSaleOrderInput.getSku());
        if(StrUtil.isNotBlank(saasAfterSaleOrderInput.getMId())){
            req.setStoreId(Long.parseLong(saasAfterSaleOrderInput.getMId()));
        }

        log.info("orderAfterSaleQueryService.queryResentOrderAfterSaleForTms req:{}", JSON.toJSONString(req));
        DubboResponse<List<QueryResentOrderAfterSaleDTO>> resp = null;
        try {
            resp = orderAfterSaleQueryService.queryResentOrderAfterSaleForTms(req);
        } catch (Exception e) {
            log.error("调用Saas接口获取售后单异常:{}",e.getMessage());
            throw new ProviderException("调用Saas接口获取售后单失败");
        }
        log.info("orderAfterSaleQueryService.queryResentOrderAfterSaleForTms resp:{}", JSON.toJSONString(resp));

        if (resp == null || !resp.isSuccess()){
            throw new ProviderException("调用Saas接口获取售后单失败");
        }
        List<QueryResentOrderAfterSaleDTO> data = resp.getData();
        if (CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return data.stream().map(SaasAfterSaleConverter::resp2Dto).collect(Collectors.toList());
    }

    public StoreAddressAuditDTO queryStoreAddressAuditRecord(Long recordId){
        AddressAuditQueryReq req = new AddressAuditQueryReq();
        req.setAuditNo(String.valueOf(recordId));
        DubboResponse<StoreAddressAuditResp> response = storeAddressAuditProvider.queryStoreAddressAuditRecord(req);
        if (response == null || !response.isSuccess()){
            throw new ProviderException("调用Saas接口获取门店地址审核信息异常");
        }
        return StoreAddressAuditConverter.resp2Dto(response.getData());
    }

    /**
     * 根据门店ID集合查询配送面单是否打印价格 true-打印 false-关闭 默认true
     * @param storeIdList 门店集合ID
     * @return 结果
     */
    public Map<Long, Boolean> queryDeliveryNotePrintPriceSwitch(List<Long> storeIdList) {
        if(CollectionUtils.isEmpty(storeIdList)){
            return Collections.emptyMap();
        }
        DubboResponse<Map<Long, Boolean>> response = tenantProvider.queryDeliveryNotePrintPriceSwitch(storeIdList);
        if (!response.isSuccess()) {
            log.error("invoke tenantProvider.queryDeliveryNotePrintPriceSwitch fail request:{}, msg:{}", JSONUtil.toJsonStr(storeIdList), response.getMsg(),new BizException("根据门店ID集合查询配送面单是否打印价格 ，" + response.getMsg()));
            return Collections.emptyMap();
        }
        if(response.getData() == null){
            return Collections.emptyMap();
        }
        return response.getData();
    }
}
