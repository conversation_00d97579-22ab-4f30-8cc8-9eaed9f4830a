package net.summerfarm.tms.facade.userCenter.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description:门店查询
 * date: 2024/02/06 11:04
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantInput {

    /**
     * 鲜沐门店ID集合
     */
    private List<Long> mIds;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店状态
     */
    private Integer status;
}
