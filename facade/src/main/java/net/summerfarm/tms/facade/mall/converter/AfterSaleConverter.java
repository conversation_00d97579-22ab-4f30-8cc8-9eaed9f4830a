package net.summerfarm.tms.facade.mall.converter;

import net.summerfarm.mall.client.resp.AfterSaleOrderQueryResp;
import net.summerfarm.tms.facade.saas.dto.AfterSaleDTO;

/**
 * Description: <br/>
 * date: 2023/9/12 11:57<br/>
 *
 * <AUTHOR> />
 */
public class AfterSaleConverter {

    public static AfterSaleDTO resp2Dto(AfterSaleOrderQueryResp resp){
        if(resp == null){
            return null;
        }
        AfterSaleDTO afterSaleDTO = new AfterSaleDTO();

        afterSaleDTO.setAfterSaleOrderNo(resp.getAfterSaleOrderNo());
        afterSaleDTO.setCreateTime(resp.getAddTime());
        afterSaleDTO.setCreateUser(resp.getApplyer());
        afterSaleDTO.setSuitId(resp.getSuitId());
        afterSaleDTO.setSource("0");
        afterSaleDTO.setOrderNo(resp.getOrderNo());
        afterSaleDTO.setStatus(resp.getStatus());

        return afterSaleDTO;
    }
}
