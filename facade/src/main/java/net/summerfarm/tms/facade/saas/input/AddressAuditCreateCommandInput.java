package net.summerfarm.tms.facade.saas.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * Description:地址审核创建命令输入
 * date: 2024/1/29 17:04
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressAuditCreateCommandInput {

    /**
     * 租户ID
     */
    @NotNull(message = "租户Id不能为空")
    private Long tenantId;

    /**
     * 门店ID
     */
    @NotNull(message = "门店Id不能为空")
    private Long storeId;

    /**
     * 省
     */
    @NotNull(message = "省不能为空")
    private String province;

    /**
     * 市
     */
    @NotNull(message = "市不能为空")
    private String city;

    /**
     * 区
     */
    @NotNull(message = "区不能为空")
    private String area;

    /**
     * 详细地址
     */
    @NotNull(message = "详细地址不能为空")
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 商家腾讯地图坐标
     */
    @NotNull(message = "poi不能为空")
    private String poi;

    @NotNull(message = "distance不能为空")
    private Long distance;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * tms发起的审批单号
     */
    @Size(
            min = 1,
            max = 50,
            message = "auditNo在30个字符内"
    )
    @NotNull(message = "auditNo不能为空")
    private String auditNo;
}
