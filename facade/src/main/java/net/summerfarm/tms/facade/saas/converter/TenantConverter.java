package net.summerfarm.tms.facade.saas.converter;

import com.cosfo.manage.client.tenant.resp.TenantResp;
import net.summerfarm.tms.facade.saas.dto.TenantDTO;

/**
 * Description:Tenant转换器
 * date: 2023/3/23 13:50
 *
 * <AUTHOR>
 */
public class TenantConverter {

    public static TenantDTO resp2dto(TenantResp tenantResp){
        if (tenantResp == null){
            return null;
        }
        TenantDTO tenantDTO = new TenantDTO();
        tenantDTO.setId(tenantResp.getId());
        tenantDTO.setTenantName(tenantResp.getTenantName());
        return tenantDTO;
    }
}
