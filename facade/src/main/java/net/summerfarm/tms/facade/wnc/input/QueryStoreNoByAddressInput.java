package net.summerfarm.tms.facade.wnc.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * Description: 根据地址查询城配仓编号<br/>
 * date: 2024/4/12 14:56<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryStoreNoByAddressInput {
    /**
     * 城市
     */
    private String city;
    /**
     * 区域
     */
    private String area;

    /**
     * poi
     */
    private String poi;

    /**
     * 地址ID
     */
    private String contactId;

    /**
     * 租户ID
     */
    private String tenantId;
}
