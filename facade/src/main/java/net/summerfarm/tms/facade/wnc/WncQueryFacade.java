package net.summerfarm.tms.facade.wnc;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.wnc.converter.AdCodeMsgConverter;
import net.summerfarm.tms.facade.wnc.converter.WarehouseLogisticsConverter;
import net.summerfarm.tms.facade.wnc.converter.WarehouseStorageCenterConverter;
import net.summerfarm.tms.facade.wnc.dto.AdCodeMsgDTO;
import net.summerfarm.tms.facade.wnc.dto.WarehousLogisticsCenterDTO;
import net.summerfarm.tms.facade.wnc.dto.WarehouseStorageCenterDTO;
import net.summerfarm.tms.facade.wnc.input.*;
import net.summerfarm.wnc.client.provider.fence.AdCodeMsgQueryProvider;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.provider.nacos.NacosConfigQueryProvider;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseLogisticsQueryProvider;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.StoreQueryReq;
import net.summerfarm.wnc.client.req.WarehouseLogisticsQueryReq;
import net.summerfarm.wnc.client.req.XmWarehouseQueryReq;
import net.summerfarm.wnc.client.req.fence.AdCodePageQueryReq;
import net.summerfarm.wnc.client.req.fence.StoreNoAddrStatusQueryReq;
import net.summerfarm.wnc.client.resp.StoreQueryResp;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageCenterResp;
import net.summerfarm.wnc.client.resp.fence.AdCodeMsgResp;
import net.summerfarm.wnc.client.resp.nacos.PopHelpOrderMerchantResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/10/20 15:15<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class WncQueryFacade {
    @DubboReference
    private WarehouseStorageQueryProvider warehouseStorageQueryProvider;
    @DubboReference
    private WarehouseLogisticsQueryProvider warehouseLogisticsQueryProvider;
    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;
    @DubboReference
    private AdCodeMsgQueryProvider adCodeMsgQueryProvider;
    @DubboReference
    private NacosConfigQueryProvider nacosConfigQueryProvider;

    /**
     * 查询城配仓信息
     * @param input 查询
     * @return 城配仓结果
     */
    public List<WarehousLogisticsCenterDTO> queryWarehouseLogisticsList(WarehouseLogisticsQueryInput input){
        WarehouseLogisticsQueryReq req = new WarehouseLogisticsQueryReq();
        req.setStoreNo(input.getStoreNo());
        req.setStoreName(input.getStoreName());
        req.setStatus(input.getStatus());
        req.setStoreNos(input.getStoreNos());
        log.info("warehouseLogisticsQueryProvider.queryWarehouseLogisticsList req:{}", JSON.toJSONString(req));

        DubboResponse<List<WarehousLogisticsCenterResp>> listDubboResponse = warehouseLogisticsQueryProvider.queryWarehouseLogisticsList(req);
        log.info("warehouseLogisticsQueryProvider.queryWarehouseLogisticsList resp:{}", JSON.toJSONString(listDubboResponse));
        if(listDubboResponse == null) {
            log.error("调用WNC获取城配仓信息异常");
            return Collections.emptyList();
        }
        if(!listDubboResponse.isSuccess()){
            log.error("调用WNC获取城配仓信息异常:{}",listDubboResponse.getMsg());
            return Collections.emptyList();
        }
        if(CollectionUtils.isEmpty(listDubboResponse.getData())){
            return Collections.emptyList();
        }
        return listDubboResponse.getData().stream().map(WarehouseLogisticsConverter::resp2DTO).collect(Collectors.toList());
    }

    /**
     * 查询仓库信息
     * @param input 查询
     * @return 仓库信息
     */
    public List<WarehouseStorageCenterDTO> queryXmWarehouseList(XmWarehouseQueryInput input){
        XmWarehouseQueryReq req = new XmWarehouseQueryReq();
        req.setWarehouseNo(input.getWarehouseNo());
        req.setStatus(input.getStatus());
        req.setType(input.getType());
        req.setWarehouseName(input.getWarehouseName());
        req.setWarehouseNos(input.getWarehouseNos());

        log.info("warehouseStorageQueryProvider.queryXmWarehouseList req:{}", JSON.toJSONString(req));
        DubboResponse<List<WarehouseStorageCenterResp>> listDubboResponse = warehouseStorageQueryProvider.queryXmWarehouseList(req);
        log.info("warehouseStorageQueryProvider.queryXmWarehouseList resp:{}", JSON.toJSONString(listDubboResponse));
        if(listDubboResponse == null){
            log.error("调用WNC获取鲜沐仓库信息异常");
            return Collections.emptyList();
        }
        if(!listDubboResponse.isSuccess()){
            log.error("调用WNC获取鲜沐仓库信息异常:{}",listDubboResponse.getMsg());
            return Collections.emptyList();
        }
        if(CollectionUtils.isEmpty(listDubboResponse.getData())){
            return Collections.emptyList();
        }
        return listDubboResponse.getData().stream().map(WarehouseStorageCenterConverter::resp2DTO).collect(Collectors.toList());
    }

    /**
     * 查询城配仓
     * @param input 查询参数
     * @return 城配仓
     */
    public Integer queryStoreNo(QueryStoreNoByAddressInput input){
        if(input.getTenantId() == null){
            throw new TmsRuntimeException("获取城配仓信息，参数租户ID不能为空");
        }
        if(StringUtils.isEmpty(input.getCity())){
            throw new TmsRuntimeException("获取城配仓信息，城市不能为空");
        }
        StoreQueryReq req = new StoreQueryReq();
        req.setCity(input.getCity());
        req.setArea(input.getArea());
        req.setContactId(StringUtils.isEmpty(input.getContactId())? null : Long.parseLong(input.getContactId()));
        req.setTenantId(Long.parseLong(input.getTenantId()));
        req.setPoi(input.getPoi());
        log.info("deliveryFenceQueryProvider.queryStoreByAddress req:{}",JSON.toJSONString(req));
        DubboResponse<StoreQueryResp> resp = deliveryFenceQueryProvider.queryStoreByAddress(req);
        log.info("deliveryFenceQueryProvider.queryStoreByAddress resp:{}",JSON.toJSONString(resp));

        if(resp == null || !resp.isSuccess()){
            throw new TmsRuntimeException("调用WNC根据地址获取城配仓信息异常");
        }
        if(resp.getData() == null || resp.getData().getStoreNo() == null){
            throw new TmsRuntimeException("未获取到地址对应的城配仓信息");
        }
        return resp.getData().getStoreNo();
    }
    /**
     * 查询所有的POP城配仓
     * @return 城配仓编号集合
     */
    public List<Integer> queryPopWarehouseLogisticsList() {
        DubboResponse<List<WarehousLogisticsCenterResp>> response = warehouseLogisticsQueryProvider.queryPopWarehouseLogisticsList();
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.error("WarehouseLogisticsQueryFacede[]queryPopWarehouseLogisticsList[]error cause:{}", JSON.toJSONString(response));
            throw new BizException("调用仓网接口异常");
        }
        List<WarehousLogisticsCenterResp> data = response.getData();
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return data.stream().map(WarehousLogisticsCenterResp::getStoreNo).collect(Collectors.toList());
    }
    /**
     * 查询城配仓是否支持加单
     * @return 结果true支持、false不支持
     */
    public Boolean queryIsSupportAddOrder(Integer storeNo) {
        if(storeNo == null){
            throw new TmsRuntimeException("获取城配仓截单时间，参数storeNo不能为空");
        }
        log.info("warehouseLogisticsQueryProvider.queryIsSupportAddOrder req:{}", storeNo);
        DubboResponse<Boolean> resp = warehouseLogisticsQueryProvider.queryIsSupportAddOrder(storeNo);
        log.info("warehouseLogisticsQueryProvider.queryIsSupportAddOrder resp:{}", JSON.toJSONString(resp));
        if(resp == null || !resp.isSuccess()){
            throw new TmsRuntimeException("调用WNC查询城配仓是否支持加单异常");
        }
        if(resp.getData() == null){
            throw new TmsRuntimeException("调用WNC查询城配仓是否支持加单无数据");
        }
        return resp.getData();
    }

    /**
     * 查询POP城配仓
     * @return 结果
     */
    public List<String> queryPopStoreNosList(){
        List<Integer> popWarehouseLogisticsList = new ArrayList<>();
        try {
            popWarehouseLogisticsList = this.queryPopWarehouseLogisticsList();
        } catch (Exception e) {
            log.error("获取所有POP城配仓异常",e);
        }
        return popWarehouseLogisticsList.stream().map(String::valueOf).collect(Collectors.toList());
    }

    /**
     * 根据code查询有效的区域信息
     * @param adCodes 编号
     */
    public List<AdCodeMsgDTO> queryEffectiveAdCodeMsgByCodes(List<String> adCodes){
        if(CollectionUtils.isEmpty(adCodes)){
            return Collections.emptyList();
        }

        return this.queryAdCodeMsg(AdCodeMsgQueryInput.builder()
                .adCodeList(adCodes)
                .status(0)
                .build());
    }

    /**
     * 根据城配仓、状态等查询区域信息
     * @param input 查询
     * @return 结果
     */
    public List<AdCodeMsgDTO> queryAdCodeMsgByStoreNoAddrStatus(AdAreaQueryInput input){
        if(input.getStoreNo() == null){
            return Collections.emptyList();
        }
        StoreNoAddrStatusQueryReq req = new StoreNoAddrStatusQueryReq();
        req.setStoreNo(input.getStoreNo());
        req.setAdCodeMsgStatus(input.getAdCodeMsgStatus());
        req.setFenceStatus(input.getFenceStatus());
        req.setWarehouseLogisticsCenterStatus(input.getWarehouseLogisticsCenterStatus());
        req.setProvinceCityAreaLikeName(input.getProvinceCityAreaLikeName());
        req.setAdCode(input.getAdCode());
        req.setCityLikeName(input.getCityLikeName());

        DubboResponse<List<AdCodeMsgResp>> resp = adCodeMsgQueryProvider.queryAdCodeMsgByStoreNoAddrStatus(req);
        if(resp == null || !resp.isSuccess()){
            throw new TmsRuntimeException("WNC adCodeMsgQueryProvider queryAdCodeMsgByStoreNoAddrStatus error");
        }
        List<AdCodeMsgResp> data = resp.getData();
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return data.stream().map(AdCodeMsgConverter::dto2Resp).collect(Collectors.toList());
    }

    /**
     * 根据code查询有效的区域信息
     * @param input 编号
     */
    public List<AdCodeMsgDTO> queryAdCodeMsg(AdCodeMsgQueryInput input){
        AdCodePageQueryReq req = new AdCodePageQueryReq();
        req.setAdCode(input.getAdCode());
        req.setProvince(input.getProvince());
        req.setCity(input.getCity());
        req.setArea(input.getArea());
        req.setStatus(input.getStatus());
        req.setFenceId(input.getFenceId());
        req.setAdCodeList(input.getAdCodeList());
        req.setStoreNos(input.getStoreNos());
        req.setStoreStatus(input.getStoreStatus());
        req.setFenceStatus(input.getFenceStatus());
        req.setPageSize(200);
        req.setStatus(0);
        int pageNum = NumberUtils.INTEGER_ONE;
        boolean hasNextPage;

        List<AdCodeMsgResp> res = Lists.newArrayList();
        do {
            req.setPageIndex(pageNum);
            DubboResponse<PageInfo<AdCodeMsgResp>> pageInfoDubboResponse = adCodeMsgQueryProvider.queryAdCodeMsgPage(req);
            if (Objects.isNull(pageInfoDubboResponse.getData())) {
                break;
            }
            List<AdCodeMsgResp> list = pageInfoDubboResponse.getData().getList();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            res.addAll(list);
            pageNum++;
            hasNextPage = pageInfoDubboResponse.getData().isHasNextPage();
        } while (hasNextPage);

        return res.stream().map(AdCodeMsgConverter::dto2Resp).collect(Collectors.toList());
    }

    /**
     * 查询所有的共配POP城配仓
     * @return 城配仓编号集合
     */
    public List<Integer> querySharedDeliveryPopWarehouseLogistics() {
        DubboResponse<List<WarehousLogisticsCenterResp>> response = warehouseLogisticsQueryProvider.querySharedDeliveryPopWarehouseLogistics();
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.error("WarehouseLogisticsQueryFacede[]querySharedDeliveryPopWarehouseLogistics[]error cause:{}", JSON.toJSONString(response));
            throw new BizException("调用仓网接口异常");
        }
        List<WarehousLogisticsCenterResp> data = response.getData();
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return data.stream().map(WarehousLogisticsCenterResp::getStoreNo).collect(Collectors.toList());
    }

    /**
     * 获取POP代下单的门店ID
     * @return 门店编号集合
     */
    public List<Long> queryPopHelperOrderMerchantInfo() {
        DubboResponse<PopHelpOrderMerchantResp> response = nacosConfigQueryProvider.queryPopHelperOrderMerchantInfo();
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.error("WarehouseLogisticsQueryFacede[]queryPopHelperOrderMerchantInfo[]error cause:{}", JSON.toJSONString(response));
            throw new BizException("调用仓网接口异常");
        }
        PopHelpOrderMerchantResp data = response.getData();
        if(data == null || CollectionUtils.isEmpty(data.getMIdList())){
            return Collections.emptyList();
        }

        return data.getMIdList();
    }
}
