package net.summerfarm.tms.facade.mallmanage.converter;

import net.summerfarm.client.common.enums.MerchantShowPriceEnum;
import net.summerfarm.client.resp.merchant.MerchantShowPriceResultResp;
import net.summerfarm.tms.facade.mallmanage.MerchantShowPriceResultDTO;

import java.util.Objects;

/**
 * Description: 门店转换类<br/>
 * date: 2025/1/20 14:32<br/>
 *
 * <AUTHOR> />
 */
public class MerchantConverter {

    public static MerchantShowPriceResultDTO resp2DTOConvert(MerchantShowPriceResultResp resp) {
        if(resp == null){
            return null;
        }
        MerchantShowPriceResultDTO dto = new MerchantShowPriceResultDTO();
        if(Objects.equals(MerchantShowPriceEnum.SHOW.getCode(), resp.getShowPrice())){
            dto.setShowPrice(true);
        }else{
            dto.setShowPrice(false);
        }
        dto.setMId(resp.getMId());
        return dto;
    }
}
