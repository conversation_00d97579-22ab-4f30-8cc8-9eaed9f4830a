package net.summerfarm.tms.facade.saas.converter;

import com.cosfo.ordercenter.client.resp.QueryResentOrderAfterSaleDTO;
import net.summerfarm.tms.facade.saas.dto.AfterSaleDTO;

/**
 * Description: <br/>
 * date: 2023/9/12 11:57<br/>
 *
 * <AUTHOR> />
 */
public class SaasAfterSaleConverter {

    public static AfterSaleDTO resp2Dto(QueryResentOrderAfterSaleDTO resp){
        if(resp == null){
            return null;
        }
        AfterSaleDTO afterSaleDTO = new AfterSaleDTO();

        afterSaleDTO.setAfterSaleOrderNo(resp.getAfterSaleOrderNo());
        afterSaleDTO.setCreateTime(resp.getCreateTime());
        afterSaleDTO.setCreateUser(resp.getCreateUser());
        afterSaleDTO.setSource("1");

        return afterSaleDTO;
    }
}
