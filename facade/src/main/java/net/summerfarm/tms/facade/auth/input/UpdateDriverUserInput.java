package net.summerfarm.tms.facade.auth.input;

/**
 * Description: <br/>
 * date: 2023/5/31 11:49<br/>
 *
 * <AUTHOR> />
 */

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class UpdateDriverUserInput implements Serializable {

    /**
     * 用户中心表 user_base_id
     */
    private Long baseUserId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 密码
     */
    private String password;

    /**
     * 状态
     */
    private Integer status;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 业务bizid
     */
    private Long bizUserId;
}
