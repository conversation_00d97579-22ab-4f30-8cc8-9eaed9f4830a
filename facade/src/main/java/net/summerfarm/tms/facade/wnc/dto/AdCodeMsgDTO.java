package net.summerfarm.tms.facade.wnc.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: AdCodeMsgDTO <br/>
 * date: 2024/11/15 14:16<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AdCodeMsgDTO {

    private Integer id;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 等级
     */
    private String level;

    /**
     * 高德id
     */
    private String gdId;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    /**
     * 状态 0 正常 1 失效 3停用
     */
    private Integer status;

    /**
     * 围栏id
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 状态，0：正常，3：暂停
     */
    private Integer fenceStatus;

    /**
     * 物流中心编号（配送仓编号）
     */
    private Integer storeNo;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 城配仓状态：0、失效 1、有效
     */
    private Integer storeStatus;
    public String getFullAddress(){
        String province = this.province != null ? this.province : "";
        String city = this.city != null ? this.city : "";
        String area = this.area != null ? this.area : "";

        return province + city + area;
    }
}
