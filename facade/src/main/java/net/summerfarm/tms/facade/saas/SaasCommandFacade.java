package net.summerfarm.tms.facade.saas;

import com.cosfo.oms.client.provider.merchant.StoreAddressAuditProvider;
import com.cosfo.oms.client.req.AddressAuditCreateReq;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.facade.saas.converter.StoreAddressAuditConverter;
import net.summerfarm.tms.facade.saas.input.AddressAuditCreateCommandInput;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * Description:Saas操作Facade
 * date: 2024/1/29 14:48
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SaasCommandFacade {

    @DubboReference
    private StoreAddressAuditProvider storeAddressAuditProvider;

    public Long createAddressAudit(AddressAuditCreateCommandInput input){
        AddressAuditCreateReq req = StoreAddressAuditConverter.input2Req(input);
        DubboResponse<Long> response = storeAddressAuditProvider.createAddressAudit(req);
        if (response == null || !response.isSuccess()){
            log.error("调用Saas接口创建门店地址审核异常，返回信息：{}", response.getMsg());
            throw new ProviderException("修改失败，请重新修改或联系售后人员处理");
        }
        return response.getData();
    }
}
