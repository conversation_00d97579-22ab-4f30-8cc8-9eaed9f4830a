package net.summerfarm.tms.facade.auth.converter;

import net.summerfarm.tms.facade.auth.dto.AuthUserDTO;
import net.xianmu.authentication.client.dto.user.AuthUserResp;

/**
 * Description: 转换类<br/>
 * date: 2024/10/23 14:27<br/>
 *
 * <AUTHOR> />
 */
public class AuthUserDTOConverter {

    public static AuthUserDTO resp2DTO(AuthUserResp resp) {
        if(resp == null){
            return null;
        }
        AuthUserDTO dto = new AuthUserDTO();

        dto.setId(resp.getId());
        dto.setUserBaseId(resp.getUserBaseId());
        dto.setTenantId(resp.getTenantId());
        dto.setCreateTime(resp.getCreateTime());
        dto.setStatus(resp.getStatus());
        dto.setSystemOrigin(resp.getSystemOrigin());
        dto.setUpdateTime(resp.getUpdateTime());
        dto.setBizUserId(resp.getBizUserId());
        dto.setPassword(resp.getPassword());
        dto.setLastLoginTime(resp.getLastLoginTime());
        dto.setAuditStatus(resp.getAuditStatus());
        dto.setOpenid(resp.getOpenid());
        dto.setMpOpenid(resp.getMpOpenid());
        dto.setUnionid(resp.getUnionid());
        dto.setName(resp.getName());
        dto.setPhone(resp.getPhone());

        return dto;
    }
}
