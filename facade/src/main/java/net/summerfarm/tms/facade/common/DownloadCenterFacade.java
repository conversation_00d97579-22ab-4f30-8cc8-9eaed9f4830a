package net.summerfarm.tms.facade.common;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.tms.facade.common.input.DownloadCenterInitInput;
import net.summerfarm.tms.facade.common.input.DownloadCenterUploadInput;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description:下载中心
 * date: 2024/2/20 15:58
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DownloadCenterFacade {

    private final String BIZ_ERR_PRE = "DC-BIZ-";

    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;

    /**
     * 初始化文件下载记录
     * @param input 输入参数
     * @return 返回文件id
     */
    public Long initRecord(DownloadCenterInitInput input) {
        log.info("下载中心文件初始化入参: {}", JSON.toJSONString(input));
        DownloadCenterInitReq downloadCenterInitReq = new DownloadCenterInitReq();
        downloadCenterInitReq.setUserId(input.getUserId());
        downloadCenterInitReq.setBizType(input.getBizType());
        downloadCenterInitReq.setFileName(input.getFileName());
        downloadCenterInitReq.setFileExpiredDay(input.getFileExpiredDay());


        DubboResponse<DownloadCenterResp> response = downloadCenterProvider.initRecord(downloadCenterInitReq);
        if (response == null || !response.isSuccess()){
            throw new ProviderException("下载任务创建失败");
        }
        DownloadCenterResp data = response.getData();
        if (Objects.isNull(data) || Objects.isNull(data.getResId())) {
            log.error("文件初始化失败: {}", JSON.toJSONString(data));
            throw new ProviderException("下载任务创建失败");
        }
        return data.getResId();
    }

    /**
     * 更新文件下载记录状态
     * @param input   输入参数
     */
    public void updateFile(DownloadCenterUploadInput input) {
        log.info("下载中心文件状态更新入参:{}", JSON.toJSONString(input));
        DownloadCenterUploadReq uploadReq = new DownloadCenterUploadReq();
        uploadReq.setResId(input.getResId());
        uploadReq.setBizStatus(input.getBizStatus());
        uploadReq.setFilePath(input.getFilePath());
        DubboResponse<Boolean> response = downloadCenterProvider.uploadFile(uploadReq);
        if (!response.isSuccess()) {
            String code = response.getCode();
            if (StringUtils.isEmpty(code)) {
                throw new ProviderException("下载任务创建失败");
            }
            if (code.startsWith(BIZ_ERR_PRE)) {
                log.warn("下载中心业务异常，可以忽视");
                return;
            }
            // 下载中心调用失败 异常情况
            throw new ProviderException("下载任务创建失败");
        }
    }
}
