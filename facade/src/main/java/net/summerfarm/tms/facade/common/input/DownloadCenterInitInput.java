package net.summerfarm.tms.facade.common.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.common.client.enums.DownloadCenterEnum;

/**
 * Description:
 * date: 2024/2/20 16:19
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DownloadCenterInitInput {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 业务类型 @必填
     */
    private Integer bizType;

    /**
     * 业务ID @选填
     */
    private String bizId;

    /**
     * 查询参数（json格式）
     */
    private String params;

    /**
     * 文件名称 @必填
     */
    private String fileName;

    /**
     * 来源
     */
    private DownloadCenterEnum.RequestSource source;

    /**
     * 设定文件过期天数 @推荐填写,默认三天
     */
    private DownloadCenterEnum.FileExpiredDayEnum fileExpiredDay;
}
