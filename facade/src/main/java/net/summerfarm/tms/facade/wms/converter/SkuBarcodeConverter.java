package net.summerfarm.tms.facade.wms.converter;

import net.summerfarm.tms.facade.wms.dto.SkuBarcodeDTO;
import net.summerfarm.wms.sku.resp.SkuBarcodeResp;

/**
 * Description:SKU条码转换器
 * date: 2024/1/12 18:00
 *
 * <AUTHOR>
 */
public class SkuBarcodeConverter {

    public static SkuBarcodeDTO resp2dto(SkuBarcodeResp skuBarcodeResp){

        if(skuBarcodeResp == null){
            return null;
        }
        SkuBarcodeDTO skuBarcodeDTO = new SkuBarcodeDTO();
        skuBarcodeDTO.setSku(skuBarcodeResp.getSku());
        skuBarcodeDTO.setBarcodes(skuBarcodeResp.getBarcodes());
        return skuBarcodeDTO;
    }
}
