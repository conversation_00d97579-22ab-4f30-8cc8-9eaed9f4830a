package net.summerfarm.tms.facade.mall.input;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

/**
 * Description: <br/>
 * date: 2023/3/17 18:11<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
public class DriverAppraiseInput {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 联系人
     */
    private String contactId;
    /**
     * 配送日期
     */
    private LocalDate deliveryTime;
}
