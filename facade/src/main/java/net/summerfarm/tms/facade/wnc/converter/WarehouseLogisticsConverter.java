package net.summerfarm.tms.facade.wnc.converter;

import net.summerfarm.tms.facade.wnc.dto.WarehousLogisticsCenterDTO;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;

/**
 * Description: <br/>
 * date: 2023/10/20 15:21<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseLogisticsConverter {

    public static WarehousLogisticsCenterDTO resp2DTO(WarehousLogisticsCenterResp resp){
        if(resp == null){
            return null;
        }
        WarehousLogisticsCenterDTO dto = new WarehousLogisticsCenterDTO();

        dto.setId(resp.getId());
        dto.setStoreNo(resp.getStoreNo());
        dto.setStoreName(resp.getStoreName());
        dto.setStatus(resp.getStatus());
        dto.setManageAdminId(resp.getManageAdminId());
        dto.setPoiNote(resp.getPoiNote());
        dto.setAddress(resp.getAddress());
        dto.setCloseOrderType(resp.getCloseOrderType());
        dto.setOriginStoreNo(resp.getOriginStoreNo());
        dto.setSotFinishTime(resp.getSotFinishTime());
        dto.setCreator(resp.getCreator());
        dto.setUpdater(resp.getUpdater());
        dto.setUpdateTime(resp.getUpdateTime());
        dto.setCreateTime(resp.getCreateTime());
        dto.setCloseTime(resp.getCloseTime());
        dto.setUpdateCloseTime(resp.getUpdateCloseTime());
        dto.setPersonContact(resp.getPersonContact());
        dto.setPhone(resp.getPhone());
        dto.setRegion(resp.getRegion());
        dto.setStorePic(resp.getStorePic());

        return dto;
    }
}
