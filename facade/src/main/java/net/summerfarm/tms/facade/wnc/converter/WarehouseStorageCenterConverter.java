package net.summerfarm.tms.facade.wnc.converter;

import net.summerfarm.tms.facade.wnc.dto.WarehouseStorageCenterDTO;
import net.summerfarm.wnc.client.resp.WarehouseStorageCenterResp;

/**
 * Description: <br/>
 * date: 2023/10/20 15:34<br/>
 *
 * <AUTHOR> />
 */
public class WarehouseStorageCenterConverter {

    public static WarehouseStorageCenterDTO resp2DTO(WarehouseStorageCenterResp resp){
        if(resp == null){
            return null;
        }
        WarehouseStorageCenterDTO dto = new WarehouseStorageCenterDTO();

        dto.setId(resp.getId());
        dto.setWarehouseNo(resp.getWarehouseNo());
        dto.setWarehouseName(resp.getWarehouseName());
        dto.setManageAdminId(resp.getManageAdminId());
        dto.setType(resp.getType());
        dto.setAreaManageId(resp.getAreaManageId());
        dto.setStatus(resp.getStatus());
        dto.setAddress(resp.getAddress());
        dto.setPoiNote(resp.getPoiNote());
        dto.setMailToAddress(resp.getMailToAddress());
        dto.setUpdater(resp.getUpdater());
        dto.setUpdateTime(resp.getUpdateTime());
        dto.setCreator(resp.getCreator());
        dto.setCreateTime(resp.getCreateTime());
        dto.setPersonContact(resp.getPersonContact());
        dto.setPhone(resp.getPhone());
        dto.setTenantId(resp.getTenantId());
        dto.setWarehousePic(resp.getWarehousePic());

        return dto;
    }
}
