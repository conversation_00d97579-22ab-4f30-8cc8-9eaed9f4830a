package net.summerfarm.tms.facade.ofc.converter;

import net.summerfarm.ofc.client.resp.fulfillment.FulfillmentDeliveryNoteOrderInfoResp;
import net.summerfarm.ofc.client.resp.fulfillment.FulfillmentDeliveryNoteOrderItemInfoResp;
import net.summerfarm.tms.facade.ofc.dto.FulfillmentDeliveryNoteOrderInfoDTO;
import net.summerfarm.tms.facade.ofc.dto.FulfillmentDeliveryNoteOrderItemInfoDTO;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 履约转换类<br/>
 * date: 2025/1/17 18:09<br/>
 *
 * <AUTHOR> />
 */
public class FulfillmentConverter {

    public static FulfillmentDeliveryNoteOrderInfoDTO deliveryNoteOrderResp2DTO(FulfillmentDeliveryNoteOrderInfoResp resp){
        if(resp == null){
            return null;
        }
        FulfillmentDeliveryNoteOrderInfoDTO dto = new FulfillmentDeliveryNoteOrderInfoDTO();

        dto.setOutOrderNo(resp.getOutOrderNo());
        dto.setTenantId(resp.getTenantId());
        dto.setBrandName(resp.getBrandName());
        dto.setFulfillmentTime(resp.getFulfillmentTime());
        dto.setStoreNo(resp.getStoreNo());
        dto.setStoreSize(resp.getStoreSize());
        dto.setOutClientName(resp.getOutClientName());
        dto.setStoreId(resp.getStoreId());
        dto.setContactId(resp.getContactId());
        dto.setContactName(resp.getContactName());
        dto.setContactPhone(resp.getContactPhone());
        dto.setAddress(resp.getAddress());
        dto.setFulfillmentStatus(resp.getFulfillmentStatus());
        dto.setOrderSource(resp.getOrderSource());
        dto.setFulfillmentType(resp.getFulfillmentType());
        dto.setOrderTotalPrice(resp.getOrderTotalPrice());
        dto.setDeliveryFee(resp.getDeliveryFee());
        dto.setOutTimesFee(resp.getOutTimesFee());
        dto.setAddressRemark(resp.getAddressRemark());
        dto.setOrderRemark(resp.getOrderRemark());
        dto.setBigCustomerId(resp.getBigCustomerId());
        dto.setSellingEntityName(resp.getSellingEntityName());

        List<FulfillmentDeliveryNoteOrderItemInfoResp> fulfillmentOrderItemList = resp.getFulfillmentOrderItemList();
        if(!CollectionUtils.isEmpty(fulfillmentOrderItemList)){
            dto.setFulfillmentOrderItemList(fulfillmentOrderItemList.stream().map(FulfillmentConverter::deliveryNoteOrderItemResp2DTO).collect(Collectors.toList()));
        }

        return dto;
    }

    public static FulfillmentDeliveryNoteOrderItemInfoDTO deliveryNoteOrderItemResp2DTO(FulfillmentDeliveryNoteOrderItemInfoResp resp){
        if(resp == null){
            return null;
        }
        FulfillmentDeliveryNoteOrderItemInfoDTO dto = new FulfillmentDeliveryNoteOrderItemInfoDTO();

        dto.setProductType(resp.getProductType());
        dto.setProductName(resp.getProductName());
        dto.setProductSpecifications(resp.getProductSpecifications());
        dto.setProductAmount(resp.getProductAmount());
        dto.setProductUnitPrice(resp.getProductUnitPrice());
        dto.setProductActualTotalPrice(resp.getProductActualTotalPrice());
        dto.setSku(resp.getSku());
        dto.setSkuName(resp.getSkuName());
        dto.setSkuSpecifications(resp.getSkuSpecifications());
        dto.setSkuStorageLocation(resp.getSkuStorageLocation());
        dto.setSkuWeightNum(resp.getSkuWeightNum());
        dto.setSkuVolume(resp.getSkuVolume());
        dto.setWarehouseNo(resp.getWarehouseNo());

        return dto;
    }
}
