package net.summerfarm.tms.facade.mall;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.admin.AdminQueryProvider;
import net.summerfarm.client.req.admin.AdminQueryReq;
import net.summerfarm.client.resp.admin.AdminResp;
import net.summerfarm.mall.client.provider.AfterSaleOrderProvider;
import net.summerfarm.mall.client.provider.DeliveryEvaluationProvider;
import net.summerfarm.mall.client.req.AfterSaleOrderQueryReq;
import net.summerfarm.mall.client.resp.AfterSaleOrderQueryResp;
import net.summerfarm.mall.client.resp.DeliveryEvaluationQueryResp;
import net.summerfarm.manage.client.merchant.req.QueryMerchantReq;
import net.summerfarm.manage.client.merchant.resp.QueryMerchantResp;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.mall.converter.AfterSaleConverter;
import net.summerfarm.tms.facade.mall.converter.DriverAppraiseConverter;
import net.summerfarm.tms.facade.mall.converter.AdminConverter;
import net.summerfarm.tms.facade.mall.dto.DriverAppraiseDTO;
import net.summerfarm.tms.facade.mall.dto.AdminDTO;
import net.summerfarm.tms.facade.mall.input.AdminQueryInput;
import net.summerfarm.tms.facade.mall.input.AfterSaleReissueOrderInput;
import net.summerfarm.tms.facade.mall.input.DriverAppraiseInput;
import net.summerfarm.tms.facade.saas.dto.AfterSaleDTO;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/1/12 14:32<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class MallQueryFacade {
    @DubboReference
    private DeliveryEvaluationProvider deliveryEvaluationProvider;
    @DubboReference
    private AdminQueryProvider adminQueryProvider;
    @DubboReference
    private AfterSaleOrderProvider afterSaleOrderProvider;


    public DriverAppraiseDTO queryOrderDriverAppraise(DriverAppraiseInput driverAppraiseInput){
        DubboResponse<DeliveryEvaluationQueryResp> deliveryEvaluation = null;
        try {
            log.info("请求商城司机评价接口报文:{}",JSON.toJSONString(driverAppraiseInput));
            deliveryEvaluation = deliveryEvaluationProvider.getDeliveryEvaluation(DriverAppraiseConverter.input2Req(driverAppraiseInput));
        } catch (Exception e) {
            log.error("调用商城获取司机服务异常",e);
            throw new TmsRuntimeException(ErrorCodeEnum.DUBBO_ERROR,"商城司机评价服务");
        }
        if(deliveryEvaluation != null && !deliveryEvaluation.isSuccess()){
            log.error("调用商城获取司机评价异常:{}",deliveryEvaluation.getMsg());
            return null;
        }
        log.info("请求商城司机评价接口返回信息:{}",JSON.toJSONString(deliveryEvaluation));
        return DriverAppraiseConverter.resp2Dto(deliveryEvaluation.getData());
    }

    public List<AdminDTO> queryAdmins(AdminQueryInput adminQueryInput){
        AdminQueryReq req = new AdminQueryReq();
        req.setAdminIds(adminQueryInput.getAdminIds());
        DubboResponse<List<AdminResp>> response = adminQueryProvider.batchQueryAdmins(req);
        if (response == null || !response.isSuccess()){
            throw new ProviderException("调用商城接口获取品牌信息异常");
        }
        List<AdminResp> data = response.getData();
        if (data == null || CollectionUtils.isEmpty(data)){
            return new ArrayList<>();
        }
        return data.stream().map(AdminConverter::resp2dto).collect(Collectors.toList());
    }

    /**
     * 查询售后补发单
     */
    public List<AfterSaleDTO> queryAfterSaleReissueOrder(AfterSaleReissueOrderInput input){
        AfterSaleOrderQueryReq req = new AfterSaleOrderQueryReq();

        req.setAddTime(input.getAddTime());
        req.setMId(input.getMId());
        req.setSku(input.getSku());

        log.info("afterSaleOrderProvider.queryAfterSaleReissueOrder req:{}",JSON.toJSONString(input));
        DubboResponse<List<AfterSaleOrderQueryResp>> resp = null;
        try {
            resp = afterSaleOrderProvider.queryAfterSaleReissueOrder(req);
        } catch (Exception e) {
            log.error("调商城接口获取售后补发单异常:{}",e.getMessage());
            throw new ProviderException("调商城接口获取售后补发单异常");
        }
        log.info("afterSaleOrderProvider.queryAfterSaleReissueOrder resp:{}",JSON.toJSONString(resp));
        if (resp == null || !resp.isSuccess()){
            throw new ProviderException("调商城接口获取售后补发单异常");
        }
        if(CollectionUtils.isEmpty(resp.getData())){
            return Collections.emptyList();
        }

        return resp.getData().stream().map(AfterSaleConverter::resp2Dto).collect(Collectors.toList());
    }
}
