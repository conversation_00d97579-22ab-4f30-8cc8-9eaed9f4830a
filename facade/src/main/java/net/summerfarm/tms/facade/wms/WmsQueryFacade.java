package net.summerfarm.tms.facade.wms;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.mall.dto.SkuDTO;
import net.summerfarm.tms.facade.wms.converter.SkuBarcodeConverter;
import net.summerfarm.tms.facade.wms.converter.WmsConverter;
import net.summerfarm.tms.facade.wms.dto.SkuBarcodeDTO;
import net.summerfarm.tms.facade.wms.dto.SkuBatchCodeDTO;
import net.summerfarm.tms.facade.wms.dto.SkuBatchCodeTraceDTO;
import net.summerfarm.tms.facade.wms.dto.WmsOrderProcessDTO;
import net.summerfarm.tms.facade.wms.input.OrderProcessingQueryInput;
import net.summerfarm.tms.facade.wms.input.QueryBatchTraceCodeByOrderNoInput;
import net.summerfarm.tms.util.ListUtil;
import net.summerfarm.wms.inventory.InventoryQueryProvider;
import net.summerfarm.wms.inventory.req.BatchQueryBySkusReq;
import net.summerfarm.wms.inventory.resp.InventoryResp;
import net.summerfarm.wms.processingtask.ProcessingTaskOrderProvider;
import net.summerfarm.wms.processingtask.dto.req.ProcessingTaskOrderQueryReqDTO;
import net.summerfarm.wms.processingtask.dto.res.ProcessingTaskOrderResDTO;
import net.summerfarm.wms.sku.SkuBarcodeQueryProvider;
import net.summerfarm.wms.sku.req.SkuBarcodeQueryReq;
import net.summerfarm.wms.sku.resp.SkuBarcodeResp;
import net.summerfarm.wms.skucode.SkuBatchCodeQueryProvider;
import net.summerfarm.wms.skucode.req.BatchCodeQueryReq;
import net.summerfarm.wms.skucode.resp.SkuBatchCodeResp;
import net.summerfarm.wms.skucodetrace.SkuBatchCodeTraceQueryProvider;
import net.summerfarm.wms.skucodetrace.req.BatchTraceCodeOrderNoReq;
import net.summerfarm.wms.skucodetrace.req.BatchTraceCodeReq;
import net.summerfarm.wms.skucodetrace.resp.SkuBatchCodeTraceBatchResp;
import net.summerfarm.wms.skucodetrace.resp.SkuBatchCodeTraceResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/2/16 15:25<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class WmsQueryFacade {

    @DubboReference
    private ProcessingTaskOrderProvider processingTaskOrderProvider;
    @DubboReference
    private InventoryQueryProvider inventoryQueryProvider;
    @DubboReference
    private SkuBatchCodeQueryProvider skuBatchCodeQueryProvider;
    @DubboReference
    private SkuBarcodeQueryProvider skuBarcodeQueryProvider;
    @DubboReference
    private SkuBatchCodeTraceQueryProvider skuBatchCodeTraceQueryProvider;

    public List<WmsOrderProcessDTO> queryOrderProcessingInfo(OrderProcessingQueryInput orderProcessingQueryInput){
        ProcessingTaskOrderQueryReqDTO processingTaskOrderQueryReqDTO = WmsConverter.OrderQueryInput2WmsOrderProcessing(orderProcessingQueryInput);
        if(processingTaskOrderQueryReqDTO == null){
            return Collections.emptyList();
        }
        //接口调用最大50个sku
        List<String> orderNoList = processingTaskOrderQueryReqDTO.getOrderNoList();
        //接口调用最大50个订单
        List<List<String>> lists = ListUtil.averageAssign(orderNoList, 50);
        Map<String, ProcessingTaskOrderResDTO> data = new HashMap<>();
        try {
            for (List<String> orderNOs : lists) {
                ProcessingTaskOrderQueryReqDTO processingTaskOrderQueryReq =new ProcessingTaskOrderQueryReqDTO();
                processingTaskOrderQueryReq.setOrderNoList(orderNOs);
                log.info("调用WMS,queryOrderProcessingInfo请求报文:{}", JSON.toJSON(processingTaskOrderQueryReq));
                DubboResponse<Map<String, ProcessingTaskOrderResDTO>> mapDubboResponse = processingTaskOrderProvider.queryOrderProcessingInfo(processingTaskOrderQueryReq);
                log.info("调用WMS,queryOrderProcessingInfo接口返回报文:{}", JSON.toJSON(mapDubboResponse));
                if(!mapDubboResponse.isSuccess()){
                    log.error("异常:调用调用WMS,queryOrderProcessingInfo异常:{}",mapDubboResponse.getMsg());
                    return Collections.emptyList();
                }
                data.putAll(mapDubboResponse.getData());
            }
        } catch (Exception e) {
            log.error("请求WMS加工信息异常",e);
        }

        List<ProcessingTaskOrderResDTO> processingTaskOrderResList = new ArrayList<>();

        for (String order : data.keySet()) {
            ProcessingTaskOrderResDTO processingTaskOrderResDTO = data.get(order);
            processingTaskOrderResDTO.setOrderNo(order);
            processingTaskOrderResList.add(processingTaskOrderResDTO);
        }

        log.info("处理数据为:{}",JSON.toJSONString(processingTaskOrderResList));

        return processingTaskOrderResList.stream().map(WmsConverter::processingTask2OrderDTO).collect(Collectors.toList());
    }

    public List<SkuDTO> batchQueryBySkus(List<String> skus){
        List<SkuDTO> skuDTOS = new ArrayList<>();
        //接口调用最大50个sku
        List<List<String>> lists = ListUtil.averageAssign(skus, 50);
        for (List<String> skuList : lists) {
            BatchQueryBySkusReq batchQueryBySkusReq = new BatchQueryBySkusReq();
            batchQueryBySkusReq.setSkus(skuList);
            DubboResponse<List<InventoryResp>> listDubboResponse = null;

            try {
                log.info("inventoryQueryProvider.batchQueryBySkus req:{}",JSON.toJSONString(batchQueryBySkusReq));
                listDubboResponse = inventoryQueryProvider.batchQueryBySkus(batchQueryBySkusReq);
                log.info("inventoryQueryProvider.batchQueryBySkus resp:{}",JSON.toJSONString(listDubboResponse));
            } catch (Exception e) {
                log.error("请求inventoryQueryProvider.batchQueryBySkus:异常{}",e);
                throw new TmsRuntimeException("请求WMS获取sku信息异常");
            }
            if(listDubboResponse == null){
                throw new TmsRuntimeException("请求WMS获取sku信息没有返回信息");
            }
            List<InventoryResp> inventoryResps = listDubboResponse.getData();
            if(inventoryResps == null){
                continue;
            }

            List<SkuDTO> skuDTOList = inventoryResps.stream().map(WmsConverter::inventoryResp2DTO).collect(Collectors.toList());
            skuDTOS.addAll(skuDTOList);
        }
        skuDTOS.forEach(sku ->{
            if(StringUtils.isBlank(sku.getPicturePath())){
                sku.setPicturePath("");
            }
            if(sku.getType() ==null){
                sku.setType(1);
            }
            if(StringUtils.isBlank(sku.getNameRemakes())){
                sku.setNameRemakes("");
            }
        });
        return skuDTOS.stream().distinct().collect(Collectors.toList());
    }


    public SkuBatchCodeDTO querySkuByBatchCode(String batchCode){
        BatchCodeQueryReq batchCodeQueryReq = new BatchCodeQueryReq();
        batchCodeQueryReq.setBatchCode(batchCode);
        DubboResponse<SkuBatchCodeResp> skuBatchCodeRespDubboResponse = null;
        try {
            log.info("inventoryQueryProvider.batchQueryBySkus req:{}",JSON.toJSONString(batchCodeQueryReq));
            skuBatchCodeRespDubboResponse = skuBatchCodeQueryProvider.querySkuByBatchCode(batchCodeQueryReq);
            log.info("skuBatchCodeQueryProvider.querySkuByBatchCode resp:{}",JSON.toJSONString(skuBatchCodeRespDubboResponse));
        } catch (Exception e) {
            log.error("请求skuBatchCodeQueryProvider.querySkuByBatchCode:异常:{}",e);
            throw new TmsRuntimeException("请求WMS获取批次信息异常,请稍后再试");
        }
        if(!skuBatchCodeRespDubboResponse.isSuccess()){
            log.error("异常:inventoryQueryProvider.batchQueryBySkus resp:{}",skuBatchCodeRespDubboResponse.getMsg());
            throw new TmsRuntimeException("请求WMS获取批次信息异常"+skuBatchCodeRespDubboResponse.getMsg());
        }

        return WmsConverter.skuBatchCodeResp2DTO(skuBatchCodeRespDubboResponse.getData());
    }

    public List<SkuBarcodeDTO> querySkuBarcode(List<String> skus) {
        if (CollectionUtils.isEmpty(skus)){
            return Collections.emptyList();
        }
        List<SkuBarcodeResp> results = Lists.newArrayList();
        List<List<String>> partList = Lists.partition(skus, 50);
        for (List<String> spiltSkus : partList) {
            SkuBarcodeQueryReq skuBarcodeQueryReq = new SkuBarcodeQueryReq();
            skuBarcodeQueryReq.setSkus(spiltSkus);
            DubboResponse<List<SkuBarcodeResp>> response = skuBarcodeQueryProvider.batchQuerySkuBarcodes(skuBarcodeQueryReq);
            if (response == null || !response.isSuccess()){
                log.error("调用WMS查询sku条码信息异常,sku:{}", JSON.toJSONString(spiltSkus), new ProviderException());
                continue;
            }
            List<SkuBarcodeResp> data = response.getData();
            if (CollectionUtils.isEmpty(data)){
                continue;
            }
            results.addAll(data);
        }
        return results.stream().map(SkuBarcodeConverter::resp2dto).collect(Collectors.toList());
    }

    /**
     * 查询WMS唯一溯源码信息
     * @param skuBatchCodeTrace 唯一溯源码
     * @return
     */
    public SkuBatchCodeTraceDTO queryBatchTraceCode(String skuBatchCodeTrace){
        if(StringUtils.isBlank(skuBatchCodeTrace)){
            return null;
        }
        BatchTraceCodeReq req = new BatchTraceCodeReq();
        req.setSkuBatchTraceCode(skuBatchCodeTrace);
        DubboResponse<SkuBatchCodeTraceResp> response = skuBatchCodeTraceQueryProvider.queryByBatchTraceCode(req);
        if (response == null || !response.isSuccess()){
            log.error("调用WMS查询唯一溯源码信息异常,sku:{}", skuBatchCodeTrace);
            return null;
        }
        return WmsConverter.codeTraceResp2DTO(response.getData());

    }

    public List<SkuBatchCodeTraceDTO> queryBatchTraceCodeByOrderNo(QueryBatchTraceCodeByOrderNoInput input) {
        if (input == null || CollectionUtils.isEmpty(input.getOrderNos())){
            return Collections.emptyList();
        }
        List<SkuBatchCodeTraceResp> results = Lists.newArrayList();
        List<List<String>> partList = Lists.partition(input.getOrderNos(), 50);
        for (List<String> subOrderNoList : partList) {
            BatchTraceCodeOrderNoReq req = new BatchTraceCodeOrderNoReq();
            req.setOrderNos(subOrderNoList);
            req.setDeliveryDate(input.getDeliveryDate());
            req.setContactId(input.getContactId());
            DubboResponse<SkuBatchCodeTraceBatchResp> response = skuBatchCodeTraceQueryProvider.queryByOrderNos(req);
            if (response == null || !response.isSuccess()) {
                throw new ProviderException(response == null ? ProviderErrorCode.DEFAULT_CODE : response.getMsg());
            }
            if (response.getData() == null || CollectionUtils.isEmpty(response.getData().getSkuBatchCodeTraceList())){
                continue;
            }
            results.addAll(response.getData().getSkuBatchCodeTraceList());
        }
        return results.stream().map(WmsConverter::codeTraceResp2DTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

}
