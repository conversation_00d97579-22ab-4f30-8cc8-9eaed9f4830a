package net.summerfarm.tms.facade.saas.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AfterSaleDTO {
    /**
     * 售后单编号
     */
    private String afterSaleOrderNo;
    /**
     * 创建日期
     */
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String createUser;

    private Integer suitId;

    /**
     * 0鲜沐 1Saas
     */
    private String source;

    /**
     * 订单编号
     */
    private String orderNo;

    private Integer status;
}
