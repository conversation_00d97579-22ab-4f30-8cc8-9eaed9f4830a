package net.summerfarm.tms.facade.mall.converter;

import net.summerfarm.mall.client.req.DeliveryEvaluationQueryReq;
import net.summerfarm.mall.client.resp.DeliveryEvaluationQueryResp;
import net.summerfarm.tms.facade.mall.dto.DriverAppraiseDTO;
import net.summerfarm.tms.facade.mall.input.DriverAppraiseInput;

/**
 * Description: <br/>
 * date: 2023/3/17 18:14<br/>
 *
 * <AUTHOR> />
 */
public class DriverAppraiseConverter {

    public static DeliveryEvaluationQueryReq input2Req(DriverAppraiseInput driverAppraiseInput){
        if(driverAppraiseInput == null){
            return null;
        }
        DeliveryEvaluationQueryReq deliveryEvaluationQueryReq = new DeliveryEvaluationQueryReq();
        deliveryEvaluationQueryReq.setContactId(driverAppraiseInput.getContactId());
        deliveryEvaluationQueryReq.setDeliveryTime(driverAppraiseInput.getDeliveryTime());
        deliveryEvaluationQueryReq.setOrderNo(driverAppraiseInput.getOrderNo());

        return deliveryEvaluationQueryReq;
    }

    public static DriverAppraiseDTO resp2Dto(DeliveryEvaluationQueryResp deliveryEvaluationQueryResp){
        if(deliveryEvaluationQueryResp == null){
            return null;
        }
        DriverAppraiseDTO driverAppraiseDTO = new DriverAppraiseDTO();

        driverAppraiseDTO.setSatisfactionLevel(deliveryEvaluationQueryResp.getSatisfactionLevel());
        driverAppraiseDTO.setTag(deliveryEvaluationQueryResp.getTag());
        driverAppraiseDTO.setRemark(deliveryEvaluationQueryResp.getRemark());
        driverAppraiseDTO.setOperatorAccountId(deliveryEvaluationQueryResp.getOperatorAccountId());
        driverAppraiseDTO.setOperator(deliveryEvaluationQueryResp.getOperator());
        driverAppraiseDTO.setOperatorPhone(deliveryEvaluationQueryResp.getOperatorPhone());

        return driverAppraiseDTO;
    }
}
