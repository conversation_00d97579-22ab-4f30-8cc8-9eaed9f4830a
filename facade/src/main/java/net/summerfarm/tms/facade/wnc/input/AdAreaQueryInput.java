package net.summerfarm.tms.facade.wnc.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 区域查询<br/>
 * date: 2024/11/15 14:58<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdAreaQueryInput {

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * adCode
     */
    private String adCode;

    /**
     * 区域状态 0 正常 1 失效 3停用
     */
    private Integer adCodeMsgStatus;

    /**
     * 围栏状态 0正常 1失效
     */
    private Integer fenceStatus;

    /**
     * 配送中心状态 状态：0、失效 1、有效
     */
    private Integer warehouseLogisticsCenterStatus;

    /**
     * 省市县名称模糊查询名称
     */
    private String provinceCityAreaLikeName;

    /**
     * 城市模糊查询名称
     */
    private String cityLikeName;
}
