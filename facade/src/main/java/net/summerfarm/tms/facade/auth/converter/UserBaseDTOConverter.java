package net.summerfarm.tms.facade.auth.converter;

import net.summerfarm.tms.facade.auth.dto.UserBaseDTO;
import net.xianmu.common.user.UserBase;
import sun.security.util.Password;

/**
 * Description: <br/>
 * date: 2023/5/31 13:40<br/>
 *
 * <AUTHOR> />
 */
public class UserBaseDTOConverter {

    public static UserBaseDTO resp2DTO(UserBase userBase){
        if(userBase == null){
            return null;
        }
        UserBaseDTO userBaseDTO = new UserBaseDTO();

        userBaseDTO.setId(userBase.getId());
        userBaseDTO.setUserBaseId(userBase.getUserBaseId());
        userBaseDTO.setUsername(userBase.getUsername());
        userBaseDTO.setNickname(userBase.getNickname());
        userBaseDTO.setPhone(userBase.getPhone());
        userBaseDTO.setTenantId(userBase.getTenantId());
        userBaseDTO.setSystemOrigin(userBase.getSystemOrigin());
        userBaseDTO.setEmail(userBase.getEmail());
        userBaseDTO.setPassword(userBase.getPassword());
        userBaseDTO.setStatus(userBase.getStatus());
        userBaseDTO.setBizUserId(userBase.getBizUserId());
        userBaseDTO.setRoleIds(userBase.getRoleIds());

        return userBaseDTO;
    }
}
