package net.summerfarm.tms.facade.auth.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/5/31 11:54<br/>
 *
 * <AUTHOR> />
 */
@Data
public class UserBaseDTO {
    /**
     * auth_user 表 id 可以理解为用户身份表
     */
    private Long id;
    /**
     *  base_user_id 用户基础表id
     */
    private Long userBaseId;
    /**
     * 登陆的名称 o
     */
    private String username;
    /**
     * 用户名称
     */
    private String nickname;
    /**
     *  手机号
     */
    private String phone;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 系统来源
     */
    private Integer systemOrigin;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 密码
     */
    private String  password;
    /**
     * 状态
     */
    private Integer status;

    /**
     *  业务表ID admin体现admin_id saas租户id  auth auth_id
     *
     */
    private Integer bizUserId;

    private List<Long> roleIds;
}
