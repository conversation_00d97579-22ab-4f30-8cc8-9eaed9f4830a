package net.summerfarm.tms.excel.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Description:路线商品Excel Pojo
 * date: 2023/6/12 14:35
 *
 * <AUTHOR>
 */
@Data
public class PathProductPojo {

    /**
     * 路线
     */
    @ExcelProperty(value = "路线", index = 0)
    private String pathCode;

    /**
     * 商品编码(sku)
     */
    @ExcelProperty(value = "商品编码(sku)", index = 1)
    private String outItemId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 2)
    private String outItemName;

    /**
     * 商品规格
     */
    @ExcelProperty(value = "商品规格", index = 3)
    private String specification;

    /**
     * 商品归属
     */
    @ExcelProperty(value = "商品归属", index = 4)
    private String productBelong;

    /**
     * 包装
     */
    @ExcelProperty(value = "包装", index = 5)
    private String unit;

    /**
     * 储存区域
     */
    @ExcelProperty(value = "储存区域", index = 6)
    private String temperature;

    /**
     * 类目类型，0:普通，1:水果
     */
    @ExcelProperty(value = "类目类型", index = 7)
    private String type;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量", index = 8)
    private Integer quantity;
}
