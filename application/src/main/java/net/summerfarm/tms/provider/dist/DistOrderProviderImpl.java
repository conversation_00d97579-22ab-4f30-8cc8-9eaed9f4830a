package net.summerfarm.tms.provider.dist;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderCommand;
import net.summerfarm.tms.dist.dto.DistOrderEndCommand;
import net.summerfarm.tms.dist.dto.DistOrderInterceptDTO;
import net.summerfarm.tms.provider.ProviderUtil;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;

/**
 * Description:委托单提供者实现类
 * date: 2022/9/21 15:51
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class DistOrderProviderImpl implements DistOrderProvider {

    @Resource
    private DistOrderService distOrderService;

    @Override
    public DubboResponse<Long> submitDistOrder(DistOrderCommand distOrderCommand) {
        TmsResult<Long> result = distOrderService.submitDistOrder(distOrderCommand);
        return ProviderUtil.convert2DubboRes(result);
    }

    @Override
    public DubboResponse<Void> cancelDistOrder(DistOrderEndCommand distOrderEndCommand) {
        TmsResult<Void> result = distOrderService.cancelDistOrder(distOrderEndCommand);
        return ProviderUtil.convert2DubboRes(result);
    }

    @Override
    public DubboResponse<Void> interceptDistOrder(List<DistOrderInterceptDTO> distOrderInterceptDTOList) {
        log.info("订单拦截接口interceptDistOrder，拦截报文：{}",JSON.toJSONString(distOrderInterceptDTOList));
        return DubboResponse.getOK();
    }

    /*@PostConstruct
    public void test(){
        String str = "[{\"deliveryTime\":\"2022-11-29\",\"interceptReason\":\"\",\"interceptReasonCode\":0,\"outOrderId\":\"01166960875417773\",\"outerContactId\":\"337334\",\"source\":\"XM_MALL\",\"storeNo\":2},{\"deliveryTime\":\"2022-11-29\",\"interceptReason\":\"\",\"interceptReasonCode\":0,\"outOrderId\":\"8285430340047273984\",\"outerContactId\":\"337334\",\"source\":\"XM_AFTER_SALE\",\"storeNo\":2},{\"deliveryTime\":\"2022-11-29\",\"interceptReason\":\"\",\"interceptReasonCode\":0,\"outOrderId\":\"8285430350361067520\",\"outerContactId\":\"337334\",\"source\":\"XM_AFTER_SALE\",\"storeNo\":2}]";
        List<DistOrderInterceptDTO> distOrderInterceptDTOS = JSON.parseArray(str, DistOrderInterceptDTO.class);
        List<Long> distIdList = distOrderService.interceptDistOrder(distOrderInterceptDTOS);
        //数据同步
        distIdList.forEach(distId -> oldTmsSyncService.startSyncByDistOrderId(distId));
        System.out.println(JSON.toJSONString(distIdList));
    }*/
}
