package net.summerfarm.tms.service.deliveryNoteTemplate.impl;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.FileUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.goods.client.enums.GoodsStorageLocationEnum;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.config.TmsNacosConfig;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateQueryParam;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateQueryRepository;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderFlatObject;
import net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderItemFlatObject;
import net.summerfarm.tms.enums.DistOrderFulfillmentDeliveryWayEnum;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.FileDownloadTypeEnums;
import net.summerfarm.tms.enums.TmsDeliveryNoteTemplateEnums;
import net.summerfarm.tms.excel.strategy.CopyPrevRowStyleAndMergeHandler;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.crm.CrmQueryFacade;
import net.summerfarm.tms.facade.crm.dto.MerchantBdDTO;
import net.summerfarm.tms.facade.goodCenter.GoodCenterQueryFacade;
import net.summerfarm.tms.facade.goodCenter.dto.GoodsDetailDTO;
import net.summerfarm.tms.facade.mallmanage.MerchantInfoQueryFacade;
import net.summerfarm.tms.facade.ofc.OfcQueryFacade;
import net.summerfarm.tms.facade.ofc.dto.FulfillmentDeliveryNoteOrderInfoDTO;
import net.summerfarm.tms.facade.ofc.dto.FulfillmentDeliveryNoteOrderItemInfoDTO;
import net.summerfarm.tms.facade.saas.SaasQueryFacade;
import net.summerfarm.tms.facade.userCenter.MerchantStoreExtQueryFacade;
import net.summerfarm.tms.facade.wnc.WncQueryFacade;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.assembler.TmsDeliveryNoteTemplateAssembler;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.*;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.DeliveryNoteRenderingExtendExcelVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.GoodsItemVO;
import net.summerfarm.tms.service.common.DownloadCenterService;
import net.summerfarm.tms.service.deliveryNoteTemplate.TmsDeliveryNoteTemplateQueryService;
import net.summerfarm.tms.service.deliveryNoteTemplate.converter.DeliveryNoteConverter;
import net.summerfarm.tms.service.deliveryNoteTemplate.handler.CustomTemplateSheetStrategy;
import net.summerfarm.tms.service.deliveryNoteTemplate.handler.PrintDirectionHandler;
import net.summerfarm.tms.service.deliveryNoteTemplate.handler.DeliveryNoteExcelMerger;
import net.summerfarm.tms.util.ExcelUtils;
import net.summerfarm.tms.util.ExecutorUtil;
import net.summerfarm.tms.util.MapSplitterUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.collections.map.HashedMap;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.security.ProviderException;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
@Service
@Slf4j
public class TmsDeliveryNoteTemplateQueryServiceImpl implements TmsDeliveryNoteTemplateQueryService {

    @Autowired
    private TmsDeliveryNoteTemplateQueryRepository tmsDeliveryNoteTemplateQueryRepository;
    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private MerchantInfoQueryFacade merchantInfoQueryFacade;
    @Resource
    private SaasQueryFacade saasQueryFacade;
    @Resource
    private MerchantStoreExtQueryFacade merchantStoreExtQueryFacade;
    @Resource
    private GoodCenterQueryFacade goodCenterQueryFacade;
    @Resource
    private WncQueryFacade wncQueryFacade;
    @Resource
    private CrmQueryFacade crmQueryFacade;
    @Resource
    private TmsNacosConfig tmsNacosConfig;

    @Override
    public PageInfo<TmsDeliveryNoteTemplateEntity> getPage(TmsDeliveryNoteTemplateQueryInput input) {
        TmsDeliveryNoteTemplateQueryParam queryParam = TmsDeliveryNoteTemplateAssembler.toTmsDeliveryNoteTemplateQueryParam(input);
        return tmsDeliveryNoteTemplateQueryRepository.getPage(queryParam);
    }

    @Override
    public TmsDeliveryNoteTemplateEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return tmsDeliveryNoteTemplateQueryRepository.selectById(id);
    }

    @Override
    public Long downloadPrintDeliveryNoteInfo(DeliveryNotePrintInput input) throws IOException {
        // 根据日期 + 城配仓编号查询TMS的单据信息
        List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList = distOrderRepository.queryDeliveryNoteByDateAndStoreNo(input.getDeliveryTime(), input.getStoreNo());
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            throw new TmsRuntimeException("暂无配送单数据");
        }
        // 排除干线转运的单据
        deliveryNoteOrderFlatObjectList = deliveryNoteOrderFlatObjectList.stream()
                .filter(e -> !Objects.equals(e.getFulfillmentDeliveryWay(), DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode()))
                .collect(Collectors.toList());
        // 按照站点进行分组
        Map<Long, List<DeliveryNoteOrderFlatObject>> deliverySiteMapList = deliveryNoteOrderFlatObjectList.stream()
                .filter(e -> Objects.nonNull(e.getDeliverySiteId()))
                .collect(Collectors.groupingBy(DeliveryNoteOrderFlatObject::getDeliverySiteId));

        List<Map<Long, List<DeliveryNoteOrderFlatObject>>> maps = MapSplitterUtil.splitMap(deliverySiteMapList, tmsNacosConfig.queryDeliveryNoteSiteNumLimit());

        // 文件名称集合
        HashSet<String> deliveryNoteInfoFileSet = new HashSet<>();
        DeliveryNoteOrderFlatObject deliveryNoteOrderFlatObject = deliveryNoteOrderFlatObjectList.get(0);
        // 分批处理，避免OOM
        for (Map<Long, List<DeliveryNoteOrderFlatObject>> map : maps) {
            List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjects = map.values().stream().flatMap(List::stream).collect(Collectors.toList());
            this.handleFulOrderInfo(deliveryNoteOrderFlatObjects, input.getDeliveryTime());
            // 处理订单信息
            Set<String> allDownLoadFileSet = deliveryNotePrintInfo(input.getStoreNo(), deliveryNoteOrderFlatObjects, input.getPrintDirection());
            deliveryNoteInfoFileSet.addAll(allDownLoadFileSet);

        }
        return download(deliveryNoteOrderFlatObject,deliveryNoteInfoFileSet, null);
    }


    /**
     * 配送单打印
     * @param storeNo 城配仓编号
     * @param deliveryNoteOrderFlatObjectList 配送订单信息
     * @param printDirection 打印方向
     * @return 文档名称集合
     */
    private Set<String> deliveryNotePrintInfo(Integer storeNo, List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList, Boolean printDirection) throws IOException {

        // 查询是否需要打印配送单
        this.handleShowDeliveryNotePrint(deliveryNoteOrderFlatObjectList);

        // 查询客户是否展示价格
        this.handleShowPriceInfo(deliveryNoteOrderFlatObjectList);

        // 查询BD信息
        this.queryBdWithInstallBdInfo(deliveryNoteOrderFlatObjectList);

        // 查询设置SKU信息
        this.querySkuWithInstallSkuInfo(deliveryNoteOrderFlatObjectList);

        // 创建配送单
        List<DeliveryNoteRenderingExtendExcelVO> deliveryNoteList = this.createDeliveryNote(deliveryNoteOrderFlatObjectList, storeNo);

        // 匹配配送单模版
        this.matchDeliveryNoteTemplate(deliveryNoteList);

        // 配送费、加单费展示的处理
        this.handleDeliveryNoteFeeWithAddFee(deliveryNoteList);

        // 配送单文件
        return this.handleMatchAndRenderDeliveryNote(deliveryNoteList, printDirection);

    }

    /**
     * 配送费、加单费处理
     * @param deliveryNoteList 配送单集合
     */
    private void handleDeliveryNoteFeeWithAddFee(List<DeliveryNoteRenderingExtendExcelVO> deliveryNoteList) {
        if(CollectionUtils.isEmpty(deliveryNoteList)){
            return;
        }
        // 不展示单价时，不要显示配送费和加单费
        for (DeliveryNoteRenderingExtendExcelVO deliveryNoteRenderingExtendExcelVO : deliveryNoteList) {
            Boolean realShowPriceFlag = deliveryNoteRenderingExtendExcelVO.getRealShowPriceFlag();
            if(realShowPriceFlag){ // NOSONAR
                continue;
            }
            List<GoodsItemVO> goodsItemVOList = deliveryNoteRenderingExtendExcelVO.getGoodsItemVOList();
           if(CollectionUtils.isEmpty(goodsItemVOList)){
               return;
           }
            // 移除配送费和加单费
            goodsItemVOList.removeIf(item -> Objects.equals(item.getItemId(),"配送费") || Objects.equals(item.getItemId(),"加单费"));
           log.info("goodsItemVOList:{}",goodsItemVOList);
        }

        // 多运费要合并展示
        for (DeliveryNoteRenderingExtendExcelVO deliveryNoteRenderingExtendExcelVO : deliveryNoteList) {
            List<GoodsItemVO> goodsItemVOList = deliveryNoteRenderingExtendExcelVO.getGoodsItemVOList();
            if(CollectionUtils.isEmpty(goodsItemVOList)){
                return;
            }
            List<GoodsItemVO> deliveryFeeList = goodsItemVOList.stream()
                    .filter(item -> Objects.equals(item.getItemId(), "配送费"))
                    .collect(Collectors.toList());

            if(CollectionUtils.isEmpty(deliveryFeeList) || deliveryFeeList.size() == 1){
                continue;
            }

            // 合并配送费
            BigDecimal totalPrice = deliveryFeeList.stream()
                    .map(GoodsItemVO::getPrice)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            goodsItemVOList.removeIf(item -> Objects.equals(item.getItemId(),"配送费"));

            GoodsItemVO itemVO = new GoodsItemVO();
            itemVO.setSeq(goodsItemVOList.size() + 1 + "");
            itemVO.setItemId("配送费");
            itemVO.setItemName("配送费");
            itemVO.setPrice(totalPrice.stripTrailingZeros().toPlainString());
            itemVO.setSubtotal(totalPrice.stripTrailingZeros().toPlainString());
            itemVO.setQuantity("1");

            goodsItemVOList.add(itemVO);
        }
    }

    /**
     * 匹配配送单模版
     * @param deliveryNoteList 配送单集合
     */
    private void matchDeliveryNoteTemplate(List<DeliveryNoteRenderingExtendExcelVO> deliveryNoteList) {
        if (CollectionUtils.isEmpty(deliveryNoteList)) {
            return;
        }

        // 查询状态在用的配送单模版信息
        TmsDeliveryNoteTemplateQueryParam param = new TmsDeliveryNoteTemplateQueryParam();
        param.setUseState(TmsDeliveryNoteTemplateEnums.UseState.USE.getValue());
        List<TmsDeliveryNoteTemplateEntity> allUserDeliveryTemplateList = tmsDeliveryNoteTemplateQueryRepository.queryWithBelong(param);

        if (CollectionUtils.isEmpty(allUserDeliveryTemplateList)) {
            return;
        }

        // 模版id和模版实体的映射
        Map<Long, TmsDeliveryNoteTemplateEntity> id2TemplateMap = allUserDeliveryTemplateList.stream()
                .collect(Collectors.toMap(TmsDeliveryNoteTemplateEntity::getId, Function.identity()));

        List<TmsDeliveryNoteTemplateBelongEntity> templateBelongEntities = allUserDeliveryTemplateList.stream()
                .map(TmsDeliveryNoteTemplateEntity::getTmsDeliveryNoteTemplateBelongEntities)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        for (DeliveryNoteRenderingExtendExcelVO deliveryNote : deliveryNoteList) {
            Integer appSource = deliveryNote.getAppSource();
            TmsDeliveryNoteTemplateEntity matchedTemplate = null;

            // 根据应用来源匹配模板
            if(Objects.equals(appSource, TmsDeliveryNoteTemplateEnums.AppSource.SAAS.getValue())){
                matchedTemplate = matchTemplateForSaas(deliveryNote, templateBelongEntities, id2TemplateMap,appSource);
            } else if(Objects.equals(appSource, TmsDeliveryNoteTemplateEnums.AppSource.SUNLUDA.getValue())){
                matchedTemplate = matchTemplateForDefault(templateBelongEntities, id2TemplateMap,appSource);
            } else if (Objects.equals(appSource, TmsDeliveryNoteTemplateEnums.AppSource.OUTSIDE.getValue())){
                matchedTemplate = matchTemplateForDefault(templateBelongEntities, id2TemplateMap,appSource);
            } else if (Objects.equals(appSource, TmsDeliveryNoteTemplateEnums.AppSource.XIANMU.getValue())){
                matchedTemplate = matchTemplateForXianmu(deliveryNote, templateBelongEntities, id2TemplateMap,appSource);
            }else {
                log.error("未知的应用来源: {}", appSource);
            }

            if(matchedTemplate == null){
                continue;
            }
            // 设置打印配送单的价格标识
            deliveryNote.setDeliveryTemplateShowPriceFlag(
                    Objects.equals(matchedTemplate.getShowPriceFlag(),
                            TmsDeliveryNoteTemplateEnums.ShowPriceFlag.SHOW_PRICE.getValue()));
            // 设置配送单模版
            deliveryNote.setDeliveryNoteTemplate(matchedTemplate);

            // 判定配送单到底是否展示价格
            if(deliveryNote.getCustomerShowPriceFlag()){// NOSONAR
                // 客户地址配置展示价格
                if(Objects.equals(matchedTemplate.getShowPriceFlag(),TmsDeliveryNoteTemplateEnums.ShowPriceFlag.NO_SHOW_PRICE.getValue())){
                    // 模版配置不展示价格 不展示价格
                    deliveryNote.setRealShowPriceFlag(false);
                }else{
                    // 模版配置展示价格   展示价格
                    deliveryNote.setRealShowPriceFlag(true);
                }
            }else{
                // 客户地址配置不展示价格
                if(Objects.equals(matchedTemplate.getShowPriceFlag(),TmsDeliveryNoteTemplateEnums.ShowPriceFlag.NO_SHOW_PRICE.getValue())){
                    // 模版配置不展示价格   不展示价格
                    deliveryNote.setRealShowPriceFlag(false);
                }else{
                    // 模版配置展示价格     不展示价格
                    deliveryNote.setRealShowPriceFlag(false);
                }
            }
        }
    }

    /**
     * 定制Saas客户匹配逻辑
     * @param deliveryNote 配送单
     * @param templateBelongEntities 模版关联信息
     * @param id2TemplateMap 模版id和模版实体的映射
     * @param appSource 来源
     * @return 匹配的模版
     */
    private TmsDeliveryNoteTemplateEntity matchTemplateForSaas(DeliveryNoteRenderingExtendExcelVO deliveryNote,
                                                               List<TmsDeliveryNoteTemplateBelongEntity> templateBelongEntities,
                                                               Map<Long, TmsDeliveryNoteTemplateEntity> id2TemplateMap,
                                                               Integer appSource) {
        Optional<TmsDeliveryNoteTemplateBelongEntity> customizedSaasDeliveryNote = templateBelongEntities.stream()
                .filter(e -> Objects.equals(appSource, e.getAppSource()))
                .filter(e -> Objects.equals(e.getTenantId(), deliveryNote.getTenantId()))
                .findFirst();

        if (customizedSaasDeliveryNote.isPresent()) {
            return id2TemplateMap.get(customizedSaasDeliveryNote.get().getDeliveryNoteTemplateId());
        }

        return matchTemplateForDefault(templateBelongEntities, id2TemplateMap,appSource);
    }


    /**
     * 默认匹配逻辑
     * @param templateBelongEntities 模版关联信息
     * @param id2TemplateMap 模版id和模版实体的映射
     * @param appSource 来源
     * @return 匹配的模版
     */
    private TmsDeliveryNoteTemplateEntity matchTemplateForDefault(List<TmsDeliveryNoteTemplateBelongEntity> templateBelongEntities,
                                                                  Map<Long, TmsDeliveryNoteTemplateEntity> id2TemplateMap,
                                                                  Integer appSource) {
        Optional<TmsDeliveryNoteTemplateBelongEntity> outDeliveryNoteBelong = templateBelongEntities.stream()
                .filter(e -> Objects.equals(appSource, e.getAppSource()))
                .filter(e -> Objects.equals(e.getScopeBusinessId(), TmsDeliveryNoteTemplateEnums.ScopeType.DEFAULT.getValue().toString()))
                .findFirst();

        if (!outDeliveryNoteBelong.isPresent()) {
            log.error("未匹配到模版信息");
            return null;
        }

        return id2TemplateMap.get(outDeliveryNoteBelong.get().getDeliveryNoteTemplateId());
    }

    /**
     * 鲜沐匹配逻辑
     * @param deliveryNote 配送单
     * @param templateBelongEntities 模版关联信息
     * @param id2TemplateMap 模版id和模版实体的映射
     * @param appSource 来源
     * @return 匹配的模版
     */
    private TmsDeliveryNoteTemplateEntity matchTemplateForXianmu(DeliveryNoteRenderingExtendExcelVO deliveryNote,
                                                                 List<TmsDeliveryNoteTemplateBelongEntity> templateBelongEntities,
                                                                 Map<Long, TmsDeliveryNoteTemplateEntity> id2TemplateMap,
                                                                 Integer appSource) {
        Optional<TmsDeliveryNoteTemplateBelongEntity> customizedXmDeliveryNote = templateBelongEntities.stream()
                .filter(e -> Objects.equals(appSource, e.getAppSource()))
                .filter(e -> Objects.equals(e.getScopeBusinessId(), String.valueOf(deliveryNote.getBigCustomerId())))
                .findFirst();

        if (customizedXmDeliveryNote.isPresent()) {
            return id2TemplateMap.get(customizedXmDeliveryNote.get().getDeliveryNoteTemplateId());
        }

        Optional<TmsDeliveryNoteTemplateBelongEntity> xmDefaultDeliveryNote = templateBelongEntities.stream()
                .filter(e -> Objects.equals(appSource, e.getAppSource()))
                .filter(e -> Objects.equals(e.getScopeBusinessId(), TmsDeliveryNoteTemplateEnums.ScopeType.DEFAULT.getValue().toString()))
                .findFirst();

        if (!xmDefaultDeliveryNote.isPresent()) {
            log.error("未匹配到鲜沐定制信息");
            return null;
        }

        return id2TemplateMap.get(xmDefaultDeliveryNote.get().getDeliveryNoteTemplateId());
    }

    /**
     * 查询设置SKU信息
     * @param deliveryNoteOrderFlatObjectList 配送单集合
     */
    private void querySkuWithInstallSkuInfo(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return;
        }

        // 查询货品信息
        List<String> skuList = deliveryNoteOrderFlatObjectList.stream()
                .filter(Objects::nonNull)
                .map(DeliveryNoteOrderFlatObject::getItems)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DeliveryNoteOrderItemFlatObject::getSku)
                .distinct()
                .collect(Collectors.toList());

        Map<String, GoodsDetailDTO> sku2GoodsDetailMap = goodCenterQueryFacade.querySkuListBySku(skuList, Constants.Tenant.XM_TENANT_ID);

        // 设置SKU的属性
        for (DeliveryNoteOrderFlatObject deliveryNoteOrderFlatObject : deliveryNoteOrderFlatObjectList) {
            List<DeliveryNoteOrderItemFlatObject> items = deliveryNoteOrderFlatObject.getItems();
            if(CollectionUtils.isEmpty(items)){
                continue;
            }
            for (DeliveryNoteOrderItemFlatObject item : items) {
                GoodsDetailDTO goodsDetailDTO = sku2GoodsDetailMap.get(item.getSku());
                if(goodsDetailDTO == null){
                    continue;
                }
                item.setSkuExtType(goodsDetailDTO.getExtType());
                item.setTemperatureStr(GoodsStorageLocationEnum.getDesc(goodsDetailDTO.getStorageLocation()));
                if(StringUtils.isEmpty(item.getWeight())){
                    item.setWeight(goodsDetailDTO.getWeight() != null ? goodsDetailDTO.getWeight().toString() : null);
                }

                if(item.getUnit() == null){
                    item.setUnit(goodsDetailDTO.getSpecificationUnit());
                }

                if(item.getVolume() == null){
                    item.setVolume(goodsDetailDTO.getVolume());
                }

                if(StringUtils.isEmpty(item.getSpecification())){
                    item.setSpecification(goodsDetailDTO.getSpecification());
                }
            }
        }
    }

    /**
     * 查询组装BD信息
     * @param deliveryNoteOrderFlatObjectList 配送单信息
     */
    private void queryBdWithInstallBdInfo(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return;
        }
        List<Long> mIds = deliveryNoteOrderFlatObjectList.stream()
                .filter(e -> DistOrderSourceEnum.getXmOrderTypeCode().contains(e.getSource()))
                .map(DeliveryNoteOrderFlatObject::getMerchantId)
                .filter(Objects::nonNull)
                .map(Long::parseLong)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, MerchantBdDTO> merchantBdDTOMap = crmQueryFacade.queryMerchantDbInfoMap(mIds);
        deliveryNoteOrderFlatObjectList.forEach(e -> {
            if(!DistOrderSourceEnum.getXmOrderTypeCode().contains(e.getSource())){
                return;
            }
            if(StringUtils.isEmpty(e.getMerchantId())){
                return;
            }
            MerchantBdDTO merchantBdDTO = merchantBdDTOMap.get(Long.parseLong(e.getMerchantId()));
            if(Objects.nonNull(merchantBdDTO)){
                e.setBdName(merchantBdDTO.getBdName());
                e.setBdPhone(merchantBdDTO.getBdPhone());
            }
        });
    }

    /**
     * 匹配渲染配送单上传到OSS
     * @param deliveryNoteList 配送单数据
     * @param printDirection 打印配置 横向 纵向
     */
    private Set<String> handleMatchAndRenderDeliveryNote(List<DeliveryNoteRenderingExtendExcelVO> deliveryNoteList, Boolean printDirection) throws IOException {
        if(CollectionUtils.isEmpty(deliveryNoteList)){
            return null;
        }
        long beginTime = System.currentTimeMillis();

        List<TmsDeliveryNoteTemplateEntity> deliveryNoteTemplateEntities = deliveryNoteList.stream().map(DeliveryNoteRenderingExtendExcelVO::getDeliveryNoteTemplate).distinct().collect(Collectors.toList());
        // 下载配送单包含的模版文件
        Map<Long, TmsDeliveryNoteTemplateEntity> tmsDeliveryNoteTemplateEntityMap = deliveryNoteTemplateEntities.stream().collect(Collectors.toMap(TmsDeliveryNoteTemplateEntity::getId, Function.identity()));
        Map<String, String> idShowFlag2FileNameMap = new HashMap<>();
        tmsDeliveryNoteTemplateEntityMap.forEach((id, template) -> {
            String showPriceTemplateOssUrl = template.getShowPriceTemplateOssUrl();
            String noShowPriceTemplateOssUrl = template.getNoShowPriceTemplateOssUrl();

            String showPriceFileName = UUID.randomUUID() + ".xlsx";
            String noShowPriceFileName = UUID.randomUUID() + ".xlsx";

            this.downloadFile(showPriceTemplateOssUrl,showPriceFileName);
            this.downloadFile(noShowPriceTemplateOssUrl,noShowPriceFileName);

            idShowFlag2FileNameMap.put(id + "#" + TmsDeliveryNoteTemplateEnums.ShowPriceFlag.SHOW_PRICE.getValue(),showPriceFileName);
            idShowFlag2FileNameMap.put(id + "#" + TmsDeliveryNoteTemplateEnums.ShowPriceFlag.NO_SHOW_PRICE.getValue(),noShowPriceFileName);
        });
        long endTime = System.currentTimeMillis();
        log.info("下载模版文件耗时：{}ms",endTime - beginTime);

        // 根据模版来分组
        for (DeliveryNoteRenderingExtendExcelVO deliveryNote : deliveryNoteList) {
            TmsDeliveryNoteTemplateEntity deliveryNoteTemplate = deliveryNote.getDeliveryNoteTemplate();
            String fileName = idShowFlag2FileNameMap.get(deliveryNoteTemplate.getId() + "#" +
                    (deliveryNote.getRealShowPriceFlag() ? TmsDeliveryNoteTemplateEnums.ShowPriceFlag.SHOW_PRICE.getValue() : TmsDeliveryNoteTemplateEnums.ShowPriceFlag.NO_SHOW_PRICE.getValue()));// NOSONAR
            deliveryNote.setTemplatePath(fileName);
        }

        long currentTimeMillis = System.currentTimeMillis();
        renderDeliveryNote(deliveryNoteList, printDirection);
        log.info("渲染配送单耗时：{}ms",System.currentTimeMillis() - currentTimeMillis);
        return new HashSet<>(idShowFlag2FileNameMap.values());
    }

    private void renderDeliveryNote(List<DeliveryNoteRenderingExtendExcelVO> deliveryNoteList, Boolean printDirection) {
        if (CollectionUtils.isEmpty(deliveryNoteList)) {
            return;
        }
        boolean isPortrait = printDirection != null ? printDirection : true;

        Map<String, List<DeliveryNoteRenderingExtendExcelVO>> templateGroups = deliveryNoteList.stream()
                .collect(Collectors.groupingBy(DeliveryNoteRenderingExtendExcelVO::getTemplatePath));

        templateGroups.forEach((templatePath, notes) -> {
            try {
                // 1. 读取模板到内存
                byte[] templateBytes = readTemplateBytes(templatePath);

                // 2. 处理模板并填充数据
                processTemplate(templatePath, templateBytes, notes, isPortrait);
            } catch (IOException e) {
                log.error("处理模板失败: {}", templatePath, e);
            }
        });
    }

    private void processTemplate(String templatePath, byte[] templateBytes, List<DeliveryNoteRenderingExtendExcelVO> notes, boolean isPortrait) {
        try (Workbook workbook = new XSSFWorkbook(new ByteArrayInputStream(templateBytes))) {
            // 复制 Sheet
            for (int i = 0; i < notes.size(); i++) {
                workbook.cloneSheet(0);
            }
            // 将修改后的工作簿写入到一个新的字节流中
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] updatedTemplateBytes = bos.toByteArray();

            // 填充数据
            try (ExcelWriter excelWriter = EasyExcel.write(templatePath)
                    .withTemplate(new ByteArrayInputStream(updatedTemplateBytes))
                    .autoCloseStream(true)
                    .registerWriteHandler(new CopyPrevRowStyleAndMergeHandler())
                    .registerWriteHandler(new PrintDirectionHandler(isPortrait))
                    .build()) {

                for (int i = 0; i < notes.size(); i++) {
                    String sheetName = this.getSheetName(notes, workbook, i);
                    // 使用 EasyExcel 填充数据
                    WriteSheet writeSheet = EasyExcel
                            .writerSheet(i + 1)
                            .registerWriteHandler(new CustomTemplateSheetStrategy(i + 1,sheetName))
                            .build();
                    DeliveryNoteRenderingExtendExcelVO note = notes.get(i);
                    FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();
                    excelWriter.fill(note.getGoodsItemVOList(), fillConfig, writeSheet);
                    excelWriter.fill(note, writeSheet);
                }
            }
        } catch (IOException e) {
            log.error("处理 Excel 失败", e);
        }
    }

    private String getSheetName(List<DeliveryNoteRenderingExtendExcelVO> notes, Workbook workbook, int i) {
        DeliveryNoteRenderingExtendExcelVO deliveryNote = notes.get(i);
        // 设置 Sheet 名称
        String suffix = "";
        Integer appSource = deliveryNote.getAppSource();

        if (Objects.equals(appSource, TmsDeliveryNoteTemplateEnums.AppSource.SAAS.getValue())) {
            suffix = "(saas)";
        }
        String deliveryPath = StringUtils.isEmpty(deliveryNote.getDeliveryPath()) ? "" : deliveryNote.getDeliveryPath();
        String baseName = truncateString(deliveryPath +
                (deliveryNote.getMerchantName() != null ? deliveryNote.getMerchantName() : "unknown")
                + suffix, 20);

        return DeliveryNoteExcelMerger.generateUniqueName(workbook, baseName);
    }

    private byte[] readTemplateBytes(String path) throws IOException {
        try (FileInputStream fis = new FileInputStream(path);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                bos.write(buffer, 0, len);
            }
            return bos.toByteArray();
        }
    }
   /* *//**
     * 配送单渲染
     * @param deliveryNote 配送单
     * @param printDirection 打印方向
     * @param outputPath
     * @throws IOException
     *//*
    private void renderDeliveryNote(DeliveryNoteRenderingExtendExcelVO deliveryNote, Boolean printDirection, String outputPath) throws IOException {
        if(printDirection == null){
            // 默认纵向打印
            printDirection = true;
        }
        FileInputStream fis = new FileInputStream(deliveryNote.getTemplatePath());
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        Workbook workbook = new XSSFWorkbook(fis);
        Sheet sheet1 = workbook.cloneSheet(0);
        // 设置 Sheet 名称
        String suffix = "";
        Integer appSource = deliveryNote.getAppSource();

        if (Objects.equals(appSource, TmsDeliveryNoteTemplateEnums.AppSource.SAAS.getValue())) {
            suffix = "(saas)";
        }
        int sheetIndex = workbook.getSheetIndex(sheet1);
        String deliveryPath = StringUtils.isEmpty(deliveryNote.getDeliveryPath()) ? "" : deliveryNote.getDeliveryPath();

        String baseName = truncateString(deliveryPath +
                (deliveryNote.getMerchantName() != null ? deliveryNote.getMerchantName() : "unknown")
                + suffix, 20);
        workbook.setSheetName(sheetIndex, DeliveryNoteExcelMerger.generateUniqueName(workbook, baseName));

        workbook.write(bos);
        bos.flush();

        // 使用EasyExcel填充数据
        try (ExcelWriter excelWriter = EasyExcel.write(outputPath)
                .withTemplate(new ByteArrayInputStream(bos.toByteArray()))
                .autoCloseStream(true)
                .registerWriteHandler(new CopyPrevRowStyleAndMergeHandler())
                .registerWriteHandler(new PrintDirectionHandler(printDirection))
                .build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().sheetName(sheet1.getSheetName()).build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).autoStyle(true).build();
            excelWriter.fill(deliveryNote.getGoodsItemVOList(), fillConfig, writeSheet);
            excelWriter.fill(deliveryNote, writeSheet);
        }
    }*/
    /**
     * 截取字符串，如果超过指定长度就截取前N个字符，否则保持原样。
     *
     * @param input 输入字符串
     * @param maxLength 最大长度
     * @return 截取后的字符串
     */
    public static String truncateString(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        if (input.length() > maxLength) {
            return input.substring(0, maxLength);
        }
        return input;
    }

    /**
     * 处理订单信息,设置配送费和加单费
     * @param deliveryNoteOrderFlatObjectList 配送单信息
     * @param deliveryTime 配送日期
     */
    private void handleFulOrderInfo(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList, LocalDate deliveryTime) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return;
        }
        // 查询OFC相关的订单商品信息
        List<String> outOrderNos = deliveryNoteOrderFlatObjectList.stream().map(DeliveryNoteOrderFlatObject::getOrderNo).distinct().collect(Collectors.toList());
        List<FulfillmentDeliveryNoteOrderInfoDTO> fulfillmentDeliveryNoteOrderInfoDTOS = ofcQueryFacade.queryFulfillmentDeliveryNoteInfo(deliveryTime, outOrderNos);
        if(CollectionUtils.isEmpty(fulfillmentDeliveryNoteOrderInfoDTOS)){
            log.info("ofcQueryFacade queryFulfillmentDeliveryNoteInfo req:deliveryTime:{},outOrderNos:{},fulfillmentDeliveryNoteOrderInfoDTOS is null",
                    deliveryTime, outOrderNos);
            return;
        }
        Map<String, FulfillmentDeliveryNoteOrderInfoDTO> orderNoFulTimeConId2FulfillmentOrderMap = new HashedMap();
        if(!CollectionUtils.isEmpty(fulfillmentDeliveryNoteOrderInfoDTOS)){
            orderNoFulTimeConId2FulfillmentOrderMap = fulfillmentDeliveryNoteOrderInfoDTOS.stream().collect(Collectors.toMap(e -> e.getOutOrderNo() + "#"
                    + e.getFulfillmentTime() + "#"
                    + e.getContactId(), Function.identity(), (oldData, newData) -> newData));
        }

        for (DeliveryNoteOrderFlatObject obj : deliveryNoteOrderFlatObjectList) {
            FulfillmentDeliveryNoteOrderInfoDTO fulOrderInfo = orderNoFulTimeConId2FulfillmentOrderMap.get(obj.getOrderNo() + "#" + obj.getDeliveryTime() + "#" + obj.getOutContactId());
            if(fulOrderInfo == null){
                continue;
            }
            // 设置订单备注
            obj.setOrderRemark(fulOrderInfo.getOrderRemark());
            obj.setOuterTenantId(fulOrderInfo.getBigCustomerId() != null ? String.valueOf(fulOrderInfo.getBigCustomerId()) : null);
            obj.setBigCustomerId(fulOrderInfo.getBigCustomerId());
            obj.setMerchantSize(fulOrderInfo.getStoreSize());
            obj.setSellingEntityName(fulOrderInfo.getSellingEntityName());
            // 设置Saas订单明细信息
            if(DistOrderSourceEnum.getSaasOrderTypeCode().contains(obj.getSource())){
                obj.setOuterTenantId(String.valueOf(fulOrderInfo.getTenantId()));
                List<FulfillmentDeliveryNoteOrderItemInfoDTO> ofcItemList = fulOrderInfo.getFulfillmentOrderItemList();
                // 直接使用Saas的订单明细
                if(CollectionUtils.isEmpty(ofcItemList)){
                   continue;
                }
                List<DeliveryNoteOrderItemFlatObject> items = new ArrayList<>();

                for (FulfillmentDeliveryNoteOrderItemInfoDTO fulItem : ofcItemList) {
                    DeliveryNoteOrderItemFlatObject tmsItem = new DeliveryNoteOrderItemFlatObject();
                    tmsItem.setSku(fulItem.getSku());
                    tmsItem.setSkuName(fulItem.getSkuName());
                    tmsItem.setQuantity(fulItem.getProductAmount());
                    tmsItem.setPrice(fulItem.getProductUnitPrice());
                    tmsItem.setUnit(fulItem.getProductUnit());
                    tmsItem.setProductName(fulItem.getProductName());
                    tmsItem.setWarehouseNo(fulItem.getWarehouseNo());

                    items.add(tmsItem);
                }
                obj.setItems(items);
            }

            List<DeliveryNoteOrderItemFlatObject> items = obj.getItems();
            if(CollectionUtils.isEmpty(items)){
                return;
            }

            // 设置配送费、加单费
            BigDecimal deliveryFee = fulOrderInfo.getDeliveryFee();
            BigDecimal outTimesFee = fulOrderInfo.getOutTimesFee();
            if(deliveryFee != null && deliveryFee.compareTo(BigDecimal.ZERO) > 0){
                DeliveryNoteOrderItemFlatObject deliveryFeeItemFlatObject = new DeliveryNoteOrderItemFlatObject();
                deliveryFeeItemFlatObject.setDistItemId("配送费");
                deliveryFeeItemFlatObject.setQuantity(1);
                deliveryFeeItemFlatObject.setPrice(deliveryFee);
                deliveryFeeItemFlatObject.setProductName("配送费");
                items.add(deliveryFeeItemFlatObject);
            }

            if(outTimesFee != null && outTimesFee.compareTo(BigDecimal.ZERO) > 0){
                DeliveryNoteOrderItemFlatObject outTimesFeeItemFlatObject = new DeliveryNoteOrderItemFlatObject();
                outTimesFeeItemFlatObject.setDistItemId("加单费");
                outTimesFeeItemFlatObject.setQuantity(1);
                outTimesFeeItemFlatObject.setPrice(outTimesFee);
                outTimesFeeItemFlatObject.setProductName("加单费");
                items.add(outTimesFeeItemFlatObject);
            }
        }
    }

    /**
     * 处理展示价格逻辑
     * @param deliveryNoteOrderFlatObjectList 配送单信息
     */
    private void handleShowPriceInfo(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return;
        }

        List<DeliveryNoteOrderFlatObject> xmOrders = deliveryNoteOrderFlatObjectList.stream().filter(e -> DistOrderSourceEnum.getXmOrderTypeCode().contains(e.getSource())).collect(Collectors.toList());
        List<DeliveryNoteOrderFlatObject> saasOrders = deliveryNoteOrderFlatObjectList.stream().filter(e -> DistOrderSourceEnum.getSaasOrderTypeCode().contains(e.getSource())).collect(Collectors.toList());

        // 查询是否展示价格
        if(!CollectionUtils.isEmpty(xmOrders)){
            // 查询XM订单是否展示价格
            List<String> merchantIds = xmOrders.stream().map(DeliveryNoteOrderFlatObject::getMerchantId).distinct().collect(Collectors.toList());
            Map<Long, Boolean> xmMerchantId2ShowFlagMap = merchantInfoQueryFacade.queryMerchantShowPriceMap(merchantIds.stream().map(Long::parseLong).collect(Collectors.toList()));

            if(!CollectionUtils.isEmpty(xmMerchantId2ShowFlagMap)){
                xmOrders.forEach(e -> {
                    e.setShowPriceFlag(xmMerchantId2ShowFlagMap.getOrDefault(Long.parseLong(e.getMerchantId()), false));
                });
            }
        }

        if(!CollectionUtils.isEmpty(saasOrders)){
            List<String> merchantIds = saasOrders.stream().map(DeliveryNoteOrderFlatObject::getMerchantId).distinct().collect(Collectors.toList());
            // 查询Saas订单是否展示价格
            Map<Long, Boolean> saasMerchantId2ShowFlagMap = saasQueryFacade.queryDeliveryNotePrintPriceSwitch(merchantIds.stream().map(Long::parseLong).collect(Collectors.toList()));

            if(!CollectionUtils.isEmpty(saasMerchantId2ShowFlagMap)){
                saasOrders.forEach(e -> {
                    e.setShowPriceFlag(saasMerchantId2ShowFlagMap.getOrDefault(Long.parseLong(e.getMerchantId()), false));
                });
            }
        }

        deliveryNoteOrderFlatObjectList.forEach(e -> {
            if(e.getShowPriceFlag() == null){
                e.setShowPriceFlag(false);
            }
        });
    }

    /**
     * 处理展示打印配送单逻辑
     * @param deliveryNoteOrderFlatObjectList 配送单信息
     */
    private void handleShowDeliveryNotePrint(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return;
        }
        List<DeliveryNoteOrderFlatObject> xmOrders = deliveryNoteOrderFlatObjectList.stream().filter(e -> DistOrderSourceEnum.getXmOrderTypeCode().contains(e.getSource())).collect(Collectors.toList());
        List<Long> mIds = xmOrders.stream().map(DeliveryNoteOrderFlatObject::getMerchantId).map(Long::parseLong).collect(Collectors.toList());
        List<Long> notNeedPrintMIds = merchantStoreExtQueryFacade.queryBatchNoNeedPrintOutConfig(mIds);

        deliveryNoteOrderFlatObjectList.forEach(e -> {
            e.setDeliveryNotePrintFlag(!notNeedPrintMIds.contains(Long.parseLong(e.getMerchantId())));
        });
    }

    /**
     * 创建配送单
     * @param deliveryNoteOrderFlatObjectList 配送单信息
     * @param storeNo 城配仓编号
     */
    private List<DeliveryNoteRenderingExtendExcelVO> createDeliveryNote(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList, Integer storeNo) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return Collections.emptyList();
        }

        // 过滤出需要打印配送单的订单信息
        deliveryNoteOrderFlatObjectList = deliveryNoteOrderFlatObjectList.stream().filter(DeliveryNoteOrderFlatObject::getDeliveryNotePrintFlag).collect(Collectors.toList());

        List<DeliveryNoteRenderingExtendExcelVO> allExcelVOList = new ArrayList<>();

        // 鲜沐配送单数据
        List<DeliveryNoteRenderingExtendExcelVO> xmDeliveryNoteList = this.xmDeliveryNoteDateCreate(deliveryNoteOrderFlatObjectList);

        // todo 顺路达配送单数据处理 兼容上线前逻辑，后续source落表后可以删除
        // POP城配仓查询
        List<Integer> popWarehouseLogisticsList = wncQueryFacade.queryPopWarehouseLogisticsList();
        if(storeNo != null && popWarehouseLogisticsList.contains(storeNo)){
            xmDeliveryNoteList.forEach(e -> {
                e.setAppSource(TmsDeliveryNoteTemplateEnums.AppSource.SUNLUDA.getValue());
            });
        }

        // saas配送单数据
        List<DeliveryNoteRenderingExtendExcelVO> saasDeliveryNoteList = this.saasDeliveryNoteDateCreate(deliveryNoteOrderFlatObjectList);

        // 外单配送单数据
        List<DeliveryNoteRenderingExtendExcelVO> outDeliveryNoteList = this.outerDeliveryNoteDateCreate(deliveryNoteOrderFlatObjectList);

        allExcelVOList.addAll(saasDeliveryNoteList);
        allExcelVOList.addAll(outDeliveryNoteList);
        allExcelVOList.addAll(xmDeliveryNoteList);

        return allExcelVOList;

    }


    /**
     * 鲜沐配送单创建
     * @param deliveryNoteOrderFlatObjectList 订单信息
     * @return 渲染后的配送单信息
     */
    private List<DeliveryNoteRenderingExtendExcelVO> xmDeliveryNoteDateCreate(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return Collections.emptyList();
        }
        // 过滤鲜沐订单
        List<DeliveryNoteOrderFlatObject> xmDeliveryOrders = deliveryNoteOrderFlatObjectList.stream()
                .filter(e -> DistOrderSourceEnum.getXmOrderTypeCode().contains(e.getSource()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(xmDeliveryOrders)){
            return Collections.emptyList();
        }

        List<DeliveryNoteRenderingExtendExcelVO> resultList = new ArrayList<>();

        // 按照鲜沐店铺站点聚合订单信息
        Map<Long, List<DeliveryNoteOrderFlatObject>> xmDeliverySite2DistOrdersMap = xmDeliveryOrders.stream().collect(Collectors.groupingBy(DeliveryNoteOrderFlatObject::getDeliverySiteId));

        // 配送单转换
        xmDeliverySite2DistOrdersMap.forEach((deliverySiteId, deliveryNoteOrders) -> {
            DeliveryNoteRenderingExtendExcelVO excelVO = DeliveryNoteConverter.objectListToExcelVO(deliveryNoteOrders);
            if(excelVO == null){
                return;
            }
            DeliveryNoteOrderFlatObject deliveryNoteOrderFlatObject = deliveryNoteOrders.get(0);
            if(deliveryNoteOrderFlatObject.getBigCustomerId() != null){
                excelVO.setBigCustomerId(deliveryNoteOrderFlatObject.getBigCustomerId());
            }
            if(StringUtils.isEmpty(deliveryNoteOrderFlatObject.getMerchantId())){
                excelVO.setMerchantId(Long.parseLong(deliveryNoteOrderFlatObject.getMerchantId()));
            }
            excelVO.setTenantId(Constants.Tenant.XM_TENANT_ID);
            if(DistOrderSourceEnum.getXmSldOrderTypeCode().contains(deliveryNoteOrderFlatObject.getSource())){
                excelVO.setAppSource(TmsDeliveryNoteTemplateEnums.AppSource.SUNLUDA.getValue());
            }else{
                excelVO.setAppSource(TmsDeliveryNoteTemplateEnums.AppSource.XIANMU.getValue());
            }
            excelVO.setCustomerShowPriceFlag(deliveryNoteOrderFlatObject.getShowPriceFlag());

            // 设置销售主体
            List<String> sellingEntityNameList = deliveryNoteOrders.stream()
                    .map(DeliveryNoteOrderFlatObject::getSellingEntityName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sellingEntityNameList)){
                excelVO.setSellingEntityName(sellingEntityNameList.get(0));
            }

            resultList.add(excelVO);
        });

        return resultList;
    }

    /**
     * saas配送单创建
     * @param deliveryNoteOrderFlatObjectList 订单信息
     * @return 渲染后的配送单信息
     */
    private List<DeliveryNoteRenderingExtendExcelVO> saasDeliveryNoteDateCreate(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return Collections.emptyList();
        }
        // 过滤出saas订单
        List<DeliveryNoteOrderFlatObject> saasDeliveryOrders = deliveryNoteOrderFlatObjectList.stream()
                .filter(e -> DistOrderSourceEnum.getSaasOrderTypeCode().contains(e.getSource()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(saasDeliveryOrders)){
            return Collections.emptyList();
        }

        List<DeliveryNoteRenderingExtendExcelVO> resultList = new ArrayList<>();

        // 按照鲜沐店铺站点聚合订单信息
        Map<Long, List<DeliveryNoteOrderFlatObject>> saasDeliverySite2DistOrdersMap = saasDeliveryOrders.stream().collect(Collectors.groupingBy(DeliveryNoteOrderFlatObject::getDeliverySiteId));

        // 配送单转换
        saasDeliverySite2DistOrdersMap.forEach((deliverySiteId, deliveryNoteOrders) -> {
            DeliveryNoteRenderingExtendExcelVO excelVO = DeliveryNoteConverter.objectListToExcelVO(deliveryNoteOrders);
            if(excelVO == null){
                return;
            }
            DeliveryNoteOrderFlatObject deliveryNoteOrderFlatObject = deliveryNoteOrders.get(0);
            if(!StringUtils.isEmpty(deliveryNoteOrderFlatObject.getOuterTenantId())){
                excelVO.setBigCustomerId(Long.parseLong(deliveryNoteOrderFlatObject.getOuterTenantId()));
            }
            if(StringUtils.isEmpty(deliveryNoteOrderFlatObject.getMerchantId())){
                excelVO.setMerchantId(Long.parseLong(deliveryNoteOrderFlatObject.getMerchantId()));
            }
            excelVO.setTenantId(Long.parseLong(deliveryNoteOrderFlatObject.getOuterTenantId()));
            excelVO.setAppSource(TmsDeliveryNoteTemplateEnums.AppSource.SAAS.getValue());
            excelVO.setCustomerShowPriceFlag(deliveryNoteOrderFlatObject.getShowPriceFlag());

            resultList.add(excelVO);
        });

        return resultList;
    }

    private List<DeliveryNoteRenderingExtendExcelVO> outerDeliveryNoteDateCreate(List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList) {
        if(CollectionUtils.isEmpty(deliveryNoteOrderFlatObjectList)){
            return Collections.emptyList();
        }
        List<DeliveryNoteOrderFlatObject> outerCityOrders = deliveryNoteOrderFlatObjectList.stream()
                .filter(e -> Objects.equals(DistOrderSourceEnum.OUTER_CITY.getCode(), e.getSource()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(outerCityOrders)){
            return Collections.emptyList();
        }

        List<DeliveryNoteRenderingExtendExcelVO> resultList = new ArrayList<>();

        // 按照鲜沐店铺站点聚合订单信息
        Map<Long, List<DeliveryNoteOrderFlatObject>> outerDeliverySite2DistOrdersMap = outerCityOrders.stream().collect(Collectors.groupingBy(DeliveryNoteOrderFlatObject::getDeliverySiteId));

        // 配送单转换
        outerDeliverySite2DistOrdersMap.forEach((deliverySiteId, deliveryNoteOrders) -> {
            DeliveryNoteRenderingExtendExcelVO excelVO = DeliveryNoteConverter.objectListToExcelVO(deliveryNoteOrders);
            if(excelVO == null){
                return;
            }
            DeliveryNoteOrderFlatObject deliveryNoteOrderFlatObject = deliveryNoteOrders.get(0);
            if(!StringUtils.isEmpty(deliveryNoteOrderFlatObject.getOuterTenantId())){
                excelVO.setBigCustomerId(Long.parseLong(deliveryNoteOrderFlatObject.getOuterTenantId()));
            }
            if(StringUtils.isEmpty(deliveryNoteOrderFlatObject.getMerchantId())){
                excelVO.setMerchantId(Long.parseLong(deliveryNoteOrderFlatObject.getMerchantId()));
            }
            excelVO.setTenantId(Long.parseLong(deliveryNoteOrderFlatObject.getOuterTenantId()));
            excelVO.setAppSource(TmsDeliveryNoteTemplateEnums.AppSource.OUTSIDE.getValue());
            excelVO.setCustomerShowPriceFlag(deliveryNoteOrderFlatObject.getShowPriceFlag());

            resultList.add(excelVO);
        });

        return resultList;
    }

    /**
     * 下载文件
     * @param fileURL 链接
     * @param savePath 保存路径
     */
    private void downloadFile(String fileURL, String savePath) {
        try {
            URL url = new URL(fileURL);
            InputStream in = url.openStream();
            ReadableByteChannel rbc = Channels.newChannel(in);// NOSONAR
            FileOutputStream fos = new FileOutputStream(savePath);// NOSONAR
            fos.getChannel().transferFrom(rbc, 0, Long.MAX_VALUE);
            fos.close();
            in.close();
            rbc.close();
            log.info("文件下载完成，保存路径为：" + savePath);
        } catch (IOException e) {
            log.error("下载文件时发生错误", e);
            e.printStackTrace();
        }
    }

    @Override
    public Long downloadOrderPrintDeliveryNoteInfo(List<String> orderNos, Boolean printDirection, String downLoadFileName) throws IOException {
        // 根据订单号查询配送单信息
        if(CollectionUtils.isEmpty(orderNos)){
            throw new TmsRuntimeException("订单号不能为空");
        }
        List<FulfillmentDeliveryNoteOrderInfoDTO> fulfillmentDeliveryNoteOrderInfoDTOS = ofcQueryFacade.queryFulfillmentDeliveryNoteInfo(null, orderNos);
        if(CollectionUtils.isEmpty(fulfillmentDeliveryNoteOrderInfoDTOS)){
            throw new TmsRuntimeException("暂无配送单数据");
        }
        // 转换为TMS对象
        List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList = fulfillmentDeliveryNoteOrderInfoDTOS.stream()
                .map(DeliveryNoteConverter::fulDeliveryNoteOrderDTO2FlatObject)
                .collect(Collectors.toList());

        // 设置deliverySiteId
        for (int i = 0; i < deliveryNoteOrderFlatObjectList.size(); i++) {
            deliveryNoteOrderFlatObjectList.get(i).setDeliverySiteId(Long.parseLong(String.valueOf(i)));
        }

        // 处理订单信息
        for (DeliveryNoteOrderFlatObject singleDeliveryNoteOrder : deliveryNoteOrderFlatObjectList) {
            this.handleFulOrderInfo(Collections.singletonList(singleDeliveryNoteOrder), singleDeliveryNoteOrder.getDeliveryTime());
        }

        DeliveryNoteOrderFlatObject deliveryNoteOrderFlatObject = deliveryNoteOrderFlatObjectList.get(0);
        Set<String> allDownLoadFileNameSet = deliveryNotePrintInfo(deliveryNoteOrderFlatObject.getStoreNo(), deliveryNoteOrderFlatObjectList, printDirection);

        return download(deliveryNoteOrderFlatObject, allDownLoadFileNameSet, downLoadFileName);
    }

    private Long download(DeliveryNoteOrderFlatObject vo, Set<String> allDownLoadFileNameSet, String downLoadFileName) {
        if(CollectionUtils.isEmpty(allDownLoadFileNameSet)){
            throw new TmsRuntimeException("暂无配送单数据");
        }
        // 获取渲染的文件 获取idShowFlag2FileNameMap 所有的value
        List<String> fileNames = new ArrayList<>(allDownLoadFileNameSet);
        String storeName = vo.getStoreName() == null ? "" : vo.getStoreName();
        String deliveryTime = vo.getDeliveryTime() == null ? "" : vo.getDeliveryTime().toString();
        // 上传到OSS
        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        recordDTO.setBizType(FileDownloadTypeEnums.XianMu.DELIVERY_NOTE_IMPORT.getValue());
        recordDTO.setTenantId(Constants.Tenant.XM_TENANT_ID);
        recordDTO.setUserId(Long.valueOf(Optional.ofNullable(UserInfoHolder.getUser()).orElse(new UserBase()).getBizUserId()));
        if(StringUtils.isEmpty(downLoadFileName)){
            recordDTO.setFileName(storeName + "-" + deliveryTime + ".xlsx");
        }else{
            recordDTO.setFileName(downLoadFileName + ".xlsx");
        }
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);

        return DownloadCenterHelper.build(ExecutorUtil.downloadCenterExecutor, recordDTO).asyncWriteWithOssResp(fileNames, (x) -> {
            long beginCurrentTimeMillis = System.currentTimeMillis();

            // 1、生成临时文件
            String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + UUID.randomUUID() + "_" + recordDTO.getFileName();
            File tempFile = new File(tempFilePath);

            // 2、写入数据
            // 调用工具类方法
            try {
                DeliveryNoteExcelMerger.mergeExcelFiles(fileNames, tempFilePath);
            } catch (IOException e) {
                e.printStackTrace();
            }


            log.info("合并文件耗时：{}ms", System.currentTimeMillis() - beginCurrentTimeMillis);
            // 3、文件上传至OSS
            OssUploadResult uploadResult;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(tempFile), recordDTO.getExpiredDayLabel());
            } catch (Exception e) {
                log.error("下载中心-上传文件到OSS失败，recordDTO:{}", JSON.toJSONString(recordDTO), e);
                throw new ProviderException("下载中心-上传文件到OSS失败");
            }
            // 4、返回OSS文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());

            // 删除多余的文件
            ExcelUtils.batchDeliveryFileByPath(fileNames);

            return downloadCenterOssRespDTO;
        });
    }

    @Override
    public Long downloadSelfPickupOrderPrintDeliveryNoteInfo(SelfPickupOrderDeliveryNotePrintInput input) throws IOException {
        List<String> orderNos = Collections.singletonList(input.getOrderNo());
        List<FulfillmentDeliveryNoteOrderInfoDTO> fulfillmentDeliveryNoteOrderInfoDTOS = ofcQueryFacade.queryFulfillmentDeliveryNoteInfo(null, orderNos);
        if(CollectionUtils.isEmpty(fulfillmentDeliveryNoteOrderInfoDTOS)){
            throw new TmsRuntimeException("暂无配送单数据");
        }
        // 过滤状态为自提的订单
        fulfillmentDeliveryNoteOrderInfoDTOS = fulfillmentDeliveryNoteOrderInfoDTOS.stream().filter(e -> Objects.equals(e.getFulfillmentStatus(), 44)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(fulfillmentDeliveryNoteOrderInfoDTOS)){
            throw new TmsRuntimeException("暂无配送单数据");
        }
        // 转换为TMS对象
        List<DeliveryNoteOrderFlatObject> deliveryNoteOrderFlatObjectList = fulfillmentDeliveryNoteOrderInfoDTOS.stream()
                .map(DeliveryNoteConverter::fulDeliveryNoteOrderDTO2FlatObject)
                .collect(Collectors.toList());
        // 设置deliverySiteId
        for (int i = 0; i < deliveryNoteOrderFlatObjectList.size(); i++) {
            deliveryNoteOrderFlatObjectList.get(i).setDeliverySiteId(Long.parseLong(String.valueOf(i)));
        }

        this.handleFulOrderInfo(deliveryNoteOrderFlatObjectList, null);

        // 过滤目标仓库编号
        for (DeliveryNoteOrderFlatObject deliveryNoteOrderFlatObject : deliveryNoteOrderFlatObjectList) {
            List<DeliveryNoteOrderItemFlatObject> items = deliveryNoteOrderFlatObject.getItems();

            List<DeliveryNoteOrderItemFlatObject> deliveryFeeItems = items.stream().filter(e -> Objects.equals(e.getDistItemId(), "配送费")).collect(Collectors.toList());

            items = items.stream()
                    .filter(e -> Objects.equals(e.getWarehouseNo(), input.getWarehouseNo()))
                    .collect(Collectors.toList());

            if(!CollectionUtils.isEmpty(deliveryFeeItems)){
                items.addAll(deliveryFeeItems);
            }

            deliveryNoteOrderFlatObject.setItems(items);
        }

        DeliveryNoteOrderFlatObject deliveryNoteOrderFlatObject = deliveryNoteOrderFlatObjectList.get(0);
        Set<String> allDownLoadFileNameSet = deliveryNotePrintInfo(deliveryNoteOrderFlatObject.getStoreNo(), deliveryNoteOrderFlatObjectList, input.getPrintDirection());
        return download(deliveryNoteOrderFlatObject, allDownLoadFileNameSet, null);
    }
}