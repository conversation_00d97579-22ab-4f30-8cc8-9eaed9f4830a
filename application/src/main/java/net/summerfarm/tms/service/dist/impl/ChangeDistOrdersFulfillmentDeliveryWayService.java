package net.summerfarm.tms.service.dist.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.enums.CommonResultStateEnum;
import net.summerfarm.tms.client.message.in.DistOrderCancelMessage;
import net.summerfarm.tms.client.message.in.DistOrderCreateMessage;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.dto.BatchChangeDistOrdersFulfillmentDeliveryWayDTO;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.input.command.BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.gray.old2new.DistOrderSyncService;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.service.dist.converter.ChangeDistOrderFulfilmentWayConverter;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 变更委托单履约配送方式服务实现类
 * date: 2025/7/21 14:38<br/>
 *
 * <AUTHOR> />
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ChangeDistOrdersFulfillmentDeliveryWayService {

    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    @Lazy
    private DistOrderSyncService distOrderSyncService;


    /**
     * 处理单个委托单履约配送方式修改
     */
    @Transactional(rollbackFor = Exception.class)
    public BatchChangeDistOrdersFulfillmentDeliveryWayDTO.DistOrderChangeResult processDistOrderFulfillmentDeliveryWayChange(BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput item, List<String> allChangeOutOrderIdList) {
        BatchChangeDistOrdersFulfillmentDeliveryWayDTO.DistOrderChangeResult result = new BatchChangeDistOrdersFulfillmentDeliveryWayDTO.DistOrderChangeResult();
        result.setOutOrderId(item.getOutOrderId());
        result.setSource(item.getSource());
        result.setOuterContactId(item.getOuterContactId());
        result.setTargetFulfillmentDeliveryWay(item.getTargetFulfillmentDeliveryWay());
        result.setDeliveryTime(item.getDeliveryTime());

        try {
            // 1. 参数校验
            String validationError = this.validateFulfillmentDeliveryWayChange(item);
            if (validationError != null) {
                result.setResultState(CommonResultStateEnum.FAIL.getCode());
                result.setFailedReason(validationError);
                return result;
            }

            // 2. 查询委托单
            DistOrderEntity distOrder = distOrderRepository.queryWithItemWithSiteByUk(item.getOutOrderId(), DistOrderSourceEnum.getDistOrderSourceByCode(item.getSource()), item.getDeliveryTime().atStartOfDay(), item.getOuterContactId());
            if (distOrder == null) {
                result.setResultState(CommonResultStateEnum.FAIL.getCode());
                result.setFailedReason("委托单不存在");
                return result;
            }

            result.setDistOrderId(distOrder.getDistId());
            result.setOriginalFulfillmentDeliveryWay(distOrder.getFulfillmentDeliveryWay());

            // 3. 状态校验
            String statusError = this.validateDistOrderStatus(distOrder);
            if (statusError != null) {
                result.setResultState(CommonResultStateEnum.FAIL.getCode());
                result.setFailedReason(statusError);
                return result;
            }

            // 4. 检查门店当天配送方式唯一性
            String uniquenessError = this.validateStoreDeliveryWayUniqueness(distOrder, item.getTargetFulfillmentDeliveryWay(), allChangeOutOrderIdList);
            if (uniquenessError != null) {
                result.setResultState(CommonResultStateEnum.FAIL.getCode());
                result.setFailedReason(uniquenessError);
                return result;
            }

            // 5. 验证通过，标记为成功
            // 注意：实际的取消和创建操作将在批量处理中统一执行
            // 这里只做验证，不执行实际的修改操作

            result.setResultState(CommonResultStateEnum.SUCCESS.getCode());
            log.info("委托单{}履约配送方式修改成功，从{}修改为{}",
                    distOrder.getDistId(), result.getOriginalFulfillmentDeliveryWay(), item.getTargetFulfillmentDeliveryWay());

        } catch (Exception e) {
            log.error("处理委托单{}履约配送方式修改时发生异常", item.getOutOrderId(), e);
            result.setResultState(1);
            result.setFailedReason("系统异常：" + e.getMessage());
        }

        return result;
    }

    /**
     * 批量更新委托单履约配送方式
     * 采用先统一取消，再统一创建的方式，避免多订单相互影响
     *
     * @param distOrdersWithTargetWay 委托单和目标配送方式的映射
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateDistOrdersFulfillmentDeliveryWay(Map<DistOrderEntity, Integer> distOrdersWithTargetWay) {
        if (distOrdersWithTargetWay == null || distOrdersWithTargetWay.isEmpty()) {
            return;
        }

        log.info("开始批量更新{}个委托单的履约配送方式", distOrdersWithTargetWay.size());

        try {
            // 第一阶段：统一取消所有需要修改的委托单
            List<DistOrderCancelMessage> cancelMessages = new ArrayList<>();
            for (DistOrderEntity distOrder : distOrdersWithTargetWay.keySet()) {
                DistOrderCancelMessage cancelMessage = ChangeDistOrderFulfilmentWayConverter.distOrderEntityToBuildCancelMsg(distOrder);
                cancelMessages.add(cancelMessage);
                log.info("准备取消委托单：{}", distOrder.getDistId());
            }

            // 批量取消
            for (DistOrderCancelMessage cancelMessage : cancelMessages) {
                distOrderSyncService.cancelDistOrder(cancelMessage);
            }
            log.info("已取消{}个委托单", cancelMessages.size());

            // 第二阶段：统一创建所有新的委托单
            List<DistOrderCreateMessage> createMessages = new ArrayList<>();
            for (Map.Entry<DistOrderEntity, Integer> entry : distOrdersWithTargetWay.entrySet()) {
                DistOrderEntity distOrder = entry.getKey();
                Integer targetDeliveryWay = entry.getValue();

                // 设置新的配送方式
                distOrder.setFulfillmentDeliveryWay(targetDeliveryWay);

                // 干线转运变成干配，换货单需要拆成多条消息发送
                if (Objects.equals(targetDeliveryWay,DistOrderFulfillmentDeliveryWayEnum.DRY_DELIVERY.getCode()) &&
                        Objects.equals(distOrder.getFulfillmentDeliveryWay(), DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode())
                        && Objects.equals(distOrder.getDistFlowVO().getType(), DistTypeEnum.DELIVERY_AND_RECYCLE.getCode())) {

                    DistOrderCreateMessage recycleCreateMessage = ChangeDistOrderFulfilmentWayConverter.distOrderEntityToBuildDistOrderMsg(distOrder);
                    recycleCreateMessage.setType(DistTypeEnum.RECYCLE.getCode());
                    recycleCreateMessage.getDistOrderItemList().forEach(item -> {
                        item.setType(DistItemDeliveryTypeEnum.RECYCLE.getCode());
                    });
                    createMessages.add(recycleCreateMessage);

                    DistOrderCreateMessage deliveryCreateMessage = ChangeDistOrderFulfilmentWayConverter.distOrderEntityToBuildDistOrderMsg(distOrder);
                    deliveryCreateMessage.setType(DistTypeEnum.DELIVERY.getCode());
                    deliveryCreateMessage.getDistOrderItemList().forEach(item -> {
                        item.setType(DistItemDeliveryTypeEnum.DELIVERY.getCode());
                    });
                    createMessages.add(deliveryCreateMessage);
                }else{
                    DistOrderCreateMessage createMessage = ChangeDistOrderFulfilmentWayConverter.distOrderEntityToBuildDistOrderMsg(distOrder);
                    createMessages.add(createMessage);
                }
                log.info("准备创建委托单：{}，新配送方式：{}", distOrder.getDistId(), targetDeliveryWay);
            }

            // 批量创建
            for (DistOrderCreateMessage createMessage : createMessages) {
                distOrderSyncService.createDistOrder(createMessage);
            }
            log.info("已创建{}个新委托单", createMessages.size());

            log.info("批量更新委托单履约配送方式完成");

        } catch (Exception e) {
            log.error("批量更新委托单履约配送方式时发生异常", e);
            throw new RuntimeException("批量更新失败：" + e.getMessage(), e);
        }
    }


    /**
     * 参数校验
     */
    private String validateFulfillmentDeliveryWayChange(BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput item) {
        // 只允许干配(0)、干线转运(4)之间互相修改
        List<Integer> allowedDeliveryWays = Arrays.asList(
                DistOrderFulfillmentDeliveryWayEnum.DRY_DELIVERY.getCode(),
                DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode()
        );

        if (!allowedDeliveryWays.contains(item.getTargetFulfillmentDeliveryWay())) {
            return "目标履约配送方式只支持干配、干线转运之间的修改";
        }

        return null;
    }


    /**
     * 委托单状态校验
     */
    private String validateDistOrderStatus(DistOrderEntity distOrder) {
        // 只有带承运状态的委托单可以修改
        List<Integer> allowedStatuses = Arrays.asList(
                DistOrderStatusEnum.TO_BE_WIRED.getCode(),
                DistOrderStatusEnum.IN_WIRED.getCode()
        );

        if (!allowedStatuses.contains(distOrder.getStatus().getCode())) {
            return "委托单状态不允许修改，只有待排线状态的委托单可以修改履约配送方式";
        }

        // 检查当前履约配送方式是否在允许修改的范围内
        List<Integer> allowedDeliveryWays = Arrays.asList(
                DistOrderFulfillmentDeliveryWayEnum.DRY_DELIVERY.getCode(),
                DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode()
        );

        if (!allowedDeliveryWays.contains(distOrder.getFulfillmentDeliveryWay())) {
            return "当前履约配送方式不支持修改，只支持干配、干线转运之间的修改";
        }

        return null;
    }

    /**
     * 检查门店当天配送方式唯一性
     */
    private String validateStoreDeliveryWayUniqueness(DistOrderEntity distOrder, Integer targetDeliveryWay, List<String> allChangeOutOrderIdList) {
        try {
            // 查询同一门店、同一天的所有委托单
            DistOrderQuery query = DistOrderQuery.builder()
                    .beginSiteId(distOrder.getBeginSite().getId())
                    .expectBeginTime(distOrder.getDistFlowVO().getExpectBeginTime().toLocalDate().atStartOfDay())
                    .endSiteId(distOrder.getEndSite().getId())
                    .status(Arrays.asList(
                            DistOrderStatusEnum.TO_BE_WIRED.getCode(),
                            DistOrderStatusEnum.IN_WIRED.getCode()
                    ))
                    .build();

            List<DistOrderEntity> sameDayOrders = distOrderRepository.queryList(query);

            // 统计修改后的配送方式分布
            Set<Integer> deliveryWaySet = new HashSet<>();

            for (DistOrderEntity order : sameDayOrders) {
                if (order.getDistId().equals(distOrder.getDistId())) {
                    // 当前委托单使用目标配送方式
                    deliveryWaySet.add(targetDeliveryWay);
                } else if (!allChangeOutOrderIdList.contains(order.getDistClientVO().getOutOrderId())) {
                    // 其他委托单使用原有配送方式
                    deliveryWaySet.add(order.getFulfillmentDeliveryWay());
                }
            }

            // 检查是否只有一种配送方式
            if (deliveryWaySet.size() > 1) {
                String allOutOrderId = sameDayOrders.stream().map(e -> e.getDistClientVO().getOutOrderId()).collect(Collectors.joining(","));
                log.info("门店当天存在多配送方式，修改后将有{}种配送方式，需要一起修改订单:{}，不符合门店当天只能有一种配送方式的要求",deliveryWaySet.size(),allOutOrderId);
                return String.format("门店当天存在多配送方式，修改后将有%d种配送方式，需要一起修改订单:%s，不符合门店当天只能有一种配送方式的要求",deliveryWaySet.size(),allOutOrderId);
            }

            return null;

        } catch (Exception e) {
            log.error("检查门店配送方式唯一性时发生异常", e);
            return "检查门店配送方式唯一性失败：" + e.getMessage();
        }
    }

}
