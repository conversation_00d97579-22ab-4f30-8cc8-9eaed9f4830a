package net.summerfarm.tms.ext;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.auth.AuthQueryFacade;
import net.xianmu.authentication.client.dto.ShiroUser;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/7/15 22:49<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class AuthExtService implements AuthService {

    @Resource
    private AuthQueryFacade authQueryFacade;

    public Integer getCurrentUserId() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            throw new TmsRuntimeException("未获取到登录信息，请重新登录");
        }
        if (user.getBizUserId() == null) {
            throw new TmsRuntimeException("账号业务ID为空，请联系管理员");
        }
        return Integer.parseInt(String.valueOf(user.getBizUserId()));
    }

    public String getCurrentUserName() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            throw new TmsRuntimeException("未获取到登录信息，请重新登录");
        }
        return user.getNickname();
    }

    public String getAdminNameById(Long adminId) {
        return authQueryFacade.queryAdminRealName(adminId);
    }

    public Long getTmsCurrentUserId() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            throw new TmsRuntimeException("未获取到登录信息，请重新登录");
        }

        return user.getBizUserId();
    }

    public String getPasswordByDriverId(Long driverId) {
        String password = "";
        try {
            password = authQueryFacade.queryDriverPassword(driverId);
        } catch (Exception e) {
            log.error("查询auth司机密码异常driverId:{}",driverId,e);
        }

        return password;
    }

    @Override
    public List<String> queryCityStoreUserPermission(Long authUserId) {
        return authQueryFacade.queryCityStoreUserPermission(authUserId);
    }
}
