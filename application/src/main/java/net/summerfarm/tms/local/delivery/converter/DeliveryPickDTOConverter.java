package net.summerfarm.tms.local.delivery.converter;

import net.summerfarm.tms.delivery.dto.DeliveryPickDTO;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;

/**
 * Description: <br/>
 * date: 2022/10/20 15:44<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryPickDTOConverter {
    public static DeliveryPickDTO entity2Dto(DeliveryPickEntity deliveryPickEntity) {
        DeliveryPickDTO deliveryPickDTO = new DeliveryPickDTO();
        deliveryPickDTO.setId(deliveryPickEntity.getId());
        deliveryPickDTO.setCreateTime(deliveryPickEntity.getCreateTime());
        deliveryPickDTO.setUpdateTime(deliveryPickEntity.getUpdateTime());
        deliveryPickDTO.setDeliveryBatchId(deliveryPickEntity.getDeliveryBatchId());
        deliveryPickDTO.setSiteId(deliveryPickEntity.getSiteId());
        deliveryPickDTO.setDriverId(deliveryPickEntity.getDriverId());
        deliveryPickDTO.setOutItemId(deliveryPickEntity.getOutItemId());
        deliveryPickDTO.setItemDesc(deliveryPickEntity.getItemDesc());
        deliveryPickDTO.setParticle(deliveryPickEntity.getParticle());
        deliveryPickDTO.setType(deliveryPickEntity.getType());
        deliveryPickDTO.setQuantity(deliveryPickEntity.getQuantity());
        deliveryPickDTO.setPickQuantity(deliveryPickEntity.getPickQuantity());
        deliveryPickDTO.setShortQuantity(deliveryPickEntity.getShortQuantity());
        deliveryPickDTO.setInterceptQuantity(deliveryPickEntity.getInterceptQuantity());
        deliveryPickDTO.setUnit(deliveryPickEntity.getUnit());
        deliveryPickDTO.setStatus(deliveryPickEntity.getStatus());
        deliveryPickDTO.setFinishTime(deliveryPickEntity.getFinishTime());
        deliveryPickDTO.setTemperature(deliveryPickEntity.getTemperature());
        deliveryPickDTO.setWeight(deliveryPickEntity.getWeight());
        deliveryPickDTO.setDeliverySiteId(deliveryPickEntity.getDeliverySiteId());
        deliveryPickDTO.setSpecification(deliveryPickEntity.getSpecification());
        deliveryPickDTO.setSkuPic(deliveryPickEntity.getSkuPic());
        deliveryPickDTO.setSkuType(deliveryPickEntity.getSkuType());
        deliveryPickDTO.setNameRemakes(deliveryPickEntity.getNameRemakes());
        deliveryPickDTO.setExtType(deliveryPickEntity.getExtType());
        deliveryPickDTO.setCategoryType(deliveryPickEntity.getCategoryType());
        deliveryPickDTO.setProcessFlag(deliveryPickEntity.getProcessFlag());
        deliveryPickDTO.setProcessQuantity(deliveryPickEntity.getProcessQuantity());
        deliveryPickDTO.setProcessWeight(deliveryPickEntity.getProcessWeight());
        deliveryPickDTO.setProcessUnit(deliveryPickEntity.getProcessUnit());
        deliveryPickDTO.setProcessConversionRatio(deliveryPickEntity.getProcessConversionRatio());
        deliveryPickDTO.setStorageArea(deliveryPickEntity.getStorageArea());
        deliveryPickDTO.setBarcodes(deliveryPickEntity.getBarcodes());
        deliveryPickDTO.setPackType(deliveryPickEntity.getPackType());
        deliveryPickDTO.setOuterClientName(deliveryPickEntity.getOuterClientName());
        deliveryPickDTO.setScanCount(deliveryPickEntity.getScanCount());
        return deliveryPickDTO;
    }

    public static DeliveryPickEntity dto2Entity(DeliveryPickDTO deliveryPick) {
        DeliveryPickEntity deliveryPickEntity = new DeliveryPickEntity();
        deliveryPickEntity.setId(deliveryPick.getId());
        deliveryPickEntity.setCreateTime(deliveryPick.getCreateTime());
        deliveryPickEntity.setUpdateTime(deliveryPick.getUpdateTime());
        deliveryPickEntity.setDeliveryBatchId(deliveryPick.getDeliveryBatchId());
        deliveryPickEntity.setSiteId(deliveryPick.getSiteId());
        deliveryPickEntity.setDriverId(deliveryPick.getDriverId());
        deliveryPickEntity.setOutItemId(deliveryPick.getOutItemId());
        deliveryPickEntity.setItemDesc(deliveryPick.getItemDesc());
        deliveryPickEntity.setParticle(deliveryPick.getParticle());
        deliveryPickEntity.setType(deliveryPick.getType());
        deliveryPickEntity.setQuantity(deliveryPick.getQuantity());
        deliveryPickEntity.setPickQuantity(deliveryPick.getPickQuantity());
        deliveryPickEntity.setShortQuantity(deliveryPick.getShortQuantity());
        deliveryPickEntity.setInterceptQuantity(deliveryPick.getInterceptQuantity());
        deliveryPickEntity.setUnit(deliveryPick.getUnit());
        deliveryPickEntity.setStatus(deliveryPick.getStatus());
        deliveryPickEntity.setFinishTime(deliveryPick.getFinishTime());
        deliveryPickEntity.setTemperature(deliveryPick.getTemperature());
        deliveryPickEntity.setWeight(deliveryPick.getWeight());
        deliveryPickEntity.setDeliverySiteId(deliveryPick.getDeliverySiteId());
        deliveryPickEntity.setSpecification(deliveryPick.getSpecification());
        deliveryPickEntity.setProcessShortQuantity(deliveryPick.getProcessShortQuantity());
        deliveryPickEntity.setPackType(deliveryPick.getPackType());
        deliveryPickEntity.setOuterClientName(deliveryPick.getOuterClientName());
        return deliveryPickEntity;
    }
}
