package net.summerfarm.tms.local.dist.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.tms.alert.DeliveryAlertDomainService;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.car.CarRepository;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.driver.DriverDomainService;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.site.SiteDomainService;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.client.message.in.DistOrderCancelMessage;
import net.summerfarm.tms.client.message.in.DistOrderCreateMessage;
import net.summerfarm.tms.client.message.in.DistSiteMessage;
import net.summerfarm.tms.common.EventBusService;
import net.summerfarm.tms.delivery.*;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.delivery.dto.DeliveryOrderSaveByDistCommand;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.dist.*;
import net.summerfarm.tms.dist.dto.*;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.input.command.BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.event.EventTypeEnum;
import net.summerfarm.tms.event.StockEvent;
import net.summerfarm.tms.event.TmsDeliveryEvent;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.ext.AuthExtService;
import net.summerfarm.tms.facade.wnc.WncQueryFacade;
import net.summerfarm.tms.facade.wnc.input.QueryStoreNoByAddressInput;
import net.summerfarm.tms.local.delivery.DistOrderStatusChangeMsgSend;
import net.summerfarm.tms.local.delivery.converter.DeliveryBatchDTOConverter;
import net.summerfarm.tms.local.delivery.converter.DeliverySiteDTOConverter;
import net.summerfarm.tms.local.delivery.converter.StockEventConverter;
import net.summerfarm.tms.local.delivery.converter.TmsDeliveryEventConverter;
import net.summerfarm.tms.local.dist.converter.DistOrderConverter;
import net.summerfarm.tms.local.dist.handler.DistOrdersBatchInterceptHandler;
import net.summerfarm.tms.dist.dto.BatchChangeDistOrdersFulfillmentDeliveryWayDTO;
import net.summerfarm.tms.local.dist.sender.DistOrderMsgSender;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.summerfarm.tms.query.delivery.*;
import net.summerfarm.tms.query.dist.DistConfigQuery;
import net.summerfarm.tms.query.dist.DistOrderMark;
import net.summerfarm.tms.query.dist.DistOrderOnlyCodeQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.service.dist.impl.ChangeDistOrdersFulfillmentDeliveryWayService;
import net.summerfarm.tms.util.JsonUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Description:委托单接口实现
 * date: 2022/9/8 11:26
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistOrderServiceImpl implements DistOrderService {

    private final DistOrderDomainService distOrderDomainService;
    private final DeliveryBatchDomainService deliveryBatchDomainService;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final DriverDomainService driverDomainService;
    private final SiteDomainService siteDomainService;
    private final DistOrderRepository distOrderRepository;
    private final DistConfigRepository distConfigRepository;
    private final SiteRepository siteRepository;
    private final AuthExtService authExtService;
    private final DeliveryOrderDomainService deliveryOrderDomainService;
    private final DeliveryAlertDomainService deliveryAlertDomainService;
    private final DeliverySiteRepository deliverySiteRepository;
    private final EventBusService eventBusService;
    private final DistOrderMsgSender distOrderMsgSender;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final DeliverySiteItemRepository deliverySiteItemRepository;
    private final DistOrderValidator distOrderValidator;
    private final WncQueryFacade wncQueryFacade;
    private final DistOrdersBatchInterceptHandler distOrdersBatchInterceptHandler;
    private final ChangeDistOrdersFulfillmentDeliveryWayService changeDistOrdersFulfillmentDeliveryWayService;
    private final CarRepository carRepository;
    private final DistOrderStatusChangeMsgSend distOrderStatusChangeMsgSend;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Long> submitDistOrder(DistOrderCommand distOrderCommand) {
        DistOrderMark distOrderMark = distOrderCommand.getDistOrderMark();
        //查询委托单
        DistOrderEntity entity = distOrderRepository.queryByUk(distOrderMark.getOuterOrderId(), distOrderMark.getSource(), distOrderMark.getExpectBeginTime(), distOrderMark.getOuterContactId());
        if (entity != null && entity.isValid()) {
            return TmsResult.success(entity.getDistId());
        }
        DistOrderEntity distOrderEntity = DistOrderConverter.command2Entity(distOrderCommand);
        // 设置默认值
        if (DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode().equals(distOrderCommand.getFulfillmentDeliveryWay())) {
            // 干线转运
            distOrderEntity.setFulfillmentDeliveryWay(DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode());
        } else {
            // 干线
            distOrderEntity.setFulfillmentDeliveryWay(DistOrderFulfillmentDeliveryWayEnum.TRUNK.getCode());
        }

        //创建委托单
        Long distId = distOrderDomainService.createDistOrder(distOrderEntity);
        distOrderEntity.setDistId(distId);
        // 干线转运
        if (DistOrderSourceEnum.getCityCode().contains(distOrderEntity.getSource().getCode()) &&
                DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode().equals(distOrderCommand.getFulfillmentDeliveryWay())) {
            // 到此结束
            return TmsResult.success(distId);
        }

        if (Objects.equals(distOrderEntity.getSource(), DistOrderSourceEnum.OWN_SALE_OUT) ||
                Objects.equals(distOrderEntity.getSource(), DistOrderSourceEnum.ALLOCATION)||
                Objects.equals(distOrderEntity.getSource(), DistOrderSourceEnum.PURCHASE)) {
            distOrderMsgSender.sendOrderCreateMsg(distOrderEntity);
        }
        //委托单自动匹配
        List<Long> matchBatchIds = distOrderDomainService.autoMatchBatch(distOrderEntity);
        //触发装载率计算
        deliveryBatchDomainService.calcBatchLoadRatioByDistChange(matchBatchIds);
        return TmsResult.success(distId);
    }

    @Override
    public TmsResult<DistOrderDTO> query(String outerOrderId, DistOrderSourceEnum distOrderSourceEnum) {
        DistOrderMark distOrderMark = new DistOrderMark(outerOrderId, distOrderSourceEnum);
        DistOrderEntity distOrderEntity = distOrderDomainService.getDistOrderDetail(distOrderMark);
        return TmsResult.success(DistOrderConverter.entity2Dto(distOrderEntity, null));
    }

    @Override
    public TmsResult<DistOrderDTO> queryDetail(DistOrderQuery distOrderQuery) {
        DistOrderEntity distOrderEntity = distOrderDomainService.getDistOrderDetail(distOrderQuery.distOrderMark());
        //查询配送批次信息
        List<DeliveryBatchEntity> deliveryBatchEntities = deliveryBatchDomainService.getDeliveryBatchByDistId(distOrderEntity.getDistId());
        List<DeliverySiteEntity> deliverySiteEntities = new ArrayList<>();
        //外部请求详情过滤出未关闭、正常状态的调度单
        if (distOrderQuery.isOuterRequest()) {
            deliveryBatchEntities = deliveryBatchEntities.stream().filter(deliveryBatchEntity -> !DeliveryBatchStatusEnum.DELIVERY_CLOSED.equals(deliveryBatchEntity.getStatus())).collect(Collectors.toList());
            //查询运输点位详情信息
            for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntities) {
                //获取起始城配仓
                DeliverySiteEntity beginSiteEntity = deliveryBatchEntity.getBeginDeliverySite();
                deliveryBatchEntity.setBeginSiteEntity(beginSiteEntity.getSiteEntity());
                //获取运输点位
                DeliverySiteEntity deliverySiteEntity = deliveryBatchEntity.getSpecifiedDeliverySite(distOrderEntity.getEndSite().getId());
                if (deliverySiteEntity == null){
                    continue;
                }
                deliverySiteEntities.add(deliverySiteEntity);
            }
        }
        return TmsResult.success(DistOrderConverter.entity2Dto(distOrderEntity, getDeliveryBatchDTOs(deliveryBatchEntities), getDeliverySiteDTOs(deliverySiteEntities)));
    }

    private List<DeliverySiteDTO> getDeliverySiteDTOs(List<DeliverySiteEntity> deliverySiteEntities) {
        List<DeliverySiteDTO> deliverySiteDTOs = new ArrayList<>();
        if (deliverySiteEntities == null || deliverySiteEntities.isEmpty()){
            return deliverySiteDTOs;
        }
        return deliverySiteEntities.stream().map(DeliverySiteDTOConverter::entity2Dto).collect(Collectors.toList());
    }

    private List<DeliveryBatchDTO> getDeliveryBatchDTOs(List<DeliveryBatchEntity> deliveryBatchEntities) {
        List<DeliveryBatchDTO> deliveryBatchDTOs = new ArrayList<>();
        if (deliveryBatchEntities == null || deliveryBatchEntities.isEmpty()) {
            return deliveryBatchDTOs;
        }
        deliveryBatchEntities.forEach(deliveryBatchEntity -> {
            DeliveryBatchDTO deliveryBatchDTO = DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity);
            if(deliveryBatchDTO.getDriverId() != null){
                DriverEntity driverEntity = driverDomainService.driverDetail(deliveryBatchDTO.getDriverId());
                deliveryBatchDTO.setDriver(driverEntity.getName());
                deliveryBatchDTO.setDriverPhone(driverEntity.getPhone());
            }
            if(deliveryBatchDTO.getCarId() != null){
                CarEntity carEntity = carRepository.getCarDetail(deliveryBatchDTO.getCarId());
                if(carEntity != null){
                    deliveryBatchDTO.setCarNumber(carEntity.getCarNumber());
                    deliveryBatchDTO.setCarType(CarTypeEnum.typeMap.get(carEntity.getType()).getDesc());
                    deliveryBatchDTO.setStorageName(carEntity.getCarStorageEnum().getName());
                }
            }
            Integer createId = deliveryBatchEntity.getCreateId();
            deliveryBatchDTO.setCreator(createId == null ? "系统" : authExtService.getAdminNameById(createId.longValue()));
            deliveryBatchDTOs.add(deliveryBatchDTO);
        });
        return deliveryBatchDTOs;
    }

    @Override
    public TmsResult<PageInfo<DistOrderDTO>> queryPage(DistOrderQuery distOrderQuery) {
        PageInfo<DistOrderEntity> distOrderEntityPageInfo = distOrderRepository.queryPage(distOrderQuery);
        List<DistOrderEntity> distOrderEntities = distOrderEntityPageInfo.getList();
        List<DistOrderDTO> distOrderDTOs = getDistOrderDTOs(distOrderEntities);

        PageInfo<DistOrderDTO> distOrderDtoPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(distOrderEntityPageInfo, distOrderDtoPageInfo);
        distOrderDtoPageInfo.setList(distOrderDTOs);
        return TmsResult.success(distOrderDtoPageInfo);
    }

    private List<DistOrderDTO> getDistOrderDTOs(List<DistOrderEntity> distOrderEntities) {
        List<DistOrderDTO> distOrderDTOs = new ArrayList<>();
        if (distOrderEntities == null || distOrderEntities.isEmpty()) {
            return distOrderDTOs;
        }
        List<Long> siteIds = distOrderEntities.stream()
                .flatMap(distOrderEntity -> Stream.of(
                        distOrderEntity.getBeginSite(),
                        distOrderEntity.getMidSite(),
                        distOrderEntity.getEndSite()))
                .filter(Objects::nonNull)
                .map(SiteEntity::getId)
                .collect(Collectors.toList());
        Map<Long, SiteEntity> siteMap = siteRepository.queryMapByIds(siteIds);
        distOrderEntities.forEach(distOrderEntity -> {
            SiteEntity beginSite = siteMap.get(distOrderEntity.getBeginSite().getId());
            SiteEntity midSite = siteMap.get(distOrderEntity.getMidSite().getId());
            SiteEntity endSite = siteMap.get(distOrderEntity.getEndSite().getId());
            //处理点位信息
            distOrderEntity.setBeginSite(beginSite);
            distOrderEntity.setMidSite(midSite);
            distOrderEntity.setEndSite(endSite);
            DistOrderDTO distOrderDTO = DistOrderConverter.entity2Dto(distOrderEntity);
            distOrderDTOs.add(distOrderDTO);
        });
        return distOrderDTOs;
    }

    @Override
    public TmsResult<Void> editDistOrder(DistOrderCommand distOrderCommand) {
        DistOrderEntity distOrderDetail = distOrderDomainService.getDistOrderDetail(distOrderCommand.getDistOrderMark());
        if (!distOrderDetail.waitCarrierFlag()) {
            throw new TmsRuntimeException("该委托单已承运，不可编辑");
        }
        distOrderDomainService.editDistOrder(DistOrderConverter.command2Entity(distOrderCommand));
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<PageInfo<DistConfigDTO>> configList(DistConfigQuery distConfigQuery) {
        return null;
    }

    @Override
    public TmsResult<Void> addConfig(DistConfigCommand distConfigCommand) {
        return null;
    }

    @Override
    public TmsResult<Void> removeConfig(Long configId) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Void> cancelDistOrder(DistOrderEndCommand distOrderEndCommand) {
        String updater = distOrderEndCommand.getUpdater();
        String updaterId = distOrderEndCommand.getUpdaterId();
        TmsAssert.notEmpty(updater, ErrorCodeEnum.PARAM_NOT_NULL, "updater");
        TmsAssert.notEmpty(updaterId, ErrorCodeEnum.PARAM_NOT_NULL, "updaterId");

        DistOrderEntity distOrderEntity = distOrderDomainService.getDistOrderDetail(distOrderEndCommand.getDistOrderMark());
        if (!distOrderEntity.waitCarrierFlag()) {
            throw new TmsRuntimeException("该委托单已承运，不可取消预约");
        }
        distOrderEntity.update(getOperator(updater), getOperatorId(updaterId));
        distOrderDomainService.cancelDistOrder(distOrderEntity);

        //发送承运单关闭或者取消钉钉消息
        List<DistOrderSourceEnum> distOrderSourceEnumList = Lists.newArrayList(DistOrderSourceEnum.SALE_OUT,
                DistOrderSourceEnum.DEMO_OUT, DistOrderSourceEnum.RECOVER_OUT,
                DistOrderSourceEnum.OWN_SALE_OUT, DistOrderSourceEnum.ALLOCATION,DistOrderSourceEnum.PURCHASE);
        if (distOrderSourceEnumList.contains(distOrderEntity.getSource())) {
            distOrderMsgSender.sendOrderCancelOrCloseMsg(distOrderEntity, false);
        }
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Void> closeDistOrder(DistOrderEndCommand distOrderEndCommand) {
        Long distId = distOrderEndCommand.getDistId();
        String closeReason = distOrderEndCommand.getCloseReason();
        TmsAssert.notNull(distId, ErrorCodeEnum.PARAM_NOT_NULL, "distId");
        TmsAssert.notNull(closeReason, ErrorCodeEnum.PARAM_NOT_NULL, "closeReason");

        DistOrderEntity distOrderEntity = distOrderDomainService.getDistOrderDetail(distOrderEndCommand.getDistOrderMark());
        if(DistOrderSourceEnum.getTrunkOuterSource().contains(distOrderEntity.getSource())){
            throw new TmsRuntimeException("该委托单为外单不可关闭");
        }
        if(Objects.equals(DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode(),distOrderEntity.getFulfillmentDeliveryWay())){
            throw new TmsRuntimeException("该委托单为干线转运不可关闭");
        }
        if (!distOrderEntity.waitCarrierFlag()) {
            throw new TmsRuntimeException("该委托单已承运，不可关闭");
        }
        distOrderEntity.update(getOperator(), getOperatorId());
        distOrderDomainService.closeDistOrder(distOrderEntity, closeReason);
        //发送承运单关闭或者取消钉钉消息
        List<DistOrderSourceEnum> distOrderSourceEnumList = Lists.newArrayList(DistOrderSourceEnum.SALE_OUT,
                DistOrderSourceEnum.DEMO_OUT, DistOrderSourceEnum.RECOVER_OUT,
                DistOrderSourceEnum.OWN_SALE_OUT, DistOrderSourceEnum.ALLOCATION,
                DistOrderSourceEnum.PURCHASE);
        if (distOrderSourceEnumList.contains(distOrderEntity.getSource())) {
            distOrderMsgSender.sendOrderCancelOrCloseMsg(distOrderEntity, true);
        }
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Void> editMidSite(Long distId, Long midSiteId) {
        TmsAssert.notNull(distId, ErrorCodeEnum.PARAM_NOT_NULL, "distId");
        TmsAssert.notNull(midSiteId, ErrorCodeEnum.PARAM_NOT_NULL, "midSiteId");
        DistOrderEntity distOrderEntity = distOrderDomainService.getDistOrderDetail(distId);
        if (Objects.equals(distOrderEntity.getFulfillmentDeliveryWay(), DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode())) {
            throw new TmsRuntimeException("干线转运不支持不可编辑中转站");
        }
        if (!distOrderEntity.waitCarrierFlag()) {
            throw new TmsRuntimeException("该委托单已承运，不可编辑中转站");
        }
        distOrderEntity.update(getOperator(), getOperatorId());
        distOrderDomainService.editDistOrderSite(distOrderEntity, midSiteId);
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Boolean> isAutoSubmitDistOrder(DistBlackConfigDTO distBlackConfigDTO) {
        DistOrderSourceEnum source = DistOrderSourceEnum.getDistOrderSourceByCode(distBlackConfigDTO.getSource());
        if (!DistOrderSourceEnum.isAuto(source)) {
            return TmsResult.success(Boolean.FALSE);
        }
        SiteDTO beginSite = distBlackConfigDTO.getBeginSite();
        SiteDTO endSite = distBlackConfigDTO.getEndSite();
        SiteEntity beginSiteEntity = siteRepository.query(SiteQuery.builder().outBusinessNo(beginSite.getOutBusinessNo()).type(beginSite.getType()).build());
        if (beginSiteEntity == null) {
            throw new TmsRuntimeException("起始点位不存在");
        }
        SiteEntity endSiteEntity = siteRepository.query(SiteQuery.builder().outBusinessNo(endSite.getOutBusinessNo()).type(endSite.getType()).build());
        if (endSiteEntity == null) {
            throw new TmsRuntimeException("终点点位不存在");
        }
        Boolean isConfigured = distOrderDomainService.queryIfConfigured(beginSiteEntity.getId(), endSiteEntity.getId(), DistConfigTypeEnum.BLACK_LIST);
        return TmsResult.success(!isConfigured);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Void> deliveryOrderSaveByDist(List<DeliveryOrderSaveByDistCommand> deliveryOrderSaveByDistCommandList) {
        for (DeliveryOrderSaveByDistCommand deliveryOrderSaveByDistCommand : deliveryOrderSaveByDistCommandList) {
            TmsAssert.notNull(deliveryOrderSaveByDistCommand.getDistOrderId(), ErrorCodeEnum.PARAM_NOT_NULL, "DistOrderId");
            TmsAssert.notNull(deliveryOrderSaveByDistCommand.getBeginSiteId(), ErrorCodeEnum.PARAM_NOT_NULL, "BeginSiteId");
            TmsAssert.notNull(deliveryOrderSaveByDistCommand.getEndSiteId(), ErrorCodeEnum.PARAM_NOT_NULL, "EndSiteId");
            TmsAssert.notNull(deliveryOrderSaveByDistCommand.getBatchList(), ErrorCodeEnum.PARAM_NOT_NULL, "BatchList");
        }
        List<Long> disOrderIdList = deliveryOrderSaveByDistCommandList.stream()
                .map(DeliveryOrderSaveByDistCommand::getDistOrderId)
                .distinct()
                .collect(Collectors.toList());
        for (Long distOrderId : disOrderIdList) {
            deliveryOrderDomainService.reset4DistOrder(distOrderId);
        }
        List<Long> matchBatchIds = this.getBatchLoadRatioChange(deliveryOrderSaveByDistCommandList);
        deliveryOrderSaveByDistCommandList.forEach(this::deliveryOrderSaveByDist);
        //触发装载率计算
        deliveryBatchDomainService.calcBatchLoadRatioByDistChange(matchBatchIds);
        // 干线转运配送中消息发送
        distOrderStatusChangeMsgSend.trunkTransportationDeliveryInProgressMessageSend(disOrderIdList);
        return TmsResult.VOID_SUCCESS;
    }

    private List<Long> getBatchLoadRatioChange(List<DeliveryOrderSaveByDistCommand> deliveryOrderSaveByDistCommandList) {
        List<Long> allDiffBatchIds = new ArrayList<>();
        for (DeliveryOrderSaveByDistCommand deliveryOrderSaveByDistCommand : deliveryOrderSaveByDistCommandList) {
            List<DeliveryBatchDTO> batchList = deliveryOrderSaveByDistCommand.getBatchList();
            if(CollectionUtils.isEmpty(batchList)){
                continue;
            }
            List<Long> newBatchIds = batchList.stream().map(DeliveryBatchDTO::getDeliveryBatchId).collect(Collectors.toList());
            //查询当前已绑定的批次ID
            List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                    .distOrderId(deliveryOrderSaveByDistCommand.getDistOrderId())
                    .beginSiteId(deliveryOrderSaveByDistCommand.getBeginSiteId())
                    .endSiteId(deliveryOrderSaveByDistCommand.getEndSiteId())
                    .neState(DeliveryOrderStatusEnum.CLOSE.getCode()).build());
            List<Long> existedBatchIds = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDeliveryBatchId).filter(Objects::nonNull).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEqualCollection(newBatchIds, existedBatchIds)){
                continue;
            }
            Collection<Long> diffBatchIds = org.apache.commons.collections4.CollectionUtils.subtract(newBatchIds, existedBatchIds);
            diffBatchIds.addAll(org.apache.commons.collections4.CollectionUtils.subtract(existedBatchIds, newBatchIds));
            allDiffBatchIds.addAll(diffBatchIds);

        }
        return allDiffBatchIds;
    }

    private void deliveryOrderSaveByDist(DeliveryOrderSaveByDistCommand deliveryOrderSaveByDistCommand) {
        DistOrderEntity distOrderEntity = distOrderRepository.queryDetail(deliveryOrderSaveByDistCommand.getDistOrderId());
        TmsAssert.notNull(distOrderEntity, ErrorCodeEnum.DB_DATA_ERROR, "委托单为空");

        Set<DeliveryBatchStatusEnum> statusActSet = Sets.newHashSet(
                DeliveryBatchStatusEnum.TO_BE_PICKED
                , DeliveryBatchStatusEnum.IN_DELIVERY
                , DeliveryBatchStatusEnum.COMPLETE_DELIVERY);
        if(deliveryOrderSaveByDistCommand.getFulfillmentDeliveryWay() != null && DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode().equals(deliveryOrderSaveByDistCommand.getFulfillmentDeliveryWay()) ){
            statusActSet = Sets.newHashSet(
                    DeliveryBatchStatusEnum.TO_BE_WIRED
                    , DeliveryBatchStatusEnum.TO_BE_PICKED
                    , DeliveryBatchStatusEnum.IN_DELIVERY);
        }
        for (DeliveryBatchDTO deliveryBatchDTO : deliveryOrderSaveByDistCommand.getBatchList()) {
            DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchDTO.getDeliveryBatchId());
            TmsAssert.isTrue(statusActSet.contains(deliveryBatchEntity.getStatus()), ErrorCodeEnum.BATCH_BIND_ERROR,
                    deliveryBatchEntity.getStatus().getTrunkName());

            DeliveryOrderEntity deliveryOrderEntity = new DeliveryOrderEntity();
            List<DeliveryOrderEntity> currentDistDeliveryOrders = distOrderEntity.getDeliveryOrders();
            if (!CollectionUtils.isEmpty(currentDistDeliveryOrders)) {
                List<DeliveryOrderEntity> deliveryOrderNoBatchIds = currentDistDeliveryOrders.stream().filter(e -> e.getDeliveryBatchId() == null).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(deliveryOrderNoBatchIds)) {
                    deliveryOrderEntity.setId(deliveryOrderNoBatchIds.get(0).getId());
                }
            }
            deliveryOrderEntity.setDistOrderId(deliveryOrderSaveByDistCommand.getDistOrderId());
            deliveryOrderEntity.setDeliveryBatchId(deliveryBatchDTO.getDeliveryBatchId());
            deliveryOrderEntity.setStatus(DeliveryOrderStatusEnum.NO_SIGN);
            deliveryOrderEntity.setOuterOrderId(distOrderEntity.getDistClientVO().getOutOrderId());
            deliveryOrderEntity.setOuterClientId(distOrderEntity.getDistClientVO().getOutClientId());
            deliveryOrderEntity.setOuterClientName(distOrderEntity.getDistClientVO().getOutClientName());
            deliveryOrderEntity.setOuterContactId(distOrderEntity.getDistClientVO().getOutContactId());
            deliveryOrderEntity.setBeginSiteId(deliveryOrderSaveByDistCommand.getBeginSiteId());
            deliveryOrderEntity.setEndSiteId(deliveryOrderSaveByDistCommand.getEndSiteId());
            deliveryOrderEntity.setSource(distOrderEntity.getSource());
            deliveryOrderEntity.initSiteType(distOrderEntity.getMidSite() == null ? null : distOrderEntity.getMidSite().getId());
            deliveryOrderEntity.setDeliveryTime(distOrderEntity.getDistFlowVO().getExpectBeginTime());
            deliveryOrderEntity.setType(DeliveryOrderTypeEnum.send.getCode());
            deliveryOrderDomainService.create(deliveryOrderEntity);
        }
    }

    @Override
    public TmsResult<Void> configInit(List<String> configs) {
        distConfigRepository.configInit(configs);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 完成委托单
     *
     * @param distId 委托单ID
     */
    @Override
    public void completeDistOrder(Long distId) {
        log.info("completeDistOrder-distId:{}", distId);
        DistOrderEntity distOrderEntity = distOrderDomainService.queryDistOrderById(distId);
        if (distOrderEntity.isComplete()) {
            log.info("distId:{},调度域重复通知,委托单已完成", distOrderEntity.getDistId());
            return;
        }
        //查询关联调度单是否完成
        Boolean isBatchFinish = deliveryBatchDomainService.queryBatchFinishByDistId(distId);
        if (!isBatchFinish) {
            log.info("distId:{},关联调度单未完成", distOrderEntity.getDistId());
            return;
        }
        //更新委托单信息
        distOrderEntity.complete();
        distOrderRepository.update(distOrderEntity);

        // 干线转运组装配送完成消息发送给OFC
        distOrderStatusChangeMsgSend.trunkTransportationFinishDeliveryMessageSend(distId);
    }

    /**
     * 获取操作人
     *
     * @return 操作人
     */
    private String getOperator() {
        return getOperator(null);
    }

    /**
     * 获取操作人ID
     *
     * @return 操作人ID
     */
    private String getOperatorId() {
        return getOperatorId(null);
    }

    /**
     * 获取操作人
     *
     * @param operator 操作人
     * @return 操作人
     */
    private String getOperator(String operator) {
        return StrUtil.isBlank(operator) ? String.valueOf(authExtService.getCurrentUserName()) : operator;
    }

    /**
     * 获取操作人ID
     *
     * @param operatorId 操作人ID
     * @return 操作人ID
     */
    private String getOperatorId(String operatorId) {
        return StrUtil.isBlank(operatorId) ? String.valueOf(authExtService.getCurrentUserId()) : operatorId;
    }

    @Override
    public TmsResult<List<DistOrderInterceptDTO>> queryInterceptSiteDistOrder(DistOrderQuery distOrderQuery) {
        TmsAssert.notNull(distOrderQuery.getSource(), ErrorCodeEnum.PARAM_NOT_NULL, "source");
        TmsAssert.notEmpty(distOrderQuery.getOuterOrderIds(), ErrorCodeEnum.PARAM_NOT_NULL, "outerOrderIds");
        if (StringUtils.isNotBlank(distOrderQuery.getOuterContactId())) {
            TmsAssert.notNull(distOrderQuery.getDeliveryTime(), ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        }
        if (Objects.equals(distOrderQuery.getSource(), DistOrderSourceEnum.XM_MALL.getCode())) {
            distOrderQuery.setSource(null);
            distOrderQuery.setSources(Arrays.asList(
                    DistOrderSourceEnum.XM_MALL.getCode(),
                    DistOrderSourceEnum.XM_MALL_TIMING.getCode(),
                    DistOrderSourceEnum.XM_AFTER_SALE.getCode(),
                    DistOrderSourceEnum.XM_SAMPLE_APPLY.getCode()
            ));
        }
        //查询订单信息
        List<DistOrderEntity> distOrderEntityList = distOrderRepository.queryList(distOrderQuery);
        //查询不到提示
        if (CollectionUtils.isEmpty(distOrderEntityList)) {
            return TmsResult.fail(ErrorCodeEnum.INTERCEPT_ERROR.code, "查询不到订单信息");
        }
        //返回数据
        ArrayList<DistOrderInterceptDTO> distOrderInterceptDTOS = new ArrayList<>();
        //不是精准的拦截，批量拦截，省心送只退当天
        if (StringUtils.isBlank(distOrderQuery.getOuterContactId())) {
            //省心送需要拦截的订单
            List<DistOrderEntity> noHeartList = distOrderEntityList.stream()
                    .filter(distOrderEntity ->
                            Objects.equals(distOrderEntity.getSource().getCode(), DistOrderSourceEnum.XM_MALL_TIMING.getCode()))
                    .filter(distOrderEntity -> LocalDate.now().isEqual(distOrderEntity.getDistFlowVO().getExpectBeginTime().toLocalDate()))
                    .collect(Collectors.toList());

            List<DistOrderEntity> otherOrderList = distOrderEntityList.stream()
                    .filter(distOrderEntity -> !Objects.equals(distOrderEntity.getSource().getCode(), DistOrderSourceEnum.XM_MALL_TIMING.getCode()))
                    .collect(Collectors.toList());

            distOrderEntityList.clear();
            distOrderEntityList.addAll(noHeartList);
            distOrderEntityList.addAll(otherOrderList);
        }
        //符合被拦截的订单
        List<DistOrderEntity> couldInterceptOrderList = distOrderEntityList.stream().filter(DistOrderEntity -> DistOrderStatusEnum.validNotCompleteStatus().contains(DistOrderEntity.getStatus().getCode())).collect(Collectors.toList());

        //拦截订单集合
        List<DistOrderEntity> interOrderList = new ArrayList<>();
        interOrderList.addAll(distOrderEntityList);

        if (distOrderQuery.isCancelFlag()) {
            for (DistOrderEntity distOrderEntity : couldInterceptOrderList) {
                //查询当前点位、时间的其他的有效的补发单和样品单
                DistOrderQuery otherDistOrderQuery = DistOrderQuery.builder()
                        .beginSiteId(distOrderEntity.getBeginSite().getId())
                        .endSiteId(distOrderEntity.getEndSite().getId())
                        .deliveryTime(distOrderEntity.getDistFlowVO().getExpectBeginTime().toLocalDate())
                        .sources(Arrays.asList(DistOrderSourceEnum.XM_AFTER_SALE.getCode(), DistOrderSourceEnum.XM_SAMPLE_APPLY.getCode()))
                        .type(DistTypeEnum.DELIVERY.getCode())
                        .status(DistOrderStatusEnum.validNotCompleteStatus())
                        .storeNo(distOrderQuery.getStoreNo())
                        .build();
                interOrderList.addAll(distOrderRepository.queryList(otherDistOrderQuery));
            }
        }


        //可以拦截的
        for (DistOrderEntity distOrderEntity : interOrderList) {
            DistOrderInterceptDTO distOrderInterceptDTO = new DistOrderInterceptDTO();

            distOrderInterceptDTO.setOutOrderId(distOrderEntity.getDistClientVO().getOutOrderId());
            distOrderInterceptDTO.setSource(distOrderEntity.getSource());
            distOrderInterceptDTO.setDeliveryTime(distOrderEntity.getDistFlowVO().getExpectBeginTime().toLocalDate());
            distOrderInterceptDTO.setState(DistOrderStatusEnum.validNotCompleteStatus().contains(distOrderEntity.getStatus().getCode()) ? 0 : 1);
            distOrderInterceptDTO.setFailedReason(DistOrderStatusEnum.validNotCompleteStatus().contains(distOrderEntity.getStatus().getCode()) ? null : "订单已完成配送不能拦截");
            if (distOrderEntity.getCancelType().getCode() != DistOrderCancelTypeEnum.normal.getCode()) {
                distOrderInterceptDTO.setState(1);
                distOrderInterceptDTO.setFailedReason("已拦截过不能拦截");
            }
            distOrderInterceptDTO.setOuterContactId(distOrderEntity.getDistClientVO().getOutContactId());

            distOrderInterceptDTOS.add(distOrderInterceptDTO);
        }

        List<String> outerOrderIds = distOrderQuery.getOuterOrderIds();
        List<String> cloudInterceptOrderIds = distOrderInterceptDTOS.stream().map(DistOrderInterceptDTO::getOutOrderId).collect(Collectors.toList());
        for (String outerOrderId : outerOrderIds) {
            if (!cloudInterceptOrderIds.contains(outerOrderId)) {
                DistOrderInterceptDTO distOrderInterceptDTO = new DistOrderInterceptDTO();

                distOrderInterceptDTO.setOutOrderId(outerOrderId);
                distOrderInterceptDTO.setState(1);
                distOrderInterceptDTO.setFailedReason("省心送订单非当天或者未查询到此订单信息");

                distOrderInterceptDTOS.add(distOrderInterceptDTO);
            }
            ;
        }

        return TmsResult.success(distOrderInterceptDTOS.stream().distinct().collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<DistOrderInterceptDTO> interceptDistOrder(List<DistOrderInterceptDTO> distOrderInterceptDTOList) {
        TmsAssert.notEmpty(distOrderInterceptDTOList, ErrorCodeEnum.PARAM_NOT_NULL, "distOrderInterceptDTOList");
        List<DistOrderEntity> needInterceptDistOrders = distOrderInterceptDTOList.stream().map(DistOrderConverter::interceptDto2entity).distinct().collect(Collectors.toList());

        List<DistOrderInterceptDTO> distOrderInterceptDTOS = new ArrayList<>();

        ArrayList<Long> distIdList = new ArrayList<>();
        ArrayList<TmsDeliveryEvent> tmsDeliveryEvents = new ArrayList<TmsDeliveryEvent>();
        ArrayList<StockEvent> stockEvents = new ArrayList<StockEvent>();

        for (DistOrderEntity distOrderEntity : needInterceptDistOrders) {
            DistOrderQuery distOrderQuery = DistOrderQuery.builder()
                    .outerOrderId(distOrderEntity.getDistClientVO().getOutOrderId())
                    .deliveryTime(distOrderEntity.getDistFlowVO().getExpectBeginTime().toLocalDate())
                    .outerContactId(distOrderEntity.getDistClientVO().getOutContactId()).build();
            DistOrderEntity distOrder = distOrderRepository.queryWithItemWithBeginSite(distOrderQuery);
            DistOrderInterceptDTO distOrderInterceptDTO = DistOrderConverter.entity2InterceptDto(distOrderEntity);
            distOrderInterceptDTO.setDistId(distOrder.getDistId());
            if(Objects.equals(distOrder.getFulfillmentDeliveryWay(),DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode())){
                distOrderInterceptDTO.setState(InterceptStateEnum.NO_INTERCEPT.getCode());
                distOrderInterceptDTO.setFailedReason("干线配送不能拦截");
                distOrderInterceptDTOS.add(distOrderInterceptDTO);
                continue;
            }
            if(!DistOrderStatusEnum.validNotCompleteStatus().contains(distOrder.getStatus().getCode()) ||
                    distOrder.getCancelType().getCode() != DistOrderCancelTypeEnum.normal.getCode()){
                distOrderInterceptDTO.setState(InterceptStateEnum.NO_INTERCEPT.getCode());
                distOrderInterceptDTO.setFailedReason("订单已完成配送不能拦截");
                if (distOrder.getCancelType().getCode() != DistOrderCancelTypeEnum.normal.getCode()) {
                    distOrderInterceptDTO.setState(InterceptStateEnum.NO_INTERCEPT.getCode());
                    distOrderInterceptDTO.setFailedReason("已拦截过不能拦截");
                }
                if (distOrder.getDistFlowVO().getType() != DistTypeEnum.DELIVERY.getCode()) {
                    distOrderInterceptDTO.setState(InterceptStateEnum.NO_INTERCEPT.getCode());
                    distOrderInterceptDTO.setFailedReason("存在回收或者换货不能拦截");
                }
                distOrderInterceptDTOS.add(distOrderInterceptDTO);
                continue;
            }

            List<DeliveryOrderEntity> deliveryOrders = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().distIdList(Collections.singletonList(distOrder.getDistId())).build());
            distOrder.setDeliveryOrders(deliveryOrders);
            List<DistOrderEntity> distOrderEntities = Collections.singletonList(distOrder);
            // 完成排线的锁检查
            distOrdersBatchInterceptHandler.completePathRedisLockCheck(distOrderEntities);

            // 相关数据查询组装
            List<DistOrderEntity> notHaveCompletedDistOrderList = new ArrayList<>();
            List<DistOrderEntity> haveCompletedCouldInterceptDistOrderList = new ArrayList<>();
            List<DeliveryOrderEntity> haveCompletedCouldInterceptDeliveryOrderEntityList= new ArrayList<>();
            List<DeliveryPickEntity> deliveryPickEntities= new ArrayList<>();
            List<DeliverySiteEntity> deliverySiteEntities= new ArrayList<>();
            List<DeliveryBatchEntity> batchEntities= new ArrayList<>();

            distOrdersBatchInterceptHandler.queryInstallDeliveryData(distOrderEntities,
                    notHaveCompletedDistOrderList,
                    haveCompletedCouldInterceptDistOrderList,
                    haveCompletedCouldInterceptDeliveryOrderEntityList,
                    deliveryPickEntities,
                    deliverySiteEntities,
                    batchEntities);

            List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryListWithItem(DeliveryOrderQuery.builder().distOrderId(distOrder.getDistId()).build());
            //批量拦截
            distOrdersBatchInterceptHandler.batchInterceptToDelayedDelivery(notHaveCompletedDistOrderList,
                    haveCompletedCouldInterceptDistOrderList,
                    haveCompletedCouldInterceptDeliveryOrderEntityList,
                    deliveryPickEntities,
                    deliverySiteEntities,
                    batchEntities
            );

            //生成拦截入库
            StockEvent stockEvent = StockEventConverter.distOrderEntity2Event(distOrder);
            stockEvents.add(stockEvent);
            //消息通知给商城
            SiteEntity citySiteEntity = siteDomainService.query(distOrder.getBeginSite().getId());

            DeliverySiteEntity sendDeliverySiteEntity = null;
            if(!CollectionUtils.isEmpty(deliverySiteEntities)){
                sendDeliverySiteEntity = deliverySiteEntities.get(0);
            }

            //通知商城侧
            TmsDeliveryEvent tmsDeliveryEvent = TmsDeliveryEventConverter.buildTmsDeliveryEvent(EventTypeEnum.INTERCEPT_DELIVERY,
                    null, sendDeliverySiteEntity, deliveryOrderEntityList, Collections.singletonList(distOrder), citySiteEntity);

            tmsDeliveryEvents.add(tmsDeliveryEvent);
            distIdList.add(distOrder.getDistId());
            distOrderInterceptDTOS.add(distOrderInterceptDTO);
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                for (TmsDeliveryEvent tmsDeliveryEvent : tmsDeliveryEvents) {
                    eventBusService.notifyDeliveryEvent(tmsDeliveryEvent);
                }
                for (StockEvent stockEvent : stockEvents) {
                    eventBusService.notifyWms(stockEvent);
                }
            }
        });

        log.info("调用订单拦截成功");
        return distOrderInterceptDTOS;

    }

    @Override
    public TmsResult<Void> configInitGray(List<String> grayStoreNos, List<String> completeStoreNos) {
        Long count = distConfigRepository.queryCount(-1L, -1L, DistConfigTypeEnum.GRAY_CONFIG);
        if (count == 0) {
            //新增
            distConfigRepository.add(grayStoreNos, DistConfigTypeEnum.GRAY_CONFIG);
        } else {
            //更新
            distConfigRepository.update(grayStoreNos, DistConfigTypeEnum.GRAY_CONFIG);
        }

        Long completeCount = distConfigRepository.queryCount(-2L, -2L, DistConfigTypeEnum.COMPLETE_CHEAK);
        if (completeCount == 0) {
            //新增
            distConfigRepository.add(completeStoreNos, DistConfigTypeEnum.COMPLETE_CHEAK);
        } else {
            //更新
            distConfigRepository.update(completeStoreNos, DistConfigTypeEnum.COMPLETE_CHEAK);
        }

        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public List<DistOrderDTO> queryDistOrderList(DistOrderQuery distOrderQuery) {
        List<DistOrderEntity> distOrderEntityList = distOrderRepository.queryList(DistOrderQuery.builder()
                .outerOrderIds(distOrderQuery.getOuterOrderIds())
                .sources(distOrderQuery.getSources())
                .expectBeginTime(distOrderQuery.getExpectBeginTime())
                .build());

        List<String> outerContactIds = distOrderQuery.getOuterContactIds();
        List<DistOrderEntity> distOrderEntities = distOrderEntityList.stream()
                .filter(distOrderEntity -> outerContactIds.contains(distOrderEntity.getDistClientVO().getOutContactId()))
                .collect(Collectors.toList());
        return distOrderEntities.stream().map(DistOrderConverter::entity2Dto).collect(Collectors.toList());
    }


    @Override
    public DistOrderDTO queryLastDistOrder(DistOrderQuery distOrderQuery) {
        return DistOrderConverter.entity2Dto(distOrderRepository.queryLastWithDeliveryOrderForceMaster(distOrderQuery));
    }

    /*@PostConstruct
    public void setSiteDomainService(){
        DistOrderEntity distOrderEntity = distOrderRepository.queryLastWithDeliveryOrderForceMaster(DistOrderQuery.builder()
                .outerOrderId("02168613531327573")
                .expectBeginTime(LocalDate.now().atStartOfDay())
                .outerContactId("340414")
                .source(203)
                .build());
        System.out.println(distOrderEntity);
    }*/

    @Override
    public DistOrderDTO queryDistOrderWithBatchDeliverySite(DistOrderQuery distOrderQuery) {
        //查询委托单
        DistOrderEntity distOrderEntity = distOrderRepository.queryWithDeliveryOrderByUk(distOrderQuery.getOuterOrderId(), DistOrderSourceEnum.getDistOrderSourceByCode(distOrderQuery.getSource()), distOrderQuery.getExpectBeginTime(), distOrderQuery.getOuterContactId());
        if(distOrderEntity == null || distOrderEntity.getDistId() == null){
            return null;
        }
        DistOrderDTO distOrderDTO = DistOrderConverter.entity2Dto(distOrderEntity);
        if(CollectionUtils.isEmpty(distOrderEntity.getDeliveryOrders())){
            return distOrderDTO;
        }
        DeliveryOrderEntity deliveryOrderEntity = distOrderEntity.getDeliveryOrders().get(0);
        if(deliveryOrderEntity.getDeliveryBatchId() == null){
            return distOrderDTO;
        }
        //查询批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryWithSiteWithBase(DeliveryBatchQuery.builder().batchId(deliveryOrderEntity.getDeliveryBatchId()).build());

        deliveryBatchEntityList = deliveryBatchEntityList.stream().filter(batch -> batch.getId() != null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return distOrderDTO;
        }
        distOrderDTO.setDeliveryBatchList(deliveryBatchEntityList.stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList()));
        //过滤出订单点位信息
        if(!CollectionUtils.isEmpty(deliveryBatchEntityList.get(0).getDeliverySiteList())){
            DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryWithItemsWithRecycle(DeliverySiteQuery.builder()
                    .batchId(deliveryOrderEntity.getDeliveryBatchId())
                    .siteId(deliveryOrderEntity.getEndSiteId()).build());
//            List<DeliverySiteEntity> deliverySiteEntities = deliveryBatchEntityList.get(0).getDeliverySiteList().stream().filter(site -> Objects.equals(site.getSiteId(), distOrderDTO.getEndSiteId())).collect(Collectors.toList());
//            List<DeliverySiteDTO> deliverySiteDTOS = deliverySiteEntities.stream().map(DeliverySiteDTOConverter::entity2Dto).collect(Collectors.toList());
            distOrderDTO.setDeliverySiteList(Collections.singletonList(DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity)));
        }
        return distOrderDTO;
    }

    @Override
    public List<DistOrderDTO> batchQueryDistOrderList(DistOrderQuery distOrderQuery) {
        if (CollectionUtils.isEmpty(distOrderQuery.getOuterOrderIds())){
            return Collections.emptyList();
        }
        List<DistOrderEntity> distOrderEntityList = distOrderRepository.queryList(DistOrderQuery.builder()
                .outerOrderIds(distOrderQuery.getOuterOrderIds())
                .sources(distOrderQuery.getSources())
                .outerContactIds(distOrderQuery.getOuterContactIds())
                .build());
        if (CollectionUtils.isEmpty(distOrderEntityList)) {
            return Collections.emptyList();
        }
        return distOrderEntityList.stream().map(DistOrderConverter::entity2Dto).collect(Collectors.toList());
    }

    @Override
    public List<DistOrderOnlyCodeDTO> queryDistOrderOnlyCode(DistOrderOnlyCodeQuery distOrderOnlyCodeQuery) {
        log.info("查询单据条码接口，请求参数{}", JsonUtil.toJson(distOrderOnlyCodeQuery));
        DistOrderEntity distOrderEntity = distOrderRepository.queryByUk(distOrderOnlyCodeQuery.getOrderNo(),
                DistOrderSourceEnum.getDistOrderSourceByCode(distOrderOnlyCodeQuery.getSource()), distOrderOnlyCodeQuery.getExpectBeginTime(),
                distOrderOnlyCodeQuery.getContactId());
        log.info("委托单对象{}", JsonUtil.toJson(distOrderEntity));
        if (Objects.isNull(distOrderEntity)) {
            return null;
        }
        DeliveryOrderQuery query = new DeliveryOrderQuery();
        query.setDistOrderId(distOrderEntity.getDistId());
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(query);
        log.info("配送明细对象{}", JsonUtil.toJson(deliveryOrderEntities));
        if (CollectionUtils.isEmpty(deliveryOrderEntities)) {
            return null;
        }
        DeliveryOrderEntity deliveryOrderEntity = deliveryOrderEntities.get(0);
        if (Objects.isNull(deliveryOrderEntity.getDeliveryBatchId())) {
            return null;
        }
        DeliverySiteQuery deliverySiteQuery = new DeliverySiteQuery();
        deliverySiteQuery.setBatchId(deliveryOrderEntity.getDeliveryBatchId());
        deliverySiteQuery.setSiteId(distOrderEntity.getEndSite().getId());
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(deliverySiteQuery);
        log.info("配送点明细{}", JsonUtil.toJson(deliverySiteEntities));

        if (CollectionUtils.isEmpty(deliverySiteEntities)) {
            return null;
        }
        DeliverySiteItemQuery deliverySiteItemQuery = new DeliverySiteItemQuery();
        deliverySiteItemQuery.setDeliverySiteIds(deliverySiteEntities.stream().map(DeliverySiteEntity::getId).collect(Collectors.toList()));
        deliverySiteItemQuery.setType(DeliverySiteItemTypeEnum.DELIVERY.getCode());
        List<DeliverySiteItemEntity> deliverySiteItemEntities = deliverySiteItemRepository.queryWithCodeList(deliverySiteItemQuery);
        log.info("配送点货物明细{}", JsonUtil.toJson(deliverySiteItemEntities));

        if (CollectionUtils.isEmpty(deliverySiteItemEntities)) {
            return null;
        }
        List<DistOrderOnlyCodeDTO> onlyCodeList = Lists.newArrayList();
        List<DeliverySiteItemCodeEntity> deliverySiteItemCodeList = deliverySiteItemEntities.stream()
                .filter(a -> !CollectionUtils.isEmpty(a.getDeliverySiteItemCodeEntityList()))
                .flatMap(a -> a.getDeliverySiteItemCodeEntityList()
                        .stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deliverySiteItemCodeList)) {
            return null;
        }
        for (DeliverySiteItemCodeEntity deliverySiteItemCodeEntity : deliverySiteItemCodeList) {

            DistOrderOnlyCodeDTO distOrderOnlyCodeDTO = new DistOrderOnlyCodeDTO();
            if (StringUtils.isNotBlank(deliverySiteItemCodeEntity.getOnlyCode())) {
                distOrderOnlyCodeDTO.setOnlyCode(deliverySiteItemCodeEntity.getOnlyCode().split("S")[0] + "S");
            }
            distOrderOnlyCodeDTO.setOrderNo(distOrderOnlyCodeQuery.getOrderNo());
            distOrderOnlyCodeDTO.setOutItemId(deliverySiteItemCodeEntity.getOutItemId());
            onlyCodeList.add(distOrderOnlyCodeDTO);
        }
        log.info("查询单据条码接口，返回结果{}", JsonUtil.toJson(onlyCodeList));

        return onlyCodeList;
    }

    @Override
    public List<DistOrderDTO> batchQueryDistOrderWithBatchDeliverySite(DistOrderQuery distOrderQuery) {
        if (CollectionUtils.isEmpty(distOrderQuery.getOuterOrderIds())){
            return Collections.emptyList();
        }
        // todo 兼容POP功能上线,数据刷完可以删除
        List<Integer> sources = distOrderQuery.getSources();
        sources.add(DistOrderSourceEnum.POP_MALL.getCode());
        sources.add(DistOrderSourceEnum.POP_AFTER_SALE.getCode());

        //查询委托单信息
        List<DistOrderEntity> distOrderEntityList = distOrderRepository.queryListWithDeliveryOrder(DistOrderQuery.builder()
                .outerOrderIds(distOrderQuery.getOuterOrderIds())
                .sources(distOrderQuery.getSources())
                .build());
        if (CollectionUtils.isEmpty(distOrderEntityList)) {
            return Collections.emptyList();
        }
        //根据点位分组查出委托单
        Map<String, List<DistOrderEntity>> distOrderMap = distOrderEntityList.stream().collect(Collectors.groupingBy(e -> e.getDistFlowVO().getExpectBeginTime() + "#" + e.getBeginSite().getId() + "#" + e.getEndSite().getId() ));
        //过滤出配送单
        List<DeliveryOrderEntity> deliveryOrderEntities = distOrderEntityList.stream().map(DistOrderEntity::getDeliveryOrders).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deliveryOrderEntities)) {
            return Collections.emptyList();
        }
        List<Long> batchIds = deliveryOrderEntities.stream().map(DeliveryOrderEntity::getDeliveryBatchId).distinct().collect(Collectors.toList());
        //查询配送批次信息
        List<DeliveryBatchEntity> deliveryBatchEntities = deliveryBatchRepository.queryWithSiteWithBase(DeliveryBatchQuery.builder().batchIds(batchIds).build());
        if (CollectionUtils.isEmpty(deliveryBatchEntities)) {
            return Collections.emptyList();
        }
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntities) {
            String batchFlag = deliveryBatchEntity.getDeliveryTime() + "#" + deliveryBatchEntity.getBeginSiteId();
            //设置配送时效
            List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
            for (DeliverySiteEntity deliverySiteEntity : deliverySiteList) {
                String siteFlag = batchFlag + "#" + deliverySiteEntity.getSiteId();
                deliverySiteEntity.setSiteDistOrders(distOrderMap.get(siteFlag));
            }
            deliveryAlertDomainService.installDeliveryAlertTimeFrameList(deliverySiteList,deliveryBatchEntity.getBeginSiteId());
        }
        Map<Long, DeliveryBatchEntity> batchMap = deliveryBatchEntities.stream().collect(Collectors.toMap(DeliveryBatchEntity::getId, Function.identity()));
        List<DistOrderDTO> distOrderDTOList = new ArrayList<>();
        for (DistOrderEntity distOrderEntity : distOrderEntityList) {
            DistOrderDTO distOrderDTO = DistOrderConverter.entity2Dto(distOrderEntity);
            distOrderDTOList.add(distOrderDTO);
            //获取配送单信息
            List<DeliveryOrderEntity> deliveryOrders = distOrderEntity.getDeliveryOrders();
            if (CollectionUtils.isEmpty(deliveryOrders)){
                continue;
            }
            Long deliveryBatchId = deliveryOrders.get(0).getDeliveryBatchId();
            DeliveryBatchEntity deliveryBatchEntity = batchMap.get(deliveryBatchId);
            if (deliveryBatchEntity == null){
                continue;
            }
            distOrderDTO.setDeliveryBatchList(Collections.singletonList(DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity)));
            Optional<DeliverySiteEntity> first = deliveryBatchEntity.getDeliverySiteList().stream().filter(site -> Objects.equals(site.getSiteId(), distOrderDTO.getEndSiteId())).findFirst();
            if (!first.isPresent()){
                continue;
            }
            distOrderDTO.setDeliverySiteList(Collections.singletonList(DeliverySiteDTOConverter.entity2Dto(first.get())));
        }
        //查询配送时效信息
        return distOrderDTOList;
    }

    @Override
    public List<DistOrderDTO> querySiteDistOrderList(DistOrderQuery distOrderQuery) {
        DistOrderSourceEnum source = DistOrderSourceEnum.getDistOrderSourceByCode(distOrderQuery.getSource());
        DistOrderEntity distOrderEntity = distOrderRepository.queryByUk(distOrderQuery.getOuterOrderId(),
                source, distOrderQuery.getExpectBeginTime(),
                distOrderQuery.getOuterContactId());
        if (distOrderEntity == null){
            throw new TmsRuntimeException("委托单不存在");
        }
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder()
                .expectBeginTime(distOrderEntity.getDistFlowVO().getExpectBeginTime())
                .beginSiteId(distOrderEntity.getBeginSite().getId())
                .endSiteId(distOrderEntity.getEndSite().getId())
                .build());
        return distOrderEntities.stream().map(DistOrderConverter::entity2Dto).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> outerCreateDistOrderValidate(List<DistOrderCreateMessage> msgs) {
        Map<String,String> resultInfoMap = new HashMap<>();

        for (DistOrderCreateMessage msg : msgs) {
            try {
                distOrderValidator.validateDistOrderCreate(msg);
                resultInfoMap.put(msg.getOuterId(),null);
            } catch (Exception e) {
                if(e instanceof TmsRuntimeException){
                    resultInfoMap.put(msg.getOuterId(),e.getMessage());
                }else{
                    log.error("outerCreateDistOrderValidate error",e);
                    resultInfoMap.put(msg.getOuterId(),e.getMessage());
                }
            }
        }
        //过滤干配类型的外单
        List<DistOrderCreateMessage> trunkCityDistCreateMsgs = msgs.stream()
                .filter(msg -> Objects.equals(DistOrderSourceEnum.OUTER_TRUNK_CITY.getCode(), msg.getSource()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(trunkCityDistCreateMsgs)){
            return resultInfoMap;
        }
        //外单干配校验
        for (DistOrderCreateMessage trunkCityDistCreateMsg : trunkCityDistCreateMsgs) {
            try {
                DistSiteMessage endSite = trunkCityDistCreateMsg.getEndSite();
                String outerTenantId = trunkCityDistCreateMsg.getOuterTenantId();
                LocalDateTime expectBeginTime = trunkCityDistCreateMsg.getExpectBeginTime();
                //拆分路段 按照终点查询城配仓
                Integer storeNo = wncQueryFacade.queryStoreNo(QueryStoreNoByAddressInput.builder().city(endSite.getCity())
                        .area(endSite.getArea())
                        .poi(endSite.getPoi())
                        .contactId(trunkCityDistCreateMsg.getOuterClientId())
                        .tenantId(outerTenantId).build());
                if(storeNo == null){
                    throw new TmsRuntimeException("外单城配仓不存在");
                }
                //查询城配仓是否已经完成排线
                deliveryBatchDomainService.storeNoCompletePathValidate(storeNo,expectBeginTime);
            } catch (Exception e) {
                if(e instanceof TmsRuntimeException){
                    resultInfoMap.put(trunkCityDistCreateMsg.getOuterId(),e.getMessage());
                }else{
                    log.error("outerCancelDistOrderValidate error",e);
                    resultInfoMap.put(trunkCityDistCreateMsg.getOuterId(),e.getMessage());
                }
            }
        }

        return resultInfoMap;
    }

    @Override
    public Map<String, String> outerCancelDistOrderValidate(List<DistOrderCancelMessage> msgs) {
        Map<String,String> resultInfoMap = new HashMap<>();

        for (DistOrderCancelMessage msg : msgs) {
            String outerOrderId = msg.getOuterOrderId();
            Integer sourceParam = msg.getSource();
            LocalDateTime expectBeginTime = msg.getExpectBeginTime();
            String outerContactId = msg.getOuterContactId();

            try {
                distOrderValidator.validateDistOrderUk(outerOrderId, sourceParam, expectBeginTime, outerContactId);
                distOrderValidator.validateOperator(msg.getUpdaterId(), msg.getUpdater());
                resultInfoMap.put(msg.getOuterId(),null);
            } catch (Exception e) {
                if(e instanceof TmsRuntimeException){
                    resultInfoMap.put(msg.getOuterId(),e.getMessage());
                }else{
                    log.error("outerCancelDistOrderValidate error",e);
                    resultInfoMap.put(msg.getOuterId(),e.getMessage());
                }
            }
        }
        List<String> outerOrderIds = msgs.stream()
                .map(DistOrderCancelMessage::getOuterOrderId)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(outerOrderIds)){
            return resultInfoMap;
        }
        Map<String, String> outerOrderId2OutIdMap = msgs.stream().collect(Collectors.toMap(DistOrderCancelMessage::getOuterOrderId, DistOrderCancelMessage::getOuterId));
        //校验
        HashMap<String, String> outerOrderId2MsgMap = distOrderDomainService.outerCancelDistOrderValidate(outerOrderIds);
        for (String outerOrderId : outerOrderId2MsgMap.keySet()) {
            resultInfoMap.put(outerOrderId2OutIdMap.get(outerOrderId),outerOrderId2MsgMap.get(outerOrderId));
        }
        return resultInfoMap;
    }

    @Override
    public void finishDistOrder(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        DistOrderEntity distOrderEntity = distOrderRepository.queryByUk(outOrderId, source, expectBeginTime, outerContactId);
        if (null == distOrderEntity) {
            log.info("完结委托单失败,distOrder not found,outOrderId:{},source:{},expectBeginTime:{},outerContactId:{}", outOrderId, source, expectBeginTime, outerContactId);
            return;
        }
        distOrderDomainService.finishDelivery(distOrderEntity.getDistId());
    }

    @Override
    public List<DistOrderDTO> queryOrderNoDeliveryInfo(List<String> orderNos, LocalDate deliveryTime) {
        if(CollectionUtils.isEmpty(orderNos) || deliveryTime == null){
            return Collections.emptyList();
        }
        // 委托单集合
        List<DistOrderEntity> distOrderEntityList = distOrderRepository.queryListWithDeliveryOrder(DistOrderQuery.builder()
                .outerOrderIds(orderNos)
                .expectBeginTime(deliveryTime.atStartOfDay())
                .stateList(Arrays.asList(DistOrderStatusEnum.TO_BE_PICKED.getCode(),
                        DistOrderStatusEnum.IN_DELIVERY.getCode(),
                        DistOrderStatusEnum.CANCEL_AFTER_WIRED.getCode(),
                        DistOrderStatusEnum.COMPLETE_DELIVERY.getCode()))
                .build());

        if(CollectionUtils.isEmpty(distOrderEntityList)){
            return Collections.emptyList();
        }

        List<DistOrderDTO> distOrderDTOS = distOrderEntityList.stream().map(DistOrderConverter::entity2Dto).collect(Collectors.toList());

        // 配送单集合
        List<DeliveryOrderEntity> deliveryOrderEntityList = distOrderEntityList.stream()
                .map(DistOrderEntity::getDeliveryOrders)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
            return distOrderDTOS;
        }

        // 配送批次集合
        List<Long> deliveryBatchIds = deliveryOrderEntityList.stream()
                .map(DeliveryOrderEntity::getDeliveryBatchId)
                .distinct()
                .collect(Collectors.toList());

        // 客户点位ID集合
        List<Long> endSiteIdList = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getEndSiteId).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(deliveryBatchIds) || CollectionUtils.isEmpty(endSiteIdList)){
            return distOrderDTOS;
        }

        // 查询批次信息
        List<DeliveryBatchEntity> deliveryBatchList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .batchIds(deliveryBatchIds)
                .build());
        Map<Long, DeliveryBatchEntity> batchId2DeliveryBatchMap = deliveryBatchList.stream().collect(Collectors.toMap(DeliveryBatchEntity::getId, Function.identity()));

        // 查询点位配送详情
        List<DeliverySiteEntity> deliverySiteDeliveryDetailList = deliverySiteRepository.queryListWithItems(DeliverySiteQuery.builder()
                .batchIdList(deliveryBatchIds)
                .siteIds(endSiteIdList)
                .build());

        Map<String, List<DeliverySiteEntity>> batchIdSiteId2DeliverySiteListMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(deliverySiteDeliveryDetailList)){
            batchIdSiteId2DeliverySiteListMap = deliverySiteDeliveryDetailList.stream()
                    .collect(Collectors.groupingBy(deliverySite -> deliverySite.getDeliveryBatchId() + "#" + deliverySite.getSiteId()));
        }

        // 组装信息
        for (DistOrderDTO distOrderDTO : distOrderDTOS) {
            List<DeliveryOrderDTO> deliveryOrders = distOrderDTO.getDeliveryOrderList();
            if(CollectionUtils.isEmpty(deliveryOrders)){
                continue;
            }
            DeliveryOrderDTO deliveryOrderDTO = deliveryOrders.get(0);
            Long deliveryBatchId = deliveryOrderDTO.getDeliveryBatchId();
            Long endSiteId = deliveryOrderDTO.getEndSiteId();

            DeliveryBatchEntity batch = batchId2DeliveryBatchMap.get(deliveryBatchId);
            if(batch == null){
                continue;
            }
            distOrderDTO.setDeliveryBatchList(Collections.singletonList(DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(batch)));


            List<DeliverySiteEntity> deliverySiteEntities = batchIdSiteId2DeliverySiteListMap.get(deliveryBatchId + "#" + endSiteId);
            if(CollectionUtils.isEmpty(deliverySiteEntities)){
                continue;
            }
            distOrderDTO.setDeliverySiteList(deliverySiteEntities.stream().map(DeliverySiteDTOConverter::entity2Dto).collect(Collectors.toList()));
        }

        return distOrderDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<DistOrderDTO> batchInterceptDistOrdersToDelayedDelivery(List<DistOrderEntity> distOrderEntities) {
        if(CollectionUtils.isEmpty(distOrderEntities)){
            return Collections.emptyList();
        }

        // 批量查询订单信息
        List<DistOrderEntity> dbDistOrderList = deliveryOrderDomainService.queryListByUK(distOrderEntities);
        if(CollectionUtils.isEmpty(dbDistOrderList)){
            return Collections.emptyList();
        }

        // 过滤掉配送方式是【干线转运】的承运单
        dbDistOrderList = dbDistOrderList.stream()
                .filter(e -> !Objects.equals(e.getFulfillmentDeliveryWay(),DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode()))
                .collect(Collectors.toList());

        // 完成排线的锁检查
        distOrdersBatchInterceptHandler.completePathRedisLockCheck(dbDistOrderList);

        // 相关数据查询组装
        List<DistOrderEntity> notHaveCompletedDistOrderList = new ArrayList<>();
        List<DistOrderEntity> haveCompletedCouldInterceptDistOrderList = new ArrayList<>();
        List<DeliveryOrderEntity> haveCompletedCouldInterceptDeliveryOrderEntityList= new ArrayList<>();
        List<DeliveryPickEntity> deliveryPickEntities= new ArrayList<>();
        List<DeliverySiteEntity> deliverySiteEntities= new ArrayList<>();
        List<DeliveryBatchEntity> batchEntities= new ArrayList<>();

        distOrdersBatchInterceptHandler.queryInstallDeliveryData(dbDistOrderList,
                notHaveCompletedDistOrderList,
                haveCompletedCouldInterceptDistOrderList,
                haveCompletedCouldInterceptDeliveryOrderEntityList,
                deliveryPickEntities,
                deliverySiteEntities,
                batchEntities);

        //批量拦截
        List<DistOrderEntity> successInterceptOrderList = distOrdersBatchInterceptHandler.batchInterceptToDelayedDelivery(notHaveCompletedDistOrderList,
                haveCompletedCouldInterceptDistOrderList,
                haveCompletedCouldInterceptDeliveryOrderEntityList,
                deliveryPickEntities,
                deliverySiteEntities,
                batchEntities
        );

        return successInterceptOrderList.stream().map(DistOrderConverter::entity2Dto).collect(Collectors.toList());
    }

    @Override
    public BatchChangeDistOrdersFulfillmentDeliveryWayDTO batchChangeDistOrdersFulfillmentDeliveryWay(List<BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput> inputs) {
        log.info("开始批量修改委托单履约配送方式，处理数量：{}", inputs.size());

        BatchChangeDistOrdersFulfillmentDeliveryWayDTO resp = new BatchChangeDistOrdersFulfillmentDeliveryWayDTO();
        List<BatchChangeDistOrdersFulfillmentDeliveryWayDTO.DistOrderChangeResult> successList = new ArrayList<>();
        List<BatchChangeDistOrdersFulfillmentDeliveryWayDTO.DistOrderChangeResult> failedList = new ArrayList<>();

        // 第一阶段：验证所有订单，收集需要修改的订单
        List<String> allChangeOutOrderIdList = inputs.stream().map(BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput::getOutOrderId).collect(Collectors.toList());
        Map<DistOrderEntity, Integer> validDistOrdersWithTargetWay = new HashMap<>();

        for (BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput item : inputs) {
            BatchChangeDistOrdersFulfillmentDeliveryWayDTO.DistOrderChangeResult result =
                    changeDistOrdersFulfillmentDeliveryWayService.processDistOrderFulfillmentDeliveryWayChange(item, allChangeOutOrderIdList);

            if (result.getResultState() == 0) {
                // 验证通过，收集到待处理列表
                DistOrderEntity distOrder = distOrderRepository.queryWithItemWithSiteByUk(
                        item.getOutOrderId(),
                        DistOrderSourceEnum.getDistOrderSourceByCode(item.getSource()),
                        item.getDeliveryTime().atStartOfDay(),
                        item.getOuterContactId()
                );
                if (distOrder != null) {
                    validDistOrdersWithTargetWay.put(distOrder, item.getTargetFulfillmentDeliveryWay());
                }
                successList.add(result);
            } else {
                failedList.add(result);
            }
        }

        // 第二阶段：批量执行取消和创建操作
        if (!validDistOrdersWithTargetWay.isEmpty()) {
            try {
                log.info("开始批量更新{}个通过验证的委托单", validDistOrdersWithTargetWay.size());
                changeDistOrdersFulfillmentDeliveryWayService.batchUpdateDistOrdersFulfillmentDeliveryWay(validDistOrdersWithTargetWay);
                log.info("批量更新委托单完成");
            } catch (Exception e) {
                log.error("批量更新委托单时发生异常", e);

                // 将所有成功的订单标记为失败
                for (BatchChangeDistOrdersFulfillmentDeliveryWayDTO.DistOrderChangeResult successResult : successList) {
                    successResult.setResultState(1);
                    successResult.setFailedReason("批量更新时发生异常：" + e.getMessage());
                    failedList.add(successResult);
                }
                successList.clear();
            }
        }

        resp.setSuccessList(successList);
        resp.setFailedList(failedList);
        resp.setTotalCount(inputs.size());
        resp.setSuccessCount(successList.size());
        resp.setFailedCount(failedList.size());

        log.info("批量修改委托单履约配送方式完成，总数：{}，成功：{}，失败：{}",
                resp.getTotalCount(), resp.getSuccessCount(), resp.getFailedCount());

        return resp;
    }
}
