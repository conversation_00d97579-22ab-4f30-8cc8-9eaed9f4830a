package net.summerfarm.tms.local.delivery.converter;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.event.EventTypeEnum;
import net.summerfarm.tms.event.TmsDeliveryEvent;
import net.summerfarm.tms.message.*;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class TmsDeliveryEventConverter {
    public static TmsDeliveryEvent buildTmsDeliveryEvent(EventTypeEnum eventTypeEnum,
                                                         DeliveryBatchEntity deliveryBatchEntity,
                                                         DeliverySiteEntity deliverySiteEntity,
                                                         List<DeliveryOrderEntity> deliveryOrderEntityList,
                                                         List<DistOrderEntity> distOrderEntityList, SiteEntity citySiteEntity) {
        TmsDeliveryEvent tmsDeliveryEvent = new TmsDeliveryEvent();
        tmsDeliveryEvent.setEventTypeDesc(eventTypeEnum.getName());
        tmsDeliveryEvent.setEventType(eventTypeEnum.getCode());

        deliveryOrderEntityList = Optional.ofNullable(deliveryOrderEntityList).orElse(new ArrayList<>());
        tmsDeliveryEvent.setDeliveryOrderMessages(deliveryOrderEntityList.stream()
                .map(TmsDeliveryEventConverter::deliveryOrder2Message)
                .collect(Collectors.toList()));
        tmsDeliveryEvent.setBatchMessage(deliveryBatch2Message(deliveryBatchEntity));
        tmsDeliveryEvent.setDeliverySiteMessage(deliverySite2Message(deliverySiteEntity,citySiteEntity));
        return tmsDeliveryEvent;
    }

    public static BatchMessage deliveryBatch2Message(DeliveryBatchEntity deliveryBatchEntity) {
        if(deliveryBatchEntity == null){
            return null;
        }
        BatchMessage batchMessage = new BatchMessage();
        batchMessage.setId(deliveryBatchEntity.getId());
        batchMessage.setStatus(deliveryBatchEntity.getStatus().getCode());
        batchMessage.setType(deliveryBatchEntity.getType());
        Optional<DriverEntity> driverEntityOptional = Optional.ofNullable(deliveryBatchEntity.getDriverEntity());
        batchMessage.setDriverName(driverEntityOptional.map(DriverEntity::getName).orElse(null));
        batchMessage.setDriverPhone(driverEntityOptional.map(DriverEntity::getPhone).orElse(null));

        Optional<CarrierEntity> carrierEntityOptional = Optional.ofNullable(deliveryBatchEntity.getCarrierEntity());
        batchMessage.setCarrierId(carrierEntityOptional.map(CarrierEntity::getId).orElse(null));
        batchMessage.setCarrierName(carrierEntityOptional.map(CarrierEntity::getCarrierName).orElse(null));

        Optional<CarEntity> carEntityOptional = Optional.ofNullable(deliveryBatchEntity.getCarEntity());
        batchMessage.setCarNumber(carEntityOptional.map(CarEntity::getCarNumber).orElse(null));
        batchMessage.setCarStorage(carEntityOptional.map(CarEntity::getCarStorageEnum).map(CarStorageEnum::getName).orElse(null));
        batchMessage.setCarType(carEntityOptional.map(CarEntity::getCarTypeEnum).map(CarTypeEnum::getDesc).orElse(null));
        batchMessage.setEstimateFare(deliveryBatchEntity.getEstimateFare());
        batchMessage.setBeginTime(deliveryBatchEntity.getBeginTime());
        batchMessage.setDeliveryTime(deliveryBatchEntity.getDeliveryTime());
        batchMessage.setTotalQuantity(deliveryBatchEntity.getTotalQuantity());
        batchMessage.setTotalVolume(deliveryBatchEntity.getTotalVolume());
        batchMessage.setTotalWeight(deliveryBatchEntity.getTotalWeight());
        batchMessage.setPlanTotalDistance(deliveryBatchEntity.getPlanTotalDistance());
        batchMessage.setRemark(deliveryBatchEntity.getRemark());
        batchMessage.setArea(deliveryBatchEntity.getArea());
        batchMessage.setClasses(deliveryBatchEntity.getClasses());
        batchMessage.setPathCode(deliveryBatchEntity.getPathCode());

        if (!CollectionUtils.isEmpty(deliveryBatchEntity.getDeliverySiteList())){
            List<DeliverySiteMessage> deliverySiteMessages = deliveryBatchEntity.getDeliverySiteList().stream().map(TmsDeliveryEventConverter::deliverySite2Message).collect(Collectors.toList());
            batchMessage.setDeliverySiteMessages(deliverySiteMessages);
        }
        return batchMessage;
    }

    public static DeliverySiteMessage deliverySite2Message(DeliverySiteEntity deliverySiteEntity) {
        if(deliverySiteEntity == null){
            return null;
        }
        DeliverySiteMessage deliverySiteMessage = new DeliverySiteMessage();
        deliverySiteMessage.setDistance(deliverySiteMessage.getDistance());
        deliverySiteMessage.setClientName(deliverySiteEntity.getOuterClientName());
        deliverySiteMessage.setId(deliverySiteEntity.getId());
        deliverySiteMessage.setDeliveryBatchId(deliverySiteEntity.getDeliveryBatchId());
        Optional<SiteEntity> siteOptional = Optional.ofNullable(deliverySiteEntity.getSiteEntity());
        deliverySiteMessage.setSiteName(siteOptional.map(SiteEntity::getSiteName).orElse(null));
        deliverySiteMessage.setSiteType(deliverySiteEntity.getType());
        deliverySiteMessage.setSequence(deliverySiteEntity.getSequence());
        deliverySiteMessage.setStatus(deliverySiteEntity.getStatus().getCode());
        deliverySiteMessage.setPlanArriveTime(deliverySiteEntity.getPlanArriveTime());
        deliverySiteMessage.setSignInTime(deliverySiteEntity.getSignInTime());
        deliverySiteMessage.setSignInPoi(deliverySiteEntity.getSignInPoi());
        deliverySiteMessage.setSignInDiffMinute(deliverySiteEntity.getSignInDiffMinute());
        deliverySiteMessage.setSignInDiffKm(deliverySiteEntity.getSignInDiffKm());
        deliverySiteMessage.setSignInPics(deliverySiteEntity.getSignInPics());
        deliverySiteMessage.setSignInRemark(deliverySiteEntity.getSignInRemark());
        deliverySiteMessage.setOutReason(deliverySiteEntity.getOutReason());
        deliverySiteMessage.setOutDistance(deliverySiteEntity.getOutDistance());
        deliverySiteMessage.setSignOutTime(deliverySiteEntity.getSignOutTime());
        deliverySiteMessage.setSignOutPics(deliverySiteEntity.getSignOutPics());
        deliverySiteMessage.setSignOutRemark(deliverySiteEntity.getSignOutRemark());
        deliverySiteMessage.setSignOutTemperature(deliverySiteEntity.getSignOutTemperature());
        deliverySiteMessage.setInterceptState(deliverySiteEntity.getInterceptState());
        deliverySiteMessage.setOuterClientName(deliverySiteEntity.getOuterClientName());
        deliverySiteMessage.setSignInDistance(deliverySiteEntity.getSignInDistance());
        deliverySiteMessage.setSignInStatus(deliverySiteEntity.getSignInStatus());
        deliverySiteMessage.setOuterClientId(deliverySiteEntity.getOuterClientId());
        deliverySiteMessage.setSignOutStatus(deliverySiteEntity.getSignOutStatus());
        if(!CollectionUtils.isEmpty(deliverySiteEntity.getDeliverySiteItemEntityList())){
            deliverySiteMessage.setDeliverySiteItemMessages(deliverySiteEntity.getDeliverySiteItemEntityList().stream()
                    .map(TmsDeliveryEventConverter::deliverySiteItem2Message)
                    .collect(Collectors.toList()));
        }
        return deliverySiteMessage;
    }

    public static DeliverySiteMessage deliverySite2Message(DeliverySiteEntity deliverySiteEntity, SiteEntity citySiteEntity) {
        if(deliverySiteEntity == null){
            return null;
        }
        DeliverySiteMessage deliverySiteMessage = deliverySite2Message(deliverySiteEntity);

        SiteEntity siteEntity = deliverySiteEntity.getSiteEntity();
        deliverySiteMessage.setContactId(siteEntity.getOutBusinessNo());
        deliverySiteMessage.setProvince(siteEntity.getProvince());
        deliverySiteMessage.setCity(siteEntity.getCity());
        deliverySiteMessage.setArea(siteEntity.getArea());
        deliverySiteMessage.setAddress(siteEntity.getAddress());
        deliverySiteMessage.setPoi(siteEntity.getPoi());
        deliverySiteMessage.setPhone(siteEntity.getPhone());
        deliverySiteMessage.setName(siteEntity.getName());
        deliverySiteMessage.setType(siteEntity.getType());
        deliverySiteMessage.setSuperviseSiteId(siteEntity.getSuperviseSiteId());
        deliverySiteMessage.setOutBusinsessNo(citySiteEntity.getOutBusinessNo());

        List<DeliverySiteItemCodeEntity> deliverySiteItemCodeEntityList = deliverySiteEntity.getDeliverySiteItemCodeEntityList();
        if(!CollectionUtils.isEmpty(deliverySiteItemCodeEntityList)){
            deliverySiteMessage.setDeliverySiteItemCodeMessages(siteItemCode2Message(deliverySiteItemCodeEntityList));
        }

        return deliverySiteMessage;
    }

    public static List<DeliverySiteItemCodeMessage> siteItemCode2Message(List<DeliverySiteItemCodeEntity> deliverySiteItemCodeEntityList) {
        if(CollectionUtils.isEmpty(deliverySiteItemCodeEntityList)){
            return Collections.emptyList();
        }
        return deliverySiteItemCodeEntityList.stream().map(e ->{
            DeliverySiteItemCodeMessage deliverySiteItemCodeMessage = new DeliverySiteItemCodeMessage();
            deliverySiteItemCodeMessage.setDeliverySiteItemId(e.getDeliverySiteItemId());
            deliverySiteItemCodeMessage.setDeliverySiteId(e.getDeliverySiteId());
            deliverySiteItemCodeMessage.setOutItemId(e.getOutItemId());
            deliverySiteItemCodeMessage.setOnlyCode(e.getOnlyCode());
            return deliverySiteItemCodeMessage;
        }).collect(Collectors.toList());
    }

    public static DeliverySiteItemMessage deliverySiteItem2Message(DeliverySiteItemEntity deliverySiteItemEntity) {
        DeliverySiteItemMessage deliverySiteItemMessage = new DeliverySiteItemMessage();
        deliverySiteItemMessage.setId(deliverySiteItemEntity.getId());
        deliverySiteItemMessage.setDeliverySiteId(deliverySiteItemEntity.getDeliverySiteId());
        deliverySiteItemMessage.setOutItemId(deliverySiteItemEntity.getOutItemId());
        deliverySiteItemMessage.setOutItemName(deliverySiteItemEntity.getOutItemName());
        deliverySiteItemMessage.setPlanReceiptCount(deliverySiteItemEntity.getPlanReceiptCount());
        deliverySiteItemMessage.setRealReceiptCount(deliverySiteItemEntity.getRealReceiptCount());
        deliverySiteItemMessage.setShortCount(deliverySiteItemEntity.getShortCount());
        deliverySiteItemMessage.setInterceptCount(deliverySiteItemEntity.getInterceptCount());
        deliverySiteItemMessage.setRejectCount(deliverySiteItemEntity.getRejectCount());
        deliverySiteItemMessage.setRejectReason(deliverySiteItemEntity.getRejectReason());
        deliverySiteItemMessage.setScanCount(deliverySiteItemEntity.getScanCount());
        deliverySiteItemMessage.setNoscanCount(deliverySiteItemEntity.getNoscanCount());
        deliverySiteItemMessage.setNoscanReason(deliverySiteItemEntity.getNoscanReason());
        deliverySiteItemMessage.setNoscanPics(deliverySiteItemEntity.getNoscanPics());
        deliverySiteItemMessage.setType(deliverySiteItemEntity.getType());
        deliverySiteItemMessage.setRemark(deliverySiteItemEntity.getRemark());
        deliverySiteItemMessage.setStatus(deliverySiteItemEntity.getStatus());
        if (deliverySiteItemEntity.getDeliverySiteItemRecycleEntity() != null){
            deliverySiteItemMessage.setDeliverySiteItemRecycleMessage(deliverySiteItemRecycle2Message(deliverySiteItemEntity.getDeliverySiteItemRecycleEntity()));
        }
        return deliverySiteItemMessage;
    }

    private static DeliverySiteItemRecycleMessage deliverySiteItemRecycle2Message(DeliverySiteItemRecycleEntity deliverySiteItemRecycleEntity) {
        DeliverySiteItemRecycleMessage deliverySiteItemRecycleMessage = new DeliverySiteItemRecycleMessage();
        deliverySiteItemRecycleMessage.setId(deliverySiteItemRecycleEntity.getId());
        deliverySiteItemRecycleMessage.setDeliverySiteItemId(deliverySiteItemRecycleEntity.getDeliverySiteItemId());
        deliverySiteItemRecycleMessage.setOutItemId(deliverySiteItemRecycleEntity.getOutItemId());
        deliverySiteItemRecycleMessage.setRecyclePics(deliverySiteItemRecycleEntity.getRecyclePics());
        deliverySiteItemRecycleMessage.setSpecificationQuantity(deliverySiteItemRecycleEntity.getSpecificationQuantity());
        deliverySiteItemRecycleMessage.setSpecificationUnit(deliverySiteItemRecycleEntity.getSpecificationUnit());
        deliverySiteItemRecycleMessage.setBasicSpecQuantity(deliverySiteItemRecycleEntity.getBasicSpecQuantity());
        deliverySiteItemRecycleMessage.setBasicSpecUnit(deliverySiteItemRecycleEntity.getBasicSpecUnit());
        DeliverySiteItemRecycleEnums.ReasonType reasonType = deliverySiteItemRecycleEntity.getReasonType();
        if (reasonType != null){
            deliverySiteItemRecycleMessage.setReasonType(reasonType.getValue());
            deliverySiteItemRecycleMessage.setReasonTypeDesc(reasonType.getContent());
        }
        deliverySiteItemRecycleMessage.setRemark(deliverySiteItemRecycleEntity.getRemark());
        return deliverySiteItemRecycleMessage;
    }

    public static DeliveryOrderMessage deliveryOrder2Message(DeliveryOrderEntity deliveryOrderEntity) {
        if(deliveryOrderEntity == null){
            return null;
        }
        DeliveryOrderMessage deliveryOrderMessage = new DeliveryOrderMessage();
        deliveryOrderMessage.setDistOrderId(deliveryOrderEntity.getDistOrderId());
        deliveryOrderMessage.setSource(deliveryOrderEntity.getSource().getCode());
        deliveryOrderMessage.setOutOrderId(deliveryOrderEntity.getOuterOrderId());
        deliveryOrderMessage.setContactId(deliveryOrderEntity.getOuterContactId());
        deliveryOrderMessage.setClientId(deliveryOrderEntity.getOuterClientId());
        deliveryOrderMessage.setClientName(deliveryOrderEntity.getOuterClientName());
        deliveryOrderMessage.setDeliveryTime(deliveryOrderEntity.getDeliveryTime());
        deliveryOrderMessage.setAddressDetail(deliveryOrderEntity.getEndSiteFullAddress());
        deliveryOrderMessage.setName(deliveryOrderEntity.getName());
        deliveryOrderMessage.setPhone(deliveryOrderEntity.getPhone());
        deliveryOrderMessage.setState(deliveryOrderEntity.getStatus().getCode());
        deliveryOrderMessage.setFinishTime(deliveryOrderEntity.getFinishTime());
        deliveryOrderMessage.setType(deliveryOrderEntity.getType());
        deliveryOrderMessage.setInterceptReason(deliveryOrderEntity.getInterceptReason());
        deliveryOrderMessage.setInterceptReasonCode(deliveryOrderEntity.getInterceptReasonCode());
        if(!CollectionUtils.isEmpty(deliveryOrderEntity.getDeliveryItemEntityList())){
            deliveryOrderMessage.setDeliveryItemMessages(deliveryOrderEntity.getDeliveryItemEntityList().stream()
                    .map(TmsDeliveryEventConverter::deliveryItem2Message)
                    .collect(Collectors.toList()));
        }else{
            deliveryOrderMessage.setDeliveryItemMessages(new ArrayList<>());
        }
        return deliveryOrderMessage;
    }

    public static DeliveryItemMessage deliveryItem2Message(DeliveryItemEntity deliveryItemEntity) {
        DeliveryItemMessage deliveryItemMessage = new DeliveryItemMessage();
        deliveryItemMessage.setDeliveryOrderId(deliveryItemEntity.getDeliveryOrderId());
        deliveryItemMessage.setDistOrderId(deliveryItemEntity.getDistOrderId());
        deliveryItemMessage.setOutOrderId(deliveryItemEntity.getOutOrderId());
        deliveryItemMessage.setOutItemId(deliveryItemEntity.getOutItemId());
        deliveryItemMessage.setPlanReceiptCount(deliveryItemEntity.getPlanReceiptCount());
        deliveryItemMessage.setRealReceiptCount(deliveryItemEntity.getRealReceiptCount());
        deliveryItemMessage.setShortCount(deliveryItemEntity.getShortCount());
        deliveryItemMessage.setInterceptCount(deliveryItemEntity.getInterceptCount());
        deliveryItemMessage.setRejectCount(deliveryItemEntity.getRejectCount());
        deliveryItemMessage.setRejectRemark(deliveryItemEntity.getRejectRemark());
        deliveryItemMessage.setScanCount(deliveryItemEntity.getScanCount());
        deliveryItemMessage.setNoscanCount(deliveryItemEntity.getNoscanCount());
        deliveryItemMessage.setNoscanReason(deliveryItemEntity.getNoscanReason());
        deliveryItemMessage.setNoscanPics(deliveryItemEntity.getNoscanPics());
        return deliveryItemMessage;
    }

    /**
     * 构建干线运输的配送单事件
     *
     * @param eventTypeEnum 事件类型
     * @param currentDistDeliveryOrder 当前配送单
     * @param currentDistOrder 当前委托单
     * @return 配送单事件
     */
    public static TmsDeliveryEvent buildTmsSingleCityOrderTrunkSendDeliveryEvent(EventTypeEnum eventTypeEnum, DeliveryOrderEntity currentDistDeliveryOrder, DistOrderEntity currentDistOrder) {

        currentDistDeliveryOrder.setDeliveryItemEntityList(generateCreateDeliveryItemEntities(currentDistDeliveryOrder, currentDistOrder));

        // 处理干线运输的配送站点明细（因为干线运输没有明细，需要根据委托单生成）
        DeliverySiteEntity deliverySiteEntity = generateCreateTrunkDeliverySiteItems(currentDistOrder);
        // 调用城配
        return buildTmsDeliveryEvent(eventTypeEnum, currentDistDeliveryOrder.getDeliveryBatchEntity(), deliverySiteEntity, Collections.singletonList(currentDistDeliveryOrder), null, currentDistOrder.getBeginSite());
    }

    /**
     * 自动生成干线运输的配送单明细
     * 因为干线运输没有实际的配送明细，需要根据委托单的物品信息生成假的配送明细
     *
     * @param currentDistDeliveryOrder 当前配送单
     * @param currentDistOrder 当前委托单
     */
    private static List<DeliveryItemEntity> generateCreateDeliveryItemEntities(DeliveryOrderEntity currentDistDeliveryOrder, DistOrderEntity currentDistOrder) {
        List<DistItemVO> distItems = currentDistOrder.getDistItems();

        return distItems.stream().map(e -> {
            DeliveryItemEntity deliveryItem = new DeliveryItemEntity();

            deliveryItem.setDeliveryOrderId(currentDistDeliveryOrder.getId());
            deliveryItem.setDistOrderId(e.getDistOrderId());
            deliveryItem.setOutOrderId(currentDistDeliveryOrder.getOuterOrderId());
            deliveryItem.setOutItemId(e.getOutItemId());
            deliveryItem.setPlanReceiptCount(e.getQuantity());
            deliveryItem.setInterceptCount(0);
            deliveryItem.setOutItemName(e.getOutItemName());
            deliveryItem.setOutItemType(e.getItemTypeEnum().getCode());
            deliveryItem.setPackType(e.getPackType());
            deliveryItem.setTemperature(e.getTemperatureEnum() != null ? e.getTemperatureEnum().getCode() : null);
            deliveryItem.setRealReceiptCount(e.getQuantity());
            deliveryItem.setRejectCount(0);
            deliveryItem.setShortCount(0);

            return deliveryItem;
        }).collect(Collectors.toList());
    }

    /**
     * 自动生成干线运输的配送站点明细
     * 因为干线运输没有实际的配送明细，需要根据委托单的物品信息生成假的配送明细
     *
     * @param distOrderEntity 委托单实体
     */
    private static DeliverySiteEntity generateCreateTrunkDeliverySiteItems(DistOrderEntity distOrderEntity) {
        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
        try {
            // 检查委托单是否有物品信息
            if (CollectionUtils.isEmpty(distOrderEntity.getDistItems())) {
                log.warn("干线运输委托单没有物品信息，无法生成配送明细，委托单ID：{}", distOrderEntity.getDistId());
                return deliverySiteEntity;
            }

            // 设置配送站点为委托单的终点
            if (deliverySiteEntity.getSiteEntity() == null) {
                deliverySiteEntity.setSiteEntity(distOrderEntity.getEndSite());
            }
            deliverySiteEntity.setPlanArriveTime(distOrderEntity.getDistFlowVO().getExpectBeginTime());
            deliverySiteEntity.setOuterClientId(distOrderEntity.getDistClientVO().getOutClientId());
            deliverySiteEntity.setSiteId(distOrderEntity.getEndSite().getId());
            deliverySiteEntity.setSiteName(distOrderEntity.getEndSite().getSiteName());
            deliverySiteEntity.setSiteType(distOrderEntity.getEndSite().getType());
            deliverySiteEntity.setStatus(DistOrderStatusEnum.COMPLETE_DELIVERY.equals(distOrderEntity.getStatus()) ? DeliverySiteStatusEnum.FINISH_DELIVERY : DeliverySiteStatusEnum.FINISH_PICK);
            // 根据委托单物品信息生成配送明细
            List<DeliverySiteItemEntity> deliverySiteItemEntityList = generateCreateDeliverySiteItemsFromDistOrder(
                    deliverySiteEntity.getId(), distOrderEntity);

            // 设置配送明细到配送站点
            deliverySiteEntity.setDeliverySiteItemEntityList(deliverySiteItemEntityList);

            log.info("成功为干线运输委托单{}生成{}个配送明细", distOrderEntity.getDistId(), deliverySiteItemEntityList.size());

        } catch (Exception e) {
            log.error("处理干线运输配送站点明细时发生异常，委托单ID：{}", distOrderEntity.getDistId(), e);
        }

        return deliverySiteEntity;
    }

    /**
     * 根据委托单物品信息生成配送站点明细
     *
     * @param deliverySiteId 配送站点ID
     * @param distOrderEntity 委托单实体
     * @return 配送站点明细列表
     */
    private static List<DeliverySiteItemEntity> generateCreateDeliverySiteItemsFromDistOrder(Long deliverySiteId, DistOrderEntity distOrderEntity) {
        List<DistItemVO> distItems = distOrderEntity.getDistItems();
        if (CollectionUtils.isEmpty(distItems)) {
            return Collections.emptyList();
        }
        // 按照SKU、配送方式进行分组
        Map<String, List<DistItemVO>> distItemMap = distItems.stream()
                .collect(Collectors.groupingBy(distItem -> distItem.getOutItemId() + "_" + distItem.getItemDeliveryTypeEnum().getCode()));
        // 生成配送站点明细
        List<DeliverySiteItemEntity> deliverySiteItemEntityList = new ArrayList<>();

        // 遍历配送明细，相同的SKU、配送方式进行汇总
        distItemMap.forEach((skuDeliveryType, skuDeliveryTypeDistItems) -> {
            DeliverySiteItemEntity deliverySiteItemEntity = new DeliverySiteItemEntity();

            DistItemVO distItemVO = skuDeliveryTypeDistItems.get(0);
            // 基础信息
            deliverySiteItemEntity.setDeliverySiteId(deliverySiteId);
            deliverySiteItemEntity.setOutItemId(distItemVO.getOutItemId());
            deliverySiteItemEntity.setOutItemName(distItemVO.getOutItemName());
            deliverySiteItemEntity.setOutItemType(distItemVO.getType());
            deliverySiteItemEntity.setPackType(distItemVO.getPackType());

            // 数量信息（干线运输默认全部签收）
            Integer quantity = skuDeliveryTypeDistItems.stream().mapToInt(DistItemVO::getQuantity).sum();
            deliverySiteItemEntity.setPlanReceiptCount(quantity);
            deliverySiteItemEntity.setRealReceiptCount(quantity);
            deliverySiteItemEntity.setScanCount(quantity);

            // 异常数量默认为0
            deliverySiteItemEntity.setShortCount(0);
            deliverySiteItemEntity.setPickShortCount(0);
            deliverySiteItemEntity.setInterceptCount(0);
            deliverySiteItemEntity.setRejectCount(0);
            deliverySiteItemEntity.setNoscanCount(0);

            // 温区信息
            if (distItemVO.getTemperatureEnum() != null) {
                deliverySiteItemEntity.setTemperature(distItemVO.getTemperatureEnum().getCode());
                deliverySiteItemEntity.setStorageArea(distItemVO.getTemperatureEnum().getCode());
            }
            // 配送类型（0：配送，1：回收）
            deliverySiteItemEntity.setType(distItemVO.getItemDeliveryTypeEnum().getCode()); // 干线运输默认为配送
            // 状态（0：正常，1：异常）
            deliverySiteItemEntity.setStatus(0); // 干线运输默认为正常

            // 设置创建和更新时间
            LocalDateTime now = LocalDateTime.now();
            deliverySiteItemEntity.setCreateTime(now);
            deliverySiteItemEntity.setUpdateTime(now);

            deliverySiteItemEntityList.add(deliverySiteItemEntity);
        });

        return deliverySiteItemEntityList;
    }
}
