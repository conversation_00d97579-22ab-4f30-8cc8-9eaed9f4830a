package net.summerfarm.tms.local.delivery.converter;

import net.summerfarm.tms.delivery.dto.TrunkDeliveryDistOrderDTO;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.enums.DistOrderSourceEnum;

/**
 * Description: <br/>
 * date: 2022/9/20 10:53<br/>
 *
 * <AUTHOR> />
 */
public class TrunkDeliveryDistOrderDTOConverter {

    public static TrunkDeliveryDistOrderDTO deliveryOrderVO2TrunkDeliveryDistOrderDTO(DeliveryOrderEntity deliveryOrderEntity) {
        TrunkDeliveryDistOrderDTO trunkDeliveryDistOrderDTO = new TrunkDeliveryDistOrderDTO();

        trunkDeliveryDistOrderDTO.setId(deliveryOrderEntity.getId());
        trunkDeliveryDistOrderDTO.setDistId(deliveryOrderEntity.getDistOrderId());
        trunkDeliveryDistOrderDTO.setBeginSiteId(deliveryOrderEntity.getBeginSiteId());
        trunkDeliveryDistOrderDTO.setOutOrderId(deliveryOrderEntity.getOuterOrderId());

        trunkDeliveryDistOrderDTO.setEndSiteId(deliveryOrderEntity.getEndSiteId());
        trunkDeliveryDistOrderDTO.setSource(deliveryOrderEntity.getSource().getCode());
        if (DistOrderSourceEnum.getCityCode().contains(deliveryOrderEntity.getSource().getCode())) {
            trunkDeliveryDistOrderDTO.setSourceDesc("销售单-干线");
        } else {
            trunkDeliveryDistOrderDTO.setSourceDesc(deliveryOrderEntity.getSourceDesc());
        }
        trunkDeliveryDistOrderDTO.setDeliveryTime(deliveryOrderEntity.getDeliveryTime());
        trunkDeliveryDistOrderDTO.setSiteType(deliveryOrderEntity.getDeliveryOrderSiteType().getCode());

        return trunkDeliveryDistOrderDTO;
    }
}