package net.summerfarm.tms.local.expense;

import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.after.ExpenseDomainService;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.constants.DataSychConstants;
import net.summerfarm.tms.delivery.dto.ExpenseDTO;
import net.summerfarm.tms.delivery.dto.ExpenseDetailDTO;
import net.summerfarm.tms.enums.ExpenseTypeEnum;
import net.summerfarm.tms.expense.TmsExpenseService;
import net.summerfarm.tms.local.expense.converter.ExpenseConverter;
import net.summerfarm.tms.util.ThreadLocalUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Description: <br/>
 * date: 2022/10/21 18:39<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class TmsExpenseServiceImpl implements TmsExpenseService {
    private final ExpenseDomainService expenseDomainService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> saveExpense(ExpenseDTO expenseDTO) {
        long reviewCnt = expenseDTO.getExpenseDetailDTOList().stream()
                .filter(expenseDetailDTO -> expenseDetailDTO.getIsReview() == 1)
                .count();
        expenseDTO.setIsReview(reviewCnt > 0 ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO);
        long typeCnt = expenseDTO.getExpenseDetailDTOList().stream().map(ExpenseDetailDTO::getType).distinct().count();
        expenseDTO.setType(typeCnt > 1 ?
                ExpenseTypeEnum.TRAFFIC_PURCHASE.getId() : expenseDTO.getExpenseDetailDTOList().get(0).getType());

        expenseDomainService.submit(ExpenseConverter.dto2Entity(expenseDTO));

        return TmsResult.VOID_SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> expenseAudit(ExpenseDTO expenseDTO) {
        Long deliverySiteId = expenseDomainService.audit(ExpenseConverter.dto2Entity(expenseDTO));
        return TmsResult.VOID_SUCCESS;
    }

}
