package net.summerfarm.tms.local.delivery;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.tms.after.ExpenseRepository;
import net.summerfarm.tms.delivery.dto.cheakinpunch.CheakInPunchSaveCommand;
import net.summerfarm.tms.delivery.dto.pick.PickScanCommand;
import net.summerfarm.tms.delivery.dto.pick.PickShortCommand;
import net.summerfarm.tms.delivery.input.PickUpFinishDetailInput;
import net.summerfarm.tms.delivery.input.PickUpFinishUpdateCommand;
import net.summerfarm.tms.delivery.repository.TmsDeliveryPickShortOrderMappingQueryRepository;
import net.summerfarm.tms.event.DeliveryPickLackGoodsMessage;
import net.summerfarm.tms.event.TmsDeliveryEvent;
import net.summerfarm.tms.facade.goodCenter.GoodCenterQueryFacade;
import net.summerfarm.tms.facade.goodCenter.dto.GoodsDTO;
import net.summerfarm.tms.facade.goodCenter.input.GoodsInput;
import net.summerfarm.tms.facade.wms.dto.SkuBarcodeDTO;
import net.summerfarm.tms.facade.wnc.WncQueryFacade;
import net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.query.NeedUploadKeepTemperatureMethodPicQueryInput;
import net.summerfarm.tms.lack.LackApprovedDomainService;
import net.summerfarm.tms.alert.DeliveryAlertDomainService;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.ContactAdjustRepository;
import net.summerfarm.tms.base.site.SiteDomainService;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.ContactAdjustEntity;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.common.EventBusService;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.constants.DataSychConstants;
import net.summerfarm.tms.delivery.*;
import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderInterceptDTO;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.event.EventTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.ext.AuthExtService;
import net.summerfarm.tms.facade.crm.CrmQueryFacade;
import net.summerfarm.tms.facade.crm.dto.BdDTO;
import net.summerfarm.tms.facade.mall.MallQueryFacade;
import net.summerfarm.tms.facade.mall.dto.DriverAppraiseDTO;
import net.summerfarm.tms.facade.mall.dto.SkuDTO;
import net.summerfarm.tms.facade.mall.input.DriverAppraiseInput;
import net.summerfarm.tms.facade.ofc.OfcQueryFacade;
import net.summerfarm.tms.facade.ofc.dto.InterceptOrderDTO;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.facade.wms.dto.WmsOrderProcessDetailDTO;
import net.summerfarm.tms.local.delivery.converter.*;
import net.summerfarm.tms.local.dist.converter.DistOrderConverter;
import net.summerfarm.tms.pick.domain.DeliveryPickScanCodeCommandDomainService;
import net.summerfarm.tms.message.out.TrunkBatchPickUpDistOrder;
import net.summerfarm.tms.message.out.TrunkBatchPickUpShortGoodsMsg;
import net.summerfarm.tms.message.out.TrunkBatchPickUpSkuShort;
import net.summerfarm.tms.query.delivery.*;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.query.site.ContactAdjustQuery;
import net.summerfarm.tms.service.delivery.CityDeliveryQueryService;
import net.summerfarm.tms.util.DistanceUtil;
import net.summerfarm.tms.util.ExecutorUtil;
import net.summerfarm.tms.util.RedisUtil;
import net.summerfarm.tms.util.ThreadLocalUtil;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/13 14:59<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliverySiteServiceImpl implements DeliverySiteService {

    private final DeliveryBatchService deliveryBatchService;
    private final DeliverySiteRepository deliverySiteRepository;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final SiteDomainService siteDomainService;
    private final DeliverySiteDomainService deliverySiteDomainService;
    private final DeliverySiteItemCodeRepository deliverySiteItemCodeRepository;
    private final DeliveryBatchDomainService deliveryBatchDomainService;
    private final DeliveryOrderDomainService deliveryOrderDomainService;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final EventBusService eventBusService;
    private final DistOrderDomainService distOrderDomainService;
    private final SiteRepository siteRepository;
    private final DistOrderRepository distOrderRepository;
    private final LackApprovedDomainService lackApprovedDomainService;
    private final DeliveryPickRepository deliveryPickRepository;
    private final OfcQueryFacade ofcQueryFacade;
    private final DistOrderService distOrderService;
    private final DeliverySiteItemRepository deliverySiteItemRepository;
    private final ContactAdjustRepository contactAdjustRepository;
    private final ExpenseRepository expenseRepository;
    private final DeliveryAlertDomainService deliveryAlertDomainService;
    private final RedisUtil redisUtil;
    private final AuthExtService authExtService;
    private final MallQueryFacade mallQueryFacade;
    private final WmsQueryFacade wmsQueryFacade;
    private final DeliveryMsgSender deliveryMsgSender;
    private final CrmQueryFacade crmQueryFacade;
    private final GoodCenterQueryFacade goodCenterQueryFacade;
    private final WncQueryFacade wncQueryFacade;
    private final DeliveryPickScanCodeCommandDomainService deliveryPickScanCodeCommandDomainService;
    private final DeliveryPickDomainService deliveryPickDomainService;
    private final TmsDeliveryPickShortOrderMappingCommandDomainService tmsDeliveryPickShortOrderMappingCommandDomainService;
    private final TmsDeliveryPickShortOrderMappingQueryRepository tmsDeliveryPickShortOrderMappingQueryRepository;
    private final CityDeliveryQueryService cityDeliveryQueryService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> signIn(DeliverySiteDTO deliverySiteDTO) {
        TmsAssert.notNull(deliverySiteDTO.getId(), ErrorCodeEnum.PARAM_NOT_NULL, "id");
        TmsAssert.notNull(deliverySiteDTO.getSignInTime(), ErrorCodeEnum.PARAM_NOT_NULL, "SignInTime");
        TmsAssert.notNull(deliverySiteDTO.getSignInDistance(), ErrorCodeEnum.PARAM_NOT_NULL, "SignInDistance");

        String signInPoi = deliverySiteDTO.getSignInPoi();

        String punchAddress = "";
        //查询poi的地址名称
        if (StringUtil.isNotBlank(signInPoi)) {
            Object signInPoiObj = redisUtil.get(signInPoi);
            if (signInPoiObj != null) {
                punchAddress = signInPoiObj.toString();
            }else{
                try {
                    punchAddress = GaoDeUtil.getNameByGeoCode(signInPoi);
                    redisUtil.set(signInPoi,punchAddress);
                    //缓存一天
                    redisUtil.expire(signInPoi,60 * 60 * 24);
                } catch (Exception e) {
                    log.info("高德定位异常", e);
                }
            }
        }
        deliverySiteDTO.setSignInAddress(punchAddress);
        deliverySiteDomainService.signIn(DeliverySiteDTOConverter.dto2Entity(deliverySiteDTO));
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<String> validateFinishPick(Long deliverySiteId) {
        TmsAssert.notNull(deliverySiteId, ErrorCodeEnum.PARAM_NOT_NULL, "deliverySiteId");
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteId);
        if (deliverySiteEntity == null) {
            throw new TmsRuntimeException("配送点位信息不存在，配送点位id：" + deliverySiteId);
        }
        String validateResult = tmsDeliveryPickShortOrderMappingCommandDomainService.validateFinishPick(deliverySiteEntity);
        return TmsResult.success(validateResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Void> finishPick(DeliverySiteDTO deliverySiteDTO) {
        TmsAssert.notNull(deliverySiteDTO.getId(), ErrorCodeEnum.PARAM_NOT_NULL, "id");
        DeliverySiteEntity deliverySiteEntity = deliverySiteDomainService.finishPick(DeliverySiteDTOConverter.dto2Entity(deliverySiteDTO));
        deliveryBatchDomainService.finishPick(deliverySiteEntity.getDeliveryBatchId());
        tmsDeliveryPickShortOrderMappingCommandDomainService.finishPick(deliverySiteDTO.getId());

        if(CollectionUtils.isNotEmpty(deliverySiteEntity.getDeliveryPickEntityList())){
            ThreadLocalUtil.getThreadLocal().put(DataSychConstants.App.FINISH_PICK,deliverySiteEntity.getDeliveryPickEntityList().get(0).getId());
        }
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<Void> signOut(DeliverySiteDTO deliverySiteDTO) {
        TmsAssert.notNull(deliverySiteDTO.getId(), ErrorCodeEnum.PARAM_NOT_NULL, "id");
        TmsAssert.notNull(deliverySiteDTO.getSignOutPic1(), ErrorCodeEnum.PARAM_NOT_NULL, "SignOutPic");
        TmsAssert.notNull(deliverySiteDTO.getSignOutTemperature(), ErrorCodeEnum.PARAM_NOT_NULL, "SignOutTemperature");
        TmsAssert.notNull(deliverySiteDTO.getSignOutTime(), ErrorCodeEnum.PARAM_NOT_NULL, "SignOutTime");
        TmsAssert.notNull(deliverySiteDTO.getVehiclePlatePics(), ErrorCodeEnum.PARAM_NOT_NULL, "车辆照片");

        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteDTO.getId());
        if (deliverySiteEntity == null || ObjectUtils.isNull(deliverySiteEntity.getId())) {
            throw new TmsRuntimeException("配送点位信息不存在，配送点位id" + deliverySiteDTO.getId());
        }

        deliverySiteDTO.setSealPics(deliverySiteEntity.getSealPics());
        deliverySiteDomainService.signOut(DeliverySiteDTOConverter.dto2Entity(deliverySiteDTO));
        return TmsResult.VOID_SUCCESS;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Void> finishDelivery(DeliverySiteDTO deliverySiteDTO,Boolean checkScanNum) {
        TmsAssert.notNull(deliverySiteDTO.getId(), ErrorCodeEnum.PARAM_NOT_NULL, "id");
        TmsAssert.notNull(deliverySiteDTO.getSignInPoi(), ErrorCodeEnum.PARAM_NOT_NULL, "SignInPoi");
        TmsAssert.notNull(deliverySiteDTO.getStatus(), ErrorCodeEnum.PARAM_NOT_NULL, "Status");
        TmsAssert.notEmpty(deliverySiteDTO.getSignInPic1(), ErrorCodeEnum.PARAM_NOT_NULL, "门店抬头");
        List<DeliverySiteItemDTO> deliverySiteItemDTOList = deliverySiteDTO.getDeliverySiteItemDTOList();
        boolean deliverySiteFlag = deliverySiteItemDTOList.stream().anyMatch(e -> Objects.equals(DeliverySiteItemTypeEnum.DELIVERY.getCode(), e.getType()));
        //含有配送任务的点位 需进行校验相关配送图片的校验
        if (deliverySiteFlag){
            TmsAssert.notNull(deliverySiteDTO.getSignInStatus(), ErrorCodeEnum.PARAM_NOT_NULL, "SignInStatus");
            TmsAssert.notEmpty(deliverySiteDTO.getSignInPic2(), ErrorCodeEnum.PARAM_NOT_NULL, "签收面单");
            TmsAssert.notEmpty(deliverySiteDTO.getSignInPic3(), ErrorCodeEnum.PARAM_NOT_NULL, "货物照片");
        }else {
            //仅含回收任务的点位 需特殊处理签收状态字段
            boolean siteSignInAbnormalFlag = deliverySiteItemDTOList.stream().anyMatch(e -> Objects.equals(DeliverySiteItemEnums.Status.ABNORMAL.getValue(), e.getStatus()));
            deliverySiteDTO.setSignInStatus(siteSignInAbnormalFlag ? 1 : 0);
        }
        //是否需要到店打卡校验
        deliverySiteDomainService.checkIsHaveinPunch(DeliverySiteDTOConverter.dto2Entity(deliverySiteDTO));
        //更新点位
        DeliverySiteEntity deliverySiteEntity = deliverySiteDomainService.finishDelivery(DeliverySiteDTOConverter.dto2Entity(deliverySiteDTO), checkScanNum);
        //更新批次
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.finishDelivery(deliverySiteEntity.getDeliveryBatchId());
        //更新配送单
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderDomainService.finishDelivery(deliverySiteEntity.getDeliveryBatchId(), deliverySiteEntity);
        List<Long> distIdList = deliveryOrderEntityList.stream()
                .map(DeliveryOrderEntity::getDistOrderId).distinct()
                .collect(Collectors.toList());
        List<DistOrderEntity> distOrderEntityList = distOrderDomainService.finishDelivery(distIdList);
        //生成缺货核准
        lackApprovedDomainService.createLackGoodsApproved(deliveryOrderEntityList,deliverySiteEntity.getId());
        //发送 委托单完成配送消息
        SiteEntity citySiteEntity = siteDomainService.query(deliveryBatchEntity.getBeginSiteId());
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                List<DeliverySiteItemCodeEntity> deliverySiteItemCodeEntityList = deliverySiteItemCodeRepository.queryListByDeliverySiteId(deliverySiteEntity.getId());
                deliverySiteEntity.setDeliverySiteItemCodeEntityList(deliverySiteItemCodeEntityList);
                TmsDeliveryEvent tmsDeliveryEvent = TmsDeliveryEventConverter.buildTmsDeliveryEvent(EventTypeEnum.COMPLETE_DELIVERY,
                        deliveryBatchEntity, deliverySiteEntity, deliveryOrderEntityList, distOrderEntityList, citySiteEntity);
                try {
                    eventBusService.notifyDeliveryEvent(tmsDeliveryEvent);
                } catch (Throwable e) {
                    log.error("完成配送通知OFC消息发送失败:{}", JSONObject.toJSONString(tmsDeliveryEvent), e);
                }

                ExecutorUtil.finishDeliveryExecutor.execute(() -> {
                    //完成配送异常回收通知消息发送
                    deliveryMsgSender.sendAbnormalRecycleMsg(deliverySiteEntity, deliveryOrderEntityList, deliveryBatchEntity, citySiteEntity);
                    //完成配送精准送时效异常消息发送
                    deliveryMsgSender.sendAbnormalPreciseDeliveryMsg(deliverySiteEntity, deliveryBatchEntity, distOrderEntityList, citySiteEntity);
                });
            }
        });

        return TmsResult.VOID_SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> changeSiteBatch(List<DeliverySiteDTO> deliverySiteDTOs) {
        if(CollectionUtils.isEmpty(deliverySiteDTOs)){
            return TmsResult.VOID_SUCCESS;
        }
        // 城配仓校验
        List<Long> wantChangePathDeliverySiteIdList = deliverySiteDTOs.stream().map(DeliverySiteDTO::getId).collect(Collectors.toList());
        List<Long> wantToChangeBatchIds = deliverySiteDTOs.stream().map(DeliverySiteDTO::getDeliveryBatchId).collect(Collectors.toList());
        this.cityStoreNoCheck(wantChangePathDeliverySiteIdList,wantToChangeBatchIds);

        //先移除
        for (DeliverySiteDTO deliverySiteDTO : deliverySiteDTOs) {
            this.siteRemove(deliverySiteDTO.getId());
        }
        ArrayList<DeliveryOrderEntity> deliveryOrderAllList = new ArrayList<>();

        DeliveryBatchEntity deliveryBatchEntity = null;
        //修改点位上的批次和配送单、委托单上面的批次信息
        for (DeliverySiteDTO deliverySiteDTO : deliverySiteDTOs) {
            //配送点位修改批次信息和顺序
            DeliverySiteEntity deliverySiteEntity = deliverySiteDomainService.changeSiteBatch(DeliverySiteDTOConverter.dto2Entity(deliverySiteDTO));
            //根据点位查询批次信息
            if(deliveryBatchEntity == null){
                deliveryBatchEntity = deliveryBatchRepository.query(deliverySiteEntity.getDeliveryBatchId());
            }
            //查询点位上面的配送单
            DeliveryOrderQuery deliveryOrderQuery = new DeliveryOrderQuery();
            deliveryOrderQuery.setDeliveryTime(deliverySiteEntity.getPlanArriveTime().toLocalDate());
            deliveryOrderQuery.setBatchId(deliverySiteEntity.getDeliveryBatchId());
            deliveryOrderQuery.setEndSiteId(deliverySiteEntity.getSiteId());
            deliveryOrderQuery.setBeginSiteId(deliveryBatchEntity.getBeginSiteId());
            List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(deliveryOrderQuery);
            deliveryOrderAllList.addAll(deliveryOrderEntities);
        }
        if(deliveryOrderAllList.size() != 0){
            //配送单上面的批次信息/修改委托单上面的信息
            deliveryOrderDomainService.changeSiteBatch(deliveryOrderAllList, deliverySiteDTOs.get(0).getDeliveryBatchId());
        }
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 城配仓校验
     * @param wantChangePathDeliverySiteIdList 想要改变路线运输站点
     * @param wantToChangeBatchIds 想要改变到路线的批次id
     */
    private void cityStoreNoCheck(List<Long> wantChangePathDeliverySiteIdList,List<Long> wantToChangeBatchIds) {
        if(CollectionUtils.isEmpty(wantChangePathDeliverySiteIdList) || CollectionUtils.isEmpty(wantToChangeBatchIds)){
            return;
        }
        // 查询点位归属的
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder().deliverySiteIds(wantChangePathDeliverySiteIdList).build());

        List<Long> currentBatchIds = deliverySiteEntities.stream().map(DeliverySiteEntity::getDeliveryBatchId).collect(Collectors.toList());

        List<Long> batchIds = new ArrayList<>();
        batchIds.addAll(currentBatchIds);
        batchIds.addAll(wantToChangeBatchIds);

        if(CollectionUtils.isEmpty(batchIds)){
            return;
        }
        // 校验城配仓是否和当前点位城配仓一致
        List<DeliveryBatchEntity> batchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder().batchIds(batchIds).build());
        List<Long> beginSiteIds = batchEntityList.stream().map(DeliveryBatchEntity::getBeginSiteId).distinct().collect(Collectors.toList());

        if(beginSiteIds.size() > 1){
            throw new TmsRuntimeException("城配仓不一致，请刷新页面");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<DeliverySiteItemCodeDTO> scanCodeSave(DeliverySiteItemCodeDTO deliverySiteItemCodeDTO) {
        //查询此二维码是否已经被使用
        Long count = deliverySiteItemCodeRepository.counyByOnlyCode(deliverySiteItemCodeDTO.getOnlyCode());
        if(count > 0){
            throw new TmsRuntimeException(ErrorCodeEnum.SCAN_CODE_HAVE);
        }
        //查询店铺是否有该商品信息
        DeliverySiteItemEntity deliverySiteItemEntity = deliverySiteItemRepository.queryByUk(deliverySiteItemCodeDTO.getDeliverySiteId(),
                deliverySiteItemCodeDTO.getOutItemId(),DeliverySiteItemTypeEnum.DELIVERY.getCode());
        if(deliverySiteItemEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NO_SKU_SCAN_CODE);
        }
        Integer scanCount = deliverySiteItemEntity.getScanCount() == null ? 0 : deliverySiteItemEntity.getScanCount();
        Integer interceptCount = deliverySiteItemEntity.getInterceptCount() == null ? 0 : deliverySiteItemEntity.getInterceptCount();
        Integer shortCount = deliverySiteItemEntity.getShortCount() == null ? 0 : deliverySiteItemEntity.getShortCount();
        Integer noscanCount = deliverySiteItemEntity.getNoscanCount() == null ? 0 : deliverySiteItemEntity.getNoscanCount();
        //扫码+拦截数量+缺货数量+无码数量 不能大于计划配送数量
        if(scanCount + interceptCount + shortCount + noscanCount >= deliverySiteItemEntity.getPlanReceiptCount()){
            throw new TmsRuntimeException(ErrorCodeEnum.SKU_SCAN_CODE_ERROR,
                    String.format("商品:%s,扫码数量:%s,拦截数量:%s,缺货数量:%s,无码数量:%s,大于计划配送数量:%s",
                            deliverySiteItemEntity.getOutItemName(),scanCount,interceptCount,
                            shortCount,noscanCount,deliverySiteItemEntity.getPlanReceiptCount()));
        }

        DeliverySiteItemCodeEntity deliverySiteItemCodeEntity = new DeliverySiteItemCodeEntity();
        deliverySiteItemCodeEntity.setDeliverySiteId(deliverySiteItemCodeDTO.getDeliverySiteId());
        deliverySiteItemCodeEntity.setOutItemId(deliverySiteItemCodeDTO.getOutItemId());
        deliverySiteItemCodeEntity.setOnlyCode(deliverySiteItemCodeDTO.getOnlyCode());
        deliverySiteDomainService.scanItemCode(deliverySiteItemCodeEntity);
        deliverySiteItemCodeDTO.setId(deliverySiteItemCodeEntity.getId());
        return TmsResult.success(deliverySiteItemCodeDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> noCodeSave(DeliverySiteItemDTO deliverySiteItemDTO) {
        deliverySiteDomainService.noCodeItemSave(DeliverySiteItemDTOConverter.dto2Entity(deliverySiteItemDTO));
        return TmsResult.VOID_SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> siteRemove(Long deliverySiteId) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteId);
        if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"请刷新浏览器重试");
        }
        if (deliverySiteEntity.getDeliverySiteInterceptState() == DeliverySiteInterceptStateEnum.cancel){
            throw new TmsRuntimeException(ErrorCodeEnum.DELIVERY_SITE_INTERCEPTED, deliverySiteEntity.getId());
        }
        Long oldBatchId = deliverySiteEntity.getDeliveryBatchId();

        //点位移除
        DeliveryBatchEntity deliveryBatchEntity = deliverySiteDomainService.siteRemove(deliverySiteEntity);
        //修改配送单点位的上面的状态
        Long siteId = deliverySiteEntity.getSiteId();
        LocalDateTime planArriveTime = deliverySiteEntity.getPlanArriveTime();

        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                .endSiteId(siteId)
                .beginSiteId(deliveryBatchEntity.getBeginSiteId())
                .neState(DeliveryOrderStatusEnum.CLOSE.getCode())
                .sourceList(DistOrderSourceEnum.getCityCode())
                .batchId(oldBatchId)
                .deliveryTime(planArriveTime.toLocalDate())
                .build()
        );
        deliveryOrderEntities.forEach(deliveryOrderEntity -> deliveryOrderEntity.setDeliveryBatchId(deliverySiteEntity.getDeliveryBatchId()));

        //委托单和配送单修改批次id
        deliveryOrderDomainService.siteRemove(deliveryOrderEntities);

        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<Void> chargeBack(Long deliverySiteId) {
        TmsAssert.notNull(deliverySiteId,ErrorCodeEnum.PARAM_NOT_NULL,"deliverySiteId");
        //查询这个点位的订单信息
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(Long.parseLong(deliverySiteId.toString()));
        SiteEntity siteEntity = siteRepository.query(deliverySiteEntity.getSiteId());
        //完成排线不能拦截、saas订单不能拦截
        if(deliverySiteEntity.getDeliveryBatchId() != null){
            DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliverySiteEntity.getDeliveryBatchId());
            if(deliveryBatchEntity.getStatus() != DeliveryBatchStatusEnum.TO_BE_WIRED){
                throw new TmsRuntimeException(ErrorCodeEnum.INTERCEPT_ERROR,"已完成排线不能退单");
            }
        }
        if(Objects.equals(TmsSiteTypeEnum.SAAS.getCode(),siteEntity.getType())){
            throw new TmsRuntimeException(ErrorCodeEnum.INTERCEPT_ERROR,"当前点位为Saas点位");
        }

        if(Objects.equals(DeliverySiteStatusEnum.FINISH_DELIVERY,deliverySiteEntity.getStatus())){
            throw new TmsRuntimeException(ErrorCodeEnum.INTERCEPT_ERROR,"当前点位已完成配送");
        }

        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder()
                .deliveryTime(deliverySiteEntity.getPlanArriveTime().toLocalDate())
                .endSiteId(deliverySiteEntity.getSiteId()).build());
        distOrderEntities.forEach(distOrderEntity -> {
            if(DistOrderSourceEnum.OUTER_CITY == distOrderEntity.getSource()){
                throw new TmsRuntimeException("点位存在外单不可拦截!");
            }
        });
        distOrderEntities.forEach(distOrderEntity -> {
            if(DistOrderSourceEnum.POP_MALL == distOrderEntity.getSource() || DistOrderSourceEnum.POP_AFTER_SALE == distOrderEntity.getSource()){
                throw new TmsRuntimeException("点位存在POP单据不可拦截!");
            }
        });
        // POP城配判断
        List<Integer> popStoreNos = wncQueryFacade.queryPopWarehouseLogisticsList();
        if(CollectionUtils.isNotEmpty(distOrderEntities) && distOrderEntities.get(0).getBeginSite() != null){
            SiteEntity beginSiteEntity = siteRepository.query(distOrderEntities.get(0).getBeginSite().getId());
            if(beginSiteEntity == null){
                throw new BizException("起点为空不可拦截!");
            }
            if(popStoreNos.contains(Integer.parseInt(beginSiteEntity.getOutBusinessNo()))){
                throw new TmsRuntimeException("POP城配仓点位不可拦截!");
            }
        }

        //过滤这个点位的订单号
        distOrderEntities = distOrderEntities.stream()
                .filter(dist -> Objects.equals(DistOrderSourceEnum.XM_MALL,dist.getSource()) ||
                        Objects.equals(DistOrderSourceEnum.XM_MALL_TIMING,dist.getSource()) ||
                        Objects.equals(DistOrderSourceEnum.XM_AFTER_SALE,dist.getSource()) ||
                        Objects.equals(DistOrderSourceEnum.XM_SAMPLE_APPLY,dist.getSource())
                ).collect(Collectors.toList());

        //查询OFC可以退的订单
        List<InterceptOrderDTO> interceptOrderDTOS = ofcQueryFacade.queryClouldInterceptOrder(distOrderEntities.stream().map(DistOrderConverter::entity2interceptDto).collect(Collectors.toList()));
        List<InterceptOrderDTO> failOrders = interceptOrderDTOS.stream()
                .filter(interceptOrderDTO -> StringUtils.isNotBlank(interceptOrderDTO.getFailedReason()))
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(failOrders)){
            throw new TmsRuntimeException(ErrorCodeEnum.INTERCEPT_ERROR, JSON.toJSONString(failOrders));
        }
        //tms拦截订单信息
        List<DistOrderInterceptDTO> distOrderInterceptDTOS = distOrderEntities.stream().map(DistOrderConverter::entity2InterceptDto).collect(Collectors.toList());
        //订单拦截信息
        List<DistOrderInterceptDTO> distOrderInterceptDTOList = distOrderService.interceptDistOrder(distOrderInterceptDTOS);

        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<String> queryOutContactId(Long deliverySiteId) {
        TmsAssert.notNull(deliverySiteId,ErrorCodeEnum.PARAM_NOT_NULL,"deliverySiteId");
        //查询这个点位的订单信息
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(Long.parseLong(deliverySiteId.toString()));

        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder()
                .deliveryTime(deliverySiteEntity.getPlanArriveTime().toLocalDate())
                .endSiteId(deliverySiteEntity.getSiteId()).build());

        return TmsResult.success(distOrderEntities.get(0).getDistClientVO().getOutContactId());
    }

    @Override
    public TmsResult<DeliverySiteDTO> queryPunch(DeliveryBatchDTO deliveryBatchDTO) {

        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.query(DeliverySiteQuery.builder()
                .batchId(deliveryBatchDTO.getDeliveryBatchId())
                .planArriveTime(deliveryBatchDTO.getDeliveryTime())
                .siteId(deliveryBatchDTO.getBeginSiteId())
                .build());

        //点位信息
        SiteEntity siteEntity = siteRepository.query(deliveryBatchDTO.getBeginSiteId());
        //点位是否需要打卡
        DeliverySiteDTO deliverySiteDTO = DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity);
        deliverySiteDTO.setNeedPunch(Objects.equals(siteEntity.getState(),DistSiteEnums.State.NEED_PUNCH.getValue()));
        deliverySiteDTO.setShouldPunchDistance(siteEntity.getPunchDistance());
        deliverySiteDTO.setShouldPunchPoi(siteEntity.getPoi());
        deliverySiteDTO.setSignInDistance(deliverySiteEntity.getSignInDistance());

        return TmsResult.success(deliverySiteDTO);
    }

    @Override
    public TmsResult<DeliverySiteDTO> punchDetail(Long deliverySiteId) {
        TmsAssert.notNull(deliverySiteId, ErrorCodeEnum.PARAM_NOT_NULL, "deliverySiteId");
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryWithSite(deliverySiteId);
        TmsAssert.notNull(deliverySiteEntity.getSiteId(), ErrorCodeEnum.NOT_FIND, "deliverySiteId="+deliverySiteId);
        return TmsResult.success(DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Void> toPunch(DeliverySitePunchCommand deliverySitePunchCommand) {
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryListWithSite(DeliverySiteQuery.builder().deliverySiteIds(deliverySitePunchCommand.getDeliverySiteIds()).build());
        TmsAssert.notEmpty(deliverySiteEntities, ErrorCodeEnum.NOT_FIND, "打卡点位不存在");
        TmsAssert.isTrue(deliverySitePunchCommand.getDeliverySiteIds().size() == deliverySiteEntities.size(), ErrorCodeEnum.NOT_FIND, "打卡点位异常");

        DeliverySiteEntity signDeliverySite = deliverySiteEntities.get(0);
        DeliveryBatchEntity signDeliveryBatch = deliveryBatchRepository.query(signDeliverySite.getDeliveryBatchId());
        if (signDeliveryBatch == null){
            throw new TmsRuntimeException("当前打卡点位所属调度单异常");
        }
        if (!signDeliveryBatch.inDeliveryIng()){
            throw new TmsRuntimeException("打卡点位所属调度单无需打卡");
        }
        // 非提货用车逻辑,出仓打卡校验
        if (!signDeliverySite.begin() && !Objects.equals(DeliveryBatchTypeEnum.all_category_pick.getCode(),signDeliveryBatch.getType())){
            Long deliveryBatchId = signDeliverySite.getDeliveryBatchId();
            Integer preSequence = signDeliverySite.getSequence() - 1;
            DeliverySiteEntity preDeliverySite = deliverySiteRepository.query(DeliverySiteQuery.builder().batchId(deliveryBatchId).sequence(preSequence).build());
            if (preDeliverySite == null || preDeliverySite.getSignOutStatus() == null){
                throw new TmsRuntimeException("请先完成上一个节点的出仓打卡");
            }
        }

        if (signDeliverySite.getSignInStatus() != null){
            throw new TmsRuntimeException("当前点位已完成到仓打卡");
        }
        //获取当前打卡时间
        LocalDateTime signTime = LocalDateTime.now();
        //获取当前打卡poi
        String signPoi = deliverySitePunchCommand.getSignPoi();
        //获取当前打卡地址
        String signAddress = deliverySitePunchCommand.getSignAddress();
        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            deliverySiteEntity.setSignInDistance(deliverySitePunchCommand.getSignDistance());
            deliverySiteEntity.setSignInPoi(signPoi);
            deliverySiteEntity.setSignInAddress(signAddress);
            deliverySiteEntity.setSignInPics(deliverySitePunchCommand.getSignPics());
            deliverySiteEntity.setSignInErrType(deliverySitePunchCommand.getSignErrType());
            deliverySiteEntity.setSignInRemark(deliverySitePunchCommand.getSignRemark());
            LocalDateTime planArriveTime = deliverySiteEntity.getPlanArriveTime();
            //设置打卡时间
            deliverySiteEntity.setSignInTime(signTime);
            //判断是否超时、超区,计算超时时间、超区距离
            long signDiffMinute = Duration.between(planArriveTime, signTime).toMinutes();
            //超出预计到达时间则超时打卡
            deliverySiteEntity.setSignInDiffMinute(signDiffMinute > 0 ? (int)signDiffMinute : null);
            SiteEntity siteEntity = deliverySiteEntity.getSiteEntity();
            double distanceBetweenPoi = DistanceUtil.getPoiDistance(siteEntity.getPoi(), signPoi);
            //超出1km则超区打卡
            double signDiffKm = (distanceBetweenPoi - 1000)/1000;
            deliverySiteEntity.setSignInDistance(new BigDecimal(distanceBetweenPoi));
            deliverySiteEntity.setSignInDiffKm(signDiffKm > 0 ? new BigDecimal(signDiffKm) : null);
            //判断到仓打卡是否异常
            deliverySiteEntity.setSignInStatus(signDiffMinute <= 0 && signDiffKm <= 0 ? 0 : 1);
            deliverySiteDomainService.signIn(deliverySiteEntity);
            if(Objects.equals(DeliveryBatchTypeEnum.all_category_pick.getCode(),signDeliveryBatch.getType())){
                // 【变更调度单状态】、【生成拣货任务】
                if(signDeliveryBatch.getStatus().getCode() == DeliveryBatchStatusEnum.TO_BE_PICKED.getCode()){
                    // 待配送状态打卡生成拣货任务
                    deliveryPickDomainService.createTrunkPickTask(signDeliverySite.getDeliveryBatchId());
                    // 变更为配送中
                    DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();
                    deliveryBatchEntity.setId(deliverySiteEntity.getDeliveryBatchId());
                    deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.IN_DELIVERY);
                    deliveryBatchRepository.update(deliveryBatchEntity);
                }
            }else{
                if (deliverySiteEntity.begin()) {
                    //起点到仓打卡则开始配送
                    DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();
                    deliveryBatchEntity.setId(deliverySiteEntity.getDeliveryBatchId());
                    deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.IN_DELIVERY);
                    deliveryBatchRepository.update(deliveryBatchEntity);
                }
            }
            // 防止提货用车 有提货任务 非提货用车类型的终点到仓可以直接完结
            if (deliverySiteEntity.end() && DeliveryBatchTypeEnum.all_category_pick.getCode() != signDeliveryBatch.getType()) {
                //终点到仓打卡则完成配送
                deliveryBatchService.finishDelivery(deliverySiteEntity.getDeliveryBatchId());
            }
        }
        if (Objects.equals(signDeliveryBatch.getType(), DeliveryBatchTypeEnum.trunk.getCode())) {
            if (deliverySiteEntities.stream().anyMatch(a -> Objects.equals(a.getSignInStatus(), DeliverySignEnum.NOT_NORMAL.getId()))) {
                deliveryMsgSender.sendSignMsg(signDeliverySite.getDeliveryBatchId(), deliverySiteEntities, true);
            }
        }

        return TmsResult.VOID_SUCCESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsResult<Void> outPunch(DeliverySitePunchCommand deliverySitePunchCommand) {
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryListWithSite(DeliverySiteQuery.builder().deliverySiteIds(deliverySitePunchCommand.getDeliverySiteIds()).build());
        TmsAssert.notEmpty(deliverySiteEntities, ErrorCodeEnum.NOT_FIND, "打卡点位不存在");
        TmsAssert.isTrue(deliverySitePunchCommand.getDeliverySiteIds().size() == deliverySiteEntities.size(), ErrorCodeEnum.NOT_FIND, "打卡点位异常");

        DeliverySiteEntity signDeliverySite = deliverySiteEntities.get(0);
        if (signDeliverySite.getSignInStatus() == null){
            throw new TmsRuntimeException("请先完成当前节点的到仓打卡");
        }
        if (signDeliverySite.getSignOutStatus() != null){
            throw new TmsRuntimeException("当前点位已完成出仓打卡");
        }
        //获取当前打卡时间
        LocalDateTime signTime = LocalDateTime.now();
        //获取当前打卡poi
        String signPoi = deliverySitePunchCommand.getSignPoi();
        //获取当前打卡地址
        String signAddress = deliverySitePunchCommand.getSignAddress();
        //查询调度单
        DeliveryBatchEntity deliveryBatch = deliveryBatchRepository.query(signDeliverySite.getDeliveryBatchId());

        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            deliverySiteEntity.setSignOutPoi(signPoi);
            deliverySiteEntity.setSignOutAddress(signAddress);
            deliverySiteEntity.setSignOutPics(deliverySitePunchCommand.getSignPics());
            deliverySiteEntity.setSealPics(deliverySitePunchCommand.getSealPics());
            deliverySiteEntity.setSignOutErrType(deliverySitePunchCommand.getSignErrType());
            deliverySiteEntity.setSignOutRemark(deliverySitePunchCommand.getSignRemark());
            deliverySiteEntity.setSignOutTemperature(deliverySitePunchCommand.getSignTemperature());
            //设置打卡时间
            deliverySiteEntity.setSignOutTime(signTime);
            //出仓打卡无超时 判断是否超区,计算超区距离
            SiteEntity siteEntity = deliverySiteEntity.getSiteEntity();
            double distanceBetweenPoi = DistanceUtil.getPoiDistance(siteEntity.getPoi(), signPoi);
            //超出1km则超区打卡
            double signDiffKm = (distanceBetweenPoi - 1000) / 1000;
            deliverySiteEntity.setSignOutDiffKm(signDiffKm > 0 ? new BigDecimal(signDiffKm) : null);
            LocalDateTime planOutTime = deliverySiteEntity.getPlanOutTime();
            long signDiffMinute = 0L;
            if (Objects.nonNull(planOutTime)) {
                //判断是否超时、超区,计算超时时间、超区距离
                signDiffMinute = Duration.between(planOutTime, signTime).toMinutes();
            }

            //判断出仓打卡是否异常
            deliverySiteEntity.setSignOutStatus(signDiffKm <= 0 && signDiffMinute <= 0 ? 0 : 1);
            deliverySiteEntity.setSignOutDiffMinute(signDiffMinute > 0 ? (int) signDiffMinute : null);
            deliverySiteDomainService.signOut(deliverySiteEntity);
        }
        
        // 终点提货用车 站点全部出仓完成则完结
        List<DeliverySiteEntity> batchHaveDeliverySiteList = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(signDeliverySite.getDeliveryBatchId()).build());
        List<DeliverySiteEntity> notHaveSignOutSites = batchHaveDeliverySiteList.stream()
                .filter(deliverySite -> deliverySite.getSignOutStatus() == null)
                .collect(Collectors.toList());
        if (DeliveryBatchTypeEnum.all_category_pick.getCode() == deliveryBatch.getType() && notHaveSignOutSites.size() == 0) {
            //提货用车 出仓打卡则完成配送
            deliveryBatchService.finishDelivery(signDeliverySite.getDeliveryBatchId());
        }

            if (deliverySiteEntities.stream().anyMatch(a -> Objects.equals(a.getSignOutStatus(), DeliverySignEnum.NOT_NORMAL.getId()))) {
                if (deliverySiteEntities.stream().allMatch(a -> !a.end())) {
                    deliveryMsgSender.sendSignMsg(signDeliverySite.getDeliveryBatchId(), deliverySiteEntities, false);
                }
            }

        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<String> poi2address(String poi) {
        TmsAssert.notEmpty(poi, ErrorCodeEnum.PARAM_NOT_NULL, "poi");
        TmsAssert.isTrue(poi.split(",").length == 2,ErrorCodeEnum.PARAM_ERROR,poi);
        String address = "";
        try {
            address = GaoDeUtil.getNameByGeoCode(poi);
        } catch (Exception e) {
            log.error("poi={},高德定位异常", poi, e);
        }
        return TmsResult.success(address);
    }

    @Override
    public TmsResult<DeliverySiteDTO> query(DeliverySiteQuery deliverySiteQuery) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.query(deliverySiteQuery);
        if(deliverySiteEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"点位信息,请刷新页面重试");
        }
        return TmsResult.success(DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity));
    }

    @Override
    public DeliverySiteDTO queryById(Long deliverySiteId) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteId);
        if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"点位信息,请刷新页面重试");
        }
        return DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity);
    }

    @Override
    public TmsResult<Boolean> isHaveExchange(Long deliverySiteId) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteId);
        LocalDateTime planArriveTime = deliverySiteEntity.getPlanArriveTime();

        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder()
                .deliveryTime(planArriveTime.toLocalDate())
                .endSiteId(deliverySiteEntity.getSiteId())
                .build());

        List<DistOrderEntity> exchangeDistOrderList = distOrderEntities.stream()
                .filter(distOrderEntity -> DistOrderStatusEnum.validNotCompleteStatus().contains(distOrderEntity.getStatus().getCode()))
                .filter(distOrderEntity -> Objects.equals(distOrderEntity.getDistFlowVO().getType(),DistTypeEnum.DELIVERY_AND_RECYCLE.getCode()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(exchangeDistOrderList)){
            return TmsResult.success(false);
        }
        return TmsResult.success(true);
    }


    @Override
    public TmsResult<DeliverySiteDTO> queryDeliverySiteDetailApp(Long deliverySiteId) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteId);
        if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"没有此配送单id信息");
        }

        // 没有完成配送的才有“上传保温措施”的校验
        if (!Objects.equals(deliverySiteEntity.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY)) {
            Boolean needUploadKeepTemperatureMethodPic = cityDeliveryQueryService.queryNeedUploadKeepTemperatureMethodPic(NeedUploadKeepTemperatureMethodPicQueryInput.builder()
                    .batchId(deliverySiteEntity.getDeliveryBatchId())
                    .build());
            if (needUploadKeepTemperatureMethodPic != null && needUploadKeepTemperatureMethodPic) {
                throw new TmsRuntimeException("上传保温措施图片才能查看配送详情");
            }
        }

        //获取配送时效
        deliverySiteEntity.setTimeFrame(deliveryAlertDomainService.queryDeliveryAlertTimeFrame(deliverySiteEntity));
        //批次信息
        DeliveryBatchEntity batch = deliveryBatchRepository.query(deliverySiteEntity.getDeliveryBatchId());
        //委托单集合
        List<DistOrderEntity> distOrderEntityList = distOrderDomainService.queryValidDistOrder(Collections.singletonList(deliverySiteEntity.getSiteId()), deliverySiteEntity.getPlanArriveTime().toLocalDate(),batch.getBeginSiteId());
        deliverySiteEntity.setSiteDistOrders(distOrderEntityList);
        //设置到店打卡信息
        deliverySiteDomainService.checkinPunchRecord(deliverySiteEntity);
        //点位配送详情
        List<DeliverySiteItemEntity> deliverySiteItemEntities = deliverySiteItemRepository.queryListWithCodeWithRecycle(DeliverySiteItemQuery.builder()
                .deliverySiteId(deliverySiteEntity.getId())
                .build());

        batch.setBeginSiteEntity(siteRepository.query(batch.getBeginSiteId()));
        deliverySiteEntity.setDeliveryBatchEntity(batch);
        //点位信息
        SiteEntity endSiteEntity = siteRepository.query(deliverySiteEntity.getSiteId());
        deliverySiteEntity.setSiteEntity(endSiteEntity);
        //地址调整
        if(CollectionUtils.isNotEmpty(distOrderEntityList)){
            ContactAdjustEntity contactAdjustEntity = contactAdjustRepository.query(ContactAdjustQuery.builder()
                    .contactId(distOrderEntityList.get(0).getDistClientVO().getOutContactId()).build());
            deliverySiteEntity.setContactStatus(contactAdjustEntity.getStatus());
        }
        //报销信息
        deliverySiteEntity.setExpenseEntity(expenseRepository.queryByDeliverySiteId(deliverySiteEntity.getId()));
        //配送订单信息
        deliverySiteEntity.setSiteDistOrders(distOrderEntityList);
        //委托单集合
        List<Long> distOrderIdList = distOrderEntityList.stream().map(DistOrderEntity::getDistId).collect(Collectors.toList());
        //查询加工数据
        List<DistOrderEntity> distOrderEntityWithItem = distOrderRepository.queryValidListWithItemByIds(distOrderIdList);
        Map<String, List<WmsOrderProcessDetailDTO>> skuProcessDetailMap = distOrderDomainService.queryProcessOutItemMap(distOrderEntityWithItem);
        //获取非包裹的sku信息
        List<String> skus = deliverySiteItemEntities.stream()
                .filter(item -> Objects.equals(item.getPackType(),DistItemEnums.PackType.SINGLE_CATEGORY.getValue()))
                .map(DeliverySiteItemEntity::getOutItemId).collect(Collectors.toList());
        List<SkuDTO> skuDTOS = wmsQueryFacade.batchQueryBySkus(skus);
        //查询wms条码信息
        List<SkuBarcodeDTO> skuBarcodeDTOS = wmsQueryFacade.querySkuBarcode(skus);

        Map<String, List<String>> skuBarcodeMap = Optional.ofNullable(skuBarcodeDTOS).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(SkuBarcodeDTO::getSku, SkuBarcodeDTO::getBarcodes, (oldValue, newValue) -> newValue));
        Map<String, String> skuMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getPicturePath, (oldValue, newValue) -> newValue));
        Map<String, Integer> skuTypeMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getType, (oldValue, newValue) -> newValue));
        Map<String, String> skuNameRemakes = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getNameRemakes, (oldValue, newValue) -> newValue));
        Map<String, Integer> skuExTypeMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getExtType, (oldValue, newValue) -> newValue));
        Map<String, Integer> storageAreaMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getStorageArea, (oldValue, newValue) -> newValue));

        deliverySiteItemEntities.forEach(item -> {
            if (skuMap.get(item.getOutItemId()) != null && Objects.equals(item.getPackType(),DistItemEnums.PackType.SINGLE_CATEGORY.getValue())) {
                item.setSkuPic(skuMap.get(item.getOutItemId()));
                item.setSkuType(skuTypeMap.get(item.getOutItemId()));
                item.setNameRemakes(skuNameRemakes.get(item.getOutItemId()));
                item.setExtType(skuExTypeMap.get(item.getOutItemId()));
                item.setStorageArea(storageAreaMap.get(item.getOutItemId()));
                item.setBarcodes(skuBarcodeMap.get(item.getOutItemId()));
            }
            //需要过滤 回收数据 回收没有加工数据
            if (Objects.equals(item.getType(), DeliverySiteItemTypeEnum.RECYCLE.getCode())) {
                return;
            }
            List<WmsOrderProcessDetailDTO> wmsOrderProcessDetailDTOs = skuProcessDetailMap.get(item.getOutItemId());
            if(!CollectionUtils.isEmpty(wmsOrderProcessDetailDTOs)){
                //加工信息
                List<DeliverySiteProcessItemEntity> deliverySiteProcessItemEntityList = wmsOrderProcessDetailDTOs.stream().map(DeliverySiteItemDTOConverter::dto2ProcessItemEntity).collect(Collectors.toList());
                List<DeliverySiteProcessItemEntity> productSkuQuantityEntityList = new ArrayList<>(deliverySiteProcessItemEntityList);

                int processCount = productSkuQuantityEntityList.stream()
                        .filter(process->process.getHaveProductSkuQuantity() != null)
                        .mapToInt(DeliverySiteProcessItemEntity::getHaveProductSkuQuantity).sum();
                if(item.getPlanReceiptCount() - item.getInterceptCount() - processCount > 0){
                    //未加工信息
                    DeliverySiteProcessItemEntity deliverySiteProcessItemEntity = new DeliverySiteProcessItemEntity();
                    deliverySiteProcessItemEntity.setQuantity(item.getPlanReceiptCount() - item.getInterceptCount() - processCount);
                    deliverySiteProcessItemEntity.setProcessFlag(1);
                    productSkuQuantityEntityList.add(deliverySiteProcessItemEntity);
                }
                item.setDeliverySiteProcessItemEntityList(productSkuQuantityEntityList);
            }

        });
        deliverySiteEntity.setDeliverySiteItemEntityList(deliverySiteItemEntities);
        //是否需要扫码判断
        deliverySiteDomainService.isNeedScanCode(deliverySiteEntity,distOrderEntityList);
        //设置拣货缺货信息
        tmsDeliveryPickShortOrderMappingCommandDomainService.installDeliverySitePickLackInfo(batch, Collections.singletonList(deliverySiteEntity));

        DeliverySiteDTO deliverySiteDTO = DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity);
        //查询BD信息
        if(endSiteEntity != null && Objects.equals(endSiteEntity.getType(),TmsSiteTypeEnum.CUSTOMER.getCode())){
            BdDTO bdDTO = null;
            if(CollectionUtils.isNotEmpty(distOrderEntityList)){
                try {
                    bdDTO = crmQueryFacade.queryContactBd(Long.parseLong(distOrderEntityList.get(0).getDistClientVO().getOutContactId()));
                } catch (Exception e) {
                    log.error("获取BD信息异常",e);
                }
            }
            deliverySiteDTO.setBdName(bdDTO == null ? "" : bdDTO.getBdName());
            deliverySiteDTO.setBdPhone(bdDTO == null ? "" : bdDTO.getBdPhone());
        }
        String sitePics = endSiteEntity == null ? null : endSiteEntity.getSitePics();
        sitePics = StrUtil.isNotBlank(sitePics) ? sitePics : deliverySiteDomainService.querySiteRecentHeadPic(deliverySiteEntity.getSiteId());
        //查询点位历史门店抬头数据
        deliverySiteDTO.setRecentHeadPic(sitePics);

        return TmsResult.success(deliverySiteDTO);
    }

    @Override
    public TmsResult<DeliverySiteDTO> queryDeliverySiteDetail(Long deliverySiteId) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteId);
        if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"没有此配送单id信息");
        }
        deliverySiteEntity.setTimeFrame(deliveryAlertDomainService.queryDeliveryAlertTimeFrame(deliverySiteEntity));
        //设置到店打卡信息
        deliverySiteDomainService.checkinPunchRecord(deliverySiteEntity);
        //批次信息
        DeliveryBatchEntity batch = deliveryBatchRepository.getBatchDetail(deliverySiteEntity.getDeliveryBatchId());
        //委托单集合
        List<DistOrderEntity> distOrderEntityList = distOrderDomainService.
                queryValidDistOrder(Collections.singletonList(deliverySiteEntity.getSiteId()), deliverySiteEntity.getPlanArriveTime().toLocalDate(),batch.getBeginSiteId());

        //点位配送详情
        List<DeliverySiteItemEntity> deliverySiteItemEntities = deliverySiteItemRepository.queryListWithCodeWithRecycle(DeliverySiteItemQuery.builder()
                .deliverySiteId(deliverySiteEntity.getId())
                .build());

        //获取sku信息
        List<String> skus = deliverySiteItemEntities.stream().map(DeliverySiteItemEntity::getOutItemId).collect(Collectors.toList());
        List<SkuDTO> skuDTOS = wmsQueryFacade.batchQueryBySkus(skus);
        //查询wms条码信息
        List<SkuBarcodeDTO> skuBarcodeDTOS = wmsQueryFacade.querySkuBarcode(skus);

        Map<String, String> skuMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getPicturePath, (oldValue, newValue) -> newValue));
        Map<String, Integer> storageAreaMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getStorageArea, (oldValue, newValue) -> newValue));
        Map<String, List<String>> skuBarcodeMap = Optional.ofNullable(skuBarcodeDTOS).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(SkuBarcodeDTO::getSku, SkuBarcodeDTO::getBarcodes, (oldValue, newValue) -> newValue));

        deliverySiteItemEntities.forEach(item -> {
            if (skuMap.get(item.getOutItemId()) != null) {
                item.setStorageArea(storageAreaMap.get(item.getOutItemId()));
                item.setBarcodes(skuBarcodeMap.get(item.getOutItemId()));
            }
            //新增了Temperature 查询为过渡阶段使用
            if(item.getTemperature() != null){
                item.setStorageArea(item.getTemperature());
            }
        });
        
        if(batch.getCreateId() != null){
            String adminName = authExtService.getAdminNameById(Long.parseLong(batch.getCreateId().toString()));
            batch.setCreateName(adminName);
        }
        batch.setBeginSiteEntity(siteRepository.query(batch.getBeginSiteId()));
        deliverySiteEntity.setDeliveryBatchEntity(batch);
        //点位信息
        deliverySiteEntity.setSiteEntity(siteRepository.query(deliverySiteEntity.getSiteId()));
        //配送订单信息
        deliverySiteEntity.setSiteDistOrders(distOrderEntityList);
        deliverySiteEntity.setDeliverySiteItemEntityList(deliverySiteItemEntities);
        DeliverySiteDTO deliverySiteDTO = DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity);
        return TmsResult.success(deliverySiteDTO);
    }

    @Override
    public DeliverySiteDTO finishDeliverySiteQuery(DeliverySiteQuery deliverySiteQuery) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryWithItems(deliverySiteQuery);
        return DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> changeSpecialCarSendWay(DeliverySiteDTO deliverySiteDTO) {
        if(Objects.equals(deliverySiteDTO.getSendWay(),DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())){
            return TmsResult.VOID_SUCCESS;
        }
        //在计划配送时间之后不能在变成专车配送
        if(LocalDate.now().isAfter(deliverySiteDTO.getPlanArriveTime().toLocalDate())){
            throw new TmsRuntimeException(ErrorCodeEnum.NO_CHANGE_SPECIAL_SEND);
        }
        //点位变为专车配送
        deliverySiteDomainService.changeSpecialCarSendWay(DeliverySiteDTOConverter.dto2Entity(deliverySiteDTO));
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(deliverySiteDTO.getDeliveryBatchId());
        if(deliveryBatchEntity == null){
            throw new TmsRuntimeException("当前点位批次不存在");
        }
        //只有专线专车才需要智能排线
        List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
        if(CollectionUtils.isEmpty(deliverySiteList)){
            throw new TmsRuntimeException("批次不存在配送站点");
        }
        List<DeliverySiteEntity> normalSendList = deliveryBatchEntity.getNormalSendDeliverySiteList();
        List<DeliverySiteEntity> specialSendList = deliveryBatchEntity.getSpecialSendDeliverySiteList();
        if(CollectionUtils.isEmpty(normalSendList) && CollectionUtils.isNotEmpty(specialSendList)){
            //智能排线
            deliverySiteDomainService.intelligentPath(deliveryBatchEntity,false);
        }

        //点位变更专车配送
        deliveryBatchDomainService.changeSpecialCarSendWay(deliveryBatchEntity);

        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<Boolean> isBeginDelivery(Long batchId) {
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(batchId);
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.query(DeliverySiteQuery.builder()
                .batchId(batchId)
                .planArriveTime(deliveryBatchEntity.getDeliveryTime())
                .siteId(deliveryBatchEntity.getBeginSiteId())
                .build());
        LocalDateTime signOutTime = deliverySiteEntity.getSignOutTime();
        if(signOutTime == null){
            return TmsResult.success(true);
        }
        return TmsResult.success(false);
    }

    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> siteShort(SiteShortCommand siteShortCommand) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryWithItems(siteShortCommand.getDeliverySiteId());
        if(deliverySiteEntity.getStatus() == DeliverySiteStatusEnum.FINISH_DELIVERY){
            throw new TmsRuntimeException(ErrorCodeEnum.DB_DATA_ERROR,"此点位"+deliverySiteEntity.getStatus().getName());
        }
        List<DeliverySiteItemEntity> deliverySiteItemEntityList = deliverySiteEntity.getDeliverySiteItemEntityList();
        for (DeliverySiteItemEntity deliverySiteItemEntity : deliverySiteItemEntityList) {
            if(Objects.equals(deliverySiteItemEntity.getId(),siteShortCommand.getDeliverySiteItemId() )){
                deliverySiteItemEntity.setShortCount(siteShortCommand.getShortCount());
            }
        }
        deliverySiteDomainService.siteShort(deliverySiteEntity);
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public DeliverySiteDTO queryWithSiteBatchById(Long deliverySiteId) {
        TmsAssert.notNull(deliverySiteId, ErrorCodeEnum.PARAM_NOT_NULL, "deliverySiteId");
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.query(DeliverySiteQuery.builder().deliverySiteId(deliverySiteId).build());
        if(deliverySiteEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"点位信息,请刷新页面重试");
        }
        deliverySiteEntity.setSiteEntity(siteRepository.query(deliverySiteEntity.getSiteId()));
        if(deliverySiteEntity.getDeliveryBatchId() != null){
            deliverySiteEntity.setDeliveryBatchEntity(deliveryBatchRepository.query(deliverySiteEntity.getDeliveryBatchId()));
        }
        return DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity);
    }

    @Async
    @Override
    public void finishPickNotifyEvents(Long deliveryPickId) {
        DeliveryPickEntity deliveryPickEntity = deliveryPickRepository.query(deliveryPickId);
        if(deliveryPickEntity == null){
            return;
        }
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.queryWithDriver(deliveryPickEntity.getDeliveryBatchId());
        //发送 委托单完成拣货消息
        SiteEntity citySiteEntity = siteDomainService.query(deliveryBatchEntity.getBeginSiteId());
        //配送点位
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryListWithSite(DeliverySiteQuery.builder()
                .batchId(deliveryBatchEntity.getId())
                .build());
        //过滤城配点位
        deliverySiteEntities = deliverySiteEntities.stream()
                .filter(site -> !Objects.equals(site.getSiteId(), deliveryBatchEntity.getBeginSiteId()))
                .collect(Collectors.toList());

        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryListWithItem(DeliveryOrderQuery.builder()
                    .batchId(deliveryPickEntity.getDeliveryBatchId())
                    .endSiteId(deliverySiteEntity.getSiteId())
                    .build());
            //过滤关闭的委托单
            deliveryOrderEntities = deliveryOrderEntities.stream()
                    .filter(distOrder -> distOrder.getStatus() != DeliveryOrderStatusEnum.CLOSE)
                    .collect(Collectors.toList());

            eventBusService.notifyDeliveryEvent(TmsDeliveryEventConverter.buildTmsDeliveryEvent(EventTypeEnum.IN_DELIVERY,
                    deliveryBatchEntity, deliverySiteEntity, deliveryOrderEntities, null,citySiteEntity));
        }

        // 发送拣货缺货消息
        List<TmsDeliveryPickShortOrderMappingEntity> deliveryPickShortOrderMappingEntities = tmsDeliveryPickShortOrderMappingQueryRepository.listByDeliveryBatchId(deliveryBatchEntity.getId());
        if (CollectionUtils.isNotEmpty(deliveryPickShortOrderMappingEntities)) {
            List<DeliveryPickLackGoodsMessage> deliveryPickLackGoodsMessages = DeliveryPickLackGoodsConverter.buildDeliveryPickLackGoodsMessageList(deliveryBatchEntity, deliveryPickShortOrderMappingEntities);
            eventBusService.notifyDeliveryPickLackGoodsMessage(deliveryPickLackGoodsMessages);
        }
    }

    @Override
    public List<TmsDriverAppraiseDTO> queryDriverAppraise(Long siteId) {
        //根据点位查询配送订单信息
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryWithDistOrders(siteId);
        if(deliverySiteEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,siteId);
        }
        List<DistOrderEntity> distOrderEntityList = deliverySiteEntity.getSiteDistOrders();
        if(CollectionUtils.isEmpty(distOrderEntityList)){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"委托单信息");
        }
        //鲜沐订单
        List<DistOrderEntity> xmMallDistList = distOrderEntityList.stream()
                .filter(distOrderEntity -> distOrderEntity.getSource() == DistOrderSourceEnum.XM_MALL)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(xmMallDistList)){
            return Collections.emptyList();
        }

        List<DriverAppraiseDTO> driverAppraiseDTOS = new ArrayList<>();
        xmMallDistList.forEach(xmDist ->{
            DriverAppraiseDTO driverAppraiseDTO = mallQueryFacade.queryOrderDriverAppraise(DriverAppraiseInput.builder()
                    .contactId(xmDist.getDistClientVO().getOutContactId())
                    .deliveryTime(xmDist.getDistFlowVO().getExpectBeginTime().toLocalDate())
                    .orderNo(xmDist.getDistClientVO().getOutOrderId())
                    .build()
            );
            if(driverAppraiseDTO != null){
                driverAppraiseDTO.setOrderNo(xmDist.getDistClientVO().getOutOrderId());
                driverAppraiseDTOS.add(driverAppraiseDTO);
            }
        });

        return driverAppraiseDTOS.stream().map(TmsDriverAppraiseDTOConverter::driverAppraiseDTO2tms).collect(Collectors.toList());
    }

    @Override
    public DeliverySiteDTO queryDeliverySiteSendDetail(Long batchId,Long siteId) {
        if(batchId == null || siteId == null){
            return null;
        }
        //查询批次司机、车辆信息、路线、承运商
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryWithSiteWithBase(DeliveryBatchQuery.builder().batchId(batchId).build());
        if(CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return null;
        }
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchEntityList.get(0);
        if(deliveryBatchEntity.getId() == null){
            return null;
        }
        //根据点位查询点位信息
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryWithItems(DeliverySiteQuery.builder()
                .batchId(deliveryBatchEntity.getId())
                .siteId(siteId)
                .build());
        if(deliverySiteEntity == null){
            return null;
        }
        deliverySiteEntity.setDeliveryBatchEntity(deliveryBatchEntity);

        return DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sendRemarkHandle(Long deliveryBatchId, Long siteId, String sendRemark) {
        //根据批次和点位查询配送点位ID
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryForceMasterByBatchIdAndSiteId(deliveryBatchId,siteId);
        deliverySiteEntity.setSendRemark(sendRemark);
        //更新配送备注
        deliverySiteRepository.update(deliverySiteEntity);
    }

    @Override
    public void siteTemConditionsCreate(List<Long> deliveryBatchIds) {
        TmsAssert.notEmpty(deliveryBatchIds, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryBatchIds");
        for (Long deliveryBatchId : deliveryBatchIds) {
            deliverySiteDomainService.siteTemConditionByBatchId(deliveryBatchId);
        }
    }

    @Override
    public DeliverySiteDTO querySiteWithSiteItem(DeliverySiteQuery query) {
        return DeliverySiteDTOConverter.entity2Dto(deliverySiteRepository.queryWithItemsWithRecycle(query));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cheakinPunch(CheakInPunchSaveCommand cheakInPunchSaveCommand) {
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(cheakInPunchSaveCommand.getDeliverySiteId());
        if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            throw new TmsRuntimeException("不存在此点位配送信息");
        }
        DeliverySiteCheckinPunchEntity entity = new DeliverySiteCheckinPunchEntity();

        entity.setDeliverySiteId(cheakInPunchSaveCommand.getDeliverySiteId());
        entity.setPunchRange(cheakInPunchSaveCommand.getPunchRange());
        entity.setPunchAddress(cheakInPunchSaveCommand.getPunchAddress());
        entity.setPunchPoi(cheakInPunchSaveCommand.getPunchPoi());
        entity.setDistanceToSite(cheakInPunchSaveCommand.getDistanceToStore());
        entity.setExceedReason(cheakInPunchSaveCommand.getExceedReason());

        deliverySiteDomainService.cheakinPunch(entity);
    }

    @Override
    public String queryOrderNoByCode(String code) {
        if(StringUtil.isBlank(code)){
            return "";
        }
        DeliverySiteItemCodeEntity itemCodeEntity = deliverySiteItemCodeRepository.queryByOnlyCode(code);
        if(itemCodeEntity == null){
            return "";
        }
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(itemCodeEntity.getDeliverySiteId());
        if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            return "";
        }
        //根据siteId和batchId 查询
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryListWithItem(DeliveryOrderQuery.builder()
                .batchId(deliverySiteEntity.getDeliveryBatchId())
                .endSiteId(deliverySiteEntity.getSiteId())
                .deliveryTime(deliverySiteEntity.getPlanArriveTime().toLocalDate())
                .build());

        if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
            return "";
        }
        //过滤商品属性找出第一个订单信息
        Map<String, List<DeliveryItemEntity>> orderNoItemListMap = deliveryOrderEntityList.stream()
                .filter(order -> Objects.equals(DeliveryOrderTypeEnum.send.getCode(),order.getType()))
                .collect(Collectors.toMap(DeliveryOrderEntity::getOuterOrderId, DeliveryOrderEntity::getDeliveryItemEntityList));

        String codeOrderNo = null;
        for (String orderNo : orderNoItemListMap.keySet()) {
            List<DeliveryItemEntity> deliveryItemEntities = orderNoItemListMap.get(orderNo);
            if(CollectionUtils.isEmpty(deliveryItemEntities)){
                continue;
            }
            List<String> skuList = deliveryItemEntities.stream().map(DeliveryItemEntity::getOutItemId).collect(Collectors.toList());
            if(skuList.contains(itemCodeEntity.getOutItemId())){
                codeOrderNo = orderNo;
                break;
            }
        }

        return codeOrderNo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sameDeliverySiteHandle() {
        //根据配送时间查询相同起点、终点、配送时间多个配送点位补偿处理
        LocalDateTime deliveryTime = LocalDate.now().atStartOfDay().plusDays(1);
        Boolean result = deliverySiteRepository.sameDeliverySiteHandle(deliveryTime);
        if(result){
            log.info("存在相同起点、终点、配送时间多个配送点位数据,已自动处理");
        }
    }

    @Override
    public SkuUnitDTO querySkuUnit(SkuUnitQuery skuUnitQuery) {
        SkuUnitDTO skuUnitDTO = new SkuUnitDTO();
        List<GoodsDTO> goodsDTOList;
        try {
            goodsDTOList = goodCenterQueryFacade.querySkuList(GoodsInput.builder().skus(Collections.singletonList(skuUnitQuery.getSku())).build());
        }catch (Exception e){
            skuUnitDTO.fallbackInit(skuUnitQuery.getSku());
            return skuUnitDTO;
        }
        if (CollectionUtils.isEmpty(goodsDTOList)){
            throw new BizException("无效sku");
        }
        GoodsDTO goodsDTO = goodsDTOList.get(0);
        skuUnitDTO.dataInit(skuUnitQuery.getSku(), goodsDTO.getSpecificationUnit(), goodsDTO.getBasicSpecUnit());
        return skuUnitDTO;
    }

    @Override
    public void siteItemStatusDataInit() {
        log.info("配送点位物品状态数据开始初始化");
        Boolean result = deliverySiteItemRepository.siteItemStatusDataInit();
        if(result){
            log.info("配送点位物品状态数据已全部初始化完成");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void interceptStateCheak(Long deliveryBatchId, Long endSiteId) {
        try {
            if(deliveryBatchId == null || endSiteId == null){
                return;
            }
            //根据批次和点位查询配送点位ID
            DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryForceMasterByBatchIdAndSiteId(deliveryBatchId,endSiteId);
            if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
                return;
            }
            //状态不是未配送的点位不处理
            if(deliverySiteEntity.getStatus() != DeliverySiteStatusEnum.NO){
                return;
            }
            DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliverySiteEntity.getDeliveryBatchId());
            if(deliveryBatchEntity == null || deliveryBatchEntity.getStatus() != DeliveryBatchStatusEnum.TO_BE_WIRED){
                return;
            }

            DeliverySiteInterceptStateEnum dSiteInterceptStateEnum = DeliverySiteInterceptStateEnum.getDeliverySiteStateByCode(deliverySiteEntity.getInterceptState());
            //查询点位下面的需要配送的订单
            List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                    .batchId(deliverySiteEntity.getDeliveryBatchId())
                    .beginSiteId(deliveryBatchEntity.getBeginSiteId())
                    .endSiteId(deliverySiteEntity.getSiteId())
                    .deliveryTime(deliverySiteEntity.getPlanArriveTime().toLocalDate())
                    .type(DeliveryOrderTypeEnum.send.getCode())
                    .build());
            log.info("点位信息:{},批次信息:{},单据信息deliveryOrderEntityList:{}"
                    ,JSON.toJSONString(deliverySiteEntity)
                    ,JSON.toJSONString(deliveryBatchEntity)
                    ,JSON.toJSONString(deliveryOrderEntityList));

            if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
                return;
            }
            List<DeliveryOrderStatusEnum> allOrderStatusEnum = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getStatus).collect(Collectors.toList());
            //点位全部拦截，订单还存在正常的单据，说明有全品类拦截，需要处理为部分拦截
            if(dSiteInterceptStateEnum == DeliverySiteInterceptStateEnum.cancel && allOrderStatusEnum.contains(DeliveryOrderStatusEnum.NO_SIGN)){
                DeliverySiteEntity deliverySiteEntityUp = new DeliverySiteEntity();
                deliverySiteEntityUp.setId(deliverySiteEntity.getId());
                deliverySiteEntityUp.setInterceptState(DeliverySiteInterceptStateEnum.partIntecept.getCode());
                deliverySiteRepository.update(deliverySiteEntityUp);
            }
            //点位部分拦截，订单只有一个关闭的单据，需要处理为全部拦截
            if(dSiteInterceptStateEnum == DeliverySiteInterceptStateEnum.partIntecept && allOrderStatusEnum.size() == 1 && allOrderStatusEnum.contains(DeliveryOrderStatusEnum.CLOSE)){
                //拦截取消的需要移除到未排线批次里面
                try {
                    this.siteRemove(deliverySiteEntity.getId());
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("订单拦截检查异常移除点位异常",e);
                }
                //修改点位状态
                DeliverySiteEntity deliverySiteEntityUp = new DeliverySiteEntity();
                deliverySiteEntityUp.setId(deliverySiteEntity.getId());
                deliverySiteEntityUp.setInterceptState(DeliverySiteInterceptStateEnum.cancel.getCode());
                deliverySiteRepository.update(deliverySiteEntityUp);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("订单拦截检查异常",e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliverySiteWithNoOrderHandle() {
        //存在配送点位无对应配送单补偿处理
        LocalDateTime deliveryTime = LocalDate.now().atStartOfDay().plusDays(1);
        Boolean result = deliverySiteRepository.deliverySiteWithNoOrderHandle(deliveryTime);
        if(result){
            log.info("存在配送点位无对应配送单数据,已自动处理");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String pickScan(PickScanCommand command) {
        DeliveryPickScanCodeEntity deliveryPickScanCodeEntity = deliveryPickScanCodeCommandDomainService.pickScan(DeliveryPickScanCodeEntity.builder()
                .deliveryBatchId(command.getBatchId())
                .onlyCode(command.getOnlyCode())
                .build());
        return deliveryPickScanCodeEntity != null ? deliveryPickScanCodeEntity.getOutItemId() : null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pickShort(PickShortCommand command) {
        // 拣货单更新缺货数据
        deliveryPickDomainService.pickShort(command.getDeliveryPickId(), command.getShortCnt());
    }

    @Override
    public List<DeliveryPickDTO> queryPickUpDetail(Long deliverySiteId) {
        if(deliverySiteId == null){
            return Collections.emptyList();
        }
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryById(deliverySiteId);
        if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            throw new TmsRuntimeException("站点信息不存在");
        }
        // 获取干线拣货信息
        Map<Long, List<DeliveryPickEntity>> deliverySiteIdDeliveryPickEntityMap = deliveryPickDomainService.queryTrunkPickDetailByBathIds(Collections.singletonList(deliverySiteEntity.getDeliveryBatchId()));

        List<DeliveryPickEntity> deliveryPickEntities = deliverySiteIdDeliveryPickEntityMap.get(deliverySiteId);
        if(CollectionUtils.isEmpty(deliveryPickEntities)){
            return Collections.emptyList();
        }
        List<String> skus = deliveryPickEntities.stream().map(DeliveryPickEntity::getOutItemId).collect(Collectors.toList());

        // 查询wms条码信息
        List<SkuBarcodeDTO> skuBarcodeDTOS = wmsQueryFacade.querySkuBarcode(skus);
        // 查询货品信息
        List<SkuDTO> skuDTOS = wmsQueryFacade.batchQueryBySkus(skus);

        Map<String, Integer> storageMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getStorageArea, (oldValue, newValue) -> newValue));
        Map<String, Integer> skuExTypeMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getExtType,(oldValue, newValue) -> newValue));
        Map<String, String> skuPicMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku,SkuDTO::getPicturePath, (oldValue, newValue) -> newValue));
        Map<String, List<String>> skuBarcodeMap = Optional.ofNullable(skuBarcodeDTOS).orElse(com.google.common.collect.Lists.newArrayList()).stream().collect(Collectors.toMap(SkuBarcodeDTO::getSku, SkuBarcodeDTO::getBarcodes, (oldValue, newValue) -> newValue));

        deliveryPickEntities.forEach(item -> {
            item.setSkuPic(skuPicMap.get(item.getOutItemId()));
            item.setBarcodes(skuBarcodeMap.get(item.getOutItemId()));
            item.setStorageArea(storageMap.get(item.getOutItemId()));
            item.setExtType(skuExTypeMap.get(item.getOutItemId()));
        });
        List<DeliveryPickDTO> deliveryPickDTOS = deliveryPickEntities.stream().map(DeliveryPickDTOConverter::entity2Dto).collect(Collectors.toList());
        deliveryPickDTOS.forEach(item -> {
            item.setDeliverySiteStatus(deliverySiteEntity.getStatus().getCode());
        });
        return deliveryPickDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void trunkPickUpFinish(PickUpFinishUpdateCommand command) {
        //校验数据
        if(command == null || command.getDeliverySiteId() == null || CollectionUtils.isEmpty(command.getPickUpFinishDetailInputs())){
            throw new TmsRuntimeException("参数异常");
        }
        List<PickUpFinishDetailInput> pickUpFinishDetailInputs = command.getPickUpFinishDetailInputs();
        pickUpFinishDetailInputs.forEach(item -> {
            if(item.getDetailStatus() == null || item.getSku() == null || item.getDeliveryPickId() == null){
                throw new TmsRuntimeException("拣货状态、SKU、拣货ID均不能为空");
            }
            if(item.getDetailStatus() == 0 && item.getShortQuantity() == null){
                throw new TmsRuntimeException(item.getSku() + "拣货缺货,缺货数量不能为空");
            }
        });
        //组装报文
        List<DeliveryPickEntity> deliveryPickEntityList = new ArrayList<>();
        pickUpFinishDetailInputs.forEach(item -> {
            DeliveryPickEntity deliveryPickEntity = new DeliveryPickEntity();
            deliveryPickEntity.setId(item.getDeliveryPickId());
            deliveryPickEntity.setOutItemId(item.getSku());
            deliveryPickEntity.setShortQuantity(item.getShortQuantity());

            deliveryPickEntityList.add(deliveryPickEntity);
        });

        // 点位完成拣货
        deliverySiteDomainService.trunkFinishPick(command.getDeliverySiteId(),deliveryPickEntityList);
        // 缺货事务后需要发送消息给PMS
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                DeliverySiteEntity deliverySite = deliverySiteDomainService.queryTrunkSiteOrderById(command.getDeliverySiteId());
                if(deliverySite == null || deliverySite.getId() == null){
                    log.error("配送站点点位信息不存在,DeliverySiteId:{}",command.getDeliverySiteId(),new BizException("配送站点点位信息不存在"));
                    return;
                }
                if(CollectionUtils.isEmpty(deliverySite.getSiteDistOrders())){
                    log.error("配送站点点位信息不存在委托单信息,DeliverySiteId:{}",command.getDeliverySiteId(),new BizException("配送站点点位信息不存在委托单信息"));
                    return;
                }
                SiteEntity siteEntity = siteDomainService.query(deliverySite.getSiteId());
                if(siteEntity == null){
                    log.error("配送点位信息不存在,siteId:{}",deliverySite.getSiteId(),new BizException("配送点位信息不存在"));
                    return;
                }

                List<TrunkBatchPickUpSkuShort> trunkBatchPickUpSkuShorts = pickUpFinishDetailInputs.stream()
                        .filter(item -> item.getShortQuantity() != null && item.getShortQuantity() != 0)
                        .map(TrunkBatchPickUpShortGoodsConverter::pickUpFinishDetailInput2Msg).collect(Collectors.toList());

                List<TrunkBatchPickUpDistOrder> distOrders = deliverySite.getSiteDistOrders().stream()
                        .map(TrunkBatchPickUpShortGoodsConverter::distOrderEntity2Msg).collect(Collectors.toList());

                // 缺货事务后需要发送消息给PMS
                eventBusService.trunkBatchPickUpShortGoods(TrunkBatchPickUpShortGoodsMsg.builder()
                        .deliveryBatchId(deliverySite.getDeliveryBatchId())
                        .trunkPickUpSkuShorts(trunkBatchPickUpSkuShorts)
                        .siteName(siteEntity.getSiteName())
                        .distOrders(distOrders)
                        .build());
            }
        });
    }

    @Override
    public List<String> queryRecentlyDateDeliveryStorePoi(LocalDate startTime, LocalDate endTime, String city, String area) {
        if (startTime == null || endTime == null || StringUtils.isBlank(city)) {
            log.warn("查询最近日期配送门店POI列表参数为空");
            return Collections.emptyList();
        }
        deliverySiteRepository.queryRecentlyDateDeliveryStorePoi(startTime, endTime,city,area);
        return Collections.emptyList();
    }
}


