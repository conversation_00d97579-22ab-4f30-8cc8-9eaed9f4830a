package net.summerfarm.tms.local.delivery.converter;

import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliveryPickDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.enums.CarTypeEnum;
import net.summerfarm.tms.local.base.site.SiteDtoConverter;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/16 17:33<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryBatchDTOConverter {

    public static DeliveryBatchDTO DeliveryBatchEntity2Dto(DeliveryBatchEntity deliveryBatchEntity) {

        DeliveryBatchDTO deliveryBatchDTO = new DeliveryBatchDTO();

        deliveryBatchDTO.setDeliveryBatchId(deliveryBatchEntity.getId());
        deliveryBatchDTO.setStatus(deliveryBatchEntity.getStatus() == null ? null : deliveryBatchEntity.getStatus().getCode());
        deliveryBatchDTO.setType(deliveryBatchEntity.getType());
        deliveryBatchDTO.setDeliveryTime(deliveryBatchEntity.getDeliveryTime());
        deliveryBatchDTO.setBeginTime(deliveryBatchEntity.getBeginTime());
        deliveryBatchDTO.setCarrierId(deliveryBatchEntity.getCarrierId());
        deliveryBatchDTO.setCarrierName(deliveryBatchEntity.getCarrierEntity() == null ? null : deliveryBatchEntity.getCarrierEntity().getCarrierName());
        deliveryBatchDTO.setDriverId(deliveryBatchEntity.getDriverId());
        deliveryBatchDTO.setDriver(deliveryBatchEntity.getDriverEntity() == null ? null : deliveryBatchEntity.getDriverEntity().getName());
        deliveryBatchDTO.setDriverPhone(deliveryBatchEntity.getDriverEntity() == null ? null : deliveryBatchEntity.getDriverEntity().getPhone());
        deliveryBatchDTO.setCarId(deliveryBatchEntity.getCarId());
        deliveryBatchDTO.setCarNumber(deliveryBatchEntity.getCarEntity() == null ? null : deliveryBatchEntity.getCarEntity().getCarNumber());
        deliveryBatchDTO.setCarType(CarTypeEnum.typeMap.get(deliveryBatchEntity.getCarEntity() == null ? null : deliveryBatchEntity.getCarEntity().getType()).getDesc());
        deliveryBatchDTO.setCarTypeCode(deliveryBatchEntity.getCarEntity() == null ? null : deliveryBatchEntity.getCarEntity().getType());
        deliveryBatchDTO.setCarVolume(CarTypeEnum.typeMap.get(deliveryBatchEntity.getCarEntity() == null ? null : deliveryBatchEntity.getCarEntity().getType()).getVolume());
        deliveryBatchDTO.setStorageName(deliveryBatchEntity.getCarEntity() == null ? null : deliveryBatchEntity.getCarEntity().getCarStorageEnum() == null ? null : deliveryBatchEntity.getCarEntity().getCarStorageEnum().getName());
        deliveryBatchDTO.setStorageCode(deliveryBatchEntity.getCarEntity() == null ? null : deliveryBatchEntity.getCarEntity().getCarStorageEnum() == null ? null : deliveryBatchEntity.getCarEntity().getCarStorageEnum().getCode());

        deliveryBatchDTO.setCreatTime(deliveryBatchEntity.getCreateTime());
        deliveryBatchDTO.setCreator(deliveryBatchEntity.getCreateName());
        deliveryBatchDTO.setCreateId(deliveryBatchEntity.getCreateId());
        deliveryBatchDTO.setCloseReason(deliveryBatchEntity.getCloseReason());
        deliveryBatchDTO.setEstimateFare(deliveryBatchEntity.getEstimateFare());
        deliveryBatchDTO.setPathName(deliveryBatchEntity.getPathName());
        deliveryBatchDTO.setBeginSiteId(deliveryBatchEntity.getBeginSiteId());
        deliveryBatchDTO.setPathId(deliveryBatchEntity.getPathId());
        deliveryBatchDTO.setPathCode(deliveryBatchEntity.getPathCode());
        deliveryBatchDTO.setCloseUser(deliveryBatchEntity.getCloseUser());
        deliveryBatchDTO.setPriceTotal(deliveryBatchEntity.getTotalPrice());
        deliveryBatchDTO.setTotalSiteNum(deliveryBatchEntity.getShopCount());
        deliveryBatchDTO.setVolumeTotal(deliveryBatchEntity.getTotalVolume());
        deliveryBatchDTO.setWeightTotal(deliveryBatchEntity.getTotalWeight());
        if(deliveryBatchEntity.getTotalQuantity() != null){
            deliveryBatchDTO.setQuantityTotal(new BigDecimal(deliveryBatchEntity.getTotalQuantity()));
        }
        deliveryBatchDTO.setUpdateTime(deliveryBatchEntity.getUpdateTime());
        deliveryBatchDTO.setBeginSiteDto(SiteDtoConverter.entity2Dto(deliveryBatchEntity.getBeginSiteEntity()));
        if(deliveryBatchEntity.getBeginSiteEntity() != null){
            deliveryBatchDTO.setBeginSiteName(deliveryBatchEntity.getBeginSiteEntity().getSiteName());
        }
        deliveryBatchDTO.setPathFullLoadRatio(deliveryBatchEntity.getPathFullLoadRatio());
        deliveryBatchDTO.setPlanTotalDistance(deliveryBatchEntity.getPlanTotalDistance() == null ? new BigDecimal(0) : deliveryBatchEntity.getPlanTotalDistance());
        deliveryBatchDTO.setRealTotalDistance(deliveryBatchEntity.getRealTotalDistance() == null ? new BigDecimal(0) : deliveryBatchEntity.getRealTotalDistance());
        deliveryBatchDTO.setIntelligenceTotalDistance(deliveryBatchEntity.getIntelligenceTotalDistance() == null ? new BigDecimal(0) : deliveryBatchEntity.getIntelligenceTotalDistance());
        deliveryBatchDTO.setArea(deliveryBatchEntity.getArea());
        deliveryBatchDTO.setClasses(deliveryBatchEntity.getClasses());
        deliveryBatchDTO.setRemark(deliveryBatchEntity.getRemark());
        List<DeliverySiteEntity> deliverySiteEntityList = deliveryBatchEntity.getDeliverySiteList();
        if (CollectionUtils.isNotEmpty(deliverySiteEntityList)) {
            List<DeliverySiteDTO> deliverySiteDTOList = deliverySiteEntityList.stream().map(DeliverySiteDTOConverter::entity2Dto).collect(Collectors.toList());
            deliveryBatchDTO.setDeliverySiteDTOList(deliverySiteDTOList);
        }

        List<DeliveryPickEntity> deliveryPickEntityList = deliveryBatchEntity.getDeliveryPickEntityList();
        if (CollectionUtils.isNotEmpty(deliveryPickEntityList)) {
            List<DeliveryPickDTO> deliveryPickDTOS = deliveryPickEntityList.stream()
                .map(DeliveryPickDTOConverter::entity2Dto).collect(Collectors.toList());
            deliveryBatchDTO.setDeliveryPickDTOS(deliveryPickDTOS);
        }

        deliveryBatchDTO.setTaxiSize(deliveryBatchEntity.getTaxiSize());
        deliveryBatchDTO.setSkuNum(deliveryBatchEntity.getSkuNum());
        deliveryBatchDTO.setTaxiMoney(deliveryBatchEntity.getTaxiMoney());
        deliveryBatchDTO.setBuyMoney(deliveryBatchEntity.getBuyMoney());

        deliveryBatchDTO.setSkuCnt(deliveryBatchEntity.getSkuCnt());
        deliveryBatchDTO.setPickUpTime(deliveryBatchEntity.getPickUpTime());
        deliveryBatchDTO.setBePathTime(deliveryBatchEntity.getBePathTime());

        deliveryBatchDTO.setSignOutPic(deliveryBatchEntity.getSignOutPic());
        deliveryBatchDTO.setSignOutTemperature(deliveryBatchEntity.getSignOutTemperature());
        deliveryBatchDTO.setSignOutTime(deliveryBatchEntity.getSignOutTime());
        deliveryBatchDTO.setSignOutRemark(deliveryBatchEntity.getSignOutRemark());
        deliveryBatchDTO.setVehiclePlatePics(deliveryBatchEntity.getVehiclePlatePics());
        deliveryBatchDTO.setFreezePics(deliveryBatchEntity.getFreezePics());
        deliveryBatchDTO.setRefrigeratePics(deliveryBatchEntity.getRefrigeratePics());
        deliveryBatchDTO.setWeightLoadRatio(deliveryBatchEntity.getWeightLoadRatio());
        deliveryBatchDTO.setVolumeLoadRatio(deliveryBatchEntity.getVolumeLoadRatio());
        deliveryBatchDTO.setQuantityLoadRatio(deliveryBatchEntity.getQuantityLoadRatio());
        deliveryBatchDTO.setFinishDeliveryTime(deliveryBatchEntity.getFinishDeliveryTime());
        deliveryBatchDTO.setCarryType(deliveryBatchEntity.getCarryType() == null ? null : deliveryBatchEntity.getCarryType().getValue());
        deliveryBatchDTO.setCarryTypeDesc(deliveryBatchEntity.getCarryType() == null ? null : deliveryBatchEntity.getCarryType().getContent());
        return deliveryBatchDTO;
    }
}
