package net.summerfarm.tms.local.base.carrier;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.carrier.CarrierDomainService;
import net.summerfarm.tms.base.carrier.CarrierInvoiceRepository;
import net.summerfarm.tms.base.carrier.CarrierRepository;
import net.summerfarm.tms.base.carrier.CarrierService;
import net.summerfarm.tms.base.carrier.command.CarrierSaveCommand;
import net.summerfarm.tms.base.carrier.command.CarrierUpdateCommand;
import net.summerfarm.tms.base.carrier.dto.CarrierDTO;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.carrier.entity.CarrierInvoiceEntity;
import net.summerfarm.tms.enums.FileDownloadTypeEnums;
import net.summerfarm.tms.excel.pojo.CarrierPojo;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.local.base.carrier.converter.CarrierDtoConverter;
import net.summerfarm.tms.local.base.carrier.converter.CarrierInvoiceDTOConverter;
import net.summerfarm.tms.query.base.carrier.CarrierQuery;
import net.summerfarm.tms.service.common.DownloadCenterService;
import net.summerfarm.tms.util.ExcelUtils;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/14 16:31<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class CarrierServiceImpl implements CarrierService {

    @Resource
    private CarrierRepository carrierRepository;

    @Resource
    private CarrierInvoiceRepository carrierInvoiceRepository;

    @Resource
    private CarrierDomainService carrierDomainService;
    @Resource
    private DownloadCenterService downloadCenterService;

    @Override
    public TmsResult<List<CarrierDTO>> searchByName(String name) {
        return TmsResult.success(carrierRepository.searchByName(name).stream().map(CarrierDtoConverter::entity2DTO).collect(Collectors.toList()));
    }

    @Override
    public PageInfo<CarrierDTO> queryPageList(CarrierQuery carrierQuery) {
        PageInfo<CarrierEntity> carrierPage = carrierRepository.queryPageList(carrierQuery);
        PageInfo<CarrierDTO> listPageInfo = new PageInfo<>();

        BeanUtils.copyProperties(carrierPage, listPageInfo);
        if(!CollectionUtils.isEmpty(carrierPage.getList())){
            List<CarrierDTO> carrierDTOList = carrierPage.getList().stream().map(CarrierDtoConverter::entity2DTO).collect(Collectors.toList());
            listPageInfo.setList(carrierDTOList);
        }

        return listPageInfo;
    }

    @Override
    public CarrierDTO queryDetail(Long id) {
        //查询承运商详情信息
        return CarrierDtoConverter.entity2DTO(carrierRepository.queryWithAccountInvoiceById(id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CarrierUpdateCommand carrierUpdateCommand) {
        if(carrierUpdateCommand.getBusinessType() == null && StrUtil.isBlank(carrierUpdateCommand.getSubBusinessType())){
            throw new TmsRuntimeException("业务类型不能为空");
        }
        carrierDomainService.editCarrier(CarrierDtoConverter.updateCommand2Entity(carrierUpdateCommand));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(CarrierSaveCommand carrierSaveCommand) {
        if(carrierSaveCommand.getBusinessType() == null && StrUtil.isBlank(carrierSaveCommand.getSubBusinessType())){
            throw new TmsRuntimeException("业务类型不能为空");
        }
        //保存承运商信息
        carrierDomainService.save(CarrierDtoConverter.saveCommand2Entity(carrierSaveCommand));
    }

    @Override
    public List<CarrierDTO> queryAllCarrier(CarrierQuery carrierQuery) {
        List<CarrierDTO> carrierDTOList = carrierRepository.queryList(carrierQuery).stream().map(CarrierDtoConverter::entity2DTO).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(carrierDTOList)){
            return carrierDTOList;
        }
        if(BooleanUtils.isTrue(carrierQuery.getHasQueryInvoice())){
            List<Long> carrierIdList = carrierDTOList.stream().map(CarrierDTO::getId).distinct().collect(Collectors.toList());
            Map<Long, CarrierInvoiceEntity> invoiceMap = carrierInvoiceRepository.queryByCarrierIdList(carrierIdList).stream().collect(Collectors.toMap(CarrierInvoiceEntity::getCarrierId, Function.identity(), (v1, v2) -> v1));
            carrierDTOList.forEach(carrierDTO -> {
                carrierDTO.setCarrierInvoiceDTO(CarrierInvoiceDTOConverter.entity2DTO(invoiceMap.get(carrierDTO.getId())));
            });
        }

        return carrierDTOList;
    }

    @Override
    public Long downloadCarrierInfo(CarrierQuery carrierQuery) {
        List<CarrierEntity> carrierEntities = carrierRepository.queryListWithInvoiceAccount(carrierQuery);
        if (CollectionUtils.isEmpty(carrierEntities)) {
           throw new TmsRuntimeException(ErrorCodeEnum.IMPORT_DATA_EMPTY);
        }
        //转化
        List<CarrierPojo> data = CarrierDtoConverter.entity2Pojo(carrierEntities);

        String fileName = "承运商信息" + DateUtil.today() + ".xls";
        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        recordDTO.setBizType(FileDownloadTypeEnums.XianMu.CARRIER_INFO_IMPORT.getValue());
        recordDTO.setTenantId(Constants.Tenant.XM_TENANT_ID);
        recordDTO.setUserId(Long.valueOf(Optional.ofNullable(UserInfoHolder.getUser()).orElse(new UserBase()).getBizUserId()));
        recordDTO.setFileName(fileName);
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
        return downloadCenterService.asyncUploadAndRecordResult(recordDTO, data, (q) -> {
            Workbook workbook =  new HSSFWorkbook();
            Sheet sheet = workbook.createSheet();
            //设置单元格宽度
            sheet.setColumnWidth(0, 4000);
            sheet.setColumnWidth(1, 10000);
            sheet.setColumnWidth(2, 4000);
            sheet.setColumnWidth(3, 4000);
            sheet.setColumnWidth(4, 5000);
            sheet.setColumnWidth(5, 6000);
            sheet.setColumnWidth(6, 10000);
            sheet.setColumnWidth(7, 10000);
            sheet.setColumnWidth(8, 10000);
            sheet.setColumnWidth(9, 10000);
            Row title = sheet.createRow(0);
            title.createCell(0).setCellValue("承运商名称");
            title.createCell(1).setCellValue("统一社会信用代码");
            title.createCell(2).setCellValue("负责人");
            title.createCell(3).setCellValue("负责人电话");
            title.createCell(4).setCellValue("地址");
            title.createCell(5).setCellValue("业务类型");
            title.createCell(6).setCellValue("发票抬头");
            title.createCell(7).setCellValue("税号");
            title.createCell(8).setCellValue("收款方名称");
            title.createCell(9).setCellValue("打款方式");
            int index = 1;
            for (int j = 0; j < data.size(); j++) {
                CarrierPojo carrierPojo = data.get(j);
                List<String> payInfo = carrierPojo.getPayInfo() == null ? Collections.emptyList() : carrierPojo.getPayInfo();
                String accountName = carrierPojo.getAccountName();

                if (CollectionUtils.isEmpty(payInfo)) {
                    Row row = sheet.createRow(index);
                    row.createCell(0).setCellValue(carrierPojo.getCarrierName());
                    row.createCell(1).setCellValue(carrierPojo.getSocialCreditCode());
                    row.createCell(2).setCellValue(carrierPojo.getDirector());
                    row.createCell(3).setCellValue(carrierPojo.getDirectorPhone());
                    row.createCell(4).setCellValue(carrierPojo.getAddress());
                    row.createCell(5).setCellValue(carrierPojo.getBusinessType());
                    row.createCell(6).setCellValue(carrierPojo.getInvoiceHead());
                    row.createCell(7).setCellValue(carrierPojo.getTaxNo());
                    index++;
                }
                for (int i = 0; i < payInfo.size(); i++) {
                    Row row = sheet.createRow(index);
                    if(i == 0){
                        row.createCell(0).setCellValue(carrierPojo.getCarrierName());
                        row.createCell(1).setCellValue(carrierPojo.getSocialCreditCode());
                        row.createCell(2).setCellValue(carrierPojo.getDirector());
                        row.createCell(3).setCellValue(carrierPojo.getDirectorPhone());
                        row.createCell(4).setCellValue(carrierPojo.getAddress());
                        row.createCell(5).setCellValue(carrierPojo.getBusinessType());
                        row.createCell(6).setCellValue(carrierPojo.getInvoiceHead());
                        row.createCell(7).setCellValue(carrierPojo.getTaxNo());
                        row.createCell(8).setCellValue(accountName);
                    }
                    row.createCell(9).setCellValue(payInfo.get(i));

                    index++;
                }
                if (payInfo.size() > 1) {
                    CellRangeAddress row0 = new CellRangeAddress(index - payInfo.size(), index-1, 0, 0);
                    CellRangeAddress row1 = new CellRangeAddress(index - payInfo.size(), index-1, 1, 1);
                    CellRangeAddress row2 = new CellRangeAddress(index - payInfo.size(), index-1, 2, 2);
                    CellRangeAddress row3 = new CellRangeAddress(index - payInfo.size(), index-1, 3, 3);
                    CellRangeAddress row4 = new CellRangeAddress(index - payInfo.size(), index-1, 4, 4);
                    CellRangeAddress row5 = new CellRangeAddress(index - payInfo.size(), index-1, 5, 5);
                    sheet.addMergedRegion(row0);
                    sheet.addMergedRegion(row1);
                    sheet.addMergedRegion(row2);
                    sheet.addMergedRegion(row3);
                    sheet.addMergedRegion(row4);
                    sheet.addMergedRegion(row5);
                }
            }
            return workbook;
        });
    }
}
