package net.summerfarm.tms.local.delivery.converter;

import net.summerfarm.tms.delivery.dto.DeliverySiteItemCodeDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteProcessItemDTO;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteProcessItemEntity;
import net.summerfarm.tms.facade.wms.dto.WmsOrderProcessDetailDTO;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/10/20 15:44<br/>
 *
 * <AUTHOR> />
 */
public class DeliverySiteItemDTOConverter {
    public static DeliverySiteItemDTO entity2Dto(DeliverySiteItemEntity deliverySiteItemEntity) {
        DeliverySiteItemDTO deliverySiteItemDTO = new DeliverySiteItemDTO();
        deliverySiteItemDTO.setId(deliverySiteItemEntity.getId());
        deliverySiteItemDTO.setCreateTime(deliverySiteItemEntity.getCreateTime());
        deliverySiteItemDTO.setUpdateTime(deliverySiteItemEntity.getUpdateTime());
        deliverySiteItemDTO.setDeliverySiteId(deliverySiteItemEntity.getDeliverySiteId());
        deliverySiteItemDTO.setOutItemId(deliverySiteItemEntity.getOutItemId());
        deliverySiteItemDTO.setOutItemName(deliverySiteItemEntity.getOutItemName());
        deliverySiteItemDTO.setPlanReceiptCount(deliverySiteItemEntity.getPlanReceiptCount());
        deliverySiteItemDTO.setRealReceiptCount(deliverySiteItemEntity.getRealReceiptCount());
        deliverySiteItemDTO.setShortCount(deliverySiteItemEntity.getShortCount());
        deliverySiteItemDTO.setInterceptCount(deliverySiteItemEntity.getInterceptCount());
        deliverySiteItemDTO.setRejectCount(deliverySiteItemEntity.getRejectCount());
        deliverySiteItemDTO.setRejectReason(deliverySiteItemEntity.getRejectReason());
        deliverySiteItemDTO.setScanCount(deliverySiteItemEntity.getScanCount());
        deliverySiteItemDTO.setNoscanCount(deliverySiteItemEntity.getNoscanCount());
        deliverySiteItemDTO.setNoscanReason(deliverySiteItemEntity.getNoscanReason());
        deliverySiteItemDTO.setNoscanPics(deliverySiteItemEntity.getNoscanPics());
        deliverySiteItemDTO.setRemark(deliverySiteItemEntity.getRemark());
        deliverySiteItemDTO.setType(deliverySiteItemEntity.getType());
        deliverySiteItemDTO.setSkuPic(deliverySiteItemEntity.getSkuPic());
        deliverySiteItemDTO.setSkuType(deliverySiteItemEntity.getSkuType());
        deliverySiteItemDTO.setOutItemType(deliverySiteItemEntity.getOutItemType());
        deliverySiteItemDTO.setNameRemakes(deliverySiteItemEntity.getNameRemakes());
        deliverySiteItemDTO.setExtType(deliverySiteItemEntity.getExtType());
        deliverySiteItemDTO.setStorageArea(deliverySiteItemEntity.getStorageArea());
        deliverySiteItemDTO.setStatus(deliverySiteItemEntity.getStatus());
        deliverySiteItemDTO.setBarcodes(deliverySiteItemEntity.getBarcodes());
        deliverySiteItemDTO.setPackType(deliverySiteItemEntity.getPackType());
        if(!CollectionUtils.isEmpty(deliverySiteItemEntity.getDeliverySiteItemCodeEntityList())){
            List<DeliverySiteItemCodeDTO> deliverySiteItemCodeDTOS = deliverySiteItemEntity.getDeliverySiteItemCodeEntityList().stream()
                    .map(DeliverySiteItemCodeDTOConverter::entity2Dto).collect(Collectors.toList());

            deliverySiteItemDTO.setDeliverySiteItemCodeDTOS(deliverySiteItemCodeDTOS);
        }
        if(!CollectionUtils.isEmpty(deliverySiteItemEntity.getDeliverySiteProcessItemEntityList())){
            List<DeliverySiteProcessItemDTO> deliverySiteProcessItemDTOList = deliverySiteItemEntity.getDeliverySiteProcessItemEntityList().stream()
                    .map(DeliverySiteItemDTOConverter::processEntity2ProcessItemDTO).collect(Collectors.toList());
            deliverySiteItemDTO.setDeliverySiteProcessItemDTOList(deliverySiteProcessItemDTOList);
        }

        if (deliverySiteItemEntity.getDeliverySiteItemRecycleEntity() != null){
            deliverySiteItemDTO.setDeliverySiteRecycleDTO(DeliverySiteItemRecycleDTOConverter.entity2Dto(deliverySiteItemEntity.getDeliverySiteItemRecycleEntity()));
        }
        deliverySiteItemDTO.setPickShortCount(deliverySiteItemEntity.getPickShortCount());
        return deliverySiteItemDTO;
    }

    public static DeliverySiteProcessItemDTO processEntity2ProcessItemDTO(DeliverySiteProcessItemEntity deliverySiteProcessItemEntity) {
        if(deliverySiteProcessItemEntity == null){
            return null;
        }
        DeliverySiteProcessItemDTO deliverySiteProcessItemDTO = new DeliverySiteProcessItemDTO();
        deliverySiteProcessItemDTO.setQuantity(deliverySiteProcessItemEntity.getQuantity());
        deliverySiteProcessItemDTO.setUnit(deliverySiteProcessItemEntity.getUnit());
        deliverySiteProcessItemDTO.setWeight(deliverySiteProcessItemEntity.getWeight());
        deliverySiteProcessItemDTO.setProcessFlag(deliverySiteProcessItemEntity.getProcessFlag());
        return deliverySiteProcessItemDTO;
    }

    public static DeliverySiteItemEntity dto2Entity(DeliverySiteItemDTO deliverySiteItemDTO) {
        DeliverySiteItemEntity deliverySiteItemEntity = new DeliverySiteItemEntity();
        deliverySiteItemEntity.setId(deliverySiteItemDTO.getId());
        deliverySiteItemEntity.setCreateTime(deliverySiteItemDTO.getCreateTime());
        deliverySiteItemEntity.setUpdateTime(deliverySiteItemDTO.getUpdateTime());
        deliverySiteItemEntity.setDeliverySiteId(deliverySiteItemDTO.getDeliverySiteId());
        deliverySiteItemEntity.setOutItemId(deliverySiteItemDTO.getOutItemId());
        deliverySiteItemEntity.setPlanReceiptCount(deliverySiteItemDTO.getPlanReceiptCount());
        deliverySiteItemEntity.setRealReceiptCount(deliverySiteItemDTO.getRealReceiptCount());
        deliverySiteItemEntity.setShortCount(deliverySiteItemDTO.getShortCount());
        deliverySiteItemEntity.setInterceptCount(deliverySiteItemDTO.getInterceptCount());
        deliverySiteItemEntity.setRejectCount(deliverySiteItemDTO.getRejectCount());
        deliverySiteItemEntity.setRejectReason(deliverySiteItemDTO.getRejectReason());
        deliverySiteItemEntity.setScanCount(deliverySiteItemDTO.getScanCount());
        deliverySiteItemEntity.setNoscanCount(deliverySiteItemDTO.getNoscanCount());
        deliverySiteItemEntity.setNoscanReason(deliverySiteItemDTO.getNoscanReason());
        deliverySiteItemEntity.setNoscanPics(deliverySiteItemDTO.getNoscanPics());
        deliverySiteItemEntity.setRemark(deliverySiteItemDTO.getRemark());
        deliverySiteItemEntity.setType(deliverySiteItemDTO.getType());
        deliverySiteItemEntity.setStatus(deliverySiteItemDTO.getStatus());
        deliverySiteItemEntity.setPackType(deliverySiteItemDTO.getPackType());
        if (deliverySiteItemDTO.getDeliverySiteRecycleDTO() != null){
            deliverySiteItemEntity.setDeliverySiteItemRecycleEntity(DeliverySiteItemRecycleDTOConverter.dto2Entity(deliverySiteItemDTO.getDeliverySiteRecycleDTO()));
        }
        return deliverySiteItemEntity;
    }

    public static DeliverySiteProcessItemEntity dto2ProcessItemEntity(WmsOrderProcessDetailDTO wmsOrderProcessDetailDTO) {
        if(wmsOrderProcessDetailDTO == null){
            return null;
        }
        DeliverySiteProcessItemEntity deliverySiteProcessItemEntity = new DeliverySiteProcessItemEntity();

        deliverySiteProcessItemEntity.setUnit(wmsOrderProcessDetailDTO.getProductSkuSpecUnit());
        deliverySiteProcessItemEntity.setWeight(String.valueOf(wmsOrderProcessDetailDTO.getProductSkuSpecWeight()));
        deliverySiteProcessItemEntity.setQuantity(wmsOrderProcessDetailDTO.getProductSkuSpecSubmitQuantity());
        deliverySiteProcessItemEntity.setHaveProductSkuQuantity(wmsOrderProcessDetailDTO.getHaveProductSkuQuantity());

        return deliverySiteProcessItemEntity;
    }

    public static DeliverySiteProcessItemEntity dto2NoProcessItemEntity(WmsOrderProcessDetailDTO wmsOrderProcessDetailDTO) {
        if(wmsOrderProcessDetailDTO == null){
            return null;
        }
        DeliverySiteProcessItemEntity deliverySiteProcessItemEntity = new DeliverySiteProcessItemEntity();

        deliverySiteProcessItemEntity.setUnit(wmsOrderProcessDetailDTO.getProductSkuUnit());
        deliverySiteProcessItemEntity.setWeight(String.valueOf(wmsOrderProcessDetailDTO.getProductSkuWeight()));
        deliverySiteProcessItemEntity.setQuantity(wmsOrderProcessDetailDTO.getNoProductSkuQuantity());
        deliverySiteProcessItemEntity.setHaveProductSkuQuantity(wmsOrderProcessDetailDTO.getHaveProductSkuQuantity());

        return deliverySiteProcessItemEntity;
    }

}
