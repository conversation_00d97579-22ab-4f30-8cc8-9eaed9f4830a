package net.summerfarm.tms.local.expense;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.NonNull;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.driver.DriverService;
import net.summerfarm.tms.base.driver.dto.DriverDTO;
import net.summerfarm.tms.common.base.BaseService;
import net.summerfarm.tms.common.util.PageInfoHelper;
import net.summerfarm.tms.domain.Contact;
import net.summerfarm.tms.domain.TmsExpenseAuditRecord;
import net.summerfarm.tms.domain.vo.DeliveryPathVO;
import net.summerfarm.tms.enums.ExpenseStatusEnum;
import net.summerfarm.tms.exceptions.DefaultServiceException;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.expense.ExpenseService;
import net.summerfarm.tms.expense.dto.*;
import net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecordGrayMapper;
import net.summerfarm.tms.mapper.*;
import net.summerfarm.tms.util.ExcelUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.tms.contexts.Global.SEPARATING_SYMBOL;
import static net.summerfarm.tms.contexts.Global.THREE_MILEAGE;
import static net.summerfarm.tms.enums.ExpenseTypeEnum.*;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-11
 */
@Service
public class ExpenseServiceImpl extends BaseService implements ExpenseService {

    @Resource
    private DeliveryPathMapper deliveryPathMapper;
    @Resource
    private ExpenseDetailMapper expenseDetailMapper;
    @Resource
    private ExpenseMapper expenseMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private TmsExpenseAuditRecordMapper tmsExpenseAuditRecordMapper;
    @Resource
    private TmsExpenseAuditRecordGrayMapper tmsExpenseAuditRecordGrayMapper;
    @Resource
    private DriverService driverService;

    @Override
    @Transactional
    public AjaxResult insertExpenseDetail(ExpenseVO expense) {

        DeliveryPathVO deliveryPath = deliveryPathMapper.selectById(expense.getDeliveryPathId());

        Expense select = expenseMapper.selectByPathId(deliveryPath.getId());
        if (select != null) {
            throw new DefaultServiceException("该配送路线已生成报销单！");
        }

        Contact contact = contactMapper.selectByContactId(deliveryPath.getContactId());
        Expense insert = new Expense(expense.getDeliveryPathId(), deliveryPath.getDeliveryTime(), getDeliveryCar().getId(), NumberUtils.INTEGER_ONE, deliveryPath.getStoreNo(), NumberUtils.INTEGER_ZERO, contact.getMId(), contact.getMname(), LocalDateTime.now(), ExpenseStatusEnum.AUDIT.ordinal());
        List<ExpenseDetailVO> expenseDetails = expense.getExpenseDetails();
        Set<Integer> types = expenseDetails.stream().map(ExpenseDetail::getType).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(types)) {
            throw new DefaultServiceException("请提交报销类型！");
        }
        if (types.contains(TRAFFIC.getId()) && types.contains(PURCHASE.getId())) {
            insert.setType(TRAFFIC_PURCHASE.getId());
        } else if (types.contains(TRAFFIC.getId())) {
            insert.setType(TRAFFIC.getId());
        } else {
            insert.setType(PURCHASE.getId());
        }
        insert.setCreator(getDeliveryCar().getDriver());
        expenseMapper.insert(insert);

        boolean flag = false;
        for (ExpenseDetailVO expenseDetail : expenseDetails) {
            expenseDetail.setExpenseId(insert.getId());
            expenseDetail.setIsReview(NumberUtils.INTEGER_ZERO);
            expenseDetail.setCreator(getDeliveryCar().getDriver());
            if (!Objects.isNull(expenseDetail.getAmount())) {
                expenseDetail.setAmount(expenseDetail.getAmount().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            }
            if (!Objects.isNull(expenseDetail.getMileage())) {
                expenseDetail.setMileage(expenseDetail.getMileage().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            }
            //复核判断
            if (!Objects.isNull(expenseDetail.getModify())) {
                expenseDetail.setIsReview(NumberUtils.INTEGER_ONE);
                flag = true;
            }
            if (!CollectionUtils.isEmpty(expenseDetail.getPictures())) {
                //拼接图片存储规则
                StringJoiner picture = new StringJoiner(SEPARATING_SYMBOL);
                expenseDetail.getPictures().forEach(picture::add);
                expenseDetail.setPhotos(picture.toString());
            }
            expenseDetailMapper.insert(expenseDetail);
        }
        if (flag) {
            Expense update = new Expense();
            update.setId(insert.getId());
            update.setIsReview(NumberUtils.INTEGER_ONE);
            expenseMapper.update(update);
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectExpenseDetail(Integer id) {
        ExpenseQueryDTO expenseQueryDTO = new ExpenseQueryDTO();
        expenseQueryDTO.setId(id);
        expenseQueryDTO.setPageIndex(1);
        expenseQueryDTO.setPageSize(1);
        PageInfo<ExpenseVO> query = this.query(expenseQueryDTO);
        if (query == null || CollectionUtils.isEmpty(query.getList())) {
            return null;
        }
        ExpenseVO expenseVO = query.getList().get(0);
        List<ExpenseDetail> details = expenseDetailMapper.selectByExpenseId(id);
        expenseVO.setExpenseDetailList(details);
        // 待审核的时候不返回操作记录
        if (!Objects.equals(ExpenseStatusEnum.AUDIT.getId(), expenseVO.getStatus())) {
            DeliveryExpenseRecordVO auditDetail = this.getAuditDetail(id);
            if (Objects.nonNull(auditDetail)) {
                expenseVO.setAuditRecord(auditDetail);
            }
        }


        return AjaxResult.getOK(expenseVO);
    }

    @Override
    public AjaxResult getMileage(String startAddress, String endAddress) {
        BigDecimal mileage = GaoDeUtil.calculDistance(GaoDeUtil.getPoiByAddress(startAddress), GaoDeUtil.getPoiByAddress(endAddress), NumberUtils.INTEGER_TWO);
        if (Objects.isNull(mileage)) {
            throw new DefaultServiceException("获取里程距离异常！");
        }
        JSONObject result = new JSONObject();
        if (THREE_MILEAGE < mileage.floatValue()) {
            result.put("mileage", NumberUtils.INTEGER_ONE);
        }
        return AjaxResult.getOK(result);

    }

    @Override
    @Transactional
    public AjaxResult updateExpenseDetail(ExpenseVO expense) {
        DeliveryPathVO deliveryPath = deliveryPathMapper.selectById(expense.getDeliveryPathId());
        Contact contact = contactMapper.selectByContactId(deliveryPath.getContactId());
        Expense insert = new Expense(expense.getDeliveryPathId(), deliveryPath.getDeliveryTime(), getDeliveryCar().getId(), NumberUtils.INTEGER_ONE, deliveryPath.getStoreNo(), NumberUtils.INTEGER_ZERO, contact.getMId(), contact.getMname(), LocalDateTime.now(), ExpenseStatusEnum.AUDIT.ordinal());
        List<ExpenseDetailVO> expenseDetails = expense.getExpenseDetails();
        Set<Integer> types = expenseDetails.stream().map(ExpenseDetail::getType).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(types)) {
            throw new DefaultServiceException("请提交报销类型！");
        }
        if (types.contains(TRAFFIC.getId()) && types.contains(PURCHASE.getId())) {
            insert.setType(TRAFFIC_PURCHASE.getId());
        } else if (types.contains(TRAFFIC.getId())) {
            insert.setType(TRAFFIC.getId());
        } else {
            insert.setType(PURCHASE.getId());
        }
        insert.setUpdater(getDeliveryCar().getDriver());
        insert.setId(expense.getId());
        expenseMapper.updateByExpense(insert);
        //删除所有审核详情
        expenseDetailMapper.deleteByExpenseId(expense.getId());
        boolean flag = false;
        for (ExpenseDetailVO expenseDetail : expenseDetails) {
            expenseDetail.setExpenseId(insert.getId());
            expenseDetail.setIsReview(NumberUtils.INTEGER_ZERO);
            expenseDetail.setCreator(getDeliveryCar().getDriver());
            if (!Objects.isNull(expenseDetail.getAmount())) {
                expenseDetail.setAmount(expenseDetail.getAmount().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            }
            if (!Objects.isNull(expenseDetail.getMileage())) {
                expenseDetail.setMileage(expenseDetail.getMileage().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            }
            //复核判断
            if (!Objects.isNull(expenseDetail.getModify())) {
                expenseDetail.setIsReview(NumberUtils.INTEGER_ONE);
                flag = true;
            }
            if (!CollectionUtils.isEmpty(expenseDetail.getPictures())) {
                //拼接图片存储规则
                StringJoiner picture = new StringJoiner(SEPARATING_SYMBOL);
                expenseDetail.getPictures().forEach(picture::add);
                expenseDetail.setPhotos(picture.toString());
            }
            expenseDetailMapper.insert(expenseDetail);
        }
        if (flag) {
            Expense update = new Expense();
            update.setId(insert.getId());
            update.setIsReview(NumberUtils.INTEGER_ONE);
            expenseMapper.update(update);
        }
        return AjaxResult.getOK();
    }

    /**
     * 查询审核信息
     *
     * @return 费用报销审核信息.
     */
    @Override
    public AjaxResult selectAuditExpense() {
        List<TmsExpenseAuditRecord> tmsExpenseAuditRecords = tmsExpenseAuditRecordMapper.selectByDriverId(getDeliveryCar().getId());
        for (TmsExpenseAuditRecord tmsExpenseAuditRecord : tmsExpenseAuditRecords) {
            DeliveryPathVO deliveryPathVO = deliveryPathMapper.selectById(tmsExpenseAuditRecord.getDeliveryPathId());
            if (deliveryPathVO != null) {
                tmsExpenseAuditRecord.setAddress(deliveryPathVO.getAddress());
            }
        }
        return AjaxResult.getOK(tmsExpenseAuditRecords);
    }

    @Override
    public PageInfo<ExpenseVO> query(ExpenseQueryDTO expenseQueryDTO) {
        PageHelper.startPage(expenseQueryDTO.getPageIndex(), expenseQueryDTO.getPageSize());
        //报销类型多选
        if (StringUtils.isNotBlank(expenseQueryDTO.getTypes())) {
            expenseQueryDTO.setTypeList(Arrays.asList(expenseQueryDTO.getTypes().split(",")));
        }
        List<ExpenseVO> expenseList = expenseMapper.select(expenseQueryDTO);
        if (!CollectionUtils.isEmpty(expenseList)) {
            List<net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord> tmsExpenseAuditRecords = tmsExpenseAuditRecordGrayMapper
                    .selectList(new LambdaQueryWrapper<net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord>()
                            .in(net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord::getExpenseId, expenseList.stream()
                                    .map(ExpenseVO::getId).collect(Collectors.toSet())));
            if (!CollectionUtils.isEmpty(tmsExpenseAuditRecords)) {
                Map<Integer, List<net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord>> expenseAuditRecordMap = tmsExpenseAuditRecords.stream()
                        .sorted(Comparator.comparing(net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord::getId).reversed())
                        .collect(Collectors.groupingBy(net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord::getExpenseId));
                for (ExpenseVO expenseVO : expenseList) {
                    if (Objects.equals(expenseVO.getStatus(), ExpenseStatusEnum.AUDIT.getId())) {
                        continue;
                    }
                    List<net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord> itemAuditRecordList = expenseAuditRecordMap.get(expenseVO.getId());
                    if (!CollectionUtils.isEmpty(itemAuditRecordList)) {
                        expenseVO.setApprovalTime(itemAuditRecordList.get(0).getCreateTime());
                    }
                }
            }

        }
        return PageInfoHelper.createPageInfo(expenseList);

    }

    @Override
    public DeliveryExpenseRecordVO getAuditDetail(@NonNull Integer expenseId) {
        List<net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord> tmsExpenseAuditRecords = tmsExpenseAuditRecordGrayMapper
                .selectList(new LambdaQueryWrapper<net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord>()
                        .eq(net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord::getExpenseId, expenseId)
                        .orderByDesc(net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord::getId));
        if (CollectionUtils.isEmpty(tmsExpenseAuditRecords)) {
            return null;
        }
        DeliveryExpenseRecordVO vo = new DeliveryExpenseRecordVO();
        net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord tmsExpenseAuditRecord = tmsExpenseAuditRecords.get(0);
        TmsResult<DriverDTO> driverDTOTmsResult = driverService.driverDetail(Long.valueOf(tmsExpenseAuditRecord.getDriverId()));
        vo.setApplicationTime(tmsExpenseAuditRecord.getSubmitTime());
        if (Objects.nonNull(driverDTOTmsResult) && Objects.nonNull(driverDTOTmsResult.getData())) {
            vo.setApplicant(driverDTOTmsResult.getData().getName());
        }
        vo.setAuditName(tmsExpenseAuditRecord.getAuditName());
        vo.setStatus(tmsExpenseAuditRecord.getStatus());
        vo.setAuditTime(tmsExpenseAuditRecord.getCreateTime());
        vo.setReason(tmsExpenseAuditRecord.getReason());
        return vo;
    }

    @Override
    public void selectExpenseExport(ExpenseVO selectKeys) {

        if (StringUtils.isNotBlank(selectKeys.getTypes())) {
            selectKeys.setTypeList(Arrays.asList(selectKeys.getTypes().split(",")));
        }
        List<ExpenseDetailVO> info = expenseMapper.selectExport(selectKeys);
        if (CollectionUtils.isEmpty(info)) {
            throw new TmsRuntimeException("暂无数据");
        }

        List<ExpenseDetailVO> purchaseDetail = info.stream().filter(detail -> Objects.equals(PURCHASE.getId(), detail.getType())).collect(Collectors.toList());
        List<ExpenseDetailVO> trafficDetail = info.stream().filter(detail -> Objects.equals(TRAFFIC.getId(), detail.getType())).collect(Collectors.toList());

        Workbook workbook = new HSSFWorkbook();
        if (!CollectionUtils.isEmpty(trafficDetail)) {
            //交通费
            Sheet trafficSheet = workbook.createSheet("交通费");
            Row trafficTitle = trafficSheet.createRow(0);
            trafficTitle.createCell(0).setCellValue("任务编号");
            trafficTitle.createCell(1).setCellValue("城配仓");
            trafficTitle.createCell(2).setCellValue("配送日期");
            trafficTitle.createCell(3).setCellValue("提交人");
            trafficTitle.createCell(4).setCellValue("是否需要复核");
            trafficTitle.createCell(5).setCellValue("配送方式");
            trafficTitle.createCell(6).setCellValue("关联店铺");
            trafficTitle.createCell(7).setCellValue("报销类型");
            trafficTitle.createCell(8).setCellValue("审核状态");
            trafficTitle.createCell(9).setCellValue("报销里程");
            trafficTitle.createCell(10).setCellValue("报销金额");
            trafficTitle.createCell(11).setCellValue("报销起点");
            trafficTitle.createCell(12).setCellValue("报销终点");

            int index = 1;
            for (ExpenseDetailVO expenseDetailVO : trafficDetail) {
                Row row = trafficSheet.createRow(index);

                row.createCell(0).setCellValue(Objects.isNull(expenseDetailVO.getExpenseId()) ? "" : expenseDetailVO.getExpenseId().toString());
                row.createCell(1).setCellValue(expenseDetailVO.getStoreName());
                row.createCell(2).setCellValue(expenseDetailVO.getDeliveryTime().toString());
                row.createCell(3).setCellValue(expenseDetailVO.getCreator());
                row.createCell(4).setCellValue(Objects.equals(NumberUtils.INTEGER_ZERO, expenseDetailVO.getIsReview()) ? "否" : "是");
                row.createCell(5).setCellValue(getSendWay(expenseDetailVO.getSendWay()));
                row.createCell(6).setCellValue(expenseDetailVO.getMName());
                row.createCell(7).setCellValue(Objects.equals(NumberUtils.INTEGER_ZERO, expenseDetailVO.getType()) ?  PURCHASE.getMsg():TRAFFIC.getMsg() );
                row.createCell(8).setCellValue(Objects.isNull(expenseDetailVO.getStatus()) ? "" : ExpenseStatusEnum.getById(expenseDetailVO.getStatus()));
                row.createCell(9).setCellValue(Objects.isNull(expenseDetailVO.getMileage()) ? "" : expenseDetailVO.getMileage().toString());
                row.createCell(10).setCellValue(expenseDetailVO.getAmount().toString());
                row.createCell(11).setCellValue(StringUtils.isEmpty(expenseDetailVO.getStartAddress()) ? "" : expenseDetailVO.getStartAddress());
                row.createCell(12).setCellValue(expenseDetailVO.getEndAddress());
                index++;
            }
        }

        if (!CollectionUtils.isEmpty(purchaseDetail)) {
            //采购费
            Sheet purchaseSheet = workbook.createSheet("采购费");
            Row purchaseTitle = purchaseSheet.createRow(0);
            purchaseTitle.createCell(0).setCellValue("任务编号");
            purchaseTitle.createCell(1).setCellValue("城配仓");
            purchaseTitle.createCell(2).setCellValue("配送日期");
            purchaseTitle.createCell(3).setCellValue("提交人");
            purchaseTitle.createCell(4).setCellValue("是否需要复核");
            purchaseTitle.createCell(5).setCellValue("配送方式");
            purchaseTitle.createCell(6).setCellValue("关联店铺");
            purchaseTitle.createCell(7).setCellValue("报销类型");
            purchaseTitle.createCell(8).setCellValue("审核状态");
            purchaseTitle.createCell(9).setCellValue("报销金额");
            purchaseTitle.createCell(10).setCellValue("报销明细");

            int index = 1;
            for (ExpenseDetailVO expenseDetailVO : purchaseDetail) {
                Row row = purchaseSheet.createRow(index);
                row.createCell(0).setCellValue(Objects.isNull(expenseDetailVO.getExpenseId()) ? "" : expenseDetailVO.getExpenseId().toString());
                row.createCell(1).setCellValue(expenseDetailVO.getStoreName());
                row.createCell(2).setCellValue(expenseDetailVO.getDeliveryTime().toString());
                row.createCell(3).setCellValue(expenseDetailVO.getCreator());
                row.createCell(4).setCellValue(Objects.equals(NumberUtils.INTEGER_ZERO, expenseDetailVO.getIsReview()) ? "否" : "是");
                row.createCell(5).setCellValue(getSendWay(expenseDetailVO.getSendWay()));
                row.createCell(6).setCellValue(expenseDetailVO.getMName());
                row.createCell(7).setCellValue(Objects.equals(NumberUtils.INTEGER_ZERO, expenseDetailVO.getType()) ?  PURCHASE.getMsg():TRAFFIC.getMsg());
                row.createCell(8).setCellValue(Objects.isNull(expenseDetailVO.getStatus()) ? "" : ExpenseStatusEnum.getById(expenseDetailVO.getStatus()));
                row.createCell(9).setCellValue(Objects.isNull(expenseDetailVO.getAmount())?"":expenseDetailVO.getAmount().toString());
                row.createCell(10).setCellValue(expenseDetailVO.getRemark());
                index++;
            }
        }

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String date = formatter.format(System.currentTimeMillis());
        String fileName = "费用报销数据导出" + date + ".xls";
        try {
            ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(collectExceptionStackMsg(e));
            throw new net.summerfarm.common.exceptions.DefaultServiceException("导出异常");
        }
    }

    private String getSendWay(Integer sendWay) {
        if (Objects.isNull(sendWay)) {
            return "";
        }
        if (Objects.equals(0, sendWay)) {
            return "正常配送";

        } else if (Objects.equals(1, sendWay)) {
            return "专车配送";
        } else {
            return "";
        }
    }

    private String collectExceptionStackMsg(Exception e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw, true));
        String strs = sw.toString();
        return strs;
    }
}
