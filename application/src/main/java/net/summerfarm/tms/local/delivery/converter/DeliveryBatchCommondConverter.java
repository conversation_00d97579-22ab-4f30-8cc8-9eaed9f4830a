package net.summerfarm.tms.local.delivery.converter;

import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.enums.DeliveryBatchEnums;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class DeliveryBatchCommondConverter {

    public static DeliveryBatchEntity commond2DeliveryBatchEntity(DeliveryOrderSaveCommond deliveryOrderCommond) {
        DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();
        deliveryBatchEntity.setType(deliveryOrderCommond.getType());
        deliveryBatchEntity.setCarrierId(deliveryOrderCommond.getCarrierId());
        deliveryBatchEntity.setCarId(deliveryOrderCommond.getCarId());
        deliveryBatchEntity.setDriverId(deliveryOrderCommond.getDriverId());
        deliveryBatchEntity.setDeliveryTime(deliveryOrderCommond.getDeliveryTime());
        deliveryBatchEntity.setBeginTime(deliveryOrderCommond.getBeginTime());
        deliveryBatchEntity.setEstimateFare(deliveryOrderCommond.getEstimateFare());
        deliveryBatchEntity.setArea(deliveryOrderCommond.getArea());
        deliveryBatchEntity.setClasses(deliveryOrderCommond.getClasses());
        deliveryBatchEntity.setRemark(deliveryOrderCommond.getRemarkInfo());
        deliveryBatchEntity.setCarryType(DeliveryBatchEnums.CarryType.getTypeByVal(deliveryOrderCommond.getCarryType()));

        //点位信息
        List<DeliverySiteEntity> deliverySiteEntityList = new ArrayList<>();
        List<DeliverySiteDTO> deliverySiteList = deliveryOrderCommond.getDeliverySiteList();
        for (DeliverySiteDTO deliverySiteDTO : deliverySiteList) {
            DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
            deliverySiteEntity.setSiteId(deliverySiteDTO.getSiteId());
            deliverySiteEntity.setPlanArriveTime(deliverySiteDTO.getPlanArriveTime());
            deliverySiteEntity.setPlanOutTime(deliverySiteDTO.getPlanOutTime());
            deliverySiteEntity.setSiteType(deliverySiteDTO.getSiteType());
            deliverySiteEntityList.add(deliverySiteEntity);
        }
        deliveryBatchEntity.setDeliverySiteList(deliverySiteEntityList);

        //配送单id
        List<Long> orderIdList = deliveryOrderCommond.getOrderIdList();
        if (orderIdList == null) {
            orderIdList = new ArrayList<>();
        }
        //配送单
        List<DeliveryOrderEntity> deliveryOrderEntityList = new ArrayList<>();
        for (Long deliveryOrderId : orderIdList) {
            DeliveryOrderEntity deliveryOrderEntity = new DeliveryOrderEntity();
            deliveryOrderEntity.setId(deliveryOrderId);

            deliveryOrderEntityList.add(deliveryOrderEntity);
        }

        deliveryBatchEntity.setDeliveryOrderEntityList(deliveryOrderEntityList);
        //费用信息
        List<DeliveryBatchFareEntity> batchFareEntities = new ArrayList<>();
        List<DeliveryBatchFareCommand> deliveryBatchFareCommandList = deliveryOrderCommond.getDeliveryBatchFareCommandList();
        if (!CollectionUtils.isEmpty(deliveryBatchFareCommandList)){
            for (DeliveryBatchFareCommand deliveryBatchFareCommand : deliveryBatchFareCommandList) {
                DeliveryBatchFareEntity deliveryBatchFareEntity = new DeliveryBatchFareEntity();
                DeliveryBatchEnums.FareType fareType = DeliveryBatchEnums.FareType.getFareByVal(deliveryBatchFareCommand.getFareType());
                deliveryBatchFareEntity.setFareType(fareType);
                deliveryBatchFareEntity.setAmount(deliveryBatchFareCommand.getAmount());
                batchFareEntities.add(deliveryBatchFareEntity);
            }
        }
        deliveryBatchEntity.setDeliveryBatchFareEntityList(batchFareEntities);

        return deliveryBatchEntity;
    }


    public static DeliveryBatchEntity commond2DeliveryBatchUpdateEntity(DeliveryOrderUpdateCommand deliveryOrderUpdateCommand) {
        DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();

        deliveryBatchEntity.setId(deliveryOrderUpdateCommand.getDeliveryBatchId());
        deliveryBatchEntity.setType(deliveryOrderUpdateCommand.getType());
        deliveryBatchEntity.setCarrierId(deliveryOrderUpdateCommand.getCarrierId());
        deliveryBatchEntity.setCarId(deliveryOrderUpdateCommand.getCarId());
        deliveryBatchEntity.setDriverId(deliveryOrderUpdateCommand.getDriverId());
        deliveryBatchEntity.setDeliveryTime(deliveryOrderUpdateCommand.getDeliveryTime());
        deliveryBatchEntity.setBeginTime(deliveryOrderUpdateCommand.getBeginTime());
        deliveryBatchEntity.setEstimateFare(deliveryOrderUpdateCommand.getEstimateFare());
        deliveryBatchEntity.setArea(deliveryOrderUpdateCommand.getArea());
        deliveryBatchEntity.setClasses(deliveryOrderUpdateCommand.getClasses());
        deliveryBatchEntity.setRemark(deliveryOrderUpdateCommand.getRemarkInfo());
        deliveryBatchEntity.setCarryType(DeliveryBatchEnums.CarryType.getTypeByVal(deliveryOrderUpdateCommand.getCarryType()));

        //点位信息
        List<DeliverySiteEntity> deliverySiteEntityList = new ArrayList<>();
        List<DeliverySiteDTO> deliverySiteList = deliveryOrderUpdateCommand.getDeliverySiteList();
        if (deliverySiteList == null) {
            deliverySiteList = new ArrayList<>();
        }
        for (DeliverySiteDTO deliverySiteDTO : deliverySiteList) {
            DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
            deliverySiteEntity.setId(deliverySiteDTO.getSiteId());
            deliverySiteEntity.setSiteId(deliverySiteDTO.getSiteId());
            deliverySiteEntity.setPlanArriveTime(deliverySiteDTO.getPlanArriveTime());
            deliverySiteEntity.setPlanOutTime(deliverySiteDTO.getPlanOutTime());
            deliverySiteEntityList.add(deliverySiteEntity);
        }
        deliveryBatchEntity.setDeliverySiteList(deliverySiteEntityList);

        //配送单id
        List<Long> orderIdList = deliveryOrderUpdateCommand.getOrderIdList();
        if (orderIdList == null) {
            orderIdList = new ArrayList<>();
        }
        //配送单
        List<DeliveryOrderEntity> deliveryOrderEntityList = new ArrayList<>();
        for (Long orderId : orderIdList) {
            DeliveryOrderEntity deliveryOrderEntity = new DeliveryOrderEntity();
            deliveryOrderEntity.setId(orderId);
            deliveryOrderEntity.setDeliveryBatchId(deliveryOrderUpdateCommand.getDeliveryBatchId());

            deliveryOrderEntityList.add(deliveryOrderEntity);
        }

        deliveryBatchEntity.setDeliveryOrderEntityList(deliveryOrderEntityList);
        //费用信息
        List<DeliveryBatchFareEntity> batchFareEntities = new ArrayList<>();
        List<DeliveryBatchFareCommand> deliveryBatchFareCommandList = deliveryOrderUpdateCommand.getDeliveryBatchFareCommandList();
        if (!CollectionUtils.isEmpty(deliveryBatchFareCommandList)){
            for (DeliveryBatchFareCommand deliveryBatchFareCommand : deliveryBatchFareCommandList) {
                DeliveryBatchFareEntity deliveryBatchFareEntity = new DeliveryBatchFareEntity();
                deliveryBatchFareEntity.setId(deliveryBatchFareCommand.getId());
                DeliveryBatchEnums.FareType fareType = DeliveryBatchEnums.FareType.getFareByVal(deliveryBatchFareCommand.getFareType());
                deliveryBatchFareEntity.setFareType(fareType);
                deliveryBatchFareEntity.setAmount(deliveryBatchFareCommand.getAmount());
                batchFareEntities.add(deliveryBatchFareEntity);
            }
        }
        deliveryBatchEntity.setDeliveryBatchFareEntityList(batchFareEntities);

        //关联关系信息
        List<DeliveryBatchRelationEntity> batchRelationEntities = new ArrayList<>();
        List<DeliveryBatchRelationCommand> deliveryBatchRelationCommandList = deliveryOrderUpdateCommand.getDeliveryBatchRelationCommandList();
        if (!CollectionUtils.isEmpty(deliveryBatchRelationCommandList)){
            batchRelationEntities = deliveryBatchRelationCommandList.stream().map(e -> new DeliveryBatchRelationEntity(e.getBatchId(), e.getRelateBatchId(), e.getCreator())).collect(Collectors.toList());
        }
        deliveryBatchEntity.setDeliveryBatchRelationEntityList(batchRelationEntities);

        return deliveryBatchEntity;
    }
}