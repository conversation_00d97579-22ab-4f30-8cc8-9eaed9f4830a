package net.summerfarm.tms.local.delivery;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.RegConstant;
import net.summerfarm.goods.client.enums.AgentTypeEnum;
import net.summerfarm.input.WaypointsInput;
import net.summerfarm.tms.alert.DeliveryAlertDomainService;
import net.summerfarm.tms.base.AbstractPageQuery;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.car.CarRepository;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.carrier.CarrierRepository;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.config.TmsConfigRepository;
import net.summerfarm.tms.base.config.entity.ConfigEntity;
import net.summerfarm.tms.base.driver.DriverDomainService;
import net.summerfarm.tms.base.driver.DriverRepository;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.driver.enums.BusinessTypeEnum;
import net.summerfarm.tms.base.path.PathDomainService;
import net.summerfarm.tms.base.path.PathRepository;
import net.summerfarm.tms.base.path.PathSectionRepository;
import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.base.path.entity.TmsPathSectionEntity;
import net.summerfarm.tms.base.site.SiteDomainService;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.common.EventBusService;
import net.summerfarm.tms.common.util.DateUtils;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.config.TmsNacosConfig;
import net.summerfarm.tms.constants.DataSychConstants;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.delivery.*;
import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.delivery.repository.TmsDeliveryBatchExtQueryRepository;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistClientVO;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.dist.vo.DistStaticVO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.event.EventTypeEnum;
import net.summerfarm.tms.event.TmsDeliveryEvent;
import net.summerfarm.tms.excel.pojo.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.ext.AuthExtService;
import net.summerfarm.tms.facade.mall.dto.SkuDTO;
import net.summerfarm.tms.facade.pms.PmsQueryFacade;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.facade.wms.dto.SkuBarcodeDTO;
import net.summerfarm.tms.facade.wnc.WncQueryFacade;
import net.summerfarm.tms.facade.wnc.dto.WarehousLogisticsCenterDTO;
import net.summerfarm.tms.facade.wnc.dto.WarehouseStorageCenterDTO;
import net.summerfarm.tms.facade.wnc.input.WarehouseLogisticsQueryInput;
import net.summerfarm.tms.facade.wnc.input.XmWarehouseQueryInput;
import net.summerfarm.tms.local.base.site.SiteDtoConverter;
import net.summerfarm.tms.local.delivery.converter.*;
import net.summerfarm.tms.local.path.PathDtoConverter;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.summerfarm.tms.message.out.CompletePathMessage;
import net.summerfarm.tms.path.dto.TmsPathDTO;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.summerfarm.tms.query.base.driver.DriverQuery;
import net.summerfarm.tms.query.delivery.*;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.query.path.PathQuery;
import net.summerfarm.tms.query.path.PathSectionQuery;
import net.summerfarm.tms.service.common.DownloadCenterService;
import net.summerfarm.tms.util.ExcelUtils;
import net.summerfarm.tms.util.ExecutorUtil;
import net.summerfarm.tms.util.ThreadLocalUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.gaode.support.enums.DriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.enums.XMDriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.service.LbsGaoDeService;
import net.xianmu.gaode.support.service.input.PathSectionInput;
import net.xianmu.gaode.support.service.vo.PathSection;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Description: <br/>
 * date: 2022/9/13 14:59<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DeliveryBatchServiceImpl implements DeliveryBatchService {

    private final DistOrderService distOrderService;
    private final DeliveryBatchDomainService deliveryBatchDomainService;
    private final AuthExtService authExtService;
    private final DriverDomainService driverDomainService;
    private final SiteDomainService siteDomainService;
    private final DistOrderDomainService distOrderDomainService;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final PathDomainService pathDomainService;
    private final PathRepository pathRepository;
    private final DeliverySiteDomainService deliverySiteDomainService;
    private final DeliveryOrderDomainService deliveryOrderDomainService;
    private final DeliverySiteRepository deliverySiteRepository;
    private final SiteRepository siteRepository;
    private final DriverRepository driverRepository;
    private final DeliveryPickDomainService deliveryPickDomainService;
    private final EventBusService eventBusService;
    private final DistOrderRepository distOrderRepository;
    private final PathSectionRepository pathSectionRepository;
    private final DeliveryPickRepository deliveryPickRepository;
    private final DeliverySectionRepository deliverySectionRepository;
    private final DeliveryAlertDomainService deliveryAlertDomainService;
    private final DeliveryBatchValidator deliveryBatchValidator;
    private final DeliveryBatchFareDomainService deliveryBatchFareDomainService;
    private final DeliveryBatchFareRepository deliveryBatchFareRepository;
    private final CarRepository carRepository;
    private final CarrierRepository carrierRepository;
    private final WmsQueryFacade wmsQueryFacade;
    private final TmsConfigRepository tmsConfigRepository;
    private final WncQueryFacade wncQueryFacade;
    private final DownloadCenterService downloadCenterService;
    private final PmsQueryFacade pmsQueryFacade;
    private final LbsGaoDeService lbsGaoDeService;
    private final TmsDeliveryPickShortOrderMappingCommandDomainService tmsDeliveryPickShortOrderMappingCommandDomainService;
    private final TmsNacosConfig tmsNacosConfig;
    private final TmsDeliveryBatchExtQueryRepository tmsDeliveryBatchExtQueryRepository;
    private final DistOrderStatusChangeMsgSend distOrderStatusChangeMsgSend;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> deliveryBatchSave(DeliveryOrderSaveCommond deliveryOrderSaveCommond) {
        //配送批次的保存
        DeliveryBatchEntity deliveryBatchEntity = DeliveryBatchCommondConverter.commond2DeliveryBatchEntity(deliveryOrderSaveCommond);
        //参数校验
        saveParamCheak(deliveryBatchEntity);
        // 调单单绑定承运单数据 保存货更新校验
        List<DeliveryOrderEntity> bindingDeliveryOrderList = deliveryBatchEntity.getDeliveryOrderEntityList();
        if(!CollectionUtils.isEmpty(bindingDeliveryOrderList)){
            this.deliveryBatchSaveOrUpdateCheck(deliveryBatchEntity,bindingDeliveryOrderList.stream().map(DeliveryOrderEntity::getId).collect(Collectors.toList()));
        }
        deliveryBatchEntity.setCreateId(authExtService.getCurrentUserId());
        String currentUserName = authExtService.getCurrentUserName();
        deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.TO_BE_PICKED);
        //配送批次的保存
        deliveryBatchDomainService.createDeliveryBatch(deliveryBatchEntity);
        //修改配送单状态
        if (CollectionUtils.isNotEmpty(deliveryBatchEntity.getDeliveryOrderEntityList())) {
            for (DeliveryOrderEntity deliveryOrderEntity : deliveryBatchEntity.getDeliveryOrderEntityList()) {
                DeliveryOrderEntity deliveryOrder = deliveryOrderRepository.query(deliveryOrderEntity.getId());
                distOrderDomainService.bindDeliveryBatchSuc(deliveryOrder.getDistOrderId());
            }
        }
        //保存干线费用明细项
        deliveryBatchFareDomainService.deliveryBatchFareSave(deliveryBatchEntity.getDeliveryBatchFareEntityList(), deliveryBatchEntity.getId(), currentUserName);
        //调度单自动关联处理
        deliveryBatchDomainService.autoRelateBatch(deliveryBatchEntity);
        //查询装载率确认变更的批次ID集合
        List<Long> confirmRelateBatchIds = deliveryBatchDomainService.confirmBatchLoadRatio(deliveryBatchEntity);
        //保存干线路段距离
        deliveryBatchDomainService.deliveryBatchPathSectionDistanceSave(deliveryBatchEntity);
        //触发装载率计算
        deliveryBatchDomainService.calcBatchLoadRatio(confirmRelateBatchIds);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 新增参数校验
     *
     * @param deliveryBatchEntity 校验
     */
    private void saveParamCheak(DeliveryBatchEntity deliveryBatchEntity) {
        //参数校验
        Integer type = deliveryBatchEntity.getType();
        //履约日期
        LocalDateTime deliveryTime = deliveryBatchEntity.getDeliveryTime();
        //承运时间(开始)
        LocalDateTime beginTime = deliveryBatchEntity.getBeginTime();
        Long carrierId = deliveryBatchEntity.getCarrierId();
        Long driverId = deliveryBatchEntity.getDriverId();
        Long carId = deliveryBatchEntity.getCarId();
        BigDecimal estimateFare = deliveryBatchEntity.getEstimateFare();
        DeliveryBatchEnums.CarryType carryType = deliveryBatchEntity.getCarryType();

        //运输线路 至少两个运输节点
        List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();

        Set<Long> siteIdSet = deliverySiteList.stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toSet());
        if (deliverySiteList.size() != siteIdSet.size()) {
            throw new TmsRuntimeException(ErrorCodeEnum.REPEAT_SITE);
        }
        //校验预计到达时间
        List<DeliverySiteEntity> deliverySiteEntityList = deliverySiteList.stream().sorted(Comparator.comparing(DeliverySiteEntity::getPlanArriveTime)).collect(Collectors.toList());
        //查询新增调度单-选择详细地址类型的可选点位列表
        Set<Long> purchaseOwnSaleOutSiteIdSet = siteDomainService.queryPurchaseOwnSalePickDistSite();
        List<Integer> needValidateSiteType = Arrays.asList(TmsSiteTypeEnum.CUSTOMER.getCode(), TmsSiteTypeEnum.PURCHASE_ADDRESS.getCode());

        for (int i = 0; i < deliverySiteEntityList.size(); i++) {
            DeliverySiteEntity deliverySiteEntity = deliverySiteList.get(i);
            DeliverySiteEntity deliverySiteEntitySort = deliverySiteEntityList.get(i);
            if (!Objects.equals(deliverySiteEntity.getSiteId(), deliverySiteEntitySort.getSiteId())) {
                throw new TmsRuntimeException(ErrorCodeEnum.TIME_ERROR);
            }
            if (Objects.nonNull(deliverySiteEntity.getPlanOutTime()) && Objects.nonNull(deliverySiteEntity.getPlanArriveTime())) {
                if (deliverySiteEntity.getPlanOutTime().isBefore(deliverySiteEntity.getPlanArriveTime())) {
                    throw new TmsRuntimeException(ErrorCodeEnum.SAME_OUT_TIME_AFTER_ARRIVE_TIME);
                }
            }
            //校验出仓时间
            if (i > 0) {
                DeliverySiteEntity beforeDeliverySite = deliverySiteEntityList.get(i - 1);
                if (Objects.nonNull(beforeDeliverySite.getPlanOutTime()) && Objects.nonNull(deliverySiteEntity.getPlanArriveTime())) {
                    if (beforeDeliverySite.getPlanOutTime().isAfter(deliverySiteEntity.getPlanArriveTime())) {
                        throw new TmsRuntimeException(ErrorCodeEnum.OUT_TIME_AFTER_ARRIVE_TIME);
                    }
                }
            }
            //校验点位是否是可选点位列表中的点位 避免因为调度单复制而带出历史点位导致与BMS报价单不匹配
            if (needValidateSiteType.contains(deliverySiteEntity.getSiteType())) {
                if (!purchaseOwnSaleOutSiteIdSet.contains(deliverySiteEntity.getSiteId())){
                    throw new TmsRuntimeException(String.format("%d号站点选择有误，请删除该站点后重新添加", i + 1));
                }
            }
        }

        //参数校验
        TmsAssert.notNull(type, ErrorCodeEnum.PARAM_NOT_NULL, "type");
        TmsAssert.notNull(deliveryTime, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        TmsAssert.notNull(beginTime, ErrorCodeEnum.PARAM_NOT_NULL, "beginTime");
        TmsAssert.notNull(carrierId, ErrorCodeEnum.PARAM_NOT_NULL, "carrierId");
        TmsAssert.notNull(driverId, ErrorCodeEnum.PARAM_NOT_NULL, "driverId");
        TmsAssert.notNull(carId, ErrorCodeEnum.PARAM_NOT_NULL, "carId");
        TmsAssert.condition(deliverySiteList.size() < 2, ErrorCodeEnum.PARAM_ERROR, "deliverySiteList");
        TmsAssert.condition(deliverySiteList.size() > 20, ErrorCodeEnum.PARAM_ERROR, "deliverySiteList");
        List<Integer> needValidateCarryTypeOfBatchType = Arrays.asList(DeliveryBatchTypeEnum.transfer.getCode(), DeliveryBatchTypeEnum.purchase.getCode());
        TmsAssert.condition(needValidateCarryTypeOfBatchType.contains(type) && carryType == null, ErrorCodeEnum.PARAM_NOT_NULL, "carryType");
        TmsAssert.condition(!needValidateCarryTypeOfBatchType.contains(type) && carryType != null, ErrorCodeEnum.PARAM_ERROR, "carryType");
        if (estimateFare != null) {
            if (estimateFare.compareTo(new BigDecimal(100000000)) > 0) {
                throw new TmsRuntimeException(ErrorCodeEnum.ESTIMATE_FARE_ERROR);
            }
        }
        //费用项校验
        deliveryBatchValidator.validateBatchFare(deliveryBatchEntity.getDeliveryBatchFareEntityList(), deliveryBatchEntity.getEstimateFare());
    }

    @Override
    public TmsResult<DeliveryBatchDTO> deliveryBatchDetail(Long deliveryBatchId) {
        TmsAssert.notNull(deliveryBatchId, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryBatchId");
        //获取批次信息
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(deliveryBatchId);
        DeliveryBatchDTO deliveryBatchDTO = DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity);
        //基础信息
        if (deliveryBatchEntity.getCreateId() != null) {
            deliveryBatchDTO.setCreator(authExtService.getAdminNameById(Long.parseLong(String.valueOf(deliveryBatchEntity.getCreateId()))));
        } else {
            deliveryBatchDTO.setCreator("系统");
        }
        //设置点位
        deliverySite(deliveryBatchEntity, deliveryBatchDTO);
        //设置配送单
        deliveryOrder(deliveryBatchEntity, deliveryBatchDTO);
        //设置运费明细
        List<DeliveryBatchFareEntity> batchFareEntities = deliveryBatchFareRepository.queryList(deliveryBatchId);
        deliveryBatchDTO.setDeliveryBatchFareDTOList(batchFareEntities.stream().map(DeliveryBatchFareConverter::entity2Dto).collect(Collectors.toList()));
        //设置关联关系
        List<DeliveryBatchRelationEntity> batchRelationEntities = deliveryBatchRepository.queryBatchRelationListWithDetail(deliveryBatchId);
        deliveryBatchDTO.setDeliveryBatchRelationDTOList(batchRelationEntities.stream().map(DeliveryBatchRelationConverter::entity2Dto).collect(Collectors.toList()));

        return TmsResult.success(deliveryBatchDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> deliveryBatchUpdate(DeliveryOrderUpdateCommand deliveryOrderUpdateCommand) {
        //调度单更新校验
        this.batchUpdateCheckData(deliveryOrderUpdateCommand);
        //配送批次的保存
        DeliveryBatchEntity deliveryBatchEntity = DeliveryBatchCommondConverter.commond2DeliveryBatchUpdateEntity(deliveryOrderUpdateCommand);
        //参数校验
        deliveryBatchUpdateParamCheak(deliveryBatchEntity);
        String currentUserName = authExtService.getCurrentUserName();
        //查询当前批次下面有哪些排线点位
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(deliveryBatchEntity.getId()).build());
        List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
        for (DeliverySiteEntity deliverySiteEntity : deliverySiteList) {
            deliverySiteEntity.setDeliveryBatchId(deliveryBatchEntity.getId());
        }
        //配送路线--先删除在新增
        deliverySiteDomainService.confirmDeliveryPlan(deliveryBatchEntity.getId(),deliveryBatchEntity.getDeliverySiteList(),deliverySiteEntities);
        //配送单的更新
        List<DistOrderEntity> toBeWirdeList = new ArrayList<>();
        List<DistOrderEntity>  inDeliveryList = new ArrayList<>();
        deliveryOrderDomainService.confirmDeliveryPlan(deliveryBatchEntity.getId(),deliveryBatchEntity.getDeliveryOrderEntityList(),
                toBeWirdeList,inDeliveryList);
        //更新承运单状态
        distOrderDomainService.confirmDeliveryPlan(toBeWirdeList,DistOrderStatusEnum.TO_BE_WIRED);
        distOrderDomainService.confirmDeliveryPlan(inDeliveryList,DistOrderStatusEnum.IN_DELIVERY);
        //配送批次更新
        deliveryBatchDomainService.confirmDeliveryPlan(deliveryBatchEntity);
        //费用明细项更新
        deliveryBatchFareDomainService.deliveryBatchFareSave(deliveryBatchEntity.getDeliveryBatchFareEntityList(), deliveryBatchEntity.getId(), currentUserName);
        //查询装载率确认变更的批次ID集合
        List<Long> confirmRelateBatchIds = deliveryBatchDomainService.confirmBatchLoadRatio(deliveryBatchEntity);
        //调度单手动关联处理
        deliveryBatchDomainService.handRelateBatch(deliveryBatchEntity, deliveryBatchEntity.getId(), currentUserName);
        //触发装载率计算
        deliveryBatchDomainService.calcBatchLoadRatio(confirmRelateBatchIds);
        // 干线转运配送中消息发送
        List<Long> distIdList = inDeliveryList.stream().map(DistOrderEntity::getDistId).collect(Collectors.toList());
        distOrderStatusChangeMsgSend.trunkTransportationDeliveryInProgressMessageSend(distIdList);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 调度单更新校验
     * @param deliveryOrderUpdateCommand 更新
     */
    private void batchUpdateCheckData(DeliveryOrderUpdateCommand deliveryOrderUpdateCommand) {
        //校验批次是否存在
        DeliveryBatchEntity deliveryBatchDb = deliveryBatchRepository.query(deliveryOrderUpdateCommand.getDeliveryBatchId());
        if(deliveryBatchDb == null || deliveryBatchDb.getId() == null){
            throw new TmsRuntimeException("当前批次ID不存在:" + deliveryOrderUpdateCommand.getDeliveryBatchId());
        }
        if(Objects.equals(deliveryBatchDb.getType(),DeliveryBatchTypeEnum.all_category_pick.getCode()) &&
                deliveryBatchDb.getStatus().getCode() >= DeliveryBatchStatusEnum.IN_DELIVERY.getCode()){
            throw new TmsRuntimeException("提货用车调度单当前状态不支持编辑");
        }
        //查询配送单类型
        List<Long> deliveryIdList = deliveryOrderUpdateCommand.getOrderIdList();
        //绑定单据不为空、调度单类型是非全品类用车，则不能绑定全品类用车单据、未到截单时间不能绑定干线转运单据
        deliveryBatchSaveOrUpdateCheck(deliveryBatchDb, deliveryIdList);
    }

    /**
     * 调单单绑定承运单数据 保存货更新校验
     * @param deliveryBatchSaveOrUpdateData 保存或更新的调度单数据
     * @param bindingBatchDeliveryOrderIds 要绑定的配送单ID
     */
    private void deliveryBatchSaveOrUpdateCheck(DeliveryBatchEntity deliveryBatchSaveOrUpdateData, List<Long> bindingBatchDeliveryOrderIds) {
        if (CollectionUtils.isEmpty(bindingBatchDeliveryOrderIds)) {
            return;
        }
        List<DeliveryOrderEntity> bindingBatchDeliveryOrders = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().deliveryIds(bindingBatchDeliveryOrderIds).build());
        if(CollectionUtils.isEmpty(bindingBatchDeliveryOrders)){
            throw new BizException("配送单数据不存在");
        }
        // 校验绑定的配送单类型是否符合当前批次类型
        this.bindingDistTypeValid(deliveryBatchSaveOrUpdateData, bindingBatchDeliveryOrders);

        // 干线转运截单时间校验
        this.trunkTransportCloseTimeValid(bindingBatchDeliveryOrders);
    }

    /**
     * 干线转运截单时间校验
     * @param bindingBatchDeliveryOrders 要绑定的配送单数据
     */
    private void trunkTransportCloseTimeValid(List<DeliveryOrderEntity> bindingBatchDeliveryOrders) {
        List<Long> distOrderIds = bindingBatchDeliveryOrders.stream()
                .map(DeliveryOrderEntity::getDistOrderId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder().distIdList(distOrderIds).build());
        List<DistOrderEntity> trunkTransportDistOrders = distOrderEntities.stream()
                .filter(e -> Objects.equals(DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode(), e.getFulfillmentDeliveryWay()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(trunkTransportDistOrders)) {
            List<Long> beginSiteIds = trunkTransportDistOrders.stream().map(e -> e.getBeginSite().getId()).distinct().collect(Collectors.toList());
            Map<Long, SiteEntity> siteIdToSiteEntityMap = siteRepository.queryMapByIds(beginSiteIds);
            distOrderEntities.forEach(dist -> {
                SiteEntity siteEntity = siteIdToSiteEntityMap.get(dist.getBeginSite().getId());
                if (siteEntity == null) {
                    return;
                }
                // 截单时间判断
                this.closeTimeValid(Integer.parseInt(siteEntity.getOutBusinessNo()), dist.getDistFlowVO().getExpectBeginTime().toLocalDate());
            });
        }
    }

    /**
     * 校验绑定的配送单类型是否符合当前批次类型
     * @param deliveryBatchSaveOrUpdateData 保存或更新的调度单数据
     * @param bindingBatchDeliveryOrders 要绑定的配送单数据
     */
    private static void bindingDistTypeValid(DeliveryBatchEntity deliveryBatchSaveOrUpdateData, List<DeliveryOrderEntity> bindingBatchDeliveryOrders) {
        List<DistOrderSourceEnum> distOrderSourceEnumList = bindingBatchDeliveryOrders.stream().map(DeliveryOrderEntity::getSource).collect(Collectors.toList());

        Integer deliveryBatchDbType = deliveryBatchSaveOrUpdateData.getType();
        if (Objects.equals(deliveryBatchDbType, DeliveryBatchTypeEnum.all_category_pick.getCode())) {
            if (!Collections.singletonList(DistOrderSourceEnum.ALL_CATEGORY_PICK).containsAll(distOrderSourceEnumList)) {
                throw new TmsRuntimeException("当前批次类型为提货用车，不能绑定非全品类用车单据");
            }
        } else if(Objects.equals(deliveryBatchDbType, DeliveryBatchTypeEnum.all_ecology.getCode())){
            if(!DistOrderSourceEnum.getTrunkOuterSource().containsAll(distOrderSourceEnumList)){
                throw new TmsRuntimeException("当前批次类型为全生态用车，不能绑定非外单单据");
            }
        } else {
            if (distOrderSourceEnumList.contains(DistOrderSourceEnum.ALL_CATEGORY_PICK)) {
                throw new TmsRuntimeException("当前批次类型为非提货用车，不能绑定全品类用车单据");
            }
            if (distOrderSourceEnumList.contains(DistOrderSourceEnum.OUTER_TRUNK) || distOrderSourceEnumList.contains(DistOrderSourceEnum.OUTER_TRUNK_CITY)) {
                throw new TmsRuntimeException("当前批次类型为非全生态用车，不能绑定外单单据");
            }
        }
    }

    /**
     * 参数校验
     *
     * @param deliveryBatchEntity
     */
    private void deliveryBatchUpdateParamCheak(DeliveryBatchEntity deliveryBatchEntity) {
        Long id = deliveryBatchEntity.getId();
        //参数校验
        Integer type = deliveryBatchEntity.getType();
        //履约日期
        LocalDateTime deliveryTime = deliveryBatchEntity.getDeliveryTime();
        //承运时间(开始)
        LocalDateTime beginTime = deliveryBatchEntity.getBeginTime();
        Long carrierId = deliveryBatchEntity.getCarrierId();
        Long driverId = deliveryBatchEntity.getDriverId();
        Long carId = deliveryBatchEntity.getCarId();
        DeliveryBatchEnums.CarryType carryType = deliveryBatchEntity.getCarryType();
        BigDecimal estimateFare = deliveryBatchEntity.getEstimateFare();
        //干线单独校验逻辑
        if(!Objects.equals(deliveryBatchEntity.getType() , DeliveryBatchTypeEnum.city)){
            //运输线路 至少两个运输节点
            List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
            Set<Long> siteIdSet = deliverySiteList.stream().map(DeliverySiteEntity::getId).collect(Collectors.toSet());
            if(deliverySiteList.size() != siteIdSet.size()){
                throw new TmsRuntimeException(ErrorCodeEnum.REPEAT_SITE);
            }
            //校验预计到达时间
            List<DeliverySiteEntity> deliverySiteEntityList = deliverySiteList.stream().sorted(Comparator.comparing(DeliverySiteEntity::getPlanArriveTime)).collect(Collectors.toList());
            for (int i = 0; i < deliverySiteEntityList.size(); i++) {
                DeliverySiteEntity deliverySiteEntity = deliverySiteList.get(i);
                DeliverySiteEntity deliverySiteEntitySort = deliverySiteEntityList.get(i);
                if (!Objects.equals(deliverySiteEntity.getId(), deliverySiteEntitySort.getId())) {
                    throw new TmsRuntimeException(ErrorCodeEnum.TIME_ERROR);
                }
                if (Objects.nonNull(deliverySiteEntity.getPlanOutTime()) && Objects.nonNull(deliverySiteEntity.getPlanArriveTime())) {
                    if (deliverySiteEntity.getPlanOutTime().isBefore(deliverySiteEntity.getPlanArriveTime())) {
                        throw new TmsRuntimeException(ErrorCodeEnum.SAME_OUT_TIME_AFTER_ARRIVE_TIME);
                    }
                }
                //校验出仓时间
                if (i > 0) {
                    DeliverySiteEntity beforeDeliverySite = deliverySiteEntityList.get(i - 1);
                    if (Objects.nonNull(beforeDeliverySite.getPlanOutTime()) && Objects.nonNull(deliverySiteEntity.getPlanArriveTime())) {
                        if (beforeDeliverySite.getPlanOutTime().isAfter(deliverySiteEntity.getPlanArriveTime())) {
                            throw new TmsRuntimeException(ErrorCodeEnum.OUT_TIME_AFTER_ARRIVE_TIME);
                        }
                    }


                }
            }
            TmsAssert.condition(deliverySiteList.size() < 2, ErrorCodeEnum.PARAM_ERROR, "deliverySiteList");
            TmsAssert.condition(deliverySiteList.size() > 20, ErrorCodeEnum.PARAM_ERROR, "deliverySiteList");
        }
        //参数校验
        TmsAssert.notNull(id, ErrorCodeEnum.PARAM_NOT_NULL, "id");
        TmsAssert.notNull(type, ErrorCodeEnum.PARAM_NOT_NULL, "type");
        TmsAssert.notNull(deliveryTime, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        TmsAssert.notNull(beginTime, ErrorCodeEnum.PARAM_NOT_NULL, "beginTime");
        TmsAssert.notNull(carrierId, ErrorCodeEnum.PARAM_NOT_NULL, "carrierId");
        TmsAssert.notNull(driverId, ErrorCodeEnum.PARAM_NOT_NULL, "driverId");
        TmsAssert.notNull(carId, ErrorCodeEnum.PARAM_NOT_NULL, "carId");
        List<Integer> needValidateCarryTypeOfBatchType = Arrays.asList(DeliveryBatchTypeEnum.transfer.getCode(), DeliveryBatchTypeEnum.purchase.getCode());
        TmsAssert.condition(needValidateCarryTypeOfBatchType.contains(type) && carryType == null, ErrorCodeEnum.PARAM_NOT_NULL, "carryType");
        TmsAssert.condition(!needValidateCarryTypeOfBatchType.contains(type) && carryType != null, ErrorCodeEnum.PARAM_ERROR, "carryType");


        List<DeliveryBatchStatusEnum> canEditStatusList = Arrays.asList(DeliveryBatchStatusEnum.TO_BE_WIRED, DeliveryBatchStatusEnum.TO_BE_PICKED, DeliveryBatchStatusEnum.IN_DELIVERY);
        DeliveryBatchEntity deliveryBatch = deliveryBatchRepository.query(id);
        TmsAssert.condition(!canEditStatusList.contains(deliveryBatch.getStatus()), ErrorCodeEnum.ERROR_STATE_EDIT, deliveryBatch.getStatus().getName());

        if (Objects.equals(deliveryBatch.getStatus(), DeliveryBatchStatusEnum.DELIVERY_CLOSED) ||
                Objects.equals(deliveryBatch.getStatus(), DeliveryBatchStatusEnum.COMPLETE_DELIVERY)) {
            throw new TmsRuntimeException(ErrorCodeEnum.BATCH_STATE_ERROR, deliveryBatch.getStatus().getName());
        }
        if (estimateFare != null) {
            if (estimateFare.compareTo(new BigDecimal(100000000)) > 0) {
                throw new TmsRuntimeException(ErrorCodeEnum.ESTIMATE_FARE_ERROR);
            }
        }
        //费用项校验
        deliveryBatchValidator.validateBatchFare(deliveryBatchEntity.getDeliveryBatchFareEntityList(), estimateFare);
        //关联关系校验 新增关联关系默认无创建人
        deliveryBatchValidator.validateBatchRelate(deliveryBatchEntity.getDeliveryBatchRelationEntityList(), deliveryBatchEntity.getId());

    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> deliveryBatchClose(Long deliveryBatchId, String closeReason) {
        //参数校验
        TmsAssert.notNull(deliveryBatchId, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryBatchId");
        TmsAssert.notNull(closeReason, ErrorCodeEnum.PARAM_NOT_NULL, "closeReason");
        //更新调单信息
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchClose(deliveryBatchId, closeReason, authExtService.getCurrentUserName());
        //存在配送单的话需要更改配送单状态
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(
                DeliveryOrderQuery.builder().batchId(deliveryBatchId).build());
        //更新配送单状态
        deliveryOrderDomainService.deliveryOrderClose(deliveryOrderEntities);
        //查询装载率确认变更的批次ID集合
        List<Long> confirmRelateBatchIds = deliveryBatchDomainService.confirmBatchLoadRatio(deliveryBatchEntity);
        //更新关联关系
        deliveryBatchDomainService.deliveryBatchRelationClose(deliveryBatchId);
        //触发装载率计算
        deliveryBatchDomainService.calcBatchLoadRatio(confirmRelateBatchIds);
        return TmsResult.VOID_SUCCESS;
    }

    private void deliveryOrder(DeliveryBatchEntity deliveryBatchEntity, DeliveryBatchDTO deliveryBatchDTO) {
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryBatchEntity.getDeliveryOrderEntityList();
        ArrayList<TrunkDeliveryDistOrderDTO> trunkDeliveryDistOrderDTOS = new ArrayList<>();
        deliveryOrderEntityList.forEach(deliveryOrder -> {
            //委托单id
            Long distOrderId = deliveryOrder.getDistOrderId();

            DeliveryOrderDTO deliveryOrderDTO = new DeliveryOrderDTO();
            deliveryOrderDTO.setDeliveryBatchId(deliveryOrder.getDeliveryBatchId());
            deliveryOrderDTO.setDistOrderId(distOrderId);
            deliveryOrderDTO.setId(deliveryOrder.getId());
            //委托单信息
            DistOrderEntity distOrderDetail = distOrderDomainService.getDistOrderDetail(distOrderId);
            TrunkDeliveryDistOrderDTO trunkDeliveryDistOrderDTO = TrunkDeliveryDistOrderDTOConverter.deliveryOrderVO2TrunkDeliveryDistOrderDTO(deliveryOrder);
            setGoodInfo(distOrderDetail.getDistStaticVO(), trunkDeliveryDistOrderDTO);

            trunkDeliveryDistOrderDTO.setBeginSiteName(siteDomainService.query(deliveryOrder.getBeginSiteId()).getSiteName());
            trunkDeliveryDistOrderDTO.setEndSiteName(siteDomainService.query(deliveryOrder.getEndSiteId()).getSiteName());
            trunkDeliveryDistOrderDTOS.add(trunkDeliveryDistOrderDTO);

        });
        deliveryBatchDTO.setTrunkDeliveryDistOrderDTOS(trunkDeliveryDistOrderDTOS);
    }

    private void deliverySite(DeliveryBatchEntity deliveryBatchEntity, DeliveryBatchDTO deliveryBatchDTO) {
        List<DeliverySiteDTO> deliverySiteDTOList = new ArrayList<>();
        //点位信息
        List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
        Map<Long, SiteEntity> siteEntityMap = siteRepository.queryMapByIds(deliverySiteList.stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList()));
        deliverySiteList.forEach(site -> {
            SiteEntity siteEntity = siteEntityMap.get(site.getSiteId());
            if(siteEntity == null){
                return;
            }
            DeliverySiteDTO deliverySiteDTO = new DeliverySiteDTO();
            deliverySiteDTO.setId(site.getId());
            deliverySiteDTO.setSiteId(site.getSiteId());
            deliverySiteDTO.setSiteName(siteEntity.getSiteName());
            deliverySiteDTO.setPlanArriveTime(site.getPlanArriveTime());
            deliverySiteDTO.setSequence(site.getSequence());
            deliverySiteDTO.setType(site.getType());
            deliverySiteDTO.setSiteType(siteEntity.getType());
            deliverySiteDTO.setSignInTime(site.getSignInTime());
            deliverySiteDTO.setSignOutTime(site.getSignOutTime());
            deliverySiteDTO.setSignInStatus(site.getSignInStatus());
            deliverySiteDTO.setSignOutStatus(site.getSignOutStatus());
            deliverySiteDTO.setPlanOutTime(site.getPlanOutTime());
            deliverySiteDTO.setSiteUse(siteEntity.getSiteUse());
            deliverySiteDTO.setOutBusinessNo(siteEntity.getOutBusinessNo());
            deliverySiteDTOList.add(deliverySiteDTO);
        });

        deliveryBatchDTO.setDeliverySiteDTOList(deliverySiteDTOList.stream().sorted(Comparator.comparing(DeliverySiteDTO::getSequence)).collect(Collectors.toList()));

        // 站点拣货数据
        Map<Long, List<DeliveryPickEntity>> deliverySiteId2PickListMap = deliveryPickDomainService.queryTrunkPickDetailByBathIds(Collections.singletonList(deliveryBatchEntity.getId()));
        if(!CollectionUtils.isEmpty(deliverySiteDTOList)){
            deliverySiteDTOList.forEach(deliverySite ->{
                // 处理拣货标识
                List<DeliveryPickEntity> currDeliverySitePickList = deliverySiteId2PickListMap.get(deliverySite.getId());
                deliverySite.setNeedPickUpFlag(!CollectionUtils.isEmpty(currDeliverySitePickList));
            });
        }
    }

    @Override
    public TmsResult<PageInfo<DeliveryBatchDTO>> list(DeliveryBatchQuery deliveryBatchQuery) {
        PageInfo<DeliveryBatchEntity> deliveryBatchEntityPageInfo = deliveryBatchDomainService.queryListWithSite(deliveryBatchQuery);

        List<DeliveryBatchEntity> list = deliveryBatchEntityPageInfo.getList();
        ArrayList<DeliveryBatchDTO> deliveryBatchDTOS = new ArrayList<>();
        Map<Long, CarrierEntity> carrierMap = getCarrierMap(list.stream().filter(a -> 
                Objects.nonNull(a.getCarrierId())).map(DeliveryBatchEntity::getCarrierId).collect(Collectors.toList()));
        Map<Long, CarEntity> carMap = getCarMap(list.stream().filter(a -> 
                Objects.nonNull(a.getCarId())).map(DeliveryBatchEntity::getCarId).collect(Collectors.toList()));
        Map<Long, DriverEntity> driverMap = getDriverMap(list.stream().filter(a -> 
                Objects.nonNull(a.getDriverId())).map(DeliveryBatchEntity::getDriverId).collect(Collectors.toSet()));
        list.forEach(deliveryBatchEntity -> {
            DeliveryBatchDTO deliveryBatchDTO = DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity);
            //获取承运商名称
            if (deliveryBatchEntity.getCarrierId() != null) {
                CarrierEntity carrierEntity = carrierMap.get(deliveryBatchEntity.getCarrierId());
                if(Objects.nonNull(carrierEntity)){
                    deliveryBatchDTO.setCarrierName(carrierEntity.getCarrierName());
                }
            }
            //获取司机名称
            if (deliveryBatchEntity.getDriverId() != null) {
                DriverEntity driverEntity = driverMap.get(deliveryBatchEntity.getDriverId());
                if (Objects.nonNull(driverEntity)) {
                    deliveryBatchDTO.setDriverId(driverEntity.getId());
                    deliveryBatchDTO.setDriver(driverEntity.getName());
                    deliveryBatchDTO.setDriverPhone(driverEntity.getPhone());
                }
            }
            //获取车辆信息
            if (deliveryBatchEntity.getCarId() != null) {
                CarEntity carEntity = carMap.get(deliveryBatchEntity.getCarId());
               if(Objects.nonNull(carEntity)) {
                   deliveryBatchDTO.setCarNumber(carEntity.getCarNumber());
                   deliveryBatchDTO.setCarType(CarTypeEnum.typeMap.get(carEntity.getType()).getDesc());
                   deliveryBatchDTO.setStorageName(carEntity.getCarStorageEnum().getName());
               }
            }
            if (deliveryBatchEntity.getCreateId() != null) {
                deliveryBatchDTO.setCreator(authExtService.getAdminNameById(Long.parseLong(String.valueOf(deliveryBatchEntity.getCreateId()))));
            } else {
                deliveryBatchDTO.setCreator("系统");
            }
            List<DeliverySiteDTO> deliverySiteDTOList = deliveryBatchDTO.getDeliverySiteDTOList();
            if(!CollectionUtils.isEmpty(deliverySiteDTOList)){
                List<DeliverySiteDTO> sortSiteList = deliverySiteDTOList.stream().sorted(Comparator.comparing(DeliverySiteDTO::getSequence)).collect(Collectors.toList());
                deliveryBatchDTO.setDeliverySiteDTOList(sortSiteList);
            }
            deliveryBatchDTOS.add(deliveryBatchDTO);
        });

        PageInfo<DeliveryBatchDTO> listPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(deliveryBatchEntityPageInfo, listPageInfo);
        listPageInfo.setList(deliveryBatchDTOS);
        return TmsResult.success(listPageInfo);
    }

    
    private Map<Long, CarrierEntity> getCarrierMap(List<Long> carrierIds) {
        if (CollectionUtils.isEmpty(carrierIds)) {
            return Maps.newHashMap();
        }
        List<CarrierEntity> carrierEntities = carrierRepository.queryPrimaryIdList(carrierIds);
        if (CollectionUtils.isEmpty(carrierEntities)) {
            return Maps.newHashMap();
        }
        return carrierEntities.stream().collect(Collectors.toMap(CarrierEntity::getId, Function.identity()));
    }
    

    @Override
    public TmsResult<List<DeliveryBatchDTO>> matchDeliveryBatch(List<DeliveryBatchDTO> deliveryBatchList, List<Long> deliverySiteIds) {
//        deliveryBatchQuery.setPageSize(400);
//        PageInfo<DeliveryBatchEntity> deliveryBatchEntityPage = deliveryBatchDomainService.queryListWithSite(deliveryBatchQuery);
        if (CollectionUtils.isEmpty(deliveryBatchList)) {
            return TmsResult.success(Collections.emptyList());
        }
        List<DeliveryBatchDTO> deliveryBatchDTOList = new ArrayList<>();
        for (DeliveryBatchDTO deliveryBatchDTO : deliveryBatchList) {
            if (isMatchPath(deliveryBatchDTO.getDeliverySiteDTOList(), deliverySiteIds)) {
                deliveryBatchDTOList.add(deliveryBatchDTO);
            }
        }
        return TmsResult.success(deliveryBatchDTOList);
    }

    private boolean isMatchPath(List<DeliverySiteDTO> deliverySiteList, List<Long> matchSiteIds) {
        if (CollectionUtils.isEmpty(deliverySiteList) || CollectionUtils.isEmpty(matchSiteIds)) {
            return false;
        }
        int i = 0, j = 0;
        deliverySiteList.sort(Comparator.comparingInt(DeliverySiteDTO::getSequence));
        for (; i < deliverySiteList.size() && j < matchSiteIds.size(); i++) {
            if (deliverySiteList.get(i).getSiteId().equals(matchSiteIds.get(j))) {
                j++;
            }
        }
        return j == matchSiteIds.size();
    }

    @Override
    public TmsResult<PageInfo<TrunkDeliveryDistOrderDTO>> intelligentDistPage(DeliveryBatchQuery deliveryBatchQuery) {
        // 参数校验
        cheakIntelligentDistParam(deliveryBatchQuery);

        // 构建查询条件
        DeliveryOrderQuery deliveryOrderQuery = buildOptimizedDeliveryOrderQuery(deliveryBatchQuery);

        // 分页查询配送单
        PageInfo<DeliveryOrderEntity> deliveryOrderPageInfo = deliveryOrderRepository.queryPage(deliveryOrderQuery);
        List<DeliveryOrderEntity> deliveryOrderList = deliveryOrderPageInfo.getList();

        // 如果没有数据，直接返回空结果
        if (CollectionUtils.isEmpty(deliveryOrderList)) {
            return TmsResult.success(new PageInfo<>());
        }

        // 批量转换数据
        List<TrunkDeliveryDistOrderDTO> resultList = batchConvertDeliveryOrders(deliveryOrderList);

        // 构建分页结果
        PageInfo<TrunkDeliveryDistOrderDTO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(deliveryOrderPageInfo, resultPageInfo);
        resultPageInfo.setList(resultList);

        return TmsResult.success(resultPageInfo);
    }

    /**
     * 构建优化的配送单查询条件
     *
     * @param deliveryBatchQuery 原始查询条件
     * @return 配送单查询条件
     */
    private DeliveryOrderQuery buildOptimizedDeliveryOrderQuery(DeliveryBatchQuery deliveryBatchQuery) {
        DeliveryOrderQuery deliveryOrderQuery = new DeliveryOrderQuery();
        deliveryOrderQuery.setPageIndex(deliveryBatchQuery.getPageIndex());
        deliveryOrderQuery.setPageSize(deliveryBatchQuery.getPageSize());
        deliveryOrderQuery.setFulfillmentDeliveryWays(DistOrderFulfillmentDeliveryWayEnum.getTrunkDeliveryWays());
        deliveryOrderQuery.setLastSql("group by dist_order_id,begin_site_id,end_site_id,delivery_time order by id desc");

        if (Boolean.TRUE.equals(deliveryBatchQuery.getNoCondition())) {
            // 智能推荐查询（无条件）
            buildIntelligentQueryConditions(deliveryOrderQuery, deliveryBatchQuery);
        } else {
            // 有条件查询
            buildConditionalQueryConditions(deliveryOrderQuery, deliveryBatchQuery);
        }

        return deliveryOrderQuery;
    }

    /**
     * 构建智能推荐查询条件
     */
    private void buildIntelligentQueryConditions(DeliveryOrderQuery deliveryOrderQuery, DeliveryBatchQuery deliveryBatchQuery) {
        deliveryOrderQuery.setSourceList(DeliveryBatchTypeSourceEnum.typeSourceMap.get(deliveryBatchQuery.getDeliveryBatchType()));
        deliveryOrderQuery.setDeliveryTime(deliveryBatchQuery.getDeliveryTime().toLocalDate());

        // 构建站点组合列表
        List<Long> deliverySiteIds = deliveryBatchQuery.getDeliverySiteIds();
        if (!CollectionUtils.isEmpty(deliverySiteIds)) {
            List<String> siteList = generateSiteCombinations(deliverySiteIds);
            deliveryOrderQuery.setSiteList(siteList);
            deliveryOrderQuery.convertSiteListToSitePairs();
        }
    }

    /**
     * 构建有条件查询
     */
    private void buildConditionalQueryConditions(DeliveryOrderQuery deliveryOrderQuery, DeliveryBatchQuery deliveryBatchQuery) {
        if (deliveryBatchQuery.getBeginDeliveryTime() != null) {
            deliveryOrderQuery.setBeginDeliveryTime(LocalDateTime.of(
                    deliveryBatchQuery.getBeginDeliveryTime().toLocalDate(), LocalTime.MIN));
        }

        if (deliveryBatchQuery.getEndDeliveryTime() != null) {
            deliveryOrderQuery.setEndDeliveryTime(LocalDateTime.of(
                    deliveryBatchQuery.getEndDeliveryTime().toLocalDate(), Constants.localTimeMaxTime));
        }

        deliveryOrderQuery.setDistOrderId(deliveryBatchQuery.getDistId());

        if (!CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchTypeList())) {
            deliveryOrderQuery.setSourceList(deliveryBatchQuery.getDeliveryBatchTypeList());
        }
    }

    /**
     * 生成站点组合列表
     */
    private List<String> generateSiteCombinations(List<Long> deliverySiteIds) {
        List<String> siteList = new ArrayList<>();
        for (int i = 0; i < deliverySiteIds.size(); i++) {
            for (int j = i + 1; j < deliverySiteIds.size(); j++) {
                siteList.add(deliverySiteIds.get(i) + "#" + deliverySiteIds.get(j));
            }
        }
        return siteList;
    }

    /**
     * 批量转换配送单为干线配送委托单DTO
     *
     * @param deliveryOrderList 配送单列表
     * @return 转换后的DTO列表
     */
    private List<TrunkDeliveryDistOrderDTO> batchConvertDeliveryOrders(List<DeliveryOrderEntity> deliveryOrderList) {
        if (CollectionUtils.isEmpty(deliveryOrderList)) {
            return new ArrayList<>();
        }

        // 批量查询站点信息
        Map<Long, SiteEntity> siteEntityMap = batchQuerySiteInfo(deliveryOrderList);

        // 批量查询委托单详情
        Map<Long, DistOrderEntity> distOrderMap = batchQueryDistOrderDetails(deliveryOrderList);

        // 批量转换数据
        return deliveryOrderList.stream()
                .map(deliveryOrder -> convertToTrunkDeliveryDistOrderDTO(deliveryOrder, siteEntityMap, distOrderMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 批量查询站点信息
     */
    private Map<Long, SiteEntity> batchQuerySiteInfo(List<DeliveryOrderEntity> deliveryOrderList) {
        Set<Long> siteIds = deliveryOrderList.stream()
                .flatMap(order -> Stream.of(order.getBeginSiteId(), order.getEndSiteId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        return siteRepository.queryMapByIds(new ArrayList<>(siteIds));
    }

    /**
     * 批量查询委托单详情
     */
    private Map<Long, DistOrderEntity> batchQueryDistOrderDetails(List<DeliveryOrderEntity> deliveryOrderList) {
        List<Long> distOrderIds = deliveryOrderList.stream()
                .map(DeliveryOrderEntity::getDistOrderId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(distOrderIds)) {
            return Collections.emptyMap();
        }
        // 使用批量查询方法
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryListWithItem(DistOrderQuery.builder().distIdList(distOrderIds).build());
        return distOrderEntities.stream()
                .collect(Collectors.toMap(DistOrderEntity::getDistId, Function.identity()));
    }

    /**
     * 转换单个配送单为干线配送委托单DTO
     */
    private TrunkDeliveryDistOrderDTO convertToTrunkDeliveryDistOrderDTO(
            DeliveryOrderEntity deliveryOrder,
            Map<Long, SiteEntity> siteEntityMap,
            Map<Long, DistOrderEntity> distOrderMap) {

        try {
            TrunkDeliveryDistOrderDTO dto = TrunkDeliveryDistOrderDTOConverter.deliveryOrderVO2TrunkDeliveryDistOrderDTO(deliveryOrder);

            // 设置站点名称
            SiteEntity beginSite = siteEntityMap.get(deliveryOrder.getBeginSiteId());
            SiteEntity endSite = siteEntityMap.get(deliveryOrder.getEndSiteId());
            dto.setBeginSiteName(beginSite != null ? beginSite.getSiteName() : null);
            dto.setEndSiteName(endSite != null ? endSite.getSiteName() : null);

            // 设置商品信息
            DistOrderEntity distOrder = distOrderMap.get(deliveryOrder.getDistOrderId());
            if (distOrder != null && distOrder.getDistStaticVO() != null) {
                setGoodInfo(distOrder.getDistStaticVO(), dto);
            }

            return dto;
        } catch (Exception e) {
            log.warn("转换配送单{}为DTO时发生异常", deliveryOrder.getId(), e);
            return null;
        }
    }

    /**
     * 查询参数校验
     *
     * @param deliveryBatchQuery 查询
     */
    private void cheakIntelligentDistParam(DeliveryBatchQuery deliveryBatchQuery) {
        List<Long> deliverySiteIds = deliveryBatchQuery.getDeliverySiteIds();
        LocalDateTime deliveryTime = deliveryBatchQuery.getDeliveryTime();
        Integer deliveryBatchType = deliveryBatchQuery.getDeliveryBatchType();
        Boolean noCondition = deliveryBatchQuery.getNoCondition();

        int pageNum = deliveryBatchQuery.getPageIndex();
        int pageSize = deliveryBatchQuery.getPageSize();

        TmsAssert.condition(deliverySiteIds == null, ErrorCodeEnum.PARAM_NOT_NULL, "deliverySiteIds");
        TmsAssert.condition(deliverySiteIds.size() <= 1, ErrorCodeEnum.SITE_SIZE_ERROR, "SITE_SIZE_ERROR");
        TmsAssert.notNull(deliveryTime, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        TmsAssert.notNull(deliveryBatchType, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryBatchType");
        TmsAssert.notNull(noCondition, ErrorCodeEnum.PARAM_NOT_NULL, "noCondition");
        TmsAssert.notNull(pageNum, ErrorCodeEnum.PARAM_NOT_NULL, "pageNum");
        TmsAssert.notNull(pageSize, ErrorCodeEnum.PARAM_NOT_NULL, "pageSize");
    }

    /**
     * 设置商品信息
     *
     * @param distStaticVO
     * @param trunkDeliveryDistOrderDTO
     */
    private void setGoodInfo(DistStaticVO distStaticVO, TrunkDeliveryDistOrderDTO trunkDeliveryDistOrderDTO) {
        trunkDeliveryDistOrderDTO.setColdQuantity(distStaticVO.getColdQuantity());
        trunkDeliveryDistOrderDTO.setColdVolume(distStaticVO.getColdVolume());
        trunkDeliveryDistOrderDTO.setColdWeight(distStaticVO.getColdWeight());

        trunkDeliveryDistOrderDTO.setFreezeQuantity(distStaticVO.getFreezeQuantity());
        trunkDeliveryDistOrderDTO.setFreezeVolume(distStaticVO.getFreezeVolume());
        trunkDeliveryDistOrderDTO.setFreezeWeight(distStaticVO.getFreezeWeight());

        trunkDeliveryDistOrderDTO.setNormalQuantity(distStaticVO.getNormalQuantity());
        trunkDeliveryDistOrderDTO.setNormalVolume(distStaticVO.getNormalVolume());
        trunkDeliveryDistOrderDTO.setNormalWeight(distStaticVO.getNormalWeight());

        trunkDeliveryDistOrderDTO.setTotalWeight(distStaticVO.getTotalWeight());
        trunkDeliveryDistOrderDTO.setTotalVolume(distStaticVO.getTotalVolume());
        trunkDeliveryDistOrderDTO.setTotalQuantity(distStaticVO.getTotalQuantity());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> finishDelivery(Long deliveryBatchId) {
        TmsAssert.notNull(deliveryBatchId, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryBatchId");
        //完成配送 需要修改调度单状态
        deliveryBatchDomainService.finishDelivery(deliveryBatchId);

        //根据配送批次查询
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().batchId(deliveryBatchId).build());
        List<Long> distOrderIdList = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
        //调用承运单接口
        distOrderIdList.forEach(distId -> {
            distOrderService.completeDistOrder(distId);
        });
        //查询配送批次信息
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.getBatchDetail(deliveryBatchId);
        //根据批次id获取运输线路
        deliveryBatchEntity.setDeliverySiteList(deliverySiteRepository.queryListWithSite(DeliverySiteQuery.builder().batchId(deliveryBatchId).build()));
        //查询配送详情,设置总数量、总体积、总重量
        List<DistOrderEntity> bindDistOrdersWithBatch = distOrderRepository.queryDistOrdersWithItemByBatchId(deliveryBatchId);
        deliveryBatchEntity.setTotalQuantity(bindDistOrdersWithBatch.stream().map(DistOrderEntity::getDistStaticVO).mapToInt(DistStaticVO::getTotalQuantity).sum());
        deliveryBatchEntity.setTotalVolume(bindDistOrdersWithBatch.stream().map(DistOrderEntity::getDistStaticVO).map(DistStaticVO::getTotalVolume).reduce(BigDecimal.ZERO, BigDecimal::add));
        deliveryBatchEntity.setTotalWeight(bindDistOrdersWithBatch.stream().map(DistOrderEntity::getDistStaticVO).map(DistStaticVO::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
        //查询运费明细信息
        List<DeliveryBatchFareEntity> batchFareEntities = deliveryBatchFareRepository.queryList(deliveryBatchId);
        deliveryBatchEntity.setDeliveryBatchFareEntityList(batchFareEntities);
        eventBusService.notifyTrunkDeliveryEvent(TrunkDeliveryEventConverter.buildTmsDeliveryEvent(EventTypeEnum.COMPLETE_DELIVERY,
                deliveryBatchEntity));
        return TmsResult.VOID_SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<TmsPathDTO> addSiteBatch(DeliveryBatchDTO deliveryBatchDTO) {
        //先查询历史路线是否存在，存在就使用该路线并生成批次，不存在就新增路线和批次
        DeliveryBatchQuery deliveryBatchQuery = new DeliveryBatchQuery();
        deliveryBatchQuery.setBeginSiteId(deliveryBatchDTO.getBeginSiteId());
        deliveryBatchQuery.setDeliveryTime(deliveryBatchDTO.getDeliveryTime());
        deliveryBatchQuery.setDeliveryBatchType(deliveryBatchDTO.getType());
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryListWithDriver(deliveryBatchQuery);

        //过滤出其他路线
        List<DeliveryBatchEntity> deliveryBatch = deliveryBatchEntityList.stream().filter(batch -> !Objects.equals(batch.getPathId(),Constants.Delivery_Batch.DEF_DELIVERY_PATH_ID)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(deliveryBatch)){
            if(deliveryBatchValidator.validateCompletePathForDeliveryBatch(deliveryBatch.get(0))){
                return TmsResult.fail(ErrorCodeEnum.DB_DATA_ERROR.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DB_DATA_ERROR, "已经完成排线"));
            }
        }
        //过滤未排线的数据
        deliveryBatchEntityList = deliveryBatchEntityList.stream().filter(deliveryBatchEntity -> StringUtils.isNotBlank(deliveryBatchEntity.getPathCode())).collect(Collectors.toList());

        //生成路线code
        String pathCode = deliveryBatchDomainService.createPathCode(deliveryBatchEntityList);

        PathQuery pathQuery = new PathQuery();
        pathQuery.setType(TmsPathTypeEnum.DELIVERY_ROAD.getCode());
        pathQuery.setBeginSiteId(deliveryBatchDTO.getBeginSiteId());
        pathQuery.setStatus(TmsPathStatusEnum.ACTIVE.getCode());
        pathQuery.setNeedPagination(false);
        PageInfo<TmsPathEntity> pathPageList = pathRepository.queryPage(pathQuery);
        List<TmsPathEntity> pathList = pathPageList.getList();

        //路线
        TmsPathEntity tmsPathEntity = pathDomainService.addSiteBatch(pathList,pathCode,authExtService.getCurrentUserId().toString(),deliveryBatchDTO.getBeginSiteId());
        //通过路线保存批次信息
        DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();
        deliveryBatchEntity.setDeliveryTime(deliveryBatchDTO.getDeliveryTime());
        deliveryBatchEntity.setType(DeliveryBatchTypeEnum.city.getCode());
        deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.TO_BE_WIRED);
        deliveryBatchEntity.setCarId(tmsPathEntity.getCarId());
        deliveryBatchEntity.setDriverId(tmsPathEntity.getDriverId());
        deliveryBatchEntity.setCarrierId(tmsPathEntity.getCarrierId());
        deliveryBatchEntity.setPathCode(pathCode);
        deliveryBatchEntity.setPathId(tmsPathEntity.getPathId());
        deliveryBatchEntity.setPathName(tmsPathEntity.getPathName());
        deliveryBatchEntity.setBeginSiteId(tmsPathEntity.getBeginSiteId());
        deliveryBatchEntity.setCreateId(authExtService.getCurrentUserId());

        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
        deliverySiteEntity.setPlanArriveTime(deliveryBatchDTO.getDeliveryTime());
        deliverySiteEntity.setSiteId(deliveryBatchDTO.getBeginSiteId());
        deliveryBatchEntity.setDeliverySiteList(Arrays.asList(deliverySiteEntity));

        deliveryBatchDomainService.createDeliveryBatch(deliveryBatchEntity);
        if(tmsPathEntity.getDriverId() != null){
            DriverEntity driverEntity = driverRepository.query(tmsPathEntity.getDriverId());
            if(driverEntity != null){
                tmsPathEntity.setDriverName(driverEntity.getName());
                tmsPathEntity.setDriverPhone(driverEntity.getPhone());
            }
        }
        tmsPathEntity.setPathCode(pathCode);
        return TmsResult.success(PathDtoConverter.entity2Dto(tmsPathEntity));
    }

    /**
     * 司机是否存在、司机身份、司机和车辆关系、司机重复信息检查、车辆重复检查、订单拦截数量校验
     * @param driverIdSet 司机id集合
     * @param carIdSet 车辆id集合
     * @param driverPathMap 司机路线集合
     * @param carIdPathMap 车辆路线集合
     * @param deliveryBatchDTO 批次
     * @param driverEntity 司机
     * @param deliveryBatchEntity 批次
     */
    private void preCheak(HashSet<Long> driverIdSet, HashSet<Long> carIdSet, HashMap<Long, String> driverPathMap, HashMap<Long, String> carIdPathMap, DeliveryBatchDTO deliveryBatchDTO, DriverEntity driverEntity, DeliveryBatchEntity deliveryBatchEntity) {
        if(deliveryBatchDTO.getDriverId() == null && deliveryBatchEntity.getDeliverySiteList() != null && deliveryBatchEntity.getDeliverySiteList().size() > 1){
            throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,deliveryBatchDTO.getPathCode()+"路线存在排线，请选择合适司机！！");
        }
        if((driverEntity == null || driverEntity.getId() == null) && (deliveryBatchEntity.getDeliverySiteList() != null && deliveryBatchEntity.getDeliverySiteList().size() > 1)){
            throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR, deliveryBatchDTO.getPathCode()+"不存在此司机");
        }
        if(Objects.equals(driverEntity.getBusinessType(), BusinessTypeEnum.trunk.getCode())){
            throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR, deliveryBatchDTO.getPathCode()+"路线存在干线司机身份，请检查！！");
        }
        //排线下面没有数据，不需要校验司机、车辆、绑定校验
        if(deliveryBatchDTO.getTotalSiteNum() != null && deliveryBatchDTO.getTotalSiteNum() != 0){
            if(driverEntity.getCityCarId() == null){
                throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR, deliveryBatchDTO.getPathCode()+"路线,司机不存在车辆绑定关系，请绑定后再选择！");
            }
            if(driverEntity.getCityCarrierId() == null){
                throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR, deliveryBatchDTO.getPathCode()+"路线,司机不存在承运商绑定关系，请绑定后再选择！");
            }
            //司机校验
            if(deliveryBatchDTO.getDriverId() != null && !driverIdSet.contains(deliveryBatchDTO.getDriverId())){
                driverIdSet.add(deliveryBatchDTO.getDriverId());
                driverPathMap.put(deliveryBatchDTO.getDriverId(), deliveryBatchDTO.getPathCode());
            }else{
                StringBuilder sb = new StringBuilder();
                sb.append("路线").append(driverPathMap.get(deliveryBatchDTO.getDriverId())).append("和")
                        .append("路线").append(deliveryBatchDTO.getPathCode()).append("存在重复司机,请核对无误后在提交！");
                throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,sb.toString());
            }
            //车辆校验
            if(!carIdSet.contains(driverEntity.getCityCarId())){
                //添加车辆id
                carIdSet.add(driverEntity.getCityCarId());
                carIdPathMap.put(driverEntity.getCityCarId(), deliveryBatchDTO.getPathCode());
            }else{
                StringBuilder sb = new StringBuilder();
                sb.append("线路").append(carIdPathMap.get(driverEntity.getCityCarId())).append("和")
                        .append("线路").append(deliveryBatchDTO.getPathCode()).append("司机车辆信息存在重复，请核对无误后再提交！");
                throw new TmsRuntimeException(sb.toString());
            }
        }
        //完成排线校验
        if(deliveryBatchEntity.getStatus() != null && deliveryBatchEntity.getStatus().getCode() != DeliveryBatchStatusEnum.TO_BE_WIRED.getCode()){
            throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,"配送任务已存在,无法完成排线");
        }
        List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
        if(CollectionUtils.isEmpty(deliverySiteList)){
            return;
        }
        if(deliverySiteList.size() -1 - deliveryBatchDTO.getTotalSiteNum() > 10){
            throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,"路线"+ deliveryBatchDTO.getPathCode()+"当前路线存在大量的点位数据变动情况，请返回查看");
        }
        //减一是去掉城配仓点位
        if(deliverySiteList.size() - 1  - deliveryBatchDTO.getTotalSiteNum() > 0){
            throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,"路线"+ deliveryBatchDTO.getPathCode()+"点位数据有变动，请返回查看");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> completePath(ArrayList<DeliveryBatchDTO> deliveryBatchDTOS) {
        if(CollectionUtils.isEmpty(deliveryBatchDTOS)){
            throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,"未开始排线");
        }
        //截单时间校验
        Integer storeNo = deliveryBatchDTOS.get(0).getStoreNo();
        LocalDateTime deliveryTime = deliveryBatchDTOS.get(0).getDeliveryTime();
        this.closeTimeValid(storeNo,deliveryTime.toLocalDate());
        //批次请求和数据数量校验
        Map<String, List<DeliveryBatchEntity>> pathCode2BatchListMap = this.batchReqRdsCountValid(deliveryBatchDTOS);
        //司机是否存在、司机身份、司机和车辆关系、司机重复信息检查、车辆重复检查、订单拦截校验、是否已经完成排线
        HashSet<Long> driverIdSet = new HashSet<Long>();
        HashSet<Long> carIdSet = new HashSet<Long>();
        HashMap<Long, String> driverPathMap = new HashMap<>();
        HashMap<Long, String> carIdPathMap = new HashMap<>();
        ArrayList<DeliveryBatchEntity> batchList = new ArrayList<>();
        for (DeliveryBatchDTO deliveryBatchDTO : deliveryBatchDTOS) {
            if(deliveryBatchDTO.getDriverId() == null && deliveryBatchDTO.getTotalSiteNum() > 0){
                throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,deliveryBatchDTO.getPathCode()+"路线存在排线，请选择合适司机！！");
            }
            List<DeliveryBatchEntity> pathCodeBatchList = pathCode2BatchListMap.get(deliveryBatchDTO.getPathCode());
            if(CollectionUtils.isEmpty(pathCodeBatchList)){
                throw new BizException(deliveryBatchDTO.getPathCode() + "没有对应的路线批次");
            }
            if(pathCodeBatchList.size() != 1){
                throw new BizException(deliveryBatchDTO.getPathCode() + "路线批次数量不符合预期");
            }
            DeliveryBatchEntity deliveryBatchInfo = pathCodeBatchList.get(0);
            if(deliveryBatchInfo == null || deliveryBatchInfo.getId() == null){
                throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,ErrorCodeEnum.COMPLETE_PATH_ERROR.msg,deliveryBatchDTO.getPathCode() + "批次不存在");
            }

            DriverEntity driverEntity = new DriverEntity();
            if(deliveryBatchDTO.getDriverId() != null){
                driverEntity = driverRepository.queryWithCityCar(DriverQuery.builder().id(deliveryBatchDTO.getDriverId()).build());
            }

            DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(deliveryBatchInfo.getId());
            deliveryBatchEntity.setCreateId(authExtService.getCurrentUserId());

            //预校验检查 司机是否存在、司机身份、司机和车辆关系、司机重复信息检查、车辆重复检查、订单拦截数量校验
            preCheak(driverIdSet, carIdSet, driverPathMap, carIdPathMap, deliveryBatchDTO, driverEntity, deliveryBatchEntity);
            deliveryBatchEntity.setCarId(driverEntity.getCityCarId());
            deliveryBatchEntity.setDriverId(driverEntity.getId());
            deliveryBatchEntity.setCarrierId(driverEntity.getCityCarrierId());
            deliveryBatchEntity.setCreateId(authExtService.getCurrentUserId());

            batchList.add(deliveryBatchEntity);
        }

        DeliveryBatchDTO deliveryBatchDTO = deliveryBatchDTOS.get(0);
        // 智能排线
        this.intelligentPath(IntelligentPathUpdateCommand.builder()
                .handleIntelligentFlag(false)
                .batchId(batchList.get(0).getId())
                .storeNo(deliveryBatchDTO.getStoreNo())
                .completePathFlag(true)
                .build());

        for (DeliveryBatchEntity deliveryBatchEntity : batchList) {
            //修改配送单和委托单状态和生成详情
            deliveryOrderDomainService.completePath(deliveryBatchEntity.getDeliveryOrderEntityList());
            //生成拣货任务（大客户需要单独生成）
            deliveryPickDomainService.completePath(deliveryBatchEntity);
            //生成配点位详情
            deliverySiteDomainService.completePath(deliveryBatchEntity);
            //确定路线
            pathDomainService.completePath(deliveryBatchEntity);
            //批次完成排线
            deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.TO_BE_PICKED);
            deliveryBatchEntity.setBePathTime(LocalDateTime.now());
            deliveryBatchDomainService.completePath(deliveryBatchEntity);
        }

        if(CollectionUtils.isEmpty(batchList) || batchList.get(0).getBeginSiteId() == null){
            return TmsResult.VOID_SUCCESS;
        }
        //数据同步
        ThreadLocalUtil.getThreadLocal().put(DataSychConstants.Manage.COMPLETE_PATH,batchList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList()));

        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 批次请求和数据数量校验
     * @param deliveryBatchDTOSReq 请求
     * @return 数据库批次信息 key->pathCode  value->List<DeliveryBatchEntity>
     */
    private Map<String, List<DeliveryBatchEntity>> batchReqRdsCountValid(ArrayList<DeliveryBatchDTO> deliveryBatchDTOSReq) {
        //查询数据库批次信息
        DeliveryBatchDTO batchReqFirst = deliveryBatchDTOSReq.get(0);
        List<DeliveryBatchEntity> rdsBatchList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .beginSiteId(batchReqFirst.getBeginSiteId())
                .deliveryTime(batchReqFirst.getDeliveryTime())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .build());
        if(CollectionUtils.isEmpty(rdsBatchList)){
            throw new BizException("数据库批次路线信息为空");
        }
        //过滤PathCode为空的数据(排除未拍路线)
        List<DeliveryBatchEntity> noCityWarehouseBatchList = rdsBatchList.stream().filter(batch -> Objects.nonNull(batch.getPathCode())).collect(Collectors.toList());
        if(noCityWarehouseBatchList.size() != deliveryBatchDTOSReq.size()){
            throw new BizException("请求批次路线数量和数据库路线数量不符合预期");
        }
        if(rdsBatchList.size() - 1 != noCityWarehouseBatchList.size()){
            throw new BizException("路线pathCode存在异常，请联系管理员");
        }
        return noCityWarehouseBatchList.stream().collect(Collectors.groupingBy(DeliveryBatchEntity::getPathCode));
    }

    /**
     * 检查城配仓是否已过截单时间（公共方法）
     *
     * @param storeNo 城配仓编号
     * @param deliveryTime 配送日期
     * @return true-已过截单时间，false-未过截单时间
     */
    public boolean isAfterCloseTime(Integer storeNo, LocalDate deliveryTime) {
        try {
            closeTimeValid(storeNo, deliveryTime);
            // 如果没有抛异常，说明已过截单时间
            return true;
        } catch (Exception e) {
            if (e instanceof TmsRuntimeException) {// NOSONAR
                // 抛异常说明未过截单时间
                return false;
            } else {
                log.error("检查城配仓截单时间时发生异常", e);
                throw e;
            }
        }
    }

    private void closeTimeValid(Integer storeNo, LocalDate deliveryTime) {
        if(storeNo == null || deliveryTime == null){
            throw new BizException("截单时间判断:城配仓和配送日期不能为空");
        }
        //完成排线时间校验
        List<WarehousLogisticsCenterDTO> storeDTOList = wncQueryFacade.queryWarehouseLogisticsList(WarehouseLogisticsQueryInput.builder().storeNo(storeNo).build());
        if(CollectionUtils.isEmpty(storeDTOList)){
            throw new TmsRuntimeException("城配仓编号不存在");
        }
        WarehousLogisticsCenterDTO storeDTO = storeDTOList.get(0);
        String closeTimeStr = storeDTO.getCloseTime();
        LocalDate endDeliveryDate = deliveryTime.minusDays(1);
        if(StringUtils.isBlank(closeTimeStr)){
            log.error("城配仓不存在截单时间:{}",storeDTO.getStoreName());
            throw new TmsRuntimeException("城配仓不存在截单时间");
        }
        // 解析时分秒字符串
        LocalTime closeTime = null;
        try {
            closeTime = LocalTime.parse(closeTimeStr);
        } catch (Exception e) {
            log.error("城配仓截单时间格式存在错误:{}",closeTimeStr);
            throw new TmsRuntimeException("城配仓截单时间格式存在错误,请联系管理员");
        }
        // 拼接日期和时间
        LocalDateTime completeDateTime = LocalDateTime.of(endDeliveryDate, closeTime);
        if(LocalDateTime.now().isBefore(completeDateTime)){
            throw new TmsRuntimeException("城配仓未到截单时间:"+completeDateTime);
        }
        //如果是POP转配城配仓,完成排线需要特殊处理加30分钟
        List<Integer> popWarehouseLogisticsList = wncQueryFacade.queryPopWarehouseLogisticsList();
        if(popWarehouseLogisticsList.contains(storeNo) && LocalDateTime.now().isBefore(completeDateTime.plusMinutes(30))){
            throw new TmsRuntimeException("POP城配仓未到完成排线时间:"+completeDateTime.plusMinutes(30));
        }

        // 查询所有的共配POP城配仓,完成排线需要特殊处理加30分钟
        List<Integer> sharedDeliveryPopWarehouseLogisticsList = wncQueryFacade.querySharedDeliveryPopWarehouseLogistics();
        if(sharedDeliveryPopWarehouseLogisticsList.contains(storeNo) && LocalDateTime.now().isBefore(completeDateTime.plusMinutes(30))){
            throw new TmsRuntimeException("共配POP城配仓未到完成排线时间:"+completeDateTime.plusMinutes(30));
        }

        //不支持加单
        if(!wncQueryFacade.queryIsSupportAddOrder(storeNo)){
            return;
        };
        //加单需要加30分钟
        if(LocalDateTime.now().isBefore(completeDateTime.plusMinutes(30))){
            throw new TmsRuntimeException("城配仓未到完成排线时间:"+completeDateTime.plusMinutes(30));
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> updatePathDriver(DeliveryBatchDTO deliveryBatchDTO) {
        //查询司机信息
        DriverEntity driverEntity = driverDomainService.driverDetail(deliveryBatchDTO.getDriverId());
        //校验
        TmsAssert.notNull(driverEntity, ErrorCodeEnum.NOT_FIND,ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.NOT_FIND,"司机"));
        TmsAssert.condition(Objects.equals(driverEntity.getStatus(), TmsDriverStatus.invalid.getCode()),
                ErrorCodeEnum.DRIVER_STATUS_INVALID,ErrorCodeEnum.DRIVER_STATUS_INVALID.msg);

        DeliveryBatchEntity batchEntity = deliveryBatchRepository.query(deliveryBatchDTO.getDeliveryBatchId());
        if(batchEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"批次");
        }
        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
        deliverySiteEntity.setSiteId(batchEntity.getBeginSiteId());
        batchEntity.setDeliverySiteList(Arrays.asList(deliverySiteEntity));
        deliveryBatchDomainService.updatePathDriver(batchEntity,driverEntity);

        return TmsResult.VOID_SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> updatePathName(DeliveryBatchDTO deliveryBatchDTO) {
        TmsAssert.notEmpty(deliveryBatchDTO.getPathName(), ErrorCodeEnum.PARAM_NOT_NULL,"路线名称");
        TmsAssert.notNull(deliveryBatchDTO.getDeliveryBatchId(), ErrorCodeEnum.PARAM_NOT_NULL,"路线批次");
        //批次上面的路线名称，在完成排线的时候统一修改路线表的上面的信息
        DeliveryBatchEntity batchEntity = deliveryBatchRepository.query(deliveryBatchDTO.getDeliveryBatchId());

        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
        deliverySiteEntity.setSiteId(batchEntity.getBeginSiteId());
        batchEntity.setDeliverySiteList(Arrays.asList(deliverySiteEntity));

        deliveryBatchDomainService.updatePathName(batchEntity,deliveryBatchDTO.getPathName());
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<List<DeliveryBatchDTO>> getBatchDetailDelivery(DeliveryPathQuery deliveryPathQuery) {
        //验证参数
        TmsAssert.notNull(deliveryPathQuery.getDeliveryTime(), ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        TmsAssert.notNull(deliveryPathQuery.getStoreNo(), ErrorCodeEnum.PARAM_NOT_NULL, "storeNo");

        //查询城配仓对应的点位
        SiteEntity siteEntity = siteRepository.query(SiteQuery.builder()
                .outBusinessNo(deliveryPathQuery.getStoreNo().toString())
                .type(TmsSiteTypeEnum.STORE.getCode()).build());
        //根据类型、开始点位和配送时间获取批次信息
        DeliveryBatchQuery deliveryBatchQuery = DeliveryBatchQuery.builder()
                .deliveryBatchType(deliveryPathQuery.getDeliveryBatchType())
                .beginSiteId(siteEntity.getId())
                .deliveryTime(deliveryPathQuery.getDeliveryTime().atStartOfDay()).build();
        List<DeliveryBatchEntity> deliveryBatchEntities = deliveryBatchRepository.queryListWithDriver(deliveryBatchQuery);
        if (CollectionUtils.isEmpty(deliveryBatchEntities)) {
            throw new TmsRuntimeException(ErrorCodeEnum.NO_BATCH_ERROR);
        }
        deliveryBatchEntities.forEach(deliveryBatch -> {
            List<DeliverySiteEntity> deliverySiteEntities = deliverySiteDomainService.queryDetailByBatchId(deliveryBatch.getId());
            //获取批次城配仓点位
            DeliverySiteEntity beginDeliverySite = deliverySiteEntities.stream()
                    .filter(deliverySiteEntity -> Objects.equals(deliverySiteEntity.getSiteEntity().getId(), deliveryBatch.getBeginSiteId()))
                    .findAny().orElse(new DeliverySiteEntity());
            deliveryBatch.setSignOutPic(beginDeliverySite.getSignOutPics());
            deliveryBatch.setSignOutTemperature(beginDeliverySite.getSignOutTemperature());
            deliveryBatch.setSignOutTime(beginDeliverySite.getSignOutTime());
            deliveryBatch.setSignOutRemark(beginDeliverySite.getSignOutRemark());
            deliveryBatch.setVehiclePlatePics(beginDeliverySite.getVehiclePlatePics());
            deliveryBatch.setRefrigeratePics(beginDeliverySite.getRefrigeratePics());
            deliveryBatch.setFreezePics(beginDeliverySite.getFreezePics());
            //去除批次本身自己的点位
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity -> !Objects.equals(deliverySiteEntity.getSiteEntity().getId(),deliveryBatch.getBeginSiteId())).collect(Collectors.toList());
            //过滤筛选条件
            deliverySiteEntities = queryConditionBatch(deliveryPathQuery, deliverySiteEntities,beginDeliverySite);
            //设置点位订单来源信息
            deliverySiteEntities.forEach(s -> s.setOrderSourceInfo(s.findOrderSource()));
            //根据批次id获取运输单信息和点位对应配送物品信息
            deliveryBatch.setDeliverySiteList(deliverySiteEntities);
            //重量、体积、价格、店铺数计算
            deliveryBatch.batchParamCalculate();
        });


        List<DeliveryBatchDTO> deliveryBatchDTOS = deliveryBatchEntities.stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList());

        List<Long> batchIds = deliveryBatchEntities.stream()
                .map(DeliveryBatchEntity::getId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        // 查询批次的保温措施信息
        Map<Long, String> batchId2KeepTemperatureMethodPicsMap = tmsDeliveryBatchExtQueryRepository.queryKeepTemperatureMethodPicsByBatchIds(batchIds);

        deliveryBatchDTOS.forEach(deliveryBatchDTO -> {
            deliveryBatchDTO.setKeepTemperatureMethodPics(batchId2KeepTemperatureMethodPicsMap.get(deliveryBatchDTO.getDeliveryBatchId()));
        });

        return TmsResult.success(deliveryBatchDTOS);
    }

    /**
     * 过滤查询
     * @param deliveryPathQuery 查询条件
     * @param deliverySiteEntities 点位信息集合
     * @param beginDeliverySite 开始配送点位
     * @return 符合条件的点位集合
     */
    private List<DeliverySiteEntity> queryConditionBatch(DeliveryPathQuery deliveryPathQuery, List<DeliverySiteEntity> deliverySiteEntities, DeliverySiteEntity beginDeliverySite) {
        //筛选店铺
        if(StringUtils.isNotBlank(deliveryPathQuery.getMname())){
            deliverySiteEntities = deliverySiteEntities.stream()
                    .filter(deliverySiteEntity -> StringUtils.isNotBlank(deliverySiteEntity.getOuterClientName())
                            && deliverySiteEntity.getOuterClientName().contains(deliveryPathQuery.getMname()))
                    .collect(Collectors.toList());
        }
        //筛选品牌
        if(StringUtils.isNotBlank(deliveryPathQuery.getOuterBrandName())){
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                        deliverySiteEntity.getSiteDistOrders().stream().map(e -> e.getDistClientVO().getOutBrandName()).collect(Collectors.toList())
                                .contains(deliveryPathQuery.getOuterBrandName())
                   ).collect(Collectors.toList());
        }
        //筛选联系地址
        if(StringUtils.isNotBlank(deliveryPathQuery.getAddress())){
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                    deliverySiteEntity.getSiteEntity().getFullAddress().contains(deliveryPathQuery.getAddress())
                   ).collect(Collectors.toList());
        }
        //任务状态
        if(deliveryPathQuery.getInterceptState() != null){
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                    Objects.equals(deliverySiteEntity.getInterceptState(),deliveryPathQuery.getInterceptState())
                   ).collect(Collectors.toList());
        }
        //联系电话
        if(StringUtils.isNotBlank(deliveryPathQuery.getPhone())){
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                    deliverySiteEntity.getSiteEntity().getPhone().contains(deliveryPathQuery.getPhone())
                   ).collect(Collectors.toList());
        }
        //任务类型
        if(deliveryPathQuery.getDeliveryType() != null){
            //回收就要查询配送回收、回收数据
            if(Objects.equals(deliveryPathQuery.getDeliveryType(),DistTypeEnum.RECYCLE.getCode())){
                deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                        !Objects.equals(deliverySiteEntity.getDeliveryType().getCode(),DistTypeEnum.DELIVERY.getCode())
                ).collect(Collectors.toList());
            }
            //配送就要查询配送回收、配送数据
            if(Objects.equals(deliveryPathQuery.getDeliveryType(),DistTypeEnum.DELIVERY.getCode())){
                deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                        !Objects.equals(deliverySiteEntity.getDeliveryType().getCode(),DistTypeEnum.RECYCLE.getCode())
                ).collect(Collectors.toList());
            }
        }
        //配送方式
        if(deliveryPathQuery.getSendWay() != null){
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                    Objects.equals(deliverySiteEntity.getSendWay(),deliveryPathQuery.getSendWay())
                   ).collect(Collectors.toList());
        }
        //订单号
        if(StringUtils.isNotBlank(deliveryPathQuery.getOrderNo())){
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                    deliverySiteEntity.getSiteDistOrders().stream()
                            .filter(distOrderEntity -> Objects.equals(distOrderEntity.getDistClientVO().getOutOrderId(), deliveryPathQuery.getOrderNo()))
                            .count() > 0
                   ).collect(Collectors.toList());
        }
        //配送状态
        if(deliveryPathQuery.getStatus() != null){
            //======之前完成排线没有变更点位留下的坑。。后面要去掉
            if(deliveryPathQuery.getStatus() == DeliverySiteStatusEnum.FINISH_PICK.getCode()){
                //查询城配仓是否已经完成已经完成拣货
                if(beginDeliverySite.getSignOutTime() != null){
                    deliverySiteEntities.forEach(deliverySiteEntity -> {
                        if(Objects.equals(deliverySiteEntity.getStatus().getCode(), DeliverySiteStatusEnum.NO.getCode())){
                            deliverySiteEntity.setStatus(DeliverySiteStatusEnum.FINISH_PICK);
                        }
                    });
                }
            }
            if(deliveryPathQuery.getStatus() == DeliverySiteStatusEnum.NO.getCode()){
                //查询城配仓是否已经完成已经完成拣货
                if(beginDeliverySite.getSignOutTime() != null){
                    deliverySiteEntities.forEach(deliverySiteEntity -> {
                        if(Objects.equals(deliverySiteEntity.getStatus().getCode(), DeliverySiteStatusEnum.NO.getCode())){
                            deliverySiteEntity.setStatus(DeliverySiteStatusEnum.FINISH_PICK);
                        }
                    });
                }
            }
            //=======
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                    Objects.equals(deliverySiteEntity.getStatus().getCode(),deliveryPathQuery.getStatus())
                   ).collect(Collectors.toList());
        }
        //配送状态
        if(CollectionUtils.isNotEmpty(deliveryPathQuery.getStatusList())){
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity ->
                    deliveryPathQuery.getStatusList().contains(deliverySiteEntity.getStatus().getCode())
            ).collect(Collectors.toList());
        }
        //缺货状态
        if(deliveryPathQuery.getLackFlag() != null){
            List<DeliverySiteEntity> deliverySiteList = new ArrayList<>();
            for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
                List<DeliverySiteItemEntity> deliverySiteItemEntityList = deliverySiteEntity.getDeliverySiteItemEntityList();
                if(CollectionUtils.isNotEmpty(deliverySiteItemEntityList)){
                    //缺货
                    if(deliveryPathQuery.getLackFlag()){
                        List<DeliverySiteItemEntity> shortSiteItemList = deliverySiteItemEntityList.stream().filter(item -> item.getShortCount() > 0).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(shortSiteItemList)){
                            deliverySiteList.add(deliverySiteEntity);
                        }
                    }
                    //不缺货
                    if(!deliveryPathQuery.getLackFlag()){
                        List<DeliverySiteItemEntity> shortSiteItemList = deliverySiteItemEntityList.stream().filter(item -> item.getShortCount() == 0).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(shortSiteItemList)){
                            deliverySiteList.add(deliverySiteEntity);
                        }
                    }
                }
            deliverySiteEntities = deliverySiteList;
            }

        }
        return deliverySiteEntities;
    }

    @Override
    public TmsResult<DeliveryBatchDTO> query(DeliveryBatchQuery deliveryBatchQuery) {
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchQuery);
        return TmsResult.success(DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity));
    }

    @Override
    public TmsResult<Boolean> isHaveNoPath(Long beginSiteId, LocalDate deliveryTime) {
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryListWithDriver(DeliveryBatchQuery.builder()
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .beginSiteId(beginSiteId)
                .deliveryTime(deliveryTime.atStartOfDay()).build());
        //获取默认批次
        List<DeliveryBatchEntity> noPathDeliveryBatchList = deliveryBatchEntityList.stream().filter(deliveryBatchEntity -> Objects.equals(deliveryBatchEntity.getPathId(), Constants.Delivery_Batch.DEF_DELIVERY_PATH_ID)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(noPathDeliveryBatchList)){
            throw new TmsRuntimeException("没有找到未排批次信息！！");
        }
        DeliveryBatchEntity deliveryBatchEntity = noPathDeliveryBatchList.get(0);
        DeliveryBatchEntity deliveryBatchDetail = deliveryBatchDomainService.deliveryBatchDetail(deliveryBatchEntity.getId());
        List<DeliverySiteEntity> excludeBeginDeliverySiteList = deliveryBatchDetail.getExcludeBeginDeliverySiteList();
        if(CollectionUtils.isEmpty(excludeBeginDeliverySiteList) || excludeBeginDeliverySiteList.size() == 0){
            return TmsResult.success(false);
        }
        List<DeliverySiteEntity> siteList = excludeBeginDeliverySiteList.stream().filter(site -> site.getInterceptState() != DeliverySiteInterceptStateEnum.cancel.getCode()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(siteList) || siteList.size() == 0){
            return TmsResult.success(false);
        }
        return TmsResult.success(true);
    }

    @Override
    public TmsResult<Void> initBatchStatus() {
        DeliveryBatchQuery deliveryBatchQuery = new DeliveryBatchQuery();
        deliveryBatchQuery.initTrunkType();
        deliveryBatchQuery.setDeliveryBatchStatus(DeliveryBatchStatusEnum.TO_BE_WIRED.getCode());
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(deliveryBatchQuery);
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            if(deliveryBatchEntity.getDriverId() != null){
                deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.TO_BE_PICKED);
                deliveryBatchRepository.update(deliveryBatchEntity);
            }
        }
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public void notifyCompletePathDeliveryEvents(Long batchId) {
        if(batchId == null){
            return ;
        }
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(batchId);

        if(deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            return;
        }
        if(CollectionUtils.isEmpty(deliveryBatchEntity.getExcludeBeginDeliverySiteList())){
            return;
        }
        SiteEntity citySiteEntity = new SiteEntity();
        if(deliveryBatchEntity.getBeginSiteId() != null){
            citySiteEntity = siteRepository.query(deliveryBatchEntity.getBeginSiteId());
        }


        List<Long> siteIdList = deliveryBatchEntity.getExcludeBeginDeliverySiteList().stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
        List<SiteEntity> siteEntities = siteRepository.queryPrimaryIdList(siteIdList);
        if(CollectionUtils.isEmpty(siteEntities)){
            return;
        }
        Map<Long, SiteEntity> siteIdEntityMap = siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));

        //发送消息，完成排线
        ArrayList<TmsDeliveryEvent> tmsDeliveryEvents = new ArrayList<>();
        for (DeliverySiteEntity deliverySiteEntity : deliveryBatchEntity.getExcludeBeginDeliverySiteList()) {
            if(siteIdEntityMap.get(deliverySiteEntity.getSiteId()) != null){
                deliverySiteEntity.setSiteEntity(siteIdEntityMap.get(deliverySiteEntity.getSiteId()));
            }
            //过滤当前点位的配送单信息
            List<DeliveryOrderEntity> siteDeliveryOrder = deliveryBatchEntity.getDeliveryOrderEntityList().stream()
                    .filter(deliveryOrder -> Objects.equals(deliveryOrder.getEndSiteId(), deliverySiteEntity.getSiteEntity().getId()))
                    .collect(Collectors.toList());

            TmsDeliveryEvent tmsDeliveryEvent = TmsDeliveryEventConverter.buildTmsDeliveryEvent(EventTypeEnum.TO_BE_PICKED,
                    deliveryBatchEntity, deliverySiteEntity, siteDeliveryOrder, null, citySiteEntity);
            tmsDeliveryEvents.add(tmsDeliveryEvent);
        }
        //通知商城侧
        for (TmsDeliveryEvent tmsDeliveryEvent : tmsDeliveryEvents) {
            eventBusService.notifyDeliveryEvent(tmsDeliveryEvent);
        }
    }

    /**
     * 每条路线配送数据
     */
    private void createPathData(Workbook workbook, List<DeliveryBatchEntity> deliveryBatchEntityList, LocalDateTime deliveryTime) {
        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 12);
        CellStyle baseCellyStyle = workbook.createCellStyle();
        baseCellyStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        baseCellyStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        baseCellyStyle.setFont(titleFont);

        CellStyle alignmentStype = workbook.createCellStyle();
        alignmentStype.setAlignment(HorizontalAlignment.LEFT);

        Font cellFont = workbook.createFont();
        cellFont.setFontName("宋体");
        cellFont.setFontHeightInPoints((short) 10);
        //获取所有的SKU
        List<String> skus = deliveryBatchEntityList.stream()
                .map(DeliveryBatchEntity::getDeliverySiteList)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DeliverySiteEntity::getSiteDistOrders)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DistOrderEntity::getDistItems)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DistItemVO::getOutItemId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        //查询sku信息
        List<SkuDTO> skuDTOList = wmsQueryFacade.batchQueryBySkus(new ArrayList<>(skus));
        Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getSku, Function.identity(), (oldData, newData) -> newData));

        for (int i = 0; i < deliveryBatchEntityList.size(); i++) {
            String path = deliveryBatchEntityList.get(i).getPathCode();
            Sheet sheet = workbook.createSheet(path == null ? "未排路线" : "路线" + path);
            sheet.setColumnWidth(0, 3000);
            sheet.setColumnWidth(1, 3000);
            sheet.setColumnWidth(2, 5000);
            sheet.setColumnWidth(3, 15000);
            sheet.setColumnWidth(4, 4300);
            sheet.setColumnWidth(5, 4300);
            sheet.setColumnWidth(6, 4000);
            sheet.setColumnWidth(7, 3800);


            CellRangeAddress firstMergeRegion = new CellRangeAddress(0, 0, 0, 5);
            sheet.addMergedRegion(firstMergeRegion);

            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(path == null ? "未排路线" : "路线" + path);
            titleCell.setCellStyle(baseCellyStyle);
            titleRow.setHeightInPoints(50F);

            Row one = sheet.createRow(1);
            one.createCell(0).setCellValue("配送日期");
            one.createCell(1).setCellValue(deliveryTime.format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT)));
            one.setHeightInPoints(20F);
            Row two = sheet.createRow(2);
            two.createCell(0).setCellValue("配送师傅");
            two.createCell(3).setCellValue("联系方式");
            two.setHeightInPoints(11.5F);
            Row three = sheet.createRow(3);
            three.createCell(0).setCellValue("车辆编号");
            three.createCell(3).setCellValue("车牌号");

            // 配送师傅数据
            if (net.summerfarm.common.util.StringUtils.isNotBlank(path)) {
                DriverEntity driverEntity = deliveryBatchEntityList.get(i).getDriverEntity();
                if(driverEntity != null){
                    two.createCell(1).setCellValue(driverEntity.getName());
                    two.createCell(4).setCellValue(driverEntity.getPhone());
                    three.createCell(1).setCellValue("");
                    three.createCell(4).setCellValue(deliveryBatchEntityList.get(i).getCarEntity().getCarNumber());
                }
            }

            Row four = sheet.createRow(4);
            four.createCell(0).setCellValue("路线总距离");
            four.createCell(3).setCellValue("总体积");

            Row mTitle = sheet.createRow(6);
            mTitle.createCell(0).setCellValue("路线编号");
            mTitle.createCell(1).setCellValue("截单编号");
            mTitle.createCell(2).setCellValue("订单客户");
            mTitle.createCell(3).setCellValue("配送地址");
            mTitle.createCell(4).setCellValue("联系人");
            mTitle.createCell(5).setCellValue("联系方式");
            mTitle.createCell(6).setCellValue("精准送时间");
            mTitle.createCell(7).setCellValue("备注");
            mTitle.createCell(8).setCellValue("回收商品名称");
            mTitle.createCell(9).setCellValue("规格");
            mTitle.createCell(10).setCellValue("数量");
            mTitle.createCell(11).setCellValue("存储区域");

            List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntityList.get(i).getDeliverySiteList();
            deliverySiteList = deliverySiteList.stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence)).collect(Collectors.toList());

            //回收商品详情
            BigDecimal totalVolume = BigDecimal.ZERO; //总体积
            BigDecimal distance = BigDecimal.ZERO;    //总距离
            int index = 7;
            for (DeliverySiteEntity deliverySiteEntity: deliverySiteList) {
                Row row = sheet.createRow(index);
                List<DistItemVO> distItemVOList = deliverySiteEntity.getSiteDistOrders().stream().map(e -> e.getDistItems())
                        .flatMap(Collection::stream).collect(Collectors.toList());

                List<DistItemVO> recovery = distItemVOList.stream().filter(distItem -> Objects.equals(distItem.getDeliveryType(), DeliverySiteItemTypeEnum.RECYCLE.getCode())).collect(Collectors.toList());

                //处理回收商品展示
                if(!org.springframework.util.CollectionUtils.isEmpty(recovery)){
                    //合并单元格
                    int size = recovery.size() -1;
                    int number = index;
                    if(recovery.size() > 1){
                        mergedRegion(sheet,index,7,size);
                        index =  index+ size;
                    }
                    for (DistItemVO distItemVO : recovery) {
                        Row sheetRow = sheet.createRow(number);
                        sheetRow.createCell(8).setCellValue(distItemVO.getOutItemName());
                        sheetRow.createCell(9).setCellValue(String.valueOf(distItemVO.getSpecification()));
                        sheetRow.createCell(10).setCellValue(distItemVO.getQuantity());
                        sheetRow.createCell(11).setCellValue(distItemVO.getTemperatureEnum().getName());
                        number ++;
                    }

                }
                row.createCell(0).setCellValue(net.summerfarm.common.util.StringUtils.isNotBlank(path) ? path + "-" + deliverySiteEntity.getSequence() : "");
                row.createCell(1).setCellValue("1");
                row.createCell(2).setCellValue(deliverySiteEntity.getOuterClientName());
                row.createCell(3).setCellValue(deliverySiteEntity.getSiteEntity().getFullAddress());
                row.createCell(4).setCellValue(deliverySiteEntity.getSiteEntity().getName());
                row.createCell(5).setCellValue(deliverySiteEntity.getSiteEntity().getPhone());
                String timeFrame = deliverySiteEntity.getSiteDistOrders().stream().filter(e -> StringUtils.isNotBlank(e.getDistFlowVO().getTimeFrame()))
                        .map(e -> e.getDistFlowVO().getTimeFrame()).collect(Collectors.joining(","));
                String remark = deliverySiteEntity.getSiteDistOrders().stream().map(DistOrderEntity::getCloseReason)
                        .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                row.createCell(6).setCellValue(timeFrame);
                row.createCell(7).setCellValue(remark);
                index++;
                deliverySiteEntity.siteDeliveryCalculate();
                totalVolume = totalVolume.add(deliverySiteEntity.getTotalVolume() == null ? BigDecimal.ZERO :  deliverySiteEntity.getTotalVolume());
                distance = distance.add(new BigDecimal(0));

            }
            four.createCell(1).setCellValue(distance.divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_EVEN).doubleValue());
            four.createCell(4).setCellValue(totalVolume.doubleValue());
            List<DeliverySiteEntity> deliverySiteEntities = deliveryBatchEntityList.get(i).getDeliverySiteList();
            List<DistOrderEntity> distOrderEntityList = deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteDistOrders).flatMap(Collection::stream).collect(Collectors.toList());

            //创建单店路线商品信息 Excel
            index = this.createSingleMerchantPathGoods(baseCellyStyle, alignmentStype, sheet, index, distOrderEntityList,skuDTOMap);
            //创建大客户路线商品信息 Excel
            index = this.createBigCustomerPathGoods(baseCellyStyle, alignmentStype, sheet, index, distOrderEntityList, skuDTOMap);
            //创建租户外单路线商品信息 Excel
            this.createTenantOutOrderPathGoods(baseCellyStyle, alignmentStype, sheet, index, distOrderEntityList);
        }
    }

    /**
     * 创建租户外单路线商品信息
     * @param baseCellyStyle 风格
     * @param alignmentStype 风格
     * @param sheet 页
     * @param index 行
     * @param distOrderEntityList 数据
     * @return 行号
     */
    private int createTenantOutOrderPathGoods(CellStyle baseCellyStyle, CellStyle alignmentStype, Sheet sheet, int index, List<DistOrderEntity> distOrderEntityList) {
        Map<String, List<DistOrderEntity>> outBrandOrderMap = distOrderEntityList.stream()
                .filter(distOrder -> Objects.equals(distOrder.getPickType(),DistPickTypeEnum.MERCHANT_PICK))
                .collect(Collectors.groupingBy(dist -> dist.getDistClientVO().getOutBrandName()));
        if(org.springframework.util.CollectionUtils.isEmpty(outBrandOrderMap)){
            return index;
        }
        //大客户数据处理
        index++;

        Set<String> tenantNameSet = outBrandOrderMap.keySet();
        for (String tenantName : tenantNameSet) {
            List<DistOrderEntity> bigDistOrderEntityList = outBrandOrderMap.get(tenantName);
            List<DistItemVO> bigDistItemList = bigDistOrderEntityList.stream().map(DistOrderEntity::getDistItems).flatMap(Collection::stream).collect(Collectors.toList());
            List<DistItemVO> sendBigDistItemList = bigDistItemList.stream().filter(item -> Objects.equals(item.getDeliveryType(), DistItemDeliveryTypeEnum.DELIVERY.getCode())).collect(Collectors.toList());

            index++;
            Row sheetRow = sheet.createRow(index);
            sheetRow.createCell(0).setCellValue("商品归属");
            sheetRow.createCell(1).setCellValue("外单-客户");
            sheetRow.createCell(2).setCellValue(tenantName);
            index++;
            Row title = sheet.createRow(index);
            title.createCell(0).setCellValue("储存位置");
            title.createCell(1).setCellValue("类目");
            title.createCell(2).setCellValue("SKU");
            title.createCell(3).setCellValue("商品名称");
            title.createCell(4).setCellValue("数量");
            title.createCell(5).setCellValue("规格");
            title.createCell(6).setCellValue("商品归属");
            index++;
            Map<TmsTemperatureEnum, List<DistItemVO>> skuCollect = sendBigDistItemList.stream().collect(Collectors.groupingBy(DistItemVO::getTemperatureEnum));

            Set<TmsTemperatureEnum> skuKeySet = skuCollect.keySet();
            for (TmsTemperatureEnum key : skuKeySet) {
                List<DistItemVO> values = skuCollect.get(key);
                Map<String, List<DistItemVO>> skuMapList = values.stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));

                if (skuMapList.size() > 1) {
                    CellRangeAddress skuMergeRegion = new CellRangeAddress(index, index + skuMapList.size() - 1, 0, 0);
                    sheet.addMergedRegion(skuMergeRegion);
                }
                for (String sku : skuMapList.keySet()) {
                    List<DistItemVO> skuList = skuMapList.get(sku);

                    Row row = sheet.createRow(index);
                    Cell storageLocationCell = row.createCell(0);
                    storageLocationCell.setCellValue(key.getName());
                    storageLocationCell.setCellStyle(baseCellyStyle);
                    row.createCell(1).setCellValue("-");
                    row.createCell(2).setCellValue(skuList.get(0).getOutItemId());
                    row.createCell(3).setCellValue(skuList.get(0).getOutItemName());
                    Cell quantityCell = row.createCell(4);
                    quantityCell.setCellStyle(alignmentStype);
                    quantityCell.setCellValue(skuList.stream().mapToInt(DistItemVO::getQuantity).sum());
                    row.createCell(5).setCellValue(skuList.get(0).getSpecification());
                    row.createCell(6).setCellValue("外单");
                    index++;
                }
            }
        }
        return index;
    }

    /**
     * 创建大客户路线商品信息 Excel
     * @param baseCellyStyle
     * @param alignmentStype
     * @param sheet
     * @param index
     * @param distOrderEntityList
     * @param skuDTOMap
     * @return
     */
    private int createSingleMerchantPathGoods(CellStyle baseCellyStyle, CellStyle alignmentStype, Sheet sheet, int index, List<DistOrderEntity> distOrderEntityList, Map<String, SkuDTO> skuDTOMap) {
        //普通客户截单数据
        List<DistOrderEntity> normalDistOrderList = distOrderEntityList.stream()
                .filter(distOrder -> Objects.equals(distOrder.getPickType(), DistPickTypeEnum.DEFAULT))
                .collect(Collectors.toList());

        List<DistItemVO> distItemVOList = normalDistOrderList.stream().map(DistOrderEntity::getDistItems).flatMap(Collection::stream).collect(Collectors.toList());
        //需要配送的委托单信息
        List<DistItemVO> sendDistItemList = distItemVOList.stream().filter(distItemVO -> Objects.equals(distItemVO.getDeliveryType(), DistItemDeliveryTypeEnum.DELIVERY.getCode())).collect(Collectors.toList());

        index++;
        Row inTitle = sheet.createRow(index);
        inTitle.createCell(0).setCellValue("商品归属");
        inTitle.createCell(1).setCellValue("单店");
        index++;
        Row skuTitle = sheet.createRow(index);
        skuTitle.createCell(0).setCellValue("储存位置");
        skuTitle.createCell(1).setCellValue("类目");
        skuTitle.createCell(2).setCellValue("SKU");
        skuTitle.createCell(3).setCellValue("商品名称");
        skuTitle.createCell(4).setCellValue("数量");
        skuTitle.createCell(5).setCellValue("规格");
        skuTitle.createCell(6).setCellValue("商品归属");

        index++;
        Map<TmsTemperatureEnum, List<DistItemVO>> collect = sendDistItemList.stream().collect(Collectors.groupingBy(DistItemVO::getTemperatureEnum));

        Set<TmsTemperatureEnum> keySet = collect.keySet();
        for (TmsTemperatureEnum key : keySet) {
            List<DistItemVO> values = collect.get(key);

            Map<String, List<DistItemVO>> skuMapList = values.stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));
            if (skuMapList.size() > 1) {
                CellRangeAddress skuMergeRegion = new CellRangeAddress(index, index + skuMapList.size() - 1, 0, 0);
                sheet.addMergedRegion(skuMergeRegion);
            }
            for (String sku : skuMapList.keySet()) {
                Row row = sheet.createRow(index);

                List<DistItemVO> distItemList = skuMapList.get(sku);

                Cell storageLocationCell = row.createCell(0);
                storageLocationCell.setCellValue(key.getName());
                storageLocationCell.setCellStyle(baseCellyStyle);
                row.createCell(1).setCellValue(distItemList.get(0).getOutItemType());
                row.createCell(2).setCellValue(distItemList.get(0).getOutItemId());
                row.createCell(3).setCellValue(distItemList.get(0).getOutItemName());
                Cell quantityCell = row.createCell(4);
                quantityCell.setCellStyle(alignmentStype);
                quantityCell.setCellValue(distItemList.stream().mapToInt(DistItemVO::getQuantity).sum());
                row.createCell(5).setCellValue(distItemList.get(0).getSpecification());
                SkuDTO skuDTO = skuDTOMap.get(sku);
                if(skuDTO != null){
                    AgentTypeEnum agentTypeEnum = AgentTypeEnum.getByType(skuDTO.getType());
                    String productsType = agentTypeEnum != null ? agentTypeEnum.getContent() : null;
                    productsType = StringUtils.isBlank(skuDTO.getNameRemakes()) ? productsType : productsType + "-" + skuDTO.getNameRemakes();
                    row.createCell(6).setCellValue(productsType);
                }

                index++;
            }
        }
        return index;
    }

    /**
     * 创建大客户路线商品信息 Excel
     * @param baseCellyStyle 基础风格
     * @param alignmentStype 基础风格
     * @param sheet sheet页
     * @param index 行
     * @param distOrderEntityList 数据
     * @param skuDTOMap SKU信息
     * @return 行
     */
    private int createBigCustomerPathGoods(CellStyle baseCellyStyle, CellStyle alignmentStype, Sheet sheet, int index, List<DistOrderEntity> distOrderEntityList, Map<String, SkuDTO> skuDTOMap) {
        Map<String, List<DistOrderEntity>> outBrandOrderMap = distOrderEntityList.stream()
                .filter(distOrder -> Objects.equals(distOrder.getPickType(),DistPickTypeEnum.BRAND_SINGLE))
                .collect(Collectors.groupingBy(dist -> dist.getDistClientVO().getOutBrandName()));
        if(org.springframework.util.CollectionUtils.isEmpty(outBrandOrderMap)){
            return index;
        }
        //大客户数据处理
        index++;

        Set<String> bigOrderSet = outBrandOrderMap.keySet();
        for (String adminName : bigOrderSet) {
            List<DistOrderEntity> bigDistOrderEntityList = outBrandOrderMap.get(adminName);
            List<DistItemVO> bigDistItemList = bigDistOrderEntityList.stream().map(DistOrderEntity::getDistItems).flatMap(Collection::stream).collect(Collectors.toList());
            List<DistItemVO> sendBigDistItemList = bigDistItemList.stream().filter(item -> Objects.equals(item.getDeliveryType(), DistItemDeliveryTypeEnum.DELIVERY.getCode())).collect(Collectors.toList());

            index++;
            Row sheetRow = sheet.createRow(index);
            sheetRow.createCell(0).setCellValue("商品归属");
            sheetRow.createCell(1).setCellValue("大客户");
            sheetRow.createCell(2).setCellValue(adminName);
            index++;
            Row adminTitle = sheet.createRow(index);
            adminTitle.createCell(0).setCellValue("储存位置");
            adminTitle.createCell(1).setCellValue("类目");
            adminTitle.createCell(2).setCellValue("SKU");
            adminTitle.createCell(3).setCellValue("商品名称");
            adminTitle.createCell(4).setCellValue("数量");
            adminTitle.createCell(5).setCellValue("规格");
            adminTitle.createCell(6).setCellValue("商品归属");
            index++;
            Map<TmsTemperatureEnum, List<DistItemVO>> skuCollect = sendBigDistItemList.stream().collect(Collectors.groupingBy(DistItemVO::getTemperatureEnum));

            Set<TmsTemperatureEnum> skuKeySet = skuCollect.keySet();
            for (TmsTemperatureEnum key : skuKeySet) {
                List<DistItemVO> values = skuCollect.get(key);
                Map<String, List<DistItemVO>> skuMapList = values.stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));

                if (skuMapList.size() > 1) {
                    CellRangeAddress skuMergeRegion = new CellRangeAddress(index, index + skuMapList.size() - 1, 0, 0);
                    sheet.addMergedRegion(skuMergeRegion);
                }
                for (String sku : skuMapList.keySet()) {
                    List<DistItemVO> skuList = skuMapList.get(sku);

                    Row row = sheet.createRow(index);
                    Cell storageLocationCell = row.createCell(0);
                    storageLocationCell.setCellValue(key.getName());
                    storageLocationCell.setCellStyle(baseCellyStyle);
                    row.createCell(1).setCellValue(skuList.get(0).getOutItemType());
                    row.createCell(2).setCellValue(skuList.get(0).getOutItemId());
                    row.createCell(3).setCellValue(skuList.get(0).getOutItemName());
                    Cell quantityCell = row.createCell(4);
                    quantityCell.setCellStyle(alignmentStype);
                    quantityCell.setCellValue(skuList.stream().mapToInt(DistItemVO::getQuantity).sum());
                    row.createCell(5).setCellValue(skuList.get(0).getSpecification());
                    SkuDTO skuDTO = skuDTOMap.get(sku);
                    if(skuDTO != null){
                        AgentTypeEnum agentTypeEnum = AgentTypeEnum.getByType(skuDTO.getType());
                        String productsType = agentTypeEnum != null ? agentTypeEnum.getContent() : null;
                        productsType = StringUtils.isBlank(skuDTO.getNameRemakes()) ? productsType : productsType + "-" + skuDTO.getNameRemakes();
                        row.createCell(6).setCellValue(productsType);
                    }
                    index++;
                }
            }
        }
        return index;
    }

    /**
     * 合并单元格
     */
    private void mergedRegion(Sheet sheet,Integer index,Integer length,Integer size){
        for (int i = 0; i < length; i++) {
            sheet.addMergedRegion(new CellRangeAddress(index ,index + size,i,i));
        }
    }

    private void createQuantityData(Workbook workbook, List<DeliveryBatchEntity> deliveryBatchEntityList, LocalDateTime deliveryTime) {
        Sheet sheet = workbook.createSheet("各路线出库量");
        int rowIndex = 0;
        Row first = sheet.createRow(rowIndex++);
        first.createCell(0).setCellValue("配送日期");
        first.createCell(1).setCellValue(deliveryTime.format(DateTimeFormatter.ISO_LOCAL_DATE));

        HashMap<String, List<DistOrderEntity>> bigMerchantMap = new HashMap<>();
        HashMap<String, List<DistOrderEntity>> normalMap = new HashMap<>();
        HashMap<String, List<DistOrderEntity>> merchantPickPath2DistOrderListMap = new HashMap<>();

        Map<String, List<DeliverySiteEntity>> pathDeliverySiteMap = deliveryBatchEntityList.stream().collect(Collectors.toMap(DeliveryBatchEntity::getPathCode, DeliveryBatchEntity::getDeliverySiteList));
        for (String path : pathDeliverySiteMap.keySet()) {
            List<DeliverySiteEntity> deliverySiteEntities = pathDeliverySiteMap.get(path);
            List<DistOrderEntity> distOrderEntityList = deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteDistOrders).flatMap(Collection::stream).collect(Collectors.toList());
            //大客户
            List<DistOrderEntity> bigMerchantColl = distOrderEntityList.stream().filter(distOrderEntity -> distOrderEntity.getPickType() == DistPickTypeEnum.BRAND_SINGLE).collect(Collectors.toList());
            //普通客户
            List<DistOrderEntity> normalColl = distOrderEntityList.stream().filter(distOrderEntity -> distOrderEntity.getPickType() == DistPickTypeEnum.DEFAULT).collect(Collectors.toList());
            //门店拣货
            List<DistOrderEntity> outerOrderMerchantPickColl = distOrderEntityList.stream().filter(distOrderEntity -> distOrderEntity.getPickType() == DistPickTypeEnum.MERCHANT_PICK).collect(Collectors.toList());

            bigMerchantMap.put(path,bigMerchantColl);
            normalMap.put(path,normalColl);
            merchantPickPath2DistOrderListMap.put(path,outerOrderMerchantPickColl);
        }

        //普通客户
        int quantity = createQuantity(normalMap, sheet, rowIndex++,false);
        HashMap<Long, String> distPathMap = new HashMap<>();
        for (String path : bigMerchantMap.keySet()) {
            List<DistOrderEntity> distOrderEntityList = bigMerchantMap.get(path);
            for (DistOrderEntity distOrderEntity : distOrderEntityList) {
                distPathMap.put(distOrderEntity.getDistId(),path);
            }
        }
        rowIndex = quantity;
        List<DeliverySiteEntity> siteList = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getDeliverySiteList).flatMap(Collection::stream).collect(Collectors.toList());
        List<DistOrderEntity> distOrderEntityList = siteList.stream().map(DeliverySiteEntity::getSiteDistOrders).flatMap(Collection::stream).collect(Collectors.toList());
        //大客户拣货
        rowIndex = this.bigCustomerQuantityCreate(sheet, rowIndex, distPathMap, distOrderEntityList);
        //门店拣货外单
        this.merchantPickQuantityCreate(sheet, rowIndex, merchantPickPath2DistOrderListMap, distOrderEntityList);

    }

    private void merchantPickQuantityCreate(Sheet sheet, int rowIndex, HashMap<String, List<DistOrderEntity>> merchantPickPath2DistOrderListMap, List<DistOrderEntity> distOrderEntityList) {
        HashMap<Long, String> outerDistPathMap = new HashMap<>();
        for (String path : merchantPickPath2DistOrderListMap.keySet()) {
            List<DistOrderEntity> outerDistPathList = merchantPickPath2DistOrderListMap.get(path);
            for (DistOrderEntity distOrderEntity : outerDistPathList) {
                outerDistPathMap.put(distOrderEntity.getDistId(),path);
            }
        }
        //门店拣货单据
        List<DistOrderEntity> merchantPickOrderList = distOrderEntityList.stream().filter(distOrderEntity -> Objects.equals(distOrderEntity.getPickType(), DistPickTypeEnum.MERCHANT_PICK)).collect(Collectors.toList());
        //租户分组订单集合
        Map<String, List<DistOrderEntity>> tenantNameDistListMap = merchantPickOrderList.stream().collect(Collectors.groupingBy(dist -> dist.getDistClientVO().getOutBrandName()));
        List<HashMap<String, List<DistOrderEntity>>> tenant2Path2DistMapList = new ArrayList<>();

        //租户名称分类
        for (String tenantName : tenantNameDistListMap.keySet()) {
            List<DistOrderEntity> pathDistList = tenantNameDistListMap.get(tenantName);

            HashMap<String, List<DistOrderEntity>> pathTenantDistOrderMap = new HashMap<>();
            for (DistOrderEntity tenantDistOrderEntity : pathDistList) {
                String path = outerDistPathMap.get(tenantDistOrderEntity.getDistId());
                //获取路线对应的订单
                List<DistOrderEntity> pathDistOrders = merchantPickPath2DistOrderListMap.get(path) == null ? new ArrayList<>() : merchantPickPath2DistOrderListMap.get(path);
                //过滤路线上面租户订单
                List<DistOrderEntity> pathTenantDistOrders = pathDistOrders.stream().filter(distOrder -> Objects.equals(tenantName, distOrder.getDistClientVO().getOutBrandName())).collect(Collectors.toList());
                pathTenantDistOrderMap.put(path,pathTenantDistOrders);
            }
            tenant2Path2DistMapList.add(pathTenantDistOrderMap);
        }
        for (HashMap<String, List<DistOrderEntity>> tenant : tenant2Path2DistMapList) {
            Set<String> path = tenant.keySet();
            if(CollectionUtils.isEmpty(path)){
                continue;
            }
            List<DistOrderEntity> distOrders = new ArrayList<>();
            for (String onePath : path) {
                distOrders = tenant.get(onePath);
                if(!CollectionUtils.isEmpty(distOrders)){
                    break;
                }
            }

            Row title = sheet.createRow(rowIndex++);
            title.createCell(0).setCellValue("外单干配");
            title.createCell(1).setCellValue(distOrders.get(0) == null ? "":distOrders.get(0).getDistClientVO().getOutBrandName());
            int index = createQuantity(tenant, sheet, rowIndex++,true);
            rowIndex = index + 1;
        }
    }

    private int bigCustomerQuantityCreate(Sheet sheet, int rowIndex, HashMap<Long, String> distPathMap, List<DistOrderEntity> distOrderEntityList) {
        //大客户委托单
        List<DistOrderEntity> bigDistOrderList = distOrderEntityList.stream().filter(distOrderEntity -> Objects.equals(distOrderEntity.getPickType(), DistPickTypeEnum.BRAND_SINGLE)).collect(Collectors.toList());
        Map<String, List<DistOrderEntity>> bigBrandDistListMap = bigDistOrderList.stream().collect(Collectors.groupingBy(dist -> dist.getDistClientVO().getOutBrandName()));
        List<HashMap<String, List<DistOrderEntity>>> pathBigMerchList = new ArrayList<>();
        for (String outBrandName : bigBrandDistListMap.keySet()) {
            List<DistOrderEntity> pathDistList = bigBrandDistListMap.get(outBrandName);
            HashMap<String, List<DistOrderEntity>> newBigMerchantMap = new HashMap<>();

            for (DistOrderEntity distOrderEntity : pathDistList) {
                String path = distPathMap.get(distOrderEntity.getDistId());
                List<DistOrderEntity> distOrderEntities = newBigMerchantMap.get(path) == null ? new ArrayList<DistOrderEntity>() : newBigMerchantMap.get(path);
                distOrderEntities.add(distOrderEntity);
                newBigMerchantMap.put(path,distOrderEntities);
            }
            pathBigMerchList.add(newBigMerchantMap);
        }

        for (HashMap<String, List<DistOrderEntity>> bigMerchant : pathBigMerchList) {
            Set<String> path = bigMerchant.keySet();
            if(CollectionUtils.isEmpty(path)){
                continue;
            }
            List<DistOrderEntity> distOrders = new ArrayList<>();
            for (String onePath : path) {
                distOrders = bigMerchant.get(onePath);
                if(!CollectionUtils.isEmpty(distOrders)){
                    break;
                }
            }

            Row title = sheet.createRow(rowIndex++);
            title.createCell(0).setCellValue("大客户");
            title.createCell(1).setCellValue(distOrders.get(0) == null ? "":distOrders.get(0).getDistClientVO().getOutBrandName());
            int index = createQuantity(bigMerchant, sheet, rowIndex++,false);
            rowIndex = index + 1;
        }
        return rowIndex;
    }

    private int createQuantity(Map<String, List<DistOrderEntity>> dataMap, Sheet sheet, int rowIndex,boolean isMerchantPick) {
        List<String> pathList = new ArrayList(dataMap.keySet());

        //获取所有的SKU
        List<String> skus = dataMap.keySet().stream()
                .map(dataMap::get)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DistOrderEntity::getDistItems)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DistItemVO::getOutItemId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //查询sku信息
        List<SkuDTO> skuDTOList = wmsQueryFacade.batchQueryBySkus(new ArrayList<>(skus));
        Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getSku, Function.identity(), (oldData, newData) -> newData));

        Row title = sheet.createRow(rowIndex++);
        title.createCell(0).setCellValue("sku编码");
        title.createCell(1).setCellValue("商品名称");
        title.createCell(2).setCellValue("规格");
        title.createCell(3).setCellValue("商品归属");
        title.createCell(4).setCellValue("包装");
        title.createCell(5).setCellValue("存储区域");
        title.createCell(6).setCellValue("类目类型");
        title.createCell(7).setCellValue("总出库量");

        int colIndex = 8;
        for (String path: pathList) {
            if(StringUtils.isNotBlank(path)){
                title.createCell(colIndex++).setCellValue("路线"+path);
            }else{
                title.createCell(colIndex++).setCellValue("未排路线");
            }
        }
        //将 List<ClosingOrder> -> List<DeliveryOrder> 结构
        List<DeliveryOrderPojo> deliveryOrders = new ArrayList<>();
        for (String path : pathList) {
            List<DistOrderEntity> distOrderEntityList = dataMap.get(path);
            if(CollectionUtils.isEmpty(distOrderEntityList)){
                continue;
            }
            //承运单id和品牌名的集合
            Map<Long, DistClientVO> distIdBrandMap = distOrderEntityList.stream().collect(Collectors.toMap(DistOrderEntity::getDistId, DistOrderEntity::getDistClientVO));
            List<DistItemVO> distItems = distOrderEntityList.stream().map(DistOrderEntity::getDistItems).flatMap(Collection::stream).collect(Collectors.toList());
            Map<String, List<DistItemVO>> skuItemMap = distItems.stream()
                    .filter(distItemVO -> Objects.equals(distItemVO.getDeliveryType(),DeliverySiteItemTypeEnum.DELIVERY.getCode()))
                    .collect(Collectors.groupingBy(DistItemVO::getOutItemId));


            for (String sku : skuItemMap.keySet()) {
                List<DistItemVO> distItemVOS = skuItemMap.get(sku);

                int amount = distItemVOS.stream().mapToInt(DistItemVO::getQuantity).sum();
                DistItemVO distItemVO = distItemVOS.get(0);
                if(distItemVO == null){
                    continue;
                }
                Map<String,Integer> result = new HashMap<>();
                result.put(path,amount);
                DeliveryOrderPojo deliveryOrder = new DeliveryOrderPojo();
                deliveryOrder.setPathQuantity(result);
                deliveryOrder.setSku(sku);
                deliveryOrder.setAmount(amount);
                deliveryOrder.setWeight(distItemVO.getSpecification());
                deliveryOrder.setPdName(distItemVO.getOutItemName());
                DistClientVO distClientVO = distIdBrandMap.get(distItemVO.getDistOrderId());
                deliveryOrder.setNameRemakes(distClientVO != null ? distClientVO.getOutBrandName() : "");
                deliveryOrder.setUnit(distItemVO.getUnit());
                deliveryOrder.setStorageLocation(distItemVO.getTemperatureEnum().getCode());
                deliveryOrder.setCategoryType(distItemVO.getItemTypeEnum().getCode());
                deliveryOrder.setTotal(amount);

                deliveryOrders.add(deliveryOrder);
            }

        };

        //填充数据
        Map<String, List<DeliveryOrderPojo>> skuList = deliveryOrders.stream().collect(Collectors.groupingBy(DeliveryOrderPojo::getSku));

        for (String sku : skuList.keySet()) {
            List<DeliveryOrderPojo> deliveryOrderPojos = skuList.get(sku);
            DeliveryOrderPojo deliveryOrderFirstPojo = deliveryOrderPojos.get(0);

            Row data = sheet.createRow(rowIndex++);
            data.createCell(0).setCellValue(sku);
            data.createCell(1).setCellValue(deliveryOrderFirstPojo.getPdName());
            data.createCell(2).setCellValue(deliveryOrderFirstPojo.getWeight());
            String productsType = "";
            SkuDTO skuDTO = skuDTOMap.get(sku);
            if(skuDTO != null){
                AgentTypeEnum agentTypeEnum = AgentTypeEnum.getByType(skuDTO.getType());
                productsType = agentTypeEnum != null ? agentTypeEnum.getContent() : null;
                productsType = StringUtils.isBlank(skuDTO.getNameRemakes()) ? productsType : productsType + "-" + skuDTO.getNameRemakes();
            }
            if(isMerchantPick){
                productsType = "外单";
            }
            data.createCell(3).setCellValue(productsType);
            data.createCell(4).setCellValue(deliveryOrderFirstPojo.getUnit());
            data.createCell(5).setCellValue(deliveryOrderFirstPojo.getStorageLocation() != null ? TmsTemperatureEnum.getTemperatureByCode(skuList.get(sku).get(0).getStorageLocation()).getName() : "");
            if(isMerchantPick){
                data.createCell(6).setCellValue("-");
            }else{
                data.createCell(6).setCellValue(Objects.equals(DistItemTypeEnum.FRUIT.getCode(),deliveryOrderFirstPojo.getCategoryType()) ? "鲜果": "非鲜果" );
            }
            data.createCell(7).setCellValue(deliveryOrderPojos.stream().mapToInt(DeliveryOrderPojo::getTotal).sum());

            for (DeliveryOrderPojo deliveryOrderPojo : deliveryOrderPojos) {
                Map<String, Integer> pathQuantity = deliveryOrderPojo.getPathQuantity();
                for (int i = 0; i < pathList.size(); i++) {
                    Integer quantity = pathQuantity.get(pathList.get(i));
                    if (quantity != null) {
                        data.createCell(i+8).setCellValue(quantity);
                    }
                }
            }
        }
        rowIndex++;
        return rowIndex;
    }

    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    @Override
    public TmsResult<Void> prePathUpload(MultipartFile file) {
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            ExcelUtils excelUtils = new ExcelUtils(workbook);
            List<Map<String, String>> mapData = excelUtils.getMapData();

            SiteEntity beginSiteEntity = null;
            List<Long> havePathSiteList = new ArrayList<>();
            List<Long> deletePathSiteList = new ArrayList<>();

            HashMap<Long, String> endSiteIdPathCodeMap = new HashMap<>();

            for (Map<String, String> map : mapData) {
                Long endSiteId = Long.valueOf(map.get("联系人id"));
                String path = map.get("预排路线");
                String storeNo = map.get("城配仓编号");
                if(beginSiteEntity == null){
                    beginSiteEntity = siteRepository.query(SiteQuery.builder().outBusinessNo(storeNo).type(TmsSiteTypeEnum.STORE.getCode()).build());
                }

                if (StringUtils.isNotBlank(path)) {
                    if (path.length() <= 1 && !path.matches(RegConstant.UPPERCASE_LETTER_REG)) {
                        throw new TmsRuntimeException(ErrorCodeEnum.FAILED,"预排路线名必须在A-Z之间");
                    } else if(path.length() > 1 && !path.matches(RegConstant.UPPERCASE_LETTER_REG_MORE) ){
                        throw new TmsRuntimeException(ErrorCodeEnum.FAILED,"预排路线名称错误");
                    }
                    havePathSiteList.add(endSiteId);
                    endSiteIdPathCodeMap.put(endSiteId,path);
                } else {
                    deletePathSiteList.add(endSiteId);
                }
            }
            if(beginSiteEntity == null){
                throw new TmsRuntimeException(ErrorCodeEnum.FAILED,"不存在此城配仓");
            }
            //查询path_section是否存在，并判断pathCode是否匹配，存在则更新
            List<TmsPathSectionEntity> pathSectionEntityList = pathSectionRepository.queryWithPath(PathSectionQuery.builder()
                    .beginSiteId(beginSiteEntity.getId())
                    .endSiteIds(havePathSiteList)
                    .type(PathSectionEnums.Type.CITY.getValue())
                    .build());

            // 过滤掉正常路线（开始点位和结束点位一样的数据）
            pathSectionEntityList = pathSectionEntityList.stream()
                    .filter(e -> !Objects.equals(e.getBeginSiteId(), e.getEndSiteId()))
                    .collect(Collectors.toList());
            //点位和路线的map
            Map<Long, TmsPathSectionEntity> endSitePathMap = pathSectionEntityList.stream().collect(Collectors.toMap(TmsPathSectionEntity::getEndSiteId, Function.identity()));
            List<TmsPathEntity> tmsPathEntityList = Lists.newArrayList();
            for (Long endSiteIdKey : endSiteIdPathCodeMap.keySet()) {
                TmsPathSectionEntity getPathSectionEntity = endSitePathMap.get(endSiteIdKey);
                if((getPathSectionEntity == null) ||
                        (getPathSectionEntity != null && getPathSectionEntity.getPathEntity() != null
                                && !Objects.equals(endSiteIdPathCodeMap.get(endSiteIdKey),getPathSectionEntity.getPathEntity().getPathCode()))){
                    //不存在，需要新建，并判断路线是否存在，不存在需要新建
                    TmsPathEntity tmsPath = new TmsPathEntity();
                    tmsPath.setBeginSiteId(beginSiteEntity.getId());
                    tmsPath.setEndSiteId(beginSiteEntity.getId());
                    tmsPath.setPathCode(endSiteIdPathCodeMap.get(endSiteIdKey));
                    tmsPath.setType(TmsPathTypeEnum.DELIVERY_ROAD.getCode());
                    tmsPath.setCreatorId(String.valueOf(authExtService.getCurrentUserId()));

                    TmsPathSectionEntity tmsPathSectionEntity = new TmsPathSectionEntity();
                    tmsPathSectionEntity.setBeginSiteId(beginSiteEntity.getId());
                    tmsPathSectionEntity.setEndSiteId(endSiteIdKey);
                    tmsPathSectionEntity.setType(PathSectionEnums.Type.CITY.getValue());

                    tmsPath.setTmsPathSectionList(Arrays.asList(tmsPathSectionEntity));
                    tmsPathEntityList.add(tmsPath);
                }
            }
            pathRepository.batchSaveOrUpdate(tmsPathEntityList);

            //删除无需code的数据
            if(beginSiteEntity.getId() != null && CollectionUtils.isNotEmpty(deletePathSiteList)){
                pathSectionRepository.removeList(PathSectionQuery.builder()
                        .beginSiteId(beginSiteEntity.getId())
                        .endSiteIds(deletePathSiteList)
                        .type(PathSectionEnums.Type.CITY.getValue())
                        .build());
            }
        } catch (Exception e) {
            log.info("预排路线导入异常", e);
            throw new TmsRuntimeException(ErrorCodeEnum.FAILED,"预排路线导入异常"+e.getMessage());
        }
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<Void> updateBatchDistance(DeliveryBatchQuery deliveryBatchQuery) {
        //根据日期范围查询批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(deliveryBatchQuery);
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder()
                    .batchId(deliveryBatchEntity.getId())
                    .build());
            if(CollectionUtils.isEmpty(deliverySiteEntities)){
                continue;
            }

            deliveryBatchEntity.setDeliverySiteList(deliverySiteEntities);
            try {
                //计算距离和保存路段
                DeliverySiteEntity beginDeliverySite = deliveryBatchEntity.getBeginDeliverySite();
                List<DeliverySiteEntity> sendSiteList = deliveryBatchEntity.getExcludeBeginDeliverySiteList();
                //需要排序
                if(!org.apache.commons.collections4.CollectionUtils.isEmpty(sendSiteList)){
                    sendSiteList = sendSiteList.stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence)).collect(Collectors.toList());
                }
                //调用高德路径规划
                PathSectionInput input = new PathSectionInput();
                List<WaypointsInput> waypointsInputList = new ArrayList<>();
                for (DeliverySiteEntity siteEntity : sendSiteList) {
                    waypointsInputList.add(WaypointsInput.builder().siteId(siteEntity.getSiteId()).poi(siteEntity.getSiteEntity().getPoi()).build());
                }
                input.setStrategyEunm(DriverRoutPlanStrategyEnum.DISTANCE_FIRST);
                PathSection pathSection = lbsGaoDeService.waypointsPathSection(input);
                //保存路段信息
                log.info("批次id:{},高德计算距离:{},poi点位:{}",deliveryBatchEntity.getId(),pathSection.getTotalDistance(), JSON.toJSONString(waypointsInputList));
                //更新预计配送距离
                deliveryBatchEntity.setPlanTotalDistance(pathSection.getTotalDistance().divide(new BigDecimal(1000)).setScale(2,BigDecimal.ROUND_HALF_UP));
                deliveryBatchRepository.updatePlanDistance(deliveryBatchEntity);

            } catch (Exception e) {
                log.info("计算智能排线公里数异常,批次id：{},配送日期：{}",deliveryBatchEntity.getId(),deliveryBatchEntity.getDeliveryTime());
                e.getStackTrace();
            }

        }
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<DeliveryBatchDTO> queryCurrentDriverBatch(DeliveryBatchQuery deliveryBatchQuery) {
        Long driverId = authExtService.getTmsCurrentUserId();
        TmsAssert.notNull(driverId, ErrorCodeEnum.GRAY_ERROR, "找不到登录用户");

        //查询当前司机的城配任务
        deliveryBatchQuery.setDriverId(driverId);
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchQuery);

        if(deliveryBatchEntity != null && deliveryBatchEntity.getId() != null){
            return TmsResult.success(DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity));
        }else{
            return TmsResult.success(null);
        }
    }

    @Override
    public TmsResult<PageInfo<DeliveryBatchDTO>> queryCurrentDriverBatchPage(DeliveryBatchQuery deliveryBatchQuery) {
        Long driverId = authExtService.getTmsCurrentUserId();
        TmsAssert.notNull(driverId, ErrorCodeEnum.GRAY_ERROR, "找不到登录用户");
        deliveryBatchQuery.setDriverId(driverId);
        deliveryBatchQuery.setDeliveryBatchType(DeliveryBatchTypeEnum.city.getCode());
        deliveryBatchQuery.setDeliveryBatchStatusList(Arrays.asList(
                DeliveryBatchStatusEnum.TO_BE_PICKED.getCode(),
                DeliveryBatchStatusEnum.IN_DELIVERY.getCode(),
                DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode()
                ));

        AbstractPageQuery.PageSortInput statusSortInput = new AbstractPageQuery.PageSortInput();
        statusSortInput.setSortBy("status");
        statusSortInput.setOrderBy("asc");
        AbstractPageQuery.PageSortInput deliveryTimeSortInput = new AbstractPageQuery.PageSortInput();
        deliveryTimeSortInput.setSortBy("delivery_time");
        deliveryTimeSortInput.setOrderBy("desc");

        deliveryBatchQuery.setSortList(Arrays.asList(statusSortInput,deliveryTimeSortInput));

        //查询当前司机的城配任务
        PageInfo<DeliveryBatchEntity> batchPage = deliveryBatchRepository.queryPage(deliveryBatchQuery);

        List<DeliveryBatchEntity> batchList = batchPage.getList();
        batchList = batchList.stream().filter(batch -> batch.getId() != null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(batchList)){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"此司机没有对应的拣货任务");
        }
        Map<Long, DeliveryBatchEntity> batchEntityMap = batchList.stream().collect(Collectors.toMap(DeliveryBatchEntity::getId, Function.identity()));
        List<Long> batchIdList = batchList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
        //获取sku种类
        //获取点位数
        List<DeliveryPickEntity> deliveryPickEntities = deliveryPickRepository.queryList(DeliveryPickQuery.builder().batchIdList(batchIdList).build());
        Map<Long, List<DeliveryPickEntity>> batchPickMap = deliveryPickEntities.stream().collect(Collectors.groupingBy(DeliveryPickEntity::getDeliveryBatchId));

        List<DeliverySiteEntity> allDeliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder()
                .batchIdList(batchEntityMap.keySet().stream().collect(Collectors.toList()))
                .interceptStates(DeliverySiteInterceptStateEnum.validStateList()).build());
        Map<Long, List<DeliverySiteEntity>> deliverySiteEntityMap = allDeliverySiteEntities.stream()
                .collect(Collectors.groupingBy(DeliverySiteEntity::getDeliveryBatchId));
        for (Long batchId : batchEntityMap.keySet()) {
            DeliveryBatchEntity deliveryBatchEntity = batchEntityMap.get(batchId);

            List<DeliveryPickEntity> deliveryPickList = batchPickMap.get(batchId);
            if(!CollectionUtils.isEmpty(deliveryPickList)){
                deliveryBatchEntity.setSkuCnt(deliveryPickList.stream().map(DeliveryPickEntity::getOutItemId).distinct().count());
            }
            //获取点位数
            List<DeliverySiteEntity> deliverySiteEntities = queryDeliverySiteInfo(deliveryBatchEntity,deliverySiteEntityMap);
            //去除批次本身自己的点位
            deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity -> !Objects.equals(deliverySiteEntity.getSiteEntity().getId(), deliveryBatchEntity.getBeginSiteId())).collect(Collectors.toList());
            //根据批次id获取运输单信息和点位对应配送物品信息
            deliveryBatchEntity.setDeliverySiteList(deliverySiteEntities);
        }
        List<DeliveryBatchDTO> deliveryBatchDTOS = batchList.stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList());
        // 如果是POP城配仓需要设置拣货扫码标识
        List<String> popStoreNosList = wncQueryFacade.queryPopStoreNosList();
        Map<Long, SiteEntity> siteId2SiteMap = siteRepository.querySiteStoreMapByOutBusinessNos(popStoreNosList);
        deliveryBatchDTOS.forEach(batch ->{
            batch.setNeedScanCodeFlag(false);
            SiteEntity siteEntity = siteId2SiteMap.get(batch.getBeginSiteId());
            if(siteEntity == null){
                return;
            }
            batch.setNeedScanCodeFlag(true);
        });

        PageInfo<DeliveryBatchDTO> listPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(batchPage, listPageInfo);
        listPageInfo.setList(deliveryBatchDTOS);
        return TmsResult.success(listPageInfo);
    }

    private List<DeliverySiteEntity> queryDeliverySiteInfo(DeliveryBatchEntity deliveryBatchEntity, Map<Long, List<DeliverySiteEntity>> deliverySiteEntityMap) {


        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteEntityMap.get(deliveryBatchEntity.getId());
        if (CollectionUtils.isEmpty(deliverySiteEntities)) {
            return null;
        }
        //可以一次查询，点位集合
        List<Long> siteIdList = deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteEntity).map(SiteEntity::getId).collect(Collectors.toList());
        //委托单集合
        List<DistOrderEntity> distOrderEntityList = distOrderDomainService.
                queryValidDistOrder(siteIdList, deliveryBatchEntity.getDeliveryTime().toLocalDate(), deliveryBatchEntity.getBeginSiteId());
        Map<Long, List<DistOrderEntity>> siteIdDistOrderEntityMap = distOrderEntityList.stream().collect(Collectors.groupingBy(i -> i.getEndSite().getId()));
        List<DeliverySiteEntity> deliverySiteDTOS = deliverySiteEntities.stream().filter(site -> Objects.equals(site.getInterceptState(), DeliverySiteInterceptStateEnum.allIntecept.getCode()))
                .collect(Collectors.toList());
        deliverySiteEntities = deliverySiteEntities.stream().filter(site -> !Objects.equals(site.getType(),DeliverySiteTypeEnum.begin.getCode())).collect(Collectors.toList());
        if (deliverySiteEntities.size() == deliverySiteDTOS.size()) {
            deliverySiteEntities.forEach(deliverySiteEntity -> {
                //根据配送时间和开始点位查询委托单状态有效的状态
                List<DistOrderEntity> distOrderEntities = siteIdDistOrderEntityMap.get(deliverySiteEntity.getSiteEntity().getId()) == null
                        ? new ArrayList<>() : siteIdDistOrderEntityMap.get(deliverySiteEntity.getSiteEntity().getId());
                //获取这个点位的委托单
                deliverySiteEntity.setSiteDistOrders(distOrderEntities);
                //设置该点位配送货物的体积和重量和金额和精准送和配送类型和拦截信息
                deliverySiteEntity.siteDeliveryCalculate();
            });
        }

        return deliverySiteEntities;
    }

    @Override
    public TmsResult<DeliveryBatchDTO> queryPickDetail(DeliveryBatchQuery deliveryBatchQuery) {
        TmsAssert.notNull(deliveryBatchQuery.getBatchId(), ErrorCodeEnum.PARAM_NOT_NULL, "batchId");
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchQuery);
        if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null) {
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND, "批次");
        }

        List<DeliveryPickEntity> deliveryPickEntities = deliveryPickRepository.queryList(DeliveryPickQuery.builder()
                .batchId(deliveryBatchQuery.getBatchId()).build());
        if(!CollectionUtils.isEmpty(deliveryPickEntities)){
            //获取sku信息,排除包裹类型
            List<String> skus = deliveryPickEntities.stream()
                    .filter(pick -> !Objects.equals(pick.getPackType(),DistItemEnums.PackType.PACKAGE.getValue()))
                    .map(DeliveryPickEntity::getOutItemId).collect(Collectors.toList());
            List<SkuDTO> skuDTOS = wmsQueryFacade.batchQueryBySkus(skus);
            skuDTOS.forEach(sku ->{
                if(StringUtils.isBlank(sku.getPicturePath())){
                    sku.setPicturePath("");
                }
                if(sku.getType() ==null){
                    sku.setType(1);
                }
                if(StringUtils.isBlank(sku.getNameRemakes())){
                    sku.setNameRemakes("");
                }
                if(sku.getExtType() == null){
                    sku.setExtType(0);
                }
            });
            //查询wms条码信息
            List<SkuBarcodeDTO> skuBarcodeDTOS = wmsQueryFacade.querySkuBarcode(skus);

            Map<String, List<String>> skuBarcodeMap = Optional.ofNullable(skuBarcodeDTOS).orElse(com.google.common.collect.Lists.newArrayList()).stream().collect(Collectors.toMap(SkuBarcodeDTO::getSku, SkuBarcodeDTO::getBarcodes, (oldValue, newValue) -> newValue));
            Map<String, String> skuMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku,SkuDTO::getPicturePath, (oldValue, newValue) -> newValue));
            Map<String, Integer> skuTypeMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getType, (oldValue, newValue) -> newValue));
            Map<String, String> skuNameRemakes = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getNameRemakes,(oldValue, newValue) -> newValue));
            Map<String, Integer> storageMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getStorageArea, (oldValue, newValue) -> newValue));
            
            Map<String, Integer> skuExTypeMap = skuDTOS.stream().collect(Collectors.toMap(SkuDTO::getSku, SkuDTO::getExtType,(oldValue, newValue) -> newValue));
            deliveryPickEntities.forEach(item -> {
                if(skuMap.get(item.getOutItemId()) != null && !Objects.equals(item.getPackType(),DistItemEnums.PackType.PACKAGE.getValue())){
                    item.setSkuPic(skuMap.get(item.getOutItemId()));
                    item.setSkuType(skuTypeMap.get(item.getOutItemId()));
                    item.setNameRemakes(skuNameRemakes.get(item.getOutItemId()));
                    item.setExtType(skuExTypeMap.get(item.getOutItemId()));
                    item.setStorageArea(storageMap.get(item.getOutItemId()));
                    item.setBarcodes(skuBarcodeMap.get(item.getOutItemId()));
                }
            });
            deliveryPickEntities.sort(Comparator.comparing(DeliveryPickEntity::getOutItemId,Comparator.nullsLast(String::compareTo)));
        }

        deliveryBatchEntity.setDeliveryPickEntityList(deliveryPickEntities);
        //查询批次城配仓的点位id
        DeliverySiteEntity citySiteEntity = deliverySiteRepository.query(DeliverySiteQuery.builder()
                .batchId(deliveryBatchEntity.getId())
                .siteId(deliveryBatchEntity.getBeginSiteId()).build()
        );
        deliveryBatchEntity.setDeliverySiteList(Arrays.asList(citySiteEntity));


        return TmsResult.success(DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity));
    }

    @Override
    public TmsResult<DeliveryBatchDTO> queryDeliverySiteList(DeliveryBatchQuery deliveryBatchQuery) {
        if(deliveryBatchQuery.getBatchId() == null){
            throw new TmsRuntimeException("批次ID不能为空");
        }
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchQuery);
        if(deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"批次");
        }
        //配送点位信息
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteDomainService.queryDetailByBatchId(deliveryBatchEntity.getId());
        //去除批次本身自己的点位
        deliverySiteEntities = deliverySiteEntities.stream().filter(deliverySiteEntity -> !Objects.equals(deliverySiteEntity.getSiteEntity().getId(),deliveryBatchEntity.getBeginSiteId())).collect(Collectors.toList());
        //设置配送时效
        deliveryAlertDomainService.installDeliveryAlertTimeFrameList(deliverySiteEntities,deliveryBatchEntity.getBeginSiteId());
        //设置到店打卡
        deliverySiteDomainService.checkinPunchByDistOrders(deliverySiteEntities);
        //设置拣货缺货信息
        tmsDeliveryPickShortOrderMappingCommandDomainService.installDeliverySitePickLackInfo(deliveryBatchEntity, deliverySiteEntities);
        // 设置点位订单来源信息
        deliverySiteEntities.forEach(s -> s.setOrderSourceInfo(s.findOrderSource()));

        deliveryBatchEntity.setDeliverySiteList(deliverySiteEntities);

        return TmsResult.success(DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity));
    }

    @Override
    public TmsResult<PageInfo<DeliveryBatchDTO>> queryTaskPage(DeliveryBatchQuery deliveryBatchQuery) {
        PageInfo<DeliveryBatchEntity> deliveryBatchEntityPageInfo = deliveryBatchDomainService.queryListWithSite(deliveryBatchQuery);
        List<DeliveryBatchEntity> list = deliveryBatchEntityPageInfo.getList();

        PageInfo<DeliveryBatchDTO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(deliveryBatchEntityPageInfo, pageInfo);
        if(CollectionUtils.isEmpty(list)){
            return TmsResult.success(pageInfo);
        }

        List<Long> batchIds = list.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());

        // 站点拣货数据
        Map<Long, List<DeliveryPickEntity>> deliverySiteId2PickListMap = deliveryPickDomainService.queryTrunkPickDetailByBathIds(batchIds);

        ArrayList<DeliveryBatchDTO> deliveryBatchDTOS = new ArrayList<>();
        list.forEach(deliveryBatchEntity -> {
            DeliveryBatchDTO deliveryBatchDTO = DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity);
            List<DeliverySiteDTO> deliverySiteDTOList = deliveryBatchDTO.getDeliverySiteDTOList();
            if(!CollectionUtils.isEmpty(deliverySiteDTOList)){
                deliverySiteDTOList.forEach(deliverySite ->{
                    // 处理拣货标识
                    List<DeliveryPickEntity> currDeliverySitePickList = deliverySiteId2PickListMap.get(deliverySite.getId());
                    deliverySite.setNeedPickUpFlag(!CollectionUtils.isEmpty(currDeliverySitePickList));
                });
            }
            deliveryBatchDTO.setDeliverySiteDTOList(calculateNearSiteDistance(deliveryBatchEntity.getId(), deliveryBatchDTO.getDeliverySiteDTOList()));
            deliveryBatchDTOS.add(deliveryBatchDTO);
        });


        pageInfo.setList(deliveryBatchDTOS);
        return TmsResult.success(pageInfo);
    }

    @Override
    public TmsResult<DeliveryBatchDTO> queryTaskDetail(Long deliveryBatchId) {
        TmsAssert.notNull(deliveryBatchId, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryBatchId");
        //获取批次信息
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(deliveryBatchId);
        TmsAssert.notNull(deliveryBatchEntity.getId(), ErrorCodeEnum.NOT_FIND, "deliveryBatchId=" + deliveryBatchId);
        DeliveryBatchDTO deliveryBatchDTO = DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity);
        List<DeliverySiteDTO> deliverySiteDTOS = calculateNearSiteDistance(deliveryBatchEntity.getId(), deliveryBatchDTO.getDeliverySiteDTOList());
        // 站点拣货数据
        Map<Long, List<DeliveryPickEntity>> deliverySiteId2PickListMap = deliveryPickDomainService.queryTrunkPickDetailByBathIds(Collections.singletonList(deliveryBatchId));
        if(!CollectionUtils.isEmpty(deliverySiteDTOS)){
            deliverySiteDTOS.forEach(deliverySite ->{
                // 处理拣货标识
                List<DeliveryPickEntity> currDeliverySitePickList = deliverySiteId2PickListMap.get(deliverySite.getId());
                deliverySite.setNeedPickUpFlag(!CollectionUtils.isEmpty(currDeliverySitePickList));
            });
        }
        deliveryBatchDTO.setDeliverySiteDTOList(deliverySiteDTOS);

        List<DeliverySiteDTO> needPickUpSiteDTOS = deliverySiteDTOS.stream().filter(DeliverySiteDTO::isNeedPickUpFlag).collect(Collectors.toList());

        // 查询供应商信息
        if(!CollectionUtils.isEmpty(needPickUpSiteDTOS)){
            List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryBatchEntity.getDeliveryOrderEntityList();
            // key->beginSiteId value->外部单号ID集合
            Map<Long, List<String>> beginSiteId2OutOrderIdListMap = deliveryOrderEntityList.stream().collect(Collectors.groupingBy(DeliveryOrderEntity::getBeginSiteId,
                    Collectors.mapping(DeliveryOrderEntity::getOuterOrderId, Collectors.toList())));

            List<String> outerOrderIds = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getOuterOrderId).distinct().collect(Collectors.toList());
            // 获取点位对应的委托单信息
            // 请求PMS获取供应商名称
            Map<Long, List<String>> outerOrderId2SupplierNameMap = pmsQueryFacade.querySupplierNameByArrangeIds(outerOrderIds);
            deliverySiteDTOS.forEach(deliverySite -> {
                List<String> OutOrderIdList = beginSiteId2OutOrderIdListMap.get(deliverySite.getSiteId());
                if(CollectionUtils.isEmpty(OutOrderIdList)){
                    return;
                }
                List<String> supplierNameList = new ArrayList<>();
                OutOrderIdList.forEach(outerOrderId ->{
                    List<String> supplierNames = outerOrderId2SupplierNameMap.get(Long.parseLong(outerOrderId));
                    if(CollectionUtils.isEmpty(supplierNames)){
                        return;
                    }
                    supplierNameList.addAll(supplierNames);
                });

                deliverySite.setSupplierName(supplierNameList.stream().distinct().collect(Collectors.joining(",")));
            });
        }


        //设置仓库、城配仓点位图片
        List<SiteDTO> siteDTOS = deliverySiteDTOS.stream().map(DeliverySiteDTO::getSiteDTO).collect(Collectors.toList());
        List<SiteDTO> storeList = siteDTOS.stream().filter(site -> Objects.equals(site.getType(), SiteTypeEnum.store.getCode())).collect(Collectors.toList());
        List<SiteDTO> warehouseList = siteDTOS.stream().filter(site -> Objects.equals(site.getType(), SiteTypeEnum.warehouse.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(storeList)){
            List<Integer> storeNos = storeList.stream().map(SiteDTO::getOutBusinessNo).map(Integer::parseInt).collect(Collectors.toList());
            List<WarehousLogisticsCenterDTO> storeDTOList = wncQueryFacade.queryWarehouseLogisticsList(WarehouseLogisticsQueryInput.builder().storeNos(storeNos).build());
            Map<Integer, WarehousLogisticsCenterDTO> storeMap = storeDTOList.stream().collect(Collectors.toMap(WarehousLogisticsCenterDTO::getStoreNo, Function.identity()));
            for (SiteDTO siteDTO : storeList) {
                WarehousLogisticsCenterDTO warehousLogisticsCenterDTO = storeMap.get(Integer.parseInt(siteDTO.getOutBusinessNo()));
                if(warehousLogisticsCenterDTO == null){
                    continue;
                }
                siteDTO.setSitePics(warehousLogisticsCenterDTO.getStorePic());
            }
        }
        if(CollectionUtils.isNotEmpty(warehouseList)){
            List<Integer> warehouseNos = warehouseList.stream().map(SiteDTO::getOutBusinessNo).map(Integer::parseInt).collect(Collectors.toList());
            List<WarehouseStorageCenterDTO> warehouseDTOList = wncQueryFacade.queryXmWarehouseList(XmWarehouseQueryInput.builder().warehouseNos(warehouseNos).build());
            Map<Integer, WarehouseStorageCenterDTO> warehouseMap = warehouseDTOList.stream().collect(Collectors.toMap(WarehouseStorageCenterDTO::getWarehouseNo, Function.identity()));
            for (SiteDTO siteDTO : warehouseList) {
                WarehouseStorageCenterDTO warehouseStorageCenterDTO = warehouseMap.get(Integer.parseInt(siteDTO.getOutBusinessNo()));
                if(warehouseStorageCenterDTO == null){
                    continue;
                }
                siteDTO.setSitePics(warehouseStorageCenterDTO.getWarehousePic());
            }
        }

        return TmsResult.success(deliveryBatchDTO);
    }

    //合并点位，点位之间距离间隔小于1km的都要合并点位
    private List<List<DeliverySiteEntity>> mergeDeliverySite(Long deliveryBatchId, List<DeliverySiteEntity> deliverySiteEntityList) {
        List<List<DeliverySiteEntity>> response = Lists.newArrayList();
        if (CollectionUtils.isEmpty(deliverySiteEntityList)) {
            return response;
        }
        //查询配送批次下相领点位的间隔距离
        Map<String, BigDecimal> deliverySectionMap = getDeliverySectionMap(deliveryBatchId);

        List<DeliverySiteEntity> sortedSites = deliverySiteEntityList.stream()
                .sorted(Comparator.comparing(DeliverySiteEntity::getSequence)).collect(Collectors.toList());
        BigDecimal totalDiff = BigDecimal.ZERO;
        List<DeliverySiteEntity> itemList = Lists.newArrayList();
        for (int i = 0; i < sortedSites.size(); i++) {
            DeliverySiteEntity deliverySiteEntity = sortedSites.get(i);
//            if (Objects.equals(DeliverySiteTypeEnum.end.getCode(), deliverySiteEntity.getType())) {
//                response.add(itemList);
//                totalDiff = BigDecimal.ZERO;
//                itemList = Lists.newArrayList();
//                continue;
//            }
            if (sortedSites.size() == 1) {
                itemList.add(deliverySiteEntity);
                response.add(itemList);
                continue;
            }
            if (i == sortedSites.size() - 1) {
                if (totalDiff.compareTo(BigDecimal.ZERO) == 0) {
                    itemList.add(deliverySiteEntity);
                    response.add(itemList);
                } else {
                    response.add(itemList);
                }
                continue;
            }
            DeliverySiteEntity nextDeliveryEntity = sortedSites.get(i + 1);
            BigDecimal nextSiteDistance = deliverySectionMap.get(deliverySiteEntity.getSiteId() + "#" +
                    nextDeliveryEntity.getSiteId());
            BigDecimal distanceDiff = nextSiteDistance == null ? null :
                    nextSiteDistance.divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP);
            if (totalDiff.compareTo(BigDecimal.ZERO) == 0) {
                if (Objects.nonNull(distanceDiff) && distanceDiff.compareTo(BigDecimal.ONE) > 0) {
                    itemList.add(deliverySiteEntity);
                    response.add(itemList);
                    totalDiff = BigDecimal.ZERO;
                    itemList = Lists.newArrayList();
                    continue;
                } else {
                    if (Objects.isNull(distanceDiff)) {
                        itemList.add(deliverySiteEntity);
                        response.add(itemList);
                        totalDiff = BigDecimal.ZERO;
                        itemList = Lists.newArrayList();
                    } else {
                        totalDiff = totalDiff.add(distanceDiff);
                        itemList.add(deliverySiteEntity);
                        itemList.add(nextDeliveryEntity);
                    }
                    continue;

                }
            } else {
                if (Objects.nonNull(distanceDiff) && totalDiff.add(distanceDiff).compareTo(BigDecimal.ONE) <= 0) {
                    totalDiff = totalDiff.add(distanceDiff);
                    itemList.add(nextDeliveryEntity);
                } else {
                    response.add(itemList);
                    totalDiff = BigDecimal.ZERO;
                    itemList = Lists.newArrayList();
                }
            }


        }
        return response;
    }

    private Map<String, BigDecimal> getDeliverySectionMap(Long deliveryBatchId) {
        List<DeliverySectionEntity> deliverySectionEntities = deliverySectionRepository.queryList(DeliverySectionQuery.builder()
                .batchId(deliveryBatchId)
                .type(DeliverySectionEnums.Type.complete_path.getValue()).build());
        //构造起始、终止点位Map
        return deliverySectionEntities.stream()
                .collect(Collectors.toMap(e -> e.getBeginSiteEntity().getId() + "#" + e.getEndSiteEntity().getId(),
                        DeliverySectionEntity::getDistance, (a, b) -> a));
    }

    private List<DeliverySiteDTO> calculateNearSiteDistance(Long deliveryBatchId, List<DeliverySiteDTO> deliverySiteDTOList) {
        if (CollectionUtils.isEmpty(deliverySiteDTOList)) {
            return deliverySiteDTOList;
        }
        DeliveryBatchEntity batchEntity = deliveryBatchRepository.query(deliveryBatchId);

        //查询配送批次下相领点位的间隔距离
        Map<String, BigDecimal> sectionDistanceMap = getDeliverySectionMap(deliveryBatchId);
        List<DeliverySiteDTO> sortedSites = deliverySiteDTOList.stream().sorted(Comparator.comparing(DeliverySiteDTO::getSequence)).collect(Collectors.toList());
        //展示地址全称类型集合
        List<Integer> types = Arrays.asList(TmsSiteTypeEnum.CUSTOMER.getCode(), TmsSiteTypeEnum.PURCHASE_ADDRESS.getCode(), TmsSiteTypeEnum.SPECIFIED.getCode());
        for (int i = 0; i < sortedSites.size(); i++) {
            DeliverySiteDTO deliverySiteDTO = sortedSites.get(i);
            //查询点位信息 点位联系人、点位poi
            SiteEntity siteEntity = siteDomainService.query(deliverySiteDTO.getSiteId());
            deliverySiteDTO.setSiteDTO(SiteDtoConverter.entity2Dto(siteEntity));
            deliverySiteDTO.setSiteName(siteEntity.getSiteName(types));
            if (Objects.equals(DeliverySiteTypeEnum.end.getCode(), deliverySiteDTO.getType())) {
                deliverySiteDTO.setNextSiteDistanceKm(BigDecimal.ZERO);
                continue;
            }
            DeliverySiteDTO nextDeliverySiteDTO = sortedSites.get(i + 1);
            BigDecimal nextSiteDistance = sectionDistanceMap.get(deliverySiteDTO.getSiteId() + "#" + nextDeliverySiteDTO.getSiteId());
            deliverySiteDTO.setNextSiteDistanceKm(nextSiteDistance == null ? null : nextSiteDistance.divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP));
        }
        //处理当前需打卡节点
        Optional<DeliverySiteDTO> curSignDeliverySite = sortedSites.stream().filter(e -> !Objects.equals(DeliverySiteStatusEnum.DEPARTED.getCode(), e.getStatus())).findFirst();
        if (curSignDeliverySite.isPresent()){
            DeliverySiteDTO deliverySiteDTO = curSignDeliverySite.get();
            if (Objects.equals(DeliverySiteTypeEnum.end.getCode(), deliverySiteDTO.getType())){
                if (deliverySiteDTO.getSignInStatus() == null){
                    deliverySiteDTO.setCurSignFlag(true);
                }
                if (deliverySiteDTO.getSignInStatus() != null &&
                        Objects.equals(batchEntity.getType(),DeliveryBatchTypeEnum.all_category_pick.getCode())){
                    deliverySiteDTO.setCurSignFlag(true);
                }
            }else {
                deliverySiteDTO.setCurSignFlag(true);
            }
        }
        return sortedSites;
    }

    @Override
    public TmsResult<List<DeliverySiteDTO>> punchDetail(Long deliveryBatchId) {
        TmsAssert.notNull(deliveryBatchId, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryBatchId");
        //获取批次信息
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(deliveryBatchId);
        TmsAssert.notNull(deliveryBatchEntity.getId(), ErrorCodeEnum.NOT_FIND, "deliveryBatchId="+deliveryBatchId);
        DeliveryBatchDTO deliveryBatchDTO = DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity);
        List<DeliverySiteDTO> deliverySiteDTOS = calculateNearSiteDistance(deliveryBatchEntity.getId(), deliveryBatchDTO.getDeliverySiteDTOList());
        return TmsResult.success(deliverySiteDTOS);
    }


    @Override
    public void queryDistOrdersBatch(List<DistOrderDTO> distOrderList) {
        List<Long> distOrderIds = distOrderList.stream().map(DistOrderDTO::getDistId).collect(Collectors.toList());

        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryListWithBatchDetail(DeliveryOrderQuery.builder().distIdList(distOrderIds).build());
        Map<Long, List<DeliveryOrderEntity>> deliveryOrderMapList = deliveryOrderEntityList.stream().collect(Collectors.groupingBy(DeliveryOrderEntity::getDistOrderId));

        for (DistOrderDTO distOrderDTO : distOrderList) {
            if(!CollectionUtils.isEmpty(deliveryOrderMapList.get(distOrderDTO.getDistId()))){
                List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderMapList.get(distOrderDTO.getDistId());
                DeliveryBatchEntity deliveryBatchEntity = deliveryOrderEntities.get(0) == null || deliveryOrderEntities.get(0).getDeliveryBatchEntity() == null ?
                        new DeliveryBatchEntity() : deliveryOrderEntities.get(0).getDeliveryBatchEntity();
                distOrderDTO.setDeliveryBatchList(Arrays.asList(DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity)));
            }
        }
    }

    @XmLock(prefixKey = RedisConstants.PathOpearation.TMS_PATH_OPERATION_COMMON, key = "{command.storeNo}",waitTime = 1000, message = "操作频繁，请稍后重试")
    @Override
    public TmsResult<Void> intelligentPath(IntelligentPathUpdateCommand command) {
        Long batchId = command.getBatchId();
        Boolean handleIntelligentFlag = command.getHandleIntelligentFlag();
        Integer storeNo = command.getStoreNo();
        Boolean completePathFlag = command.getCompletePathFlag();
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(batchId);
        if(deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            throw new TmsRuntimeException("未找到此配送批次:"+batchId+"，请刷新页面稍后再试");
        }
        if(deliveryBatchEntity.getStatus() != DeliveryBatchStatusEnum.TO_BE_WIRED){
            throw new TmsRuntimeException(ErrorCodeEnum.BATCH_STATUS_ERROR,deliveryBatchEntity.getStatus().getName());
        }

        //查询这个城配仓和日期下的所有批次
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .beginSiteId(deliveryBatchEntity.getBeginSiteId())
                .deliveryTime(deliveryBatchEntity.getDeliveryTime())
                .type(DeliveryBatchTypeEnum.city.getCode())
                .build()
        );
        //过滤未排线批次
        deliveryBatchEntityList = deliveryBatchEntityList.stream().filter(batch -> batch.getPathId() > 0).collect(Collectors.toList());
        List<Long> batchIds = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(batchIds)){
            throw new TmsRuntimeException("无路线信息无需要智能排线");
        }

        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchIdList(batchIds).build());
        Map<Long, List<DeliverySiteEntity>> batch2DeliverySitesMap = deliverySiteEntities.stream().collect(Collectors.groupingBy(DeliverySiteEntity::getDeliveryBatchId));

        List<String> storeNos = tmsNacosConfig.queryOrtoolsStoreNos();
        if(storeNos.contains(String.valueOf(storeNo))){
            // 多线程处理智能排线次序
            moreThreadHandleIntelligentPathSequence(handleIntelligentFlag, deliveryBatchEntityList, batch2DeliverySitesMap,completePathFlag);
        }else{
            // 循环处理智能排线次序
            forEachHandleIntelligentPathSequence(handleIntelligentFlag, deliveryBatchEntityList, batch2DeliverySitesMap,completePathFlag);
        }

        return TmsResult.VOID_SUCCESS;
    }

    private void forEachHandleIntelligentPathSequence(Boolean handleIntelligentFlag, List<DeliveryBatchEntity> deliveryBatchEntityList,
                                                      Map<Long, List<DeliverySiteEntity>> batch2DeliverySitesMap,Boolean completePathFlag) {
        List<DeliveryBatchEntity> needIntelligentPathBatchList = deliveryBatchEntityList.stream()
                .filter(e -> this.needIntelligentPath(e, batch2DeliverySitesMap,completePathFlag))
                .collect(Collectors.toList());

        needIntelligentPathBatchList.forEach(batchEntity -> {
            deliverySiteDomainService.intelligentPath(batchEntity,handleIntelligentFlag == null || handleIntelligentFlag);
        });
    }

    private void moreThreadHandleIntelligentPathSequence(Boolean handleIntelligentFlag, List<DeliveryBatchEntity> deliveryBatchEntityList,
                                                         Map<Long, List<DeliverySiteEntity>> batch2DeliverySitesMap,Boolean completePathFlag) {
        List<CompletableFuture<Void>> deliveryBatchIntelligentPathFutures = deliveryBatchEntityList.stream()
                .filter(e -> this.needIntelligentPath(e, batch2DeliverySitesMap,completePathFlag))
                .map(batchEntity -> CompletableFuture.runAsync(
                        () -> {
                            try {
                                deliverySiteDomainService.intelligentPath(batchEntity, handleIntelligentFlag == null || handleIntelligentFlag);
                            } catch (Exception e) {
                                log.error("批次{}排线失败", batchEntity.getId(), e);
                            }
                        },
                        ExecutorUtil.ortoolsIntelligentPathExecutor  // 使用专用线程池
                ))
                .collect(Collectors.toList());

        try {
            CompletableFuture.allOf(deliveryBatchIntelligentPathFutures.toArray(new CompletableFuture[0]))
                    .exceptionally(ex -> {
                        log.error("ortools智能排线整体任务异常,已调用贪心算法处理", ex);
                        return null;
                    })
                    .get(30, TimeUnit.SECONDS);  // 设置全局超时
        } catch (TimeoutException e) {
            log.warn("排线任务超时，已强制终止");
            deliveryBatchIntelligentPathFutures.forEach(f -> f.cancel(true));
            // 循环处理贪心算法
            deliveryBatchEntityList.forEach(batch -> batch.setNeedGreedyIntelligentPath(true));
            this.forEachHandleIntelligentPathSequence(handleIntelligentFlag, deliveryBatchEntityList, batch2DeliverySitesMap,completePathFlag);
        } catch (Exception e) {
            log.error("排线任务执行异常,已设置贪心算法配置", e);
            // 循环处理贪心算法
            deliveryBatchEntityList.forEach(batch -> batch.setNeedGreedyIntelligentPath(true));
            this.forEachHandleIntelligentPathSequence(handleIntelligentFlag, deliveryBatchEntityList, batch2DeliverySitesMap,completePathFlag);
        }
    }

    /**
     * 是否需要智能排线
     * @param batchEntity 批次路线信息
     * @param batch2DeliverySitesMap 批次站点信息
     * @param completePathFlag 是否完成排线
     * @return true:需要智能排线
     */
    private Boolean needIntelligentPath(DeliveryBatchEntity batchEntity,Map<Long, List<DeliverySiteEntity>> batch2DeliverySitesMap,
                                        Boolean completePathFlag){
        //根据批次id获取运输线路
        List<DeliverySiteEntity> batchDeliverySiteList = batch2DeliverySitesMap.get(batchEntity.getId());
        if(CollectionUtils.isEmpty(batchDeliverySiteList)){
            return false;
        }
        if(batchEntity.getStatus() != DeliveryBatchStatusEnum.TO_BE_WIRED){
            log.error("非待排线状态，不能智能排线，已跳过，批次id:"+batchEntity.getId()+",状态:"+batchEntity.getStatus().getName(),new BizException("非待排线状态，不能智能排线，已跳过"));
            return false;
        }
        batchEntity.setDeliverySiteList(batchDeliverySiteList);
        List<DeliverySiteEntity> specialSendSiteList = batchEntity.getSpecialSendDeliverySiteList();
        List<DeliverySiteEntity> normalSendSiteList = batchEntity.getNormalSendDeliverySiteList();

        if (completePathFlag != null && completePathFlag) {
            return true;
        }

        //专线专车 待排线状态 无需智能排线，完成排线会处理
        if(CollectionUtils.isEmpty(normalSendSiteList) && CollectionUtils.isNotEmpty(specialSendSiteList)){
            return false;
        }

        return true;
    }

    @Override
    public TmsResult<List<DeliveryBatchDTO>> queryList(DeliveryBatchQuery deliveryBatchQuery) {
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(deliveryBatchQuery);
        return TmsResult.success(deliveryBatchEntityList.stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList()));
    }

    @Override
    public void finishCompletePathNotifyTms(Long batchId) {
        eventBusService.finishCompletePathNotifyTms(batchId);
    }

    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    @Override
    public void createBigCustomerPick(Long batchId) {
        if(batchId == null){
            return;
        }
        //查询详情，只查询完成排线时间就行，因为点位和配送单早就挂上面了，几乎不存在延迟的情况
        DeliveryBatchEntity deliveryBatchEntityForceMaster = deliveryBatchRepository.queryByIdForceMaster(batchId);
        if(deliveryBatchEntityForceMaster == null || deliveryBatchEntityForceMaster.getId() == null){
            return;
        }
        log.info("deliveryBatchRepository->queryByIdForceMaster res:{}",JSON.toJSONString(deliveryBatchEntityForceMaster));
        if(deliveryBatchEntityForceMaster.getBePathTime() == null){
            throw new TmsRuntimeException(batchId + "批次查询主库信息的完成排线时间为null,请留意！！");
        }
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(batchId);
        deliveryBatchEntity.setBePathTime(deliveryBatchEntityForceMaster.getBePathTime());
        deliveryPickDomainService.createBigCustomerPick(deliveryBatchEntity);
    }

    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    @Override
    public void completePathBySelf(Long batchId) {
        if (batchId == null) {
            return;
        }
        //查询详情
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(batchId);
        //移除数据
        deliveryPickRepository.removeByBatchId(deliveryBatchEntity.getId());
        //生成拣货任务
        deliveryPickDomainService.completePath(deliveryBatchEntity);
    }

    @Override
    public void sendUnSignBatchDeliverySiteMsg() {
        // 获取指定条件的调度单
        PageInfo<DeliveryBatchEntity> deliveryBatchPageInfo = getDeliveryBatchEntityPageInfo();
        if (Objects.isNull(deliveryBatchPageInfo) || CollectionUtils.isEmpty(deliveryBatchPageInfo.getList())) {
            return;
        }
        Map<Long, DeliveryBatchEntity> deliveryBatchMap = getLongDeliveryBatchEntityMap(deliveryBatchPageInfo);
        // 发送未按要求时间到达仓库异常信息提示
        sendArriveOrOutMsg(deliveryBatchMap, true);
        // 未按照要求出发时间出仓消息提示
        sendArriveOrOutMsg(deliveryBatchMap, false);
    }

    /**
     * 发送出发或者到达异常信息提示
     *
     * @param deliveryBatchMap
     * @param arriveMsg        是否是出发消息
     */
    private void sendArriveOrOutMsg(Map<Long, DeliveryBatchEntity> deliveryBatchMap, boolean arriveMsg) {
        // 查询符合条件下的调度单配送点位明细
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryWaitSendMsgList(deliveryBatchMap.keySet(), arriveMsg);
        if (org.springframework.util.CollectionUtils.isEmpty(deliverySiteEntities)) {
            return;
        }

        //查询所有的符合条件的点位信息
        Map<Long, SiteEntity> siteEntityMap = getSiteEntityMap(deliverySiteEntities);
        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            deliverySiteEntity.setSiteEntity(siteEntityMap.get(deliverySiteEntity.getSiteId()));
        }
        Map<Long, List<DeliverySiteEntity>> deliverySiteMap = deliverySiteEntities.stream()
                .collect(Collectors.groupingBy(DeliverySiteEntity::getDeliveryBatchId));
        deliveryBatchMap.forEach((batchId, deliveryBatchEntity) -> {
            List<DeliverySiteEntity> effectiveDeliverySites = deliverySiteMap.get(batchId);
            if (!CollectionUtils.isEmpty(effectiveDeliverySites)) {
                if (StringUtils.isBlank(deliveryBatchEntity.getPathName())) {
                    StringJoiner pathJoiner = new StringJoiner("-");
                    for (DeliverySiteEntity deliverySiteDTO : deliveryBatchEntity.getDeliverySiteList()) {
                        pathJoiner.add(deliverySiteDTO.getSiteName());
                    }
                    deliveryBatchEntity.setPathName(pathJoiner.toString());
                }
                deliveryBatchEntity.setDeliverySiteList(effectiveDeliverySites);
            } else {
                deliveryBatchEntity.setDeliverySiteList(Lists.newArrayList());
            }
        });
        List<DeliveryBatchEntity> effectiveDeliveryBatchList = deliveryBatchMap.values().stream()
                .filter(a -> !CollectionUtils.isEmpty(a.getDeliverySiteList())).collect(Collectors.toList());
        Set<Long> driverIdList = effectiveDeliveryBatchList.stream().map(DeliveryBatchEntity::getDriverId)
                .collect(Collectors.toSet());
        List<Long> carIdList = effectiveDeliveryBatchList.stream().map(DeliveryBatchEntity::getCarId)
                .collect(Collectors.toList());

        Map<Long, DriverEntity> driverMap = getDriverMap(driverIdList);
        Map<Long, CarEntity> carMap = getCarMap(carIdList);
        ConfigEntity configEntity = tmsConfigRepository.queryByKey(TmsConfigKeyEnum.DELIVERY_BATCH_ERROR_URL);
        if (Objects.isNull(configEntity) || StringUtil.isBlank(configEntity.getValue())) {
            log.error("未找到司机打卡异常钉钉消息群配置，请检查!");
            return;
        }
        for (DeliveryBatchEntity deliveryBatchEntity : effectiveDeliveryBatchList) {
            List<List<DeliverySiteEntity>> mergeDeliverySite = mergeDeliverySite(deliveryBatchEntity.getId(),
                    deliveryBatchEntity.getDeliverySiteList());
            DriverEntity driverEntity = driverMap.get(deliveryBatchEntity.getDriverId());
            CarEntity carEntity = carMap.get(deliveryBatchEntity.getCarId());
            for (List<DeliverySiteEntity> needSendMsgDeliverySiteList : mergeDeliverySite) {
                try {
                    sendMsgRemind(needSendMsgDeliverySiteList, deliveryBatchEntity, driverEntity,
                            carEntity, configEntity, arriveMsg);
                }catch(Exception e){
                    log.error("调度单钉钉消息发送失败，异常原因"+e.getMessage());
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void diffBatchHandle() {
        //根据配送时间查询相同起点、终点、配送时间配送单不同批次数据
        LocalDateTime deliveryTime = LocalDate.now().atStartOfDay().plusDays(1);
        Boolean result = deliveryOrderRepository.diffBatchHandle(deliveryTime);
        if(result){
            log.info("存在相同起点、终点、配送时间配送单不同批次数据,已自动处理");
        }
    }

    private void sendMsgRemind(List<DeliverySiteEntity> deliverySiteEntities,
                               DeliveryBatchEntity deliveryBatchEntity,
                               DriverEntity driverEntity,
                               CarEntity carEntity, ConfigEntity configEntity, boolean arriveMsg) {
        if (CollectionUtils.isEmpty(deliverySiteEntities)) {
            return;
        }
        StringJoiner siteNameSj = new StringJoiner("、");
        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            siteNameSj.add(deliverySiteEntity.getSiteEntity().getSiteName());
        }

        StringBuilder textSb = new StringBuilder();
        textSb.append("调度异常通知").append("\n\n");
        textSb.append("调度单号: ").append(deliveryBatchEntity.getId()).append("\n\n");
        String pathName = deliveryBatchEntity.getPathName();
        //原先路线两种展示样式，飞书消息展示>换行处理
        pathName = pathName == null || pathName.contains("-") ? pathName : pathName.replaceAll("—>","-");
        textSb.append("路线信息: ").append(pathName).append("\n\n");
        textSb.append("司机信息: ").append(Objects.nonNull(driverEntity) ? driverEntity.getName() + "(" + driverEntity.getPhone() + ")" : "")
                .append("\n\n");
        textSb.append("车辆信息: ");
        if (Objects.isNull(carEntity)) {
            textSb.append(" ");
        } else {
            textSb.append(carEntity.getCarNumber()).append("(").append(carEntity.getCarTypeEnum().getDesc())
                    .append("|").append(carEntity.getCarStorageEnum().getName()).append(")");
        }
        textSb.append("\n\n");
        textSb.append("异常站点: ").append(siteNameSj.toString()).append("\n\n");
        textSb.append("异常说明: ");
        if (arriveMsg) {
            textSb.append("未按指定时间入仓");
        } else {
            textSb.append("未按指定时间出仓");
        }
        HashMap<String, String> msgMap = new HashMap<>();
        msgMap.put("text", textSb.toString());
        msgMap.put("title", configEntity.getRemark());
        DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, configEntity.getValue(), () -> msgMap);
        deliverySiteRepository.updateSendMsgFlag(deliverySiteEntities.stream()
                .map(DeliverySiteEntity::getId).collect(Collectors.toSet()), arriveMsg);
    }


    private Map<Long, DriverEntity> getDriverMap(Set<Long> driverIdList) {
        Map<Long, DriverEntity> driverEntityMap = Maps.newHashMap();
        if (org.springframework.util.CollectionUtils.isEmpty(driverIdList)) {
            return driverEntityMap;
        }
        DriverQuery driverQuery = new DriverQuery();
        driverQuery.setIds(driverIdList);
        List<DriverEntity> driverEntities = driverRepository.queryList(driverQuery);
        if (org.springframework.util.CollectionUtils.isEmpty(driverEntities)) {
            return driverEntityMap;
        }
        return driverEntities.stream()
                .collect(Collectors.toMap(DriverEntity::getId, Function.identity()));
    }

    private Map<Long, CarEntity> getCarMap(List<Long> carIdList) {
        Map<Long, CarEntity> map = Maps.newHashMap();
        if (org.springframework.util.CollectionUtils.isEmpty(carIdList)) {
            return map;
        }
        List<CarEntity> carEntities = carRepository.getCarByIdList(carIdList);

        if (org.springframework.util.CollectionUtils.isEmpty(carEntities)) {
            return map;
        }
        return carEntities.stream().collect(Collectors.toMap(CarEntity::getId, Function.identity(), (a, b) -> a));
    }


    private PageInfo<DeliveryBatchEntity> getDeliveryBatchEntityPageInfo() {
        DeliveryBatchQuery deliveryBatchQuery = new DeliveryBatchQuery();
        deliveryBatchQuery.setPageSize(0);
        deliveryBatchQuery.setPageIndex(0);
        deliveryBatchQuery.setDeliveryBatchStatusList(com.google.common.collect.Lists.newArrayList(DeliveryBatchStatusEnum.IN_DELIVERY.getCode()
                , DeliveryBatchStatusEnum.TO_BE_PICKED.getCode()));
        deliveryBatchQuery.setDeliveryBatchTypeList(com.google.common.collect.Lists.newArrayList(DeliveryBatchTypeEnum.trunk.getCode()));
        AbstractPageQuery.PageSortInput deliveryTimeSortInput = new AbstractPageQuery.PageSortInput();
        deliveryTimeSortInput.setSortBy("id");
        deliveryTimeSortInput.setOrderBy("desc");

        deliveryBatchQuery.setSortList(Arrays.asList(deliveryTimeSortInput));

        PageInfo<DeliveryBatchEntity> deliveryBatchEntityPageInfo = deliveryBatchDomainService.queryListWithSite(deliveryBatchQuery);


        return deliveryBatchEntityPageInfo;
    }

    private Map<Long, SiteEntity> getSiteEntityMap(List<DeliverySiteEntity> deliverySiteEntities) {
        SiteQuery siteQuery = new SiteQuery();
        siteQuery.setSiteIds(deliverySiteEntities.stream()
                .map(DeliverySiteEntity::getSiteId).collect(Collectors.toList()));
        List<SiteEntity> siteEntities = siteRepository.siteSearch(siteQuery);
        Map<Long, SiteEntity> siteEntityMap = siteEntities.stream()
                .collect(Collectors.toMap(SiteEntity::getId, Function.identity()));
        return siteEntityMap;
    }

    private Map<Long, DeliveryBatchEntity> getLongDeliveryBatchEntityMap(PageInfo<DeliveryBatchEntity> deliveryBatchPageInfo) {
        List<DeliveryBatchEntity> deliveryBatchList = deliveryBatchPageInfo.getList();
        Map<Long, DeliveryBatchEntity> deliveryBatchMap = deliveryBatchList.stream()
                .collect(Collectors.toMap(DeliveryBatchEntity::getId, Function.identity()));
        return deliveryBatchMap;
    }

    @Override
    public Boolean deliveryBatchValidator(Long deliveryBatchId) {
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchId);
        if(deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"点位批次");
        }

        return deliveryBatchValidator.validateCompletePathForDeliveryBatch(deliveryBatchEntity);
    }

    @Override
    public List<DeliveryBatchDTO> queryCityTrunkBatch(Long batchId) {
        TmsAssert.notNull(batchId, ErrorCodeEnum.PARAM_NOT_NULL, "batchId");
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(batchId);
        if(deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,batchId);
        }

        //查询干线批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryWithSiteWithBase(DeliveryBatchQuery.builder()
                .deliveryTime(deliveryBatchEntity.getDeliveryTime())
                .deliveryBatchType(DeliveryBatchTypeEnum.trunk.getCode())
                .deliveryBatchStatusList(Arrays.asList(
                        DeliveryBatchStatusEnum.TO_BE_PICKED.getCode(),
                        DeliveryBatchStatusEnum.IN_DELIVERY.getCode(),
                        DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode()))
                .build());
        if(CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return new ArrayList<>();
        }
        //过滤出存在城配的点位信息批次信息
        deliveryBatchEntityList = deliveryBatchEntityList.stream().filter(e -> e.getDeliverySiteList().stream()
                .map(DeliverySiteEntity::getSiteId).collect(Collectors.toList()).contains(deliveryBatchEntity.getBeginSiteId()))
                .collect(Collectors.toList());

        for (DeliveryBatchEntity batchEntity : deliveryBatchEntityList) {
            List<DeliverySiteEntity> deliverySiteList = batchEntity.getDeliverySiteList();
            deliverySiteList = deliverySiteList.stream().filter(site -> Objects.equals(site.getSiteId(),deliveryBatchEntity.getBeginSiteId())).collect(Collectors.toList());
            batchEntity.setDeliverySiteList(deliverySiteList);
        }

        return deliveryBatchEntityList.stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList());
    }

    @Override
    public DeliveryBatchDTO queryDriverCurrentInfo(DeliveryBatchQuery deliveryBatchQuery) {
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchQuery);
        if(deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"批次");
        }
        //配送点位信息
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder()
                .batchId(deliveryBatchEntity.getId()).build());
        if(!CollectionUtils.isEmpty(deliverySiteEntities)){
            deliverySiteEntities = deliverySiteEntities.stream().sorted(Comparator.comparing(DeliverySiteEntity::getSignInTime,Comparator.nullsLast(LocalDateTime::compareTo)).reversed()).collect(Collectors.toList());
            deliveryBatchEntity.setDeliverySiteList(deliverySiteEntities);
        }

        return DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity);
    }

    @Override
    public void finishCompletePathAllMessageSend(CompletePathMessage completePathMessage) {
        if(completePathMessage == null || CollectionUtils.isEmpty(completePathMessage.getDeliveryBatchIds())){
            return;
        }

        eventBusService.finishCompletePathAllMessageSend(completePathMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calcPathDistance(@NonNull CalcTmsPathDistanceMessage calcTmsPathDistanceMessage) {
        Long batchId = calcTmsPathDistanceMessage.getBatchId();
        if(batchId == null){
            return;
        }
        DeliveryBatchEntity batchEntity = deliveryBatchRepository.query(batchId);
        if(batchEntity == null){
            return;
        }

        // 智能排线单独处理
        if (Objects.equals(DeliverySectionEnums.Type.INTELLIGENCE_PATH, calcTmsPathDistanceMessage.getType())) {
            List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder().batchId(batchId).build());
            batchEntity.setDeliverySiteList(deliverySiteEntities);

            List<DeliverySiteEntity> intelligenceList = batchEntity.getExcludeBeginDeliverySiteList();
            DeliverySiteEntity beginDeliverySite = batchEntity.getBeginDeliverySite();

            //计算智能排线的数据
            List<DeliverySiteEntity> normalSendSiteList = null;
            try {
                normalSendSiteList = intelligenceList.stream()
                        .filter(site -> Objects.equals(site.getSendWay(), DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()))
                        .sorted(Comparator.comparing(DeliverySiteEntity::getIntelligenceSequence))
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.error("批次id:{}智能排线数据处理异常,重试中",calcTmsPathDistanceMessage.getBatchId(),e);
                throw new RuntimeException("智能排线数据处理异常,重试中");
            }

            List<net.xianmu.gaode.support.service.input.WaypointsInput> waypointsInputList = new ArrayList<>();
            waypointsInputList.add(net.xianmu.gaode.support.service.input.WaypointsInput.builder()
                    .siteId(beginDeliverySite.getSiteId())
                    .poi(beginDeliverySite.getSiteEntity().getPoi()).build());

            // 非专车配送的智能排线数据
            if (CollectionUtils.isNotEmpty(normalSendSiteList)) {
                for (DeliverySiteEntity sendSiteEntity : normalSendSiteList) {
                    waypointsInputList.add(net.xianmu.gaode.support.service.input.WaypointsInput.builder()
                            .siteId(sendSiteEntity.getSiteId())
                            .poi(sendSiteEntity.getSiteEntity().getPoi()).build());
                }
            } else {
                // 计算专车智能排线的数据
                List<DeliverySiteEntity> specialSendDeliverySiteList = batchEntity.getSpecialSendDeliverySiteList().stream()
                        .sorted(Comparator.comparing(DeliverySiteEntity::getIntelligenceSequence)).collect(Collectors.toList());

                for (DeliverySiteEntity sendSiteEntity : specialSendDeliverySiteList) {
                    waypointsInputList.add(net.xianmu.gaode.support.service.input.WaypointsInput.builder()
                            .siteId(sendSiteEntity.getSiteId())
                            .poi(sendSiteEntity.getSiteEntity().getPoi()).build());
                }
            }

            // 替换数据
            calcTmsPathDistanceMessage.setWaypointsInputList(waypointsInputList);
        }

        SiteEntity beginSiteEntity = siteRepository.query(batchEntity.getBeginSiteId());
        if(beginSiteEntity != null && tmsNacosConfig.queryGaodeMinDistanceStoreNos().contains(beginSiteEntity.getOutBusinessNo())){
            deliveryBatchRepository.calculateDistance(calcTmsPathDistanceMessage.getWaypointsInputList(),
                    batchId,calcTmsPathDistanceMessage.getType(), XMDriverRoutPlanStrategyEnum.COMBINATION_STRATEGY);
        }else{
            deliveryBatchRepository.calculateDistance(calcTmsPathDistanceMessage.getWaypointsInputList(),
                    batchId,calcTmsPathDistanceMessage.getType(), null);
        }

    }

    @Override
    public void batchOutTimeMonitor(String postDate) {
        LocalDate deliveryDate = LocalDate.now();
        if (StrUtil.isNotBlank(postDate)){
            deliveryDate = LocalDate.parse(postDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        //完成排线批次
        List<DeliveryBatchStatusEnum> completePathBatchStatus = Arrays.asList(DeliveryBatchStatusEnum.TO_BE_PICKED, DeliveryBatchStatusEnum.IN_DELIVERY, DeliveryBatchStatusEnum.COMPLETE_DELIVERY);
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryWithSiteWithBase(DeliveryBatchQuery.builder()
                .deliveryTime(deliveryDate.atStartOfDay())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .deliveryBatchStatusList(completePathBatchStatus.stream().map(DeliveryBatchStatusEnum::getCode).collect(Collectors.toList()))
                .build());
        if (CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return;
        }
        //排除未排路线的默认批次其他所有需配送批次
        deliveryBatchEntityList = deliveryBatchEntityList.stream().filter(e -> e.getPathId() != -1).collect(Collectors.toList());
        StringBuilder text = new StringBuilder();
        text.append("截止").append(deliveryDate).append("各城配仓路线出仓情况如下:").append("\n\n");
        long noOutBatchNum = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getBeginDeliverySite).filter(Objects::nonNull).filter(e -> e.getSignOutTime() == null).count();
        if (noOutBatchNum == 0){
            text.append("所有城配仓线路均出仓完成");
        }else {
            //排除未排路线的默认批次其他所有需配送批次
            Map<Long, List<DeliveryBatchEntity>> batchMap = deliveryBatchEntityList.stream().collect(Collectors.groupingBy(DeliveryBatchEntity::getBeginSiteId));
            for (Map.Entry<Long, List<DeliveryBatchEntity>> batchEntry : batchMap.entrySet()) {
                List<DeliveryBatchEntity> batchListWithBeginSite = batchEntry.getValue();
                if (CollectionUtils.isEmpty(batchListWithBeginSite)){
                    continue;
                }
                long no0utBatchNumWithBeginSite = batchListWithBeginSite.stream().map(DeliveryBatchEntity::getBeginDeliverySite).filter(Objects::nonNull).filter(e -> e.getSignOutTime() == null).count();
                if (no0utBatchNumWithBeginSite == 0){
                    continue;
                }
                Long beginSiteId = batchEntry.getKey();
                SiteEntity beginSiteEntity = siteDomainService.query(beginSiteId);
                if (beginSiteEntity == null){
                    continue;
                }
                long outBatchNumWithBeginSite = batchListWithBeginSite.size() - no0utBatchNumWithBeginSite;
                text.append(beginSiteEntity.getName()).append(",")
                        .append("配送线路")
                        .append(batchListWithBeginSite.size()).append("条，")
                        .append("已出仓路线")
                        .append(outBatchNumWithBeginSite).append("条，")
                        .append("剩余未出仓路线").append(no0utBatchNumWithBeginSite).append("\n\n");
            }
        }

        //存在配置中的下载路径
        ConfigEntity downloadConfig = tmsConfigRepository.queryByKey(TmsConfigKeyEnum.OUT_TIME_MONITOR_DOWNLOAD_URL);
        if (downloadConfig == null || StrUtil.isBlank(downloadConfig.getValue())){
            throw new ProviderException("钉钉城配仓出仓晚点监控数据下载路径未配置");
        }
        String downloadUrlPrefix = downloadConfig.getValue();
        String params = String.format("postDate=%s)", deliveryDate);
        //拼接下载地址
        String downloadUrl = downloadUrlPrefix + params;
        text.append("\n\n").append(downloadUrl);


        HashMap<String, String> dingTalkPushMap = Maps.newHashMapWithExpectedSize(2);
        dingTalkPushMap.put("text",text.toString());
        dingTalkPushMap.put("title", "城配仓出仓时间监控");
        //配置中发送群机器人的url
        ConfigEntity urlConfig = tmsConfigRepository.queryByKey(TmsConfigKeyEnum.OUT_TIME_MONITOR_ROBOT_URL);
        if (urlConfig == null || StrUtil.isBlank(urlConfig.getValue())){
            throw new ProviderException("钉钉城配仓出仓晚点监控提醒机器人URL未配置");
        }
        String dingTalkRobotUrl = urlConfig.getValue();
        DingTalkRobotUtil.sendMsgAndAtAll("markdown", dingTalkRobotUrl, () -> dingTalkPushMap);
    }

    @Override
    public void batchOutTimeMonitorDown(String postDate, HttpServletResponse response) {
        LocalDate deliveryDate = LocalDate.now();
        if (StrUtil.isNotBlank(postDate)){
            deliveryDate = LocalDate.parse(postDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        List<StoreOutTimeMonitorPojo> outTimeMonitorList = this.getOutTimeMonitorData(deliveryDate);

        String fileName = String.format("城配仓出仓时间监控%s.xls", deliveryDate.format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT)));
        HttpServletResponse excelResponse = this.getExcelHttpResponse(fileName, response);
        try {
            EasyExcel.write(excelResponse.getOutputStream(), StoreOutTimeMonitorPojo.class).excelType(ExcelTypeEnum.XLS)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("城配仓出仓").doWrite(outTimeMonitorList);
        } catch (IOException e) {
            throw new ProviderException("城配仓出仓时间监控详细数据导出异常", e);
        }

    }

    @Override
    public void deliveryBatchRelateValidate(DeliveryBatchRelateValidateQuery deliveryBatchRelateValidateQuery) {
        deliveryBatchValidator.validateBatchRelateBase(deliveryBatchRelateValidateQuery.getBatchId(), deliveryBatchRelateValidateQuery.getRelateBatchId());
        boolean isExistRelate = deliveryBatchValidator.validateBatchRelateExist(deliveryBatchRelateValidateQuery.getBatchId(), deliveryBatchRelateValidateQuery.getRelateBatchId());
        //新增关联校验是否存在相同关联
        if (isExistRelate){
            throw new TmsRuntimeException("调度单已存在,请重新选择!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeBatchLoadRatioCalc(Long deliveryBatchId) {
        if (deliveryBatchId == null){
            return;
        }
        deliveryBatchDomainService.calculateAndUpdateLoadRatio(deliveryBatchId);
    }

    @Override
    public List<DeliveryBatchDTO> queryRelateBatchList(DeliveryBatchRelateQuery deliveryBatchRelateQuery) {
        if (deliveryBatchRelateQuery.getBatchId() == null){
            return Collections.emptyList();
        }
        DeliveryBatchQuery query = DeliveryBatchQuery.builder().batchIds(Collections.singletonList(deliveryBatchRelateQuery.getBatchId())).build();
        query.initTrunkType();
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryWithSite(query);
        return deliveryBatchEntityList.stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList());
    }

    private List<StoreOutTimeMonitorPojo> getOutTimeMonitorData(LocalDate deliveryDate) {
        //完成排线批次
        List<DeliveryBatchStatusEnum> completePathBatchStatus = Arrays.asList(DeliveryBatchStatusEnum.TO_BE_PICKED, DeliveryBatchStatusEnum.IN_DELIVERY, DeliveryBatchStatusEnum.COMPLETE_DELIVERY);
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryWithSiteWithBase(DeliveryBatchQuery.builder()
                .deliveryTime(deliveryDate.atStartOfDay())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .deliveryBatchStatusList(completePathBatchStatus.stream().map(DeliveryBatchStatusEnum::getCode).collect(Collectors.toList()))
                .build());
        if (CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return Collections.emptyList();
        }
        //排除未排路线的默认批次其他所有需配送批次
        deliveryBatchEntityList = deliveryBatchEntityList.stream().filter(e -> e.getPathId() != -1).collect(Collectors.toList());
        Map<Long, List<DeliveryBatchEntity>> batchMap = deliveryBatchEntityList.stream().collect(Collectors.groupingBy(DeliveryBatchEntity::getBeginSiteId));
        String deliveryDateStr = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(deliveryDate);

        DateTimeFormatter outTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //查询批次缺货数量
        List<Long> batchIds = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
        List<BatchPickDetailEntity> batchPickDetailEntities = deliveryPickRepository.queryByBatch(batchIds);
        Map<Long, Integer> batchPickMap = batchPickDetailEntities.stream().collect(Collectors.toMap(BatchPickDetailEntity::getDeliveryBatchId, BatchPickDetailEntity::getShortQuantity, (oldData, newData) -> newData));
        List<StoreOutTimeMonitorPojo> storeOutTimeMonitorPojoList = new ArrayList<>();

        LocalDateTime signOutTime = LocalDateTime.now();
        String currentDateStr = signOutTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        for (Map.Entry<Long, List<DeliveryBatchEntity>> batchEntry : batchMap.entrySet()) {
            Long beginSiteId = batchEntry.getKey();
            List<DeliveryBatchEntity> batchListWithBeginSite = batchEntry.getValue();
            SiteEntity beginSiteEntity = siteDomainService.query(beginSiteId);
            if (beginSiteEntity == null || CollectionUtils.isEmpty(batchListWithBeginSite)){
                continue;
            }
            LocalDateTime requiredOutTime = null;
            if (StrUtil.isNotBlank(beginSiteEntity.getOutTime())){
                requiredOutTime = LocalDateTime.parse(String.format("%sT%s", deliveryDateStr, beginSiteEntity.getOutTime()));
            }


            for (DeliveryBatchEntity deliveryBatchEntity : batchListWithBeginSite) {
                StoreOutTimeMonitorPojo storeOutTimeMonitorPojo = new StoreOutTimeMonitorPojo();
                storeOutTimeMonitorPojo.setDeliveryTime(deliveryDateStr);
                storeOutTimeMonitorPojo.setStoreName(beginSiteEntity.getName());
                storeOutTimeMonitorPojo.setPathCode(deliveryBatchEntity.getPathCode());

                List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
                //排查默认起始城配仓点位
                storeOutTimeMonitorPojo.setDeliverySiteCount(CollectionUtils.isEmpty(deliverySiteList) ? 0 : deliverySiteList.size() - 1);

                DriverEntity driverEntity = deliveryBatchEntity.getDriverEntity();
                String driverName = driverEntity == null || driverEntity.getId() == null ? "" : driverEntity.getName();
                String driverPhone = driverEntity == null || driverEntity.getId() == null ? "" : driverEntity.getPhone();
                storeOutTimeMonitorPojo.setDriverName(driverName);
                storeOutTimeMonitorPojo.setDriverPhone(driverPhone);

                DeliverySiteEntity beginDeliverySite = deliveryBatchEntity.getBeginDeliverySite();
                //是否出仓标识 未出仓默认当前时间
                boolean signOutFlag = beginDeliverySite != null && beginDeliverySite.getSignOutTime() != null;
                if (signOutFlag){
                    //已出仓取出仓时间
                    signOutTime = beginDeliverySite.getSignOutTime();
                }
                storeOutTimeMonitorPojo.setOutTime(signOutFlag ? outTimeFormatter.format(beginDeliverySite.getSignOutTime()) : "");
                storeOutTimeMonitorPojo.setOutTimeRule(StringUtils.isNotBlank(beginSiteEntity.getOutTime()) ?
                        currentDateStr + " "+beginSiteEntity.getOutTime() : null);
                storeOutTimeMonitorPojo.setOutFlag(requiredOutTime == null ? "" : signOutTime.isAfter(requiredOutTime) ? "超时" : "未超时");
                Integer pickShortCount = batchPickMap.get(deliveryBatchEntity.getId());
                storeOutTimeMonitorPojo.setPickShortCount(pickShortCount == null ? 0 : pickShortCount);
                storeOutTimeMonitorPojoList.add(storeOutTimeMonitorPojo);
            }
        }
        return storeOutTimeMonitorPojoList;
    }

    private HttpServletResponse getExcelHttpResponse(String fileName, HttpServletResponse response) {

        String encodeFileName;
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        try {
            encodeFileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            encodeFileName = fileName;
        }
        response.setContentType("application/vnd.ms-excel; charset=utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + encodeFileName);
        return response;
    }

    private List<PathProductPojo> getPathProductData(List<DeliveryBatchEntity> deliveryBatchList) {
        if (CollectionUtils.isEmpty(deliveryBatchList)){
            return Collections.emptyList();
        }
        //全量商品路线数据
        List<PathProductPojo> pathProductPojoList = new ArrayList<>();
        //普通品路线数据
        List<PathProductPojo> commonPathProductPojoList = new ArrayList<>();
        //查询SKU信息
        Set<String> skus = deliveryBatchList.stream()
                .map(DeliveryBatchEntity::getDeliverySiteList)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DeliverySiteEntity::getSiteDistOrders)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DistOrderEntity::getDistItems)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(DistItemVO::getOutItemId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchList) {
            List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
            if (CollectionUtils.isEmpty(deliverySiteList)){
                continue;
            }
            List<DistOrderEntity> distOrderEntityList = deliverySiteList.stream().map(DeliverySiteEntity::getSiteDistOrders).flatMap(Collection::stream).collect(Collectors.toList());

            //创建普通订单商品的SKU Excel数据处理
            List<PathProductPojo> commonSkuExcelData = this.createCommonSkuExcelData(deliveryBatchEntity.getPathCode(), distOrderEntityList);
            //外单品 Excel数据处理
            List<PathProductPojo> outSkuExcelData = this.createOutSkuExcelData(deliveryBatchEntity.getPathCode(), distOrderEntityList);

            commonPathProductPojoList.addAll(commonSkuExcelData);
            pathProductPojoList.addAll(outSkuExcelData);
        }
        //获取sku信息
        List<SkuDTO> skuDTOList = wmsQueryFacade.batchQueryBySkus(new ArrayList<>(skus));
        Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getSku, Function.identity(), (oldData, newData) -> newData));
        for (PathProductPojo pathProductPojo : commonPathProductPojoList) {
            SkuDTO skuDTO = skuDTOMap.get(pathProductPojo.getOutItemId());
            if (skuDTO == null){
                continue;
            }
            pathProductPojo.setOutItemName(pathProductPojo.getOutItemName() + getExtTypeStr(skuDTO.getExtType()));
            if(skuDTO.getType() != null){
                AgentTypeEnum agentTypeEnum = AgentTypeEnum.getByType(skuDTO.getType());
                String productBelong = agentTypeEnum != null ? agentTypeEnum.getContent() : null;
                productBelong = StringUtil.isBlank(skuDTO.getNameRemakes()) ? productBelong : productBelong + "-" + skuDTO.getNameRemakes();
                pathProductPojo.setProductBelong(productBelong);
            }
        }
        pathProductPojoList.addAll(commonPathProductPojoList);
        //按路线排序
        pathProductPojoList = pathProductPojoList.stream().sorted(Comparator.comparing(PathProductPojo::getPathCode)).collect(Collectors.toList());
        return pathProductPojoList;
    }

    private List<PathProductPojo> createOutSkuExcelData(String pathCode, List<DistOrderEntity> distOrderEntityList) {
        List<PathProductPojo> pathProductPojoList = new ArrayList<>();
        if(CollectionUtils.isEmpty(distOrderEntityList)){
            return pathProductPojoList;
        }
        //获取外单订单信息，根据租户分组
        Map<String, List<DistOrderEntity>> tenantId2OrderMap = distOrderEntityList.stream()
                .filter(distOrder -> DistOrderSourceEnum.OUTER_CITY == distOrder.getSource())
                .collect(Collectors.groupingBy(distOrder -> distOrder.getDistClientVO().getOutTenantId()));

        for (String tenantId : tenantId2OrderMap.keySet()) {
            //按照租户分组SKU
            List<DistOrderEntity> distOrderEntities = tenantId2OrderMap.get(tenantId);
            if(CollectionUtils.isEmpty(distOrderEntities)){
                continue;
            }
            Map<String, List<DistItemVO>> sku2ItemsMap = distOrderEntities.stream()
                    .map(DistOrderEntity::getDistItems)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .collect(Collectors.groupingBy(DistItemVO::getOutItemId));
            for (String sku : sku2ItemsMap.keySet()) {
                List<DistItemVO> distItems = sku2ItemsMap.get(sku);
                if(CollectionUtils.isEmpty(distItems)){
                    continue;
                }
                PathProductPojo pathProductPojo = new PathProductPojo();
                pathProductPojo.setPathCode(pathCode == null ? "未排路线" : pathCode);
                DistItemVO distItem = distItems.get(0);
                pathProductPojo.setOutItemId(sku);
                pathProductPojo.setOutItemName(distItem.getOutItemName());
                pathProductPojo.setSpecification(distItem.getSpecification());
                pathProductPojo.setUnit(distItem.getUnit());
                pathProductPojo.setTemperature(distItem.getTemperatureEnum().getName());
                pathProductPojo.setType("-");
                pathProductPojo.setQuantity(distItems.stream().mapToInt(DistItemVO::getQuantity).sum());
                pathProductPojo.setProductBelong("外单");
                pathProductPojoList.add(pathProductPojo);
            }
        }

        return pathProductPojoList;
    }

    /**
     * 普通商品Excel数据处理
     * @param pathCode 路线
     * @param distOrderEntityList 订单
     * @return 结果
     */
    private List<PathProductPojo> createCommonSkuExcelData(String pathCode, List<DistOrderEntity> distOrderEntityList) {
        if (CollectionUtils.isEmpty(distOrderEntityList)){
            return Collections.emptyList();
        }
        //过滤外单、回收数据
        List<DistItemVO> pathDistItems = distOrderEntityList.stream()
                .filter(distOrder -> DistOrderSourceEnum.OUTER_CITY != distOrder.getSource())
                .map(DistOrderEntity::getDistItems)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .filter(e -> Objects.equals(DistItemDeliveryTypeEnum.DELIVERY, e.getItemDeliveryTypeEnum()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pathDistItems)){
            return Collections.emptyList();
        }
        //单品的数据 根据sku汇总统计
        Map<String, List<DistItemVO>> singleSkuItemsMap = pathDistItems.stream().collect(Collectors.groupingBy(DistItemVO::getOutItemId));

        List<PathProductPojo> pathProductPojoList = new ArrayList<>();
        for (Map.Entry<String, List<DistItemVO>> skuItemEntry : singleSkuItemsMap.entrySet()) {
            PathProductPojo pathProductPojo = new PathProductPojo();
            pathProductPojo.setPathCode(pathCode == null ? "未排路线" : pathCode);
            String outItemId = skuItemEntry.getKey();
            List<DistItemVO> distItems = skuItemEntry.getValue();
            if (CollectionUtils.isEmpty(distItems)){
                continue;
            }
            DistItemVO distItem = distItems.get(0);
            pathProductPojo.setOutItemId(outItemId);
            pathProductPojo.setOutItemName(distItem.getOutItemName());
            pathProductPojo.setSpecification(distItem.getSpecification());
            pathProductPojo.setUnit(distItem.getUnit());
            pathProductPojo.setTemperature(distItem.getTemperatureEnum().getName());
            pathProductPojo.setType(Objects.equals(DistItemTypeEnum.FRUIT, distItem.getItemTypeEnum()) ? "鲜果":"非鲜果");
            pathProductPojo.setQuantity(distItems.stream().mapToInt(DistItemVO::getQuantity).sum());
            pathProductPojoList.add(pathProductPojo);
        }

        return pathProductPojoList;
    }

    private String getExtTypeStr(Integer extType) {
        if (Objects.equals(1, extType)) {
            return "（活动）";
        } else if (Objects.equals(2, extType)) {
            return "（临保）";
        }
        return "";
    }


    @Override
    public List<DeliverySiteDTO> queryDeliveryAgeing(DeliveryPathQuery deliveryPathQuery) {
        //查询城配仓对应的点位
        SiteEntity siteEntity = siteRepository.query(SiteQuery.builder()
                .outBusinessNo(deliveryPathQuery.getStoreNo().toString())
                .type(TmsSiteTypeEnum.STORE.getCode()).build());
        if(siteEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,deliveryPathQuery.getStoreNo());
        }
        LocalDateTime deliveryTime = deliveryPathQuery.getDeliveryTime().atStartOfDay();
        //根据类型、开始点位和配送时间获取批次信息
        List<DeliveryBatchEntity> deliveryBatchEntities = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .deliveryBatchType(deliveryPathQuery.getDeliveryBatchType())
                .beginSiteId(siteEntity.getId())
                .deliveryTime(deliveryTime).build());
        List<Long> batchIds = deliveryBatchEntities.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(batchIds)){
            return Collections.emptyList();
        }
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteDomainService.queryDetail(batchIds, deliveryTime, siteEntity.getId());

        return deliverySiteEntities.stream().map(DeliverySiteDTOConverter::entity2Dto).collect(Collectors.toList());
    }

    @Override
    public DeliveryBatchDTO queryDetail(Long deliveryBatchId) {
        if (deliveryBatchId == null){
            return null;
        }
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.getBatchDetail(deliveryBatchId);
        return DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliveryBatchEntity);
    }

    @Override
    public Long downloadBatchRecord(DeliveryBatchQuery deliveryBatchQuery) {
        if (deliveryBatchQuery.getBeginDeliveryTime() == null || deliveryBatchQuery.getEndDeliveryTime() == null){
            throw new TmsRuntimeException("请选择履约时间");
        }

        if (deliveryBatchQuery.getEndDeliveryTime() != null) {
            LocalDateTime endDeliveryTime = LocalDateTime.of(deliveryBatchQuery.getEndDeliveryTime().toLocalDate(), Constants.localTimeMaxTime);
            deliveryBatchQuery.setEndDeliveryTime(endDeliveryTime);
        }
        if (deliveryBatchQuery.getEndTime() != null) {
            LocalDateTime endTime = LocalDateTime.of(deliveryBatchQuery.getEndTime().toLocalDate(), Constants.localTimeMaxTime);
            deliveryBatchQuery.setEndTime(endTime);
        }

        if(CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchTypeList())){
            deliveryBatchQuery.setDeliveryBatchTypeList(DeliveryBatchTypeEnum.getAllTrunkType());
        }

        List<DeliveryBatchEntity> deliveryBatchEntityList =  deliveryBatchRepository.queryWithSiteWithBase(deliveryBatchQuery);
        if(CollectionUtils.isEmpty(deliveryBatchEntityList)){
            throw new TmsRuntimeException(ErrorCodeEnum.IMPORT_DATA_EMPTY);
        }

        deliveryBatchEntityList.sort(Comparator.comparing(DeliveryBatchEntity::getDeliveryTime,Comparator.nullsLast(LocalDateTime::compareTo).reversed())
                .thenComparing(DeliveryBatchEntity::getType,Comparator.nullsLast(Integer::compareTo))
                .thenComparing(DeliveryBatchEntity::getPathName,Comparator.nullsLast(String::compareTo))
        );

        List<TrunkBatchPojo> data = deliveryBatchEntityList.stream().map(TrunkBatchPojoConverter::entity2Pojo).collect(Collectors.toList());

        String fileName = "调度单" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls";
        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        recordDTO.setBizType(FileDownloadTypeEnums.XianMu.BATCH_RECORD_IMPORT.getValue());
        recordDTO.setTenantId(Constants.Tenant.XM_TENANT_ID);
        recordDTO.setUserId(Long.valueOf(Optional.ofNullable(UserInfoHolder.getUser()).orElse(new UserBase()).getBizUserId()));
        recordDTO.setFileName(fileName);
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
        return downloadCenterService.asyncUploadAndRecordResult(recordDTO, data, (file, q) -> {
            ExcelWriter excelWriter = EasyExcelFactory.write(file, TrunkBatchPojo.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet sheet = EasyExcelFactory.writerSheet("调度单").build();
            excelWriter.write(data, sheet);
            excelWriter.finish();
        });
    }

    @Override
    public Long downloadPrePath(String storeNo) {
        TmsAssert.isTrue(StringUtils.isNotBlank(storeNo),ErrorCodeEnum.PARAM_NOT_NULL,"storeNo不能为空");
        LocalDate deliveryStartDate = LocalDate.now().minusDays(30);
        LocalDate deliveryEndDate = LocalDate.now();

        //开始点位
        SiteEntity beginSiteEntity = siteRepository.query(SiteQuery.builder()
                .outBusinessNo(storeNo)
                .type(TmsSiteTypeEnum.STORE.getCode()).build());
        if(beginSiteEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,storeNo);
        }
        //获取批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .beginSiteId(beginSiteEntity.getId())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .beginDeliveryTime(deliveryStartDate.atStartOfDay())
                .endDeliveryTime(LocalDateTime.of(deliveryEndDate, Constants.localTimeMaxTime))
                .build());
        if (CollectionUtils.isEmpty(deliveryBatchEntityList)){
            throw new TmsRuntimeException(ErrorCodeEnum.NO_BATCH_ERROR);
        }

        List<Long> batchIdList = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());

        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchIdList(batchIdList).build());
        if (CollectionUtils.isEmpty(deliverySiteEntities)){
            throw new TmsRuntimeException(ErrorCodeEnum.NO_BATCH_ERROR);
        }
        Map<Long, List<DeliverySiteEntity>> siteCountMap = deliverySiteEntities.stream()
                .filter(deliverySiteEntity -> !Objects.equals(deliverySiteEntity.getSiteId(),beginSiteEntity.getId()))
                .collect(Collectors.groupingBy(DeliverySiteEntity::getSiteId));
        List<Long> siteIdList = new ArrayList<>(siteCountMap.keySet());


        //查询店铺信息
        Map<Long,String> siteIdShopMap = distOrderRepository.querySiteShopName(DistOrderQuery.builder()
                .beginSiteId(beginSiteEntity.getId())
                .endSiteIds(siteIdList)
                .build()
        );

        //查询地址信息
        List<SiteEntity> siteEntities = siteRepository.queryPrimaryIdList(siteIdList);
        Map<Long, SiteEntity> siteIdMap = siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));

        //路段信息
        List<TmsPathSectionEntity> tmsPathSectionEntities = pathSectionRepository.queryList(PathSectionQuery.builder()
                .beginSiteId(beginSiteEntity.getId())
                .endSiteIds(siteIdList)
                .type(PathSectionEnums.Type.CITY.getValue())
                .build()
        );

        Map<Long, List<TmsPathSectionEntity>> sitePathMap = tmsPathSectionEntities.stream().collect(Collectors.groupingBy(TmsPathSectionEntity::getEndSiteId));

        List<Long> pathIdList = tmsPathSectionEntities.stream().map(TmsPathSectionEntity::getPathId).collect(Collectors.toList());
        //根据id查询路线
        List<TmsPathEntity> pathEntities = pathRepository.queryListByIdList(pathIdList);
        Map<Long, TmsPathEntity> pathIdPathMap = pathEntities.stream().collect(Collectors.toMap(TmsPathEntity::getPathId, Function.identity()));

        List<DeliveryPrePathPojo> preList = new ArrayList<>();
        for (Long siteId : siteIdList) {
            if(siteCountMap.get(siteId).size() <= 1){
                continue;
            }
            if(siteIdMap.get(siteId) == null){
                continue;
            }
            DeliveryPrePathPojo deliveryPrePathPojo = new DeliveryPrePathPojo();
            deliveryPrePathPojo.setStoreNo(storeNo);
            deliveryPrePathPojo.setSiteId(siteId);
            deliveryPrePathPojo.setShopName(siteIdShopMap.get(siteId));
            deliveryPrePathPojo.setPhone(siteIdMap.get(siteId).getPhone());
            deliveryPrePathPojo.setAddress(siteIdMap.get(siteId).getFullAddress());
            deliveryPrePathPojo.setTimes(siteCountMap.get(siteId).size());
            List<TmsPathSectionEntity> pathSectionList = sitePathMap.get(siteId);
            TmsPathEntity tmsPathEntity = null;
            if(!CollectionUtils.isEmpty(pathSectionList)){
                TmsPathSectionEntity tmsPathSectionEntity = pathSectionList.get(0);
                tmsPathEntity = pathIdPathMap.get(tmsPathSectionEntity.getPathId());
            }
            if(tmsPathEntity != null){
                deliveryPrePathPojo.setPrePath(tmsPathEntity.getPathCode());
            }else{
                deliveryPrePathPojo.setPrePath("");
            }

            preList.add(deliveryPrePathPojo);
        }

        List<DeliveryPrePathPojo> data = preList.stream().sorted(Comparator.comparing(DeliveryPrePathPojo::getTimes).reversed()).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(data)){
            throw new TmsRuntimeException(ErrorCodeEnum.IMPORT_DATA_EMPTY);
        }

        String fileName = "配送名单" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls";
        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        recordDTO.setBizType(FileDownloadTypeEnums.XianMu.PRE_PATH_IMPORT.getValue());
        recordDTO.setTenantId(Constants.Tenant.XM_TENANT_ID);
        recordDTO.setUserId(Long.valueOf(Optional.ofNullable(UserInfoHolder.getUser()).orElse(new UserBase()).getBizUserId()));
        recordDTO.setFileName(fileName);
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
        return downloadCenterService.asyncUploadAndRecordResult(recordDTO, data, (file, q) -> {
            ExcelWriter excelWriter = EasyExcelFactory.write(file, DeliveryPrePathPojo.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet sheet = EasyExcelFactory.writerSheet("配送名单").build();
            excelWriter.write(data, sheet);
            excelWriter.finish();
        });
    }

    @Override
    public Long downloadPath(String storeNo, LocalDate time) {
        TmsAssert.notNull(time,ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        TmsAssert.notEmpty(storeNo,ErrorCodeEnum.PARAM_NOT_NULL, "storeNo");

        SiteEntity siteEntity = siteRepository.siteDetail(Long.parseLong(storeNo), TmsSiteTypeEnum.STORE.getCode());
        if(siteEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,storeNo);
        }
        LocalDateTime deliveryTime = time.atStartOfDay();
        //查询批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryListWithDriver(DeliveryBatchQuery.builder()
                .deliveryTime(deliveryTime)
                .beginSiteId(siteEntity.getId())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .build());
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            //根据批次id获取运输线路
            deliveryBatchEntity.setDeliverySiteList(deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(deliveryBatchEntity.getId()).build()));
            List<DeliverySiteEntity> excludeBeginDeliverySiteList = deliveryBatchEntity.getExcludeBeginDeliverySiteList();
            deliveryBatchEntity.setDeliverySiteList(excludeBeginDeliverySiteList);
            //获取运输明细
            List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(
                    DeliveryOrderQuery.builder().batchId(deliveryBatchEntity.getId()).build());

            if(CollectionUtils.isEmpty(deliveryOrderEntities)){
                continue;
            }
            List<Long> distOrderList = deliveryOrderEntities.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
            List<DistOrderEntity> distOrderEntities = distOrderRepository.queryListWithItem(DistOrderQuery.builder().distIdList(distOrderList).build());

            Map<Long, List<DistOrderEntity>> endSiteDistOrdersMap = distOrderEntities.stream().collect(Collectors.groupingBy(e -> e.getEndSite().getId()));
            List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
            List<Long> siteIdList = deliverySiteList.stream().map(e -> e.getSiteEntity().getId()).collect(Collectors.toList());
            Map<Long, List<SiteEntity>> siteListMap = siteRepository.queryPrimaryIdList(siteIdList).stream().collect(Collectors.groupingBy(SiteEntity::getId));
            for (DeliverySiteEntity deliverySiteEntity : deliverySiteList) {
                deliverySiteEntity.setSiteEntity(siteListMap.get(deliverySiteEntity.getSiteId()).get(0));
                deliverySiteEntity.setSiteDistOrders(endSiteDistOrdersMap.get(deliverySiteEntity.getSiteId()));
            }
        }

        //按照路线编号排序
        ArrayList<DeliveryBatchEntity> deliveryBatchEntities = new ArrayList<>();
        List<DeliveryBatchEntity> beginBatchList = deliveryBatchEntityList.stream().filter(batch -> StringUtils.isBlank(batch.getPathCode())).collect(Collectors.toList());
        List<DeliveryBatchEntity> batchList = deliveryBatchEntityList.stream().filter(batch -> StringUtils.isNotBlank(batch.getPathCode())).collect(Collectors.toList());
        List<DeliveryBatchEntity> sortBatch = batchList.stream().sorted(Comparator.comparing(DeliveryBatchEntity::getPathCode)).collect(Collectors.toList());

        deliveryBatchEntities.addAll(beginBatchList);
        deliveryBatchEntities.addAll(sortBatch);
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntities) {
            if(CollectionUtils.isEmpty(deliveryBatchEntity.getDeliverySiteList())){
                continue;
            }
            //过滤因为拦截导致的点位没有订单的问题
            List<DeliverySiteEntity> deliverySiteEntities = deliveryBatchEntity.getDeliverySiteList().stream()
                    .filter(deliverySiteEntity -> CollectionUtils.isNotEmpty(deliverySiteEntity.getSiteDistOrders()))
                    .collect(Collectors.toList());
            deliveryBatchEntity.setDeliverySiteList(deliverySiteEntities);
        }

        if(CollectionUtils.isEmpty(deliveryBatchEntityList)){
            throw new TmsRuntimeException(ErrorCodeEnum.IMPORT_DATA_EMPTY);
        }

        String fileName = siteEntity.getName()+ "路线" + deliveryTime.format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT)) + ".xls";
        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        recordDTO.setBizType(FileDownloadTypeEnums.XianMu.PATH_IMPORT.getValue());
        recordDTO.setTenantId(Constants.Tenant.XM_TENANT_ID);
        recordDTO.setUserId(Long.valueOf(Optional.ofNullable(UserInfoHolder.getUser()).orElse(new UserBase()).getBizUserId()));
        recordDTO.setFileName(fileName);
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
        return downloadCenterService.asyncUploadAndRecordResult(recordDTO, deliveryBatchEntities, (q) -> {
            Workbook workbook =  new HSSFWorkbook();
            //数据填充
            this.createQuantityData(workbook,deliveryBatchEntities,deliveryTime);
            //每条路线配送数据
            this.createPathData(workbook,deliveryBatchEntities,deliveryTime);

            return workbook;
        });
    }

    @Override
    public Long downloadPathProduct(String storeNo, LocalDate deliveryTime) {
        TmsAssert.notNull(deliveryTime,ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        TmsAssert.notEmpty(storeNo,ErrorCodeEnum.PARAM_NOT_NULL, "storeNo");

        SiteEntity beginSiteEntity = siteRepository.query(SiteQuery.builder().outBusinessNo(storeNo).type(TmsSiteTypeEnum.STORE.getCode()).build());
        if(beginSiteEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,storeNo);
        }
        //查询批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .deliveryTime(deliveryTime.atStartOfDay())
                .beginSiteId(beginSiteEntity.getId())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .build());
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            //获取运输明细
            List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().batchId(deliveryBatchEntity.getId()).build());
            if(CollectionUtils.isEmpty(deliveryOrderEntities)){
                continue;
            }
            List<Long> distOrderList = deliveryOrderEntities.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
            List<DistOrderEntity> distOrderEntities = distOrderRepository.queryListWithItem(DistOrderQuery.builder().distIdList(distOrderList).build());
            if(CollectionUtils.isEmpty(distOrderEntities)){
                continue;
            }
            //根据批次id获取运输线路
            deliveryBatchEntity.setDeliverySiteList(deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(deliveryBatchEntity.getId()).build()));
            List<DeliverySiteEntity> excludeBeginDeliverySiteList = deliveryBatchEntity.getExcludeBeginDeliverySiteList();
            deliveryBatchEntity.setDeliverySiteList(excludeBeginDeliverySiteList);

            //过滤掉完成排线前拦截的数据无需处理
            List<DistOrderEntity> validDistOrderEntities = distOrderEntities.stream().filter(e -> !DistOrderStatusEnum.CANCEL_BEFORE_WIRED.equals(e.getStatus())).collect(Collectors.toList());
            Map<Long, List<DistOrderEntity>> endSiteDistOrdersMap = validDistOrderEntities.stream().collect(Collectors.groupingBy(e -> e.getEndSite().getId()));
            //处理每个运输点位的委托单
            for (DeliverySiteEntity deliverySiteEntity : deliveryBatchEntity.getDeliverySiteList()) {
                List<DistOrderEntity> distOrdersWithEndSite = endSiteDistOrdersMap.get(deliverySiteEntity.getSiteId());
                deliverySiteEntity.setSiteDistOrders(Optional.ofNullable(distOrdersWithEndSite).orElse(Collections.emptyList()));
            }
        }
        List<PathProductPojo> pathProductList = this.getPathProductData(deliveryBatchEntityList);

        if(CollectionUtils.isEmpty(pathProductList)){
            throw new TmsRuntimeException(ErrorCodeEnum.IMPORT_DATA_EMPTY);
        }

        String fileName = String.format("%s下载路线商品.xls", deliveryTime.format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT)) + beginSiteEntity.getName());
        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        recordDTO.setBizType(FileDownloadTypeEnums.XianMu.PATH_PRODUCT_IMPORT.getValue());
        recordDTO.setTenantId(Constants.Tenant.XM_TENANT_ID);
        recordDTO.setUserId(Long.valueOf(Optional.ofNullable(UserInfoHolder.getUser()).orElse(new UserBase()).getBizUserId()));
        recordDTO.setFileName(fileName);
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
        return downloadCenterService.asyncUploadAndRecordResult(recordDTO, pathProductList, (file, q) -> {
            ExcelWriter excelWriter = EasyExcelFactory.write(file, PathProductPojo.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, String.format("%s汇总", beginSiteEntity.getName())).build();
            excelWriter.write(pathProductList, writeSheet);

            Map<String, List<PathProductPojo>> pathProductMap = pathProductList.stream().collect(Collectors.groupingBy(PathProductPojo::getPathCode));
            List<String> paths = pathProductList.stream().map(PathProductPojo::getPathCode).distinct().sorted().collect(Collectors.toList());
            for (int i = 0; i < paths.size(); i++) {
                String path = paths.get(i);
                List<PathProductPojo> pathProductPojoData = pathProductMap.get(path);
                String sheetName = path.contains("路线") ? path : String.format("路线%s", path);
                WriteSheet otherWriteSheet = EasyExcelFactory.writerSheet(i + 1, sheetName).build();
                excelWriter.write(pathProductPojoData, otherWriteSheet);
            }
            excelWriter.finish();
        });
    }

    @Override
    public List<DeliveryBatchDTO> queryTrunkDeliveryDetail(DeliveryBatchQuery deliveryBatchQuery) {
        deliveryBatchQuery.setDeliveryBatchTypeList(DeliveryBatchTypeEnum.getAllTrunkType());
        //查询批次信息（司机、车辆、承运商，运输点位）
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryWithSiteWithBase(deliveryBatchQuery);
        //设置总数量、总体积、总重量
        deliveryBatchDomainService.installTotalInfo(deliveryBatchEntityList);

        return deliveryBatchEntityList.stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList());
    }

    @Override
    public Integer queryStoreNoById(Long batchId) {
        if(batchId == null){
            throw new TmsRuntimeException("queryStoreNoById batchId is null");
        }
        DeliveryBatchEntity batchEntity = deliveryBatchRepository.query(batchId);
        if(batchEntity == null){
            throw new TmsRuntimeException("请求批次不存在");
        }
        Long beginSiteId = batchEntity.getBeginSiteId();
        SiteEntity beginSiteEntity = siteRepository.query(beginSiteId);
        if(beginSiteEntity == null){
            throw new TmsRuntimeException("批次对应的起点不存在");
        }
        if(!Objects.equals(beginSiteEntity.getType(), SiteTypeEnum.store.getCode())){
            throw new TmsRuntimeException("起点非城配仓点位");
        }
        return Integer.parseInt(beginSiteEntity.getOutBusinessNo());
    }

    @Override
    public void backDoorUpdateBatchIntelligentDistance(Integer storeNo, LocalDate deliveryTime, List<Long> batchIds) {
        DeliveryBatchQuery deliveryBatchQuery = new DeliveryBatchQuery();
        if(storeNo != null && deliveryTime != null){
            SiteEntity site = siteRepository.query(SiteQuery.builder()
                    .type(SiteTypeEnum.store.getCode())
                    .outBusinessNo(String.valueOf(storeNo))
                    .build());
            if(site == null){
                throw new TmsRuntimeException("无效配送仓编号");
            }
            deliveryBatchQuery.setDeliveryTime(deliveryTime.atStartOfDay());
            deliveryBatchQuery.setBeginSiteId(site.getId());
            deliveryBatchQuery.setType(DeliveryBatchTypeEnum.city.getCode());
        }
        if(!CollectionUtils.isEmpty(batchIds)){
            deliveryBatchQuery.setBatchIds(batchIds);
            deliveryBatchQuery.setType(DeliveryBatchTypeEnum.city.getCode());
        }

        //根据日期范围查询批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(deliveryBatchQuery);
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            // 忽略默认路线
            if(Objects.equals(deliveryBatchEntity.getPathId(),Constants.Delivery_Batch.DEF_DELIVERY_PATH_ID)){
                continue;
            }
            List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder()
                    .batchId(deliveryBatchEntity.getId())
                    .build());
            if(CollectionUtils.isEmpty(deliverySiteEntities)){
                continue;
            }
            deliveryBatchEntity.setDeliverySiteList(deliverySiteEntities);
            // 循环处理智能排线次序
            deliverySiteDomainService.intelligentPath(deliveryBatchEntity,false);

            List<DeliverySiteEntity> deliverySiteSecondEntities = deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder()
                    .batchId(deliveryBatchEntity.getId())
                    .build());

            deliveryBatchEntity.setDeliverySiteList(deliverySiteSecondEntities);
            try {
                // 获取城配仓站点信息
                DeliverySiteEntity beginDeliverySite = deliveryBatchEntity.getBeginDeliverySite();

                //计算距离和保存路段
                List<DeliverySiteEntity> sendSiteList = deliveryBatchEntity.getExcludeBeginDeliverySiteList();

                // 过滤出正常配送方式
                sendSiteList = sendSiteList.stream().filter(e -> Objects.equals(e.getSendWay(), DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())).collect(Collectors.toList());

                // 需要按照智能排线排序
                if(!CollectionUtils.isEmpty(sendSiteList)){
                    sendSiteList = sendSiteList.stream().sorted(Comparator.comparing(DeliverySiteEntity::getIntelligenceSequence)).collect(Collectors.toList());
                }

                List<net.xianmu.gaode.support.service.input.WaypointsInput> waypointsInputList = new ArrayList<>();

                net.xianmu.gaode.support.service.input.WaypointsInput beginWaypointInput = new net.xianmu.gaode.support.service.input.WaypointsInput();
                beginWaypointInput.setSiteId(beginDeliverySite.getSiteId());
                beginWaypointInput.setPoi(beginDeliverySite.getSiteEntity().getPoi());
                waypointsInputList.add(beginWaypointInput);

                for (DeliverySiteEntity siteEntity : sendSiteList) {
                    net.xianmu.gaode.support.service.input.WaypointsInput waypointsInput = new net.xianmu.gaode.support.service.input.WaypointsInput();
                    waypointsInput.setSiteId(siteEntity.getSiteId());
                    waypointsInput.setPoi(siteEntity.getSiteEntity().getPoi());
                    waypointsInputList.add(waypointsInput);
                }

                // 保存智能排线公里数、路段信息
                deliveryBatchRepository.calculateDistance(waypointsInputList,deliveryBatchEntity.getId(), DeliverySectionEnums.Type.INTELLIGENCE_PATH, XMDriverRoutPlanStrategyEnum.COMBINATION_STRATEGY);

            } catch (Exception e) {
                log.error("计算智能排线公里数异常", e);
            }

        }
    }
}


