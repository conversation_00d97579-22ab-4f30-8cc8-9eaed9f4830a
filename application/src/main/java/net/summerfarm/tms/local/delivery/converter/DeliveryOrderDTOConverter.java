package net.summerfarm.tms.local.delivery.converter;

import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;

public class DeliveryOrderDTOConverter {

    public static DeliveryOrderDTO vo2Dto(DeliveryOrderEntity deliveryOrderEntity) {
        DeliveryOrderDTO deliveryOrderDTO = new DeliveryOrderDTO();
        deliveryOrderDTO.setStatus(deliveryOrderEntity.getStatus().getCode());
        deliveryOrderDTO.setStatusDesc(deliveryOrderEntity.getStatus().getName());
        deliveryOrderDTO.setId(deliveryOrderEntity.getId());
        deliveryOrderDTO.setDistOrderId(deliveryOrderEntity.getDistOrderId());
        deliveryOrderDTO.setDeliveryBatchId(deliveryOrderEntity.getDeliveryBatchId());
        deliveryOrderDTO.setBeginSiteId(deliveryOrderEntity.getBeginSiteId());
        deliveryOrderDTO.setBeginSiteName(deliveryOrderEntity.getBeginSiteName());
        deliveryOrderDTO.setEndSiteId(deliveryOrderEntity.getEndSiteId());
        deliveryOrderDTO.setEndSiteName(deliveryOrderEntity.getEndSiteName());
        deliveryOrderDTO.setOuterOrderId(deliveryOrderEntity.getOuterOrderId());

        return deliveryOrderDTO;
    }
}
