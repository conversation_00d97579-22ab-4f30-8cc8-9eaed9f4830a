package net.summerfarm.tms.local.delivery.converter;

import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.event.StockEvent;
import net.summerfarm.tms.lack.entity.LackApprovedEntity;
import net.summerfarm.tms.message.out.StockItemInfoMessage;
import net.summerfarm.tms.message.out.StockMessage;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/11/10 16:59<br/>
 *
 * <AUTHOR> />
 */
public class StockEventConverter {

    public static StockEvent LackEntity2Event(LackApprovedEntity lackApprovedEntity, String merchantName) {
        StockMessage stockMessage = new StockMessage();
        stockMessage.setStoreNo(lackApprovedEntity.getStoreNo());
        stockMessage.setTaskType(null);
        stockMessage.setOperationType(20);
        List<StockItemInfoMessage> stockItemInfoMessages = new ArrayList<>();

        StockItemInfoMessage stockItemInfoMessage = new StockItemInfoMessage();
        stockItemInfoMessage.setSku(lackApprovedEntity.getSku());
        stockItemInfoMessage.setWarehouseNo(lackApprovedEntity.getWarehouseNo());
        stockItemInfoMessage.setQuantity(lackApprovedEntity.getStockLackNum());

        stockItemInfoMessages.add(stockItemInfoMessage);
        stockMessage.setSkuInfoList(stockItemInfoMessages);
        stockMessage.setSourceId(lackApprovedEntity.getId().toString());
        stockMessage.setMerchantName(merchantName);

        StockEvent stockEvent = new StockEvent();

        stockEvent.setType(MqConstants.WMSType.TMS_STOCK_IN_STORE);
        stockEvent.setData(stockMessage);

        return stockEvent;
    }

    public static StockEvent distOrderEntity2Event(DistOrderEntity distOrderEntity) {
        List<DistItemVO> distItems = distOrderEntity.getDistItems();
        if(CollectionUtils.isEmpty(distItems)){
            return null;
        }
        List<StockItemInfoMessage> stockItemInfoMessages = new ArrayList<>();
        for (DistItemVO distItem : distItems) {
            StockItemInfoMessage stockItemInfoMessage = new StockItemInfoMessage();
            stockItemInfoMessage.setSku(distItem.getOutItemId());
            stockItemInfoMessage.setQuantity(distItem.getQuantity());

            stockItemInfoMessages.add(stockItemInfoMessage);
        }
        StockMessage stockMessage = new StockMessage();
        stockMessage.setStoreNo(Integer.parseInt(distOrderEntity.getBeginSite().getOutBusinessNo()));
        stockMessage.setTaskType(3);
        stockMessage.setOperationType(19);
        stockMessage.setSkuInfoList(stockItemInfoMessages);
        stockMessage.setSourceId(distOrderEntity.getDistClientVO().getOutOrderId());
        stockMessage.setMerchantName(distOrderEntity.getDistClientVO().getOutClientName());

        StockEvent stockEvent = new StockEvent();

        stockEvent.setType(MqConstants.WMSType.TMS_STOCK_IN_STORE);
        stockEvent.setData(stockMessage);

        return stockEvent;
    }
}
