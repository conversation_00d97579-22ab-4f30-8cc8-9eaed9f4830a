package net.summerfarm.tms.local.base.car;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.car.CarDomainService;
import net.summerfarm.tms.base.car.CarRepository;
import net.summerfarm.tms.base.car.CarService;
import net.summerfarm.tms.base.car.dto.CarDTO;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.driver.DriverDomainService;
import net.summerfarm.tms.base.driver.DriverRepository;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.enums.FileDownloadTypeEnums;
import net.summerfarm.tms.excel.pojo.CarExcelPojo;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.ext.AuthExtService;
import net.summerfarm.tms.query.base.car.CarQuery;
import net.summerfarm.tms.service.common.DownloadCenterService;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/7/15 11:30<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class CarServiceImpl implements CarService {

    @Autowired
    private CarRepository carRepository;
    @Autowired
    private CarDomainService carDomainService;
    @Autowired
    private DriverDomainService driverDomainService;
    @Autowired
    private DriverRepository driverRepository;
    @Resource
    private AuthExtService authExtService;
    @Resource
    private DownloadCenterService downloadCenterService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public TmsResult<Void> save(CarDTO carDTO) {
        CarEntity carEntity = CarDtoConverter.dto2Entity(carDTO);
        carEntity.setAdminId(authExtService.getCurrentUserId());
        carDomainService.create(carEntity);
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public TmsResult<PageInfo<CarDTO>> queryPage(CarQuery carQuery) {
        PageInfo<CarEntity> carEntityPageInfo = carRepository.queryPage(carQuery);
        List<CarEntity> carEntities = carEntityPageInfo.getList();

        List<CarDTO> carDtos = carEntities.stream().map(CarDtoConverter::entity2Dto).collect(Collectors.toList());
        carDtos.forEach(car -> {
            if (car.getAdminId() != null) {
                car.setAdminName(authExtService.getAdminNameById(Long.parseLong(String.valueOf(car.getAdminId()))));
            }
        });
        PageInfo<CarDTO> listPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(carEntityPageInfo, listPageInfo);
        listPageInfo.setList(carDtos);
        return TmsResult.success(listPageInfo);
    }

    @Override
    public TmsResult<CarDTO> getDetail(Long id) {
        //获取车辆信息
        CarEntity detail = carDomainService.getDetail(id);
        //获取关联司机信息
        detail.setDriverEntityList(driverDomainService.getDriverByCarId(detail.getId()));

        return TmsResult.success(CarDtoConverter.entity2Dto(detail));
    }

    @Override
    public TmsResult<List<CarDTO>> getCarByCarNumber(String carNumber) {
        List<CarEntity> carEntities = carRepository.getCarByCarNumber(carNumber);
        return TmsResult.success(carEntities.stream().map(CarDtoConverter::entity2Dto).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public TmsResult<Void> edit(CarDTO carDTO) {
        carDTO.setAdminId(authExtService.getCurrentUserId());
        carDomainService.edit(CarDtoConverter.dto2Entity(carDTO));
        return TmsResult.VOID_SUCCESS;
    }

    @Override
    public Long downloadCarInfo(CarQuery carQuery) {
        List<CarEntity> carEntities = carRepository.queryList(carQuery);
        if (CollectionUtils.isEmpty(carEntities)) {
            throw new TmsRuntimeException(ErrorCodeEnum.IMPORT_DATA_EMPTY);
        }
        Set<Long> carIds = carEntities.stream().map(CarEntity::getId).collect(Collectors.toSet());
        Map<Long, List<DriverEntity>> carDriverMap = driverRepository.getDriverByCarIds(carIds);

        List<CarDTO> carDtos = carEntities.stream().map(CarDtoConverter::entity2Dto).collect(Collectors.toList());
        List<CarExcelPojo> data = carDtos.stream()
                .map(a -> CarDtoConverter.entityToPojo(a, carDriverMap)).collect(Collectors.toList());

        String fileName = "车辆信息" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        recordDTO.setBizType(FileDownloadTypeEnums.XianMu.CAR_INFO_IMPORT.getValue());
        recordDTO.setTenantId(Constants.Tenant.XM_TENANT_ID);
        recordDTO.setUserId(Long.valueOf(Optional.ofNullable(UserInfoHolder.getUser()).orElse(new UserBase()).getBizUserId()));
        recordDTO.setFileName(fileName);
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
        return downloadCenterService.asyncUploadAndRecordResult(recordDTO, data, (file, q) -> {
            ExcelWriter excelWriter = EasyExcelFactory.write(file, CarExcelPojo.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet sheet = EasyExcelFactory.writerSheet("车辆信息").build();
            excelWriter.write(data, sheet);
            excelWriter.finish();
        });
    }

    /**
     * Excel生成
     *
     * @param dataList
     * @param response
     * @param thisFileName
     */
    public void excelCreate(List<CarExcelPojo> dataList, HttpServletResponse response, String thisFileName) throws IOException {
        String date = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = null;
        try {
            fileName = URLEncoder.encode(thisFileName + date, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            thisFileName = "导出异常";
            log.error("xxx处理失败:{}", e.getMessage(), e);
        }
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 这里需要设置不关闭流
        EasyExcel.write(response.getOutputStream(), CarExcelPojo.class).sheet(thisFileName).doWrite(dataList);
    }

}
    