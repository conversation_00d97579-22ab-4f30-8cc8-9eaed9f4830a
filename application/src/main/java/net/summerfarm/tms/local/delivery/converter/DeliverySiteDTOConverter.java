package net.summerfarm.tms.local.delivery.converter;

import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.dto.cheakinpunch.DeliverySiteCheckinPunchDTO;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.local.base.site.SiteDtoConverter;
import net.summerfarm.tms.local.delivery.converter.cheakinpunch.DeliverySiteCheckinPunchDTOConverter;
import net.summerfarm.tms.local.dist.converter.DistOrderConverter;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public class DeliverySiteDTOConverter {

    public static DeliverySiteEntity dto2Entity(DeliverySiteDTO deliverySiteDTO) {
        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
        deliverySiteEntity.setId(deliverySiteDTO.getId());
        deliverySiteEntity.setDeliveryBatchId(deliverySiteDTO.getDeliveryBatchId());
        deliverySiteEntity.setPlanArriveTime(deliverySiteDTO.getPlanArriveTime());
        deliverySiteEntity.setSequence(deliverySiteDTO.getSequence());
        deliverySiteEntity.setSiteName(deliverySiteDTO.getSiteName());
        deliverySiteEntity.setSiteId(deliverySiteDTO.getSiteId());
        deliverySiteEntity.setType(deliverySiteDTO.getType());
        deliverySiteEntity.setSiteType(deliverySiteDTO.getSiteType());
        deliverySiteEntity.setSignInTime(deliverySiteDTO.getSignInTime());
        deliverySiteEntity.setSignInDiffMinute(deliverySiteDTO.getSignInDiffMinute());
        deliverySiteEntity.setSignInDistance(deliverySiteDTO.getSignInDistance());
        deliverySiteEntity.setSignInPoi(deliverySiteDTO.getSignInPoi());
        deliverySiteEntity.setSignInDiffKm(deliverySiteDTO.getSignInDiffKm());
        deliverySiteEntity.setSignInRemark(deliverySiteDTO.getSignInRemark());
        deliverySiteEntity.setOuterClientId(deliverySiteDTO.getOuterClientId());
        deliverySiteEntity.setOuterClientName(deliverySiteDTO.getOuterClientName());
        deliverySiteEntity.setOutReason(deliverySiteDTO.getOutReason());
        deliverySiteEntity.setOutDistance(deliverySiteDTO.getOutDistance());
        deliverySiteEntity.setSignInPics(deliverySiteDTO.getSignInPic1());
        deliverySiteEntity.setSignInStatus(deliverySiteDTO.getSignInStatus());
        deliverySiteEntity.setSignOutTemperature(deliverySiteDTO.getSignOutTemperature());
        deliverySiteEntity.setSignOutPics(deliverySiteDTO.getSignOutPic1());
        deliverySiteEntity.setVehiclePlatePics(deliverySiteDTO.getVehiclePlatePics());
        deliverySiteEntity.setSealPics(deliverySiteDTO.getSealPics());
        deliverySiteEntity.setSignOutTime(deliverySiteDTO.getSignOutTime());
        deliverySiteEntity.setSignInErrType(deliverySiteDTO.getSignInErrType());
        deliverySiteEntity.setSignOutErrType(deliverySiteDTO.getSignOutErrType());
        deliverySiteEntity.setSignOutStatus(deliverySiteDTO.getSignOutStatus());
        deliverySiteEntity.setSignInAddress(deliverySiteDTO.getSignInAddress());
        deliverySiteEntity.setSignOutPoi(deliverySiteDTO.getSignOutPoi());
        deliverySiteEntity.setSignOutAddress(deliverySiteDTO.getSignOutAddress());
        deliverySiteEntity.setSendWay(deliverySiteDTO.getSendWay());
        deliverySiteEntity.setSignOutRemark(deliverySiteDTO.getSignOutRemark());
        deliverySiteEntity.setSignInSignPic(deliverySiteDTO.getSignInPic2());
        deliverySiteEntity.setSignInProductPic(deliverySiteDTO.getSignInPic3());
        deliverySiteEntity.setOutPic(deliverySiteDTO.getOutPic());
        deliverySiteEntity.setOutReasonType(deliverySiteDTO.getOutReasonType());
        deliverySiteEntity.setRefrigeratePics(deliverySiteDTO.getRefrigeratePics());
        deliverySiteEntity.setFreezePics(deliverySiteDTO.getFreezePics());
        if (deliverySiteDTO.getDeliveryPickDTOS() != null) {
            deliverySiteEntity.setDeliveryPickEntityList(
                    deliverySiteDTO.getDeliveryPickDTOS().stream()
                            .map(DeliveryPickDTOConverter::dto2Entity)
                            .collect(Collectors.toList()));
        }
        if (deliverySiteDTO.getDeliverySiteItemDTOList() != null) {
            deliverySiteEntity.setDeliverySiteItemEntityList(
                    deliverySiteDTO.getDeliverySiteItemDTOList()
                            .stream()
                            .map(DeliverySiteItemDTOConverter::dto2Entity)
                            .collect(Collectors.toList())
            );
        }
        return deliverySiteEntity;
    }

    public static DeliverySiteDTO entity2Dto(DeliverySiteEntity deliverySiteEntity) {
        DeliverySiteDTO deliverySiteDTO = new DeliverySiteDTO();
        if(deliverySiteEntity == null){
            return deliverySiteDTO;
        }
        deliverySiteDTO.setId(deliverySiteEntity.getId());
        deliverySiteDTO.setDeliveryBatchId(deliverySiteEntity.getDeliveryBatchId());
        deliverySiteDTO.setType(deliverySiteEntity.getType());
        deliverySiteDTO.setSequence(deliverySiteEntity.getSequence());
        if(deliverySiteEntity.getStatus() != null){
            deliverySiteDTO.setStatus(deliverySiteEntity.getStatus().getCode());
            deliverySiteDTO.setStatusDesc(deliverySiteEntity.getStatus().getName());
        }
        if(deliverySiteEntity.getSiteEntity() != null){
            deliverySiteDTO.setSiteName(deliverySiteEntity.getSiteEntity().getSiteName() == null ? deliverySiteEntity.getSiteName() : deliverySiteEntity.getSiteEntity().getSiteName());
            deliverySiteDTO.setSiteId(deliverySiteEntity.getSiteEntity().getId());
            deliverySiteDTO.setSiteType(deliverySiteEntity.getSiteEntity().getType());
        }
        deliverySiteDTO.setSiteDTO(SiteDtoConverter.entity2Dto(deliverySiteEntity.getSiteEntity()));
        deliverySiteDTO.setPlanArriveTime(deliverySiteEntity.getPlanArriveTime());
        deliverySiteDTO.setSignInStatus(deliverySiteEntity.getSignInStatus());
        deliverySiteDTO.setSignInTime(deliverySiteEntity.getSignInTime());
        deliverySiteDTO.setSignInDiffMinute(deliverySiteEntity.getSignInDiffMinute());
        deliverySiteDTO.setSignInDiffKm(deliverySiteEntity.getSignInDiffKm());
        deliverySiteDTO.setSignInPic1(deliverySiteEntity.getSignInPics());
//        deliverySiteDTO.setSignInPic2(deliverySiteEntity.getSignInPic2());
        deliverySiteDTO.setSignInRemark(deliverySiteEntity.getSignInRemark());
        deliverySiteDTO.setSignOutTime(deliverySiteEntity.getSignOutTime());
        deliverySiteDTO.setSignOutDiffMinute(deliverySiteEntity.getSignOutDiffMinute());
        deliverySiteDTO.setSignOutDiffKm(deliverySiteEntity.getSignOutDiffKm());
        deliverySiteDTO.setSignOutPic1(deliverySiteEntity.getSignOutPics());
        deliverySiteDTO.setSignInPoi(deliverySiteEntity.getSignInPoi());
        deliverySiteDTO.setSignInDistance(deliverySiteEntity.getSignInDistance());
        deliverySiteDTO.setSignOutRemark(deliverySiteEntity.getSignOutRemark());
        deliverySiteDTO.setDeliveryType(deliverySiteEntity.getDeliveryType());
        deliverySiteDTO.setTotalVolume(deliverySiteEntity.getTotalVolume());
        deliverySiteDTO.setTotalWeight(deliverySiteEntity.getTotalWeight());
        deliverySiteDTO.setTotalPrice(deliverySiteEntity.getTotalPrice());
        deliverySiteDTO.setTimeFrame(deliverySiteEntity.getTimeFrame());
        deliverySiteDTO.setDistance(deliverySiteEntity.getDistance());
        deliverySiteDTO.setOuterClientName(deliverySiteEntity.getOuterClientName());
        deliverySiteDTO.setOuterClientId(deliverySiteEntity.getOuterClientId());
        deliverySiteDTO.setSignInStatus(deliverySiteEntity.getSignInStatus());
        deliverySiteDTO.setSignOutStatus(deliverySiteEntity.getSignOutStatus());
        deliverySiteDTO.setSignInAddress(deliverySiteEntity.getSignInAddress());
        deliverySiteDTO.setSignOutPoi(deliverySiteEntity.getSignOutPoi());
        deliverySiteDTO.setSignOutAddress(deliverySiteEntity.getSignOutAddress());
        deliverySiteDTO.setSendWay(deliverySiteEntity.getSendWay());
        if(deliverySiteEntity.getDeliverySiteInterceptState() != null){
            if(deliverySiteEntity.getDeliverySiteInterceptState().getCode() == 1){
                deliverySiteDTO.setInterceptState(deliverySiteEntity.getDeliverySiteInterceptState().getCode()-1);
            }
            if(deliverySiteEntity.getDeliverySiteInterceptState().getCode() == 2){
                deliverySiteDTO.setInterceptState(deliverySiteEntity.getDeliverySiteInterceptState().getCode() -1);
            }
            if(deliverySiteEntity.getDeliverySiteInterceptState().getCode() == 3){
                deliverySiteDTO.setInterceptState(deliverySiteEntity.getDeliverySiteInterceptState().getCode() -1);
            }
            if(deliverySiteEntity.getDeliverySiteInterceptState().getCode() == 4){
                deliverySiteDTO.setInterceptState(deliverySiteEntity.getDeliverySiteInterceptState().getCode() -1);
            }
        }

        deliverySiteDTO.setOutReason(deliverySiteEntity.getOutReason());
        deliverySiteDTO.setOutDistance(deliverySiteEntity.getOutDistance());
        deliverySiteDTO.setContactStatus(deliverySiteEntity.getContactStatus());
        deliverySiteDTO.setIntelligenceSequence(deliverySiteEntity.getIntelligenceSequence());

        deliverySiteDTO.setSignInErrType(deliverySiteEntity.getSignInErrType());
        deliverySiteDTO.setSignOutErrType(deliverySiteEntity.getSignOutErrType());
        List<DistOrderEntity> siteDistOrders = deliverySiteEntity.getSiteDistOrders();
        if (!CollectionUtils.isEmpty(siteDistOrders)) {
            deliverySiteDTO.setDistOrderDTOS(siteDistOrders.stream().map(DistOrderConverter::entity2Dto).collect(Collectors.toList()));
        }
        if (deliverySiteEntity.getDeliveryPickEntityList() != null) {
            deliverySiteDTO.setDeliveryPickDTOS(
                    deliverySiteEntity.getDeliveryPickEntityList().stream()
                            .map(DeliveryPickDTOConverter::entity2Dto)
                            .collect(Collectors.toList()));
        }
        if (deliverySiteEntity.getDeliverySiteItemEntityList() != null) {
            deliverySiteDTO.setDeliverySiteItemDTOList(
                    deliverySiteEntity.getDeliverySiteItemEntityList().stream()
                            .map(DeliverySiteItemDTOConverter::entity2Dto)
                            .collect(Collectors.toList()));
        }
        if (deliverySiteEntity.getDeliveryBatchEntity() != null) {
            deliverySiteDTO.setDeliveryBatchDTO(DeliveryBatchDTOConverter.DeliveryBatchEntity2Dto(deliverySiteEntity.getDeliveryBatchEntity()));
        }
        if (deliverySiteEntity.getExpenseEntity() != null) {
            deliverySiteDTO.setExpenseDTO(net.summerfarm.tms.local.expense.converter.ExpenseConverter.entity2Dto(deliverySiteEntity.getExpenseEntity()));
        }
        deliverySiteDTO.setSignInPic2(deliverySiteEntity.getSignInSignPic());
        deliverySiteDTO.setSignInPic3(deliverySiteEntity.getSignInProductPic());
        deliverySiteDTO.setOutPic(deliverySiteEntity.getOutPic());
        deliverySiteDTO.setOutReasonType(deliverySiteEntity.getOutReasonType());
        deliverySiteDTO.setPlanOutTime(deliverySiteEntity.getPlanOutTime());
        deliverySiteDTO.setSendRemark(deliverySiteEntity.getSendRemark());
        deliverySiteDTO.setVehiclePlatePics(deliverySiteEntity.getVehiclePlatePics());
        deliverySiteDTO.setSealPics(deliverySiteEntity.getSealPics());
        deliverySiteDTO.setOutPic(deliverySiteEntity.getOutPic());
        deliverySiteDTO.setOutReasonType(deliverySiteEntity.getOutReasonType());
        deliverySiteDTO.setScanCodeFlag(deliverySiteEntity.getScanCodeFlag() );
        deliverySiteDTO.setCheckinPunchFlag(deliverySiteEntity.getCheckinPunchFlag());
        deliverySiteDTO.setPunchRange(deliverySiteEntity.getPunchRange());
        if(deliverySiteEntity.getCheckinPunchEntity() != null){
            DeliverySiteCheckinPunchDTO deliverySiteCheckinPunchDTO = DeliverySiteCheckinPunchDTOConverter.entity2DTO(deliverySiteEntity.getCheckinPunchEntity());
            deliverySiteDTO.setCheckinPunchDTO(deliverySiteCheckinPunchDTO);
        }
        deliverySiteDTO.setOuterBrandName(deliverySiteEntity.getOuterBrandName());
        deliverySiteDTO.setOuterBrandId(deliverySiteEntity.getOuterBrandId());
        deliverySiteDTO.setOrderSourceInfo(deliverySiteEntity.getOrderSourceInfo());
        deliverySiteDTO.setPickLackFlag(deliverySiteEntity.getPickLackFlag());
        deliverySiteDTO.setAdCodeMsgId(deliverySiteEntity.getAdCodeMsgId());

        return deliverySiteDTO;
    }
}
