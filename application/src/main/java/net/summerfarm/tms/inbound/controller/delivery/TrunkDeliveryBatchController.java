package net.summerfarm.tms.inbound.controller.delivery;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.HandleException;
import net.summerfarm.tms.inbound.controller.delivery.vo.trunk.*;
import net.summerfarm.tms.inbound.converter.delivery.trunk.TrunkDeliveryBatchRelateVOConverter;
import net.summerfarm.tms.inbound.converter.delivery.trunk.TrunkDeliveryBatchVOConverter;
import net.summerfarm.tms.path.PathUtil;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryBatchRelateQuery;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 干线 调度单 接口
 */
@RestController
@RequestMapping("/tms-new/trunk-delivery-batch")
@HandleException
public class TrunkDeliveryBatchController {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Resource
    private DistOrderService distOrderService;

    /**
     * 调度单分页查询
     */
    @PostMapping("/query/list")
    public TmsResult<PageInfo<TrunkDeliveryBatchVO>> list(@RequestBody DeliveryBatchQuery deliveryBatchQuery) {
        deliveryBatchQuery.initTrunkType();
        return TmsResult.success(TrunkDeliveryBatchVOConverter.dtoPage2Vo(deliveryBatchService.list(deliveryBatchQuery)));
    }

    /**
     * 调度单查询
     */
    @PostMapping("/query/list-by-site")
    public TmsResult<List<TrunkDeliveryBatchBySiteVO>> listBySite(@RequestBody DeliveryBatchQuery deliveryBatchQuery) {
        //查询  承运单
        TmsResult<DistOrderDTO> tmsResult = distOrderService.query(deliveryBatchQuery.getOutOrderId()
                , DistOrderSourceEnum.getDistOrderSourceByCode(deliveryBatchQuery.getSource()));
        if (!tmsResult.isSuccess() || tmsResult.getData() == null) {
            return TmsResult.success(Lists.newArrayList(Collections.emptyList()));
        }
        DistOrderDTO distOrderDTO = tmsResult.getData();
        List<DeliveryOrderDTO> deliveryOrderDTOList = new ArrayList<>();
        Set<String> hitSet = new HashSet<>();
        Set<Long> batchIdSet = new HashSet<>();
        //根据承运单 找出路段list
        for (DeliveryOrderDTO deliveryOrderDTO : distOrderDTO.getDeliveryOrderList()) {
            String pathName = PathUtil.buildPathName(deliveryOrderDTO.getBeginSiteName(), deliveryOrderDTO.getEndSiteName());
            if (!hitSet.contains(pathName)) {
                hitSet.add(pathName);
                deliveryOrderDTOList.add(deliveryOrderDTO);
            }
            batchIdSet.add(deliveryOrderDTO.getDeliveryBatchId());
        }
        //搜索符合条件的调度单
        DeliveryBatchQuery batchQuery = new DeliveryBatchQuery();
        batchQuery.setDeliveryBatchType(deliveryBatchQuery.getDeliveryBatchType());
        batchQuery.setDeliveryBatchStatusList(Lists.newArrayList(
                DeliveryBatchStatusEnum.TO_BE_PICKED.getCode(),
                DeliveryBatchStatusEnum.IN_DELIVERY.getCode(),
                DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode()
        ));
        batchQuery.setDeliveryTime(distOrderDTO.getExpectBeginTime().toLocalDate().atStartOfDay());
        batchQuery.setDeliveryBatchType(DeliveryBatchTypeSourceEnum.getBySource(distOrderDTO.getSource()));
        //每一个路段 构造一个子列表对象
        List<TrunkDeliveryBatchBySiteVO> trunkDeliveryBatchBySiteVOList = new ArrayList<>();

        List<DeliveryBatchDTO> deliveryBatchList = getDeliveryBatchDTOS(batchQuery);

        for (DeliveryOrderDTO deliveryOrderDTO : deliveryOrderDTOList) {
            //查询匹配的调度单
            batchQuery.setDeliverySiteIds(
                    Lists.newArrayList(deliveryOrderDTO.getBeginSiteId(), deliveryOrderDTO.getEndSiteId()));
            TmsResult<List<DeliveryBatchDTO>> matchDeliveryBatch = deliveryBatchService.matchDeliveryBatch(deliveryBatchList,
                    Lists.newArrayList(deliveryOrderDTO.getBeginSiteId(), deliveryOrderDTO.getEndSiteId()));
            List<TrunkDeliveryBatchVO> trunkDeliveryBatchVOList = matchDeliveryBatch.getData().stream()
                    .map(TrunkDeliveryBatchVOConverter::dto2Vo)
                    .map(trunkDeliveryBatchVO -> {
                        if (batchIdSet.contains(trunkDeliveryBatchVO.getBatchId())) {
                            trunkDeliveryBatchVO.setBindDist(1);
                        }
                        return trunkDeliveryBatchVO;
                    })
                    .collect(Collectors.toList());
            //返回前端的数据
            TrunkDeliveryBatchBySiteVO trunkDeliveryBatchBySiteVO = new TrunkDeliveryBatchBySiteVO();
            trunkDeliveryBatchBySiteVO.setBatchList(trunkDeliveryBatchVOList);
            trunkDeliveryBatchBySiteVO.setBeginSiteId(deliveryOrderDTO.getBeginSiteId());
            trunkDeliveryBatchBySiteVO.setEndSiteId(deliveryOrderDTO.getEndSiteId());
            trunkDeliveryBatchBySiteVO.setDistOrderId(distOrderDTO.getDistId());
            trunkDeliveryBatchBySiteVO.setPathName(
                    PathUtil.buildPathName(deliveryOrderDTO.getBeginSiteName(), deliveryOrderDTO.getEndSiteName()));

            trunkDeliveryBatchBySiteVOList.add(trunkDeliveryBatchBySiteVO);
        }
        return TmsResult.success(trunkDeliveryBatchBySiteVOList);
    }

    private List<DeliveryBatchDTO> getDeliveryBatchDTOS(DeliveryBatchQuery deliveryBatchQuery) {
        deliveryBatchQuery.setPageSize(400);
        TmsResult<PageInfo<DeliveryBatchDTO>> list = deliveryBatchService.list(deliveryBatchQuery);
        if(Objects.isNull(list) || Objects.isNull(list.getData()) || CollectionUtils.isEmpty(list.getData().getList())){
            return Lists.newArrayList();
        }
        return list.getData().getList();
    }

    /**
     * 调度单绑定
     */
    @PostMapping("/upsert/update-by-site")
    public TmsResult<Void> updateBySite(@RequestBody DeliveryOrderSaveByDistBatchCommand deliveryOrderSaveByDistBatchCommand) {
        return distOrderService.deliveryOrderSaveByDist(deliveryOrderSaveByDistBatchCommand.getCommandList());
    }

    /**
     * 调度单详情
     */
    @PostMapping("/query/detail")
    public TmsResult<TrunkDeliveryBatchVO> deliveryBatchDetail(@RequestParam Long deliveryBatchId) {
        //调度单信息
        return TmsResult.success(TrunkDeliveryBatchVOConverter.dto2Vo(deliveryBatchService.deliveryBatchDetail(deliveryBatchId).getData()));
    }

    /**
     * 关闭调度单
     */
    @PostMapping("/upsert/close")
    public TmsResult<Void> deliveryBatchClose(@RequestParam Long deliveryBatchId, @RequestParam String closeReason) {
        return deliveryBatchService.deliveryBatchClose(deliveryBatchId, closeReason);
    }

    /**
     * 保存调度单
     */
    @PostMapping("/upsert/save")
    public TmsResult<Void> deliveryBatchSave(@RequestBody @Validated DeliveryOrderSaveCommond deliveryOrderCommand) {
        return deliveryBatchService.deliveryBatchSave(deliveryOrderCommand);
    }

    /**
     * 更新调度单
     */
    @PostMapping("/upsert/update")
    public TmsResult<Void> deliveryBatchUpdate(@RequestBody @Validated DeliveryOrderUpdateCommand deliveryOrderUpdateCommand) {
        return deliveryBatchService.deliveryBatchUpdate(deliveryOrderUpdateCommand);
    }

    /**
     * 完成配送
     */
    @PostMapping("/upsert/finishDelivery")
    public TmsResult<Void> finishDelivery(@RequestParam Long deliveryBatchId) {
        return deliveryBatchService.finishDelivery(deliveryBatchId);
    }

    /**
     * 承运单智能推荐分页
     */
    @PostMapping("/query/intelligentDistPage")
    public TmsResult<PageInfo<TrunkDeliveryDistOrderDTO>> intelligentDistPage(@RequestBody @Validated DeliveryBatchQuery deliveryBatchQuery) {
        return deliveryBatchService.intelligentDistPage(deliveryBatchQuery);
    }

    /**
     * 更新调度单错误状态
     */
    @PostMapping("/upsert/init-batch-status")
    public TmsResult<Void> initBatchStatus() {
        return deliveryBatchService.initBatchStatus();
    }

    /**
     * 批量完成配送
     */
    @PostMapping("/upsert/batchFinishDelivery")
    public TmsResult<Void> finishDelivery(@RequestBody List<Long> deliveryBatchIds) {
        if (deliveryBatchIds == null || deliveryBatchIds.isEmpty()){
            return TmsResult.VOID_SUCCESS;
        }
        List<Long> sortedDeliveryBatchIds = deliveryBatchIds.stream().sorted().collect(Collectors.toList());
        for (Long deliveryBatchId : sortedDeliveryBatchIds) {
            deliveryBatchService.finishDelivery(deliveryBatchId);
        }
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * Excel导出调度单-接入下载中心
     */
    @PostMapping("/export-async/batch-record")
    public TmsResult<Long> downloadBatchRecord(@RequestBody DeliveryBatchQuery deliveryBatchQuery) {
        return TmsResult.success(deliveryBatchService.downloadBatchRecord(deliveryBatchQuery));
    }

    /**
     * 查询城配干线车次
     */
    @PostMapping("/query/city-trunk-batch")
    public TmsResult<List<CityTrunkBatchVO>> queryCityTrunkBatch(@RequestParam Long batchId) {
        return TmsResult.success(deliveryBatchService.queryCityTrunkBatch(batchId).stream()
                .map(TrunkDeliveryBatchVOConverter::dto2CityTrunkBatchVO).collect(Collectors.toList()));
    }

    /**
     * 调度单关联前置校验
     */
    @PostMapping("/query/relate-validate")
    public TmsResult<Void> deliveryBatchRelateValidate(@RequestBody @Validated DeliveryBatchRelateValidateQuery deliveryBatchRelateValidateQuery) {
        deliveryBatchService.deliveryBatchRelateValidate(deliveryBatchRelateValidateQuery);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 调度单关联查询
     */
    @PostMapping("/query/relate-list")
    public TmsResult<List<TrunkDeliveryBatchRelateVO>> queryRelateBatchList(@RequestBody DeliveryBatchRelateQuery deliveryBatchRelateQuery) {
        return TmsResult.success(deliveryBatchService.queryRelateBatchList(deliveryBatchRelateQuery)
                .stream().map(TrunkDeliveryBatchRelateVOConverter::dto2Vo).collect(Collectors.toList()));
    }

    /**
     * 计算装载率
     */
    @PostMapping("/query/load-ratio-calculate")
    public TmsResult<Void> loadRatioCalculate(@RequestParam Long batchId) {
        deliveryBatchService.executeBatchLoadRatioCalc(batchId);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 批量计算装载率
     */
    @PostMapping("/query/batch-load-ratio-calculate")
    public TmsResult<Void> loadRatioCalculate(@RequestBody List<Long> batchIds) {
        for (Long batchId : batchIds) {
            deliveryBatchService.executeBatchLoadRatioCalc(batchId);
        }
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 干线用车类型
     */
    @PostMapping("/query/trunk-batch-type")
    public TmsResult<List<TrunkBatchTypeVO>> queryTrunkBatchType() {
        DeliveryBatchTypeEnum[] values = DeliveryBatchTypeEnum.values();
        List<TrunkBatchTypeVO> trunkBatchTypes = new ArrayList<>();

        for (DeliveryBatchTypeEnum value : values) {
            if(DeliveryBatchTypeEnum.city == value){
                continue;
            }
            TrunkBatchTypeVO trunkBatchTypeVO = new TrunkBatchTypeVO();
            trunkBatchTypeVO.setType(value.getCode());
            trunkBatchTypeVO.setTypeName(value.getName());
            trunkBatchTypes.add(trunkBatchTypeVO);
        }
        return TmsResult.success(trunkBatchTypes);
    }

    /**
     * 干线用车承运商品类型
     */
    @PostMapping("/query/trunk-batch-carry-type")
    public TmsResult<List<TrunkBatchCarryTypeVO>> queryTrunkBatchCarryType() {
        List<TrunkBatchCarryTypeVO> result = Arrays.stream(DeliveryBatchEnums.CarryType.values()).map(e -> {
            TrunkBatchCarryTypeVO trunkBatchCarryTypeVO = new TrunkBatchCarryTypeVO();
            trunkBatchCarryTypeVO.setCode(e.getValue());
            trunkBatchCarryTypeVO.setDesc(e.getContent());
            return trunkBatchCarryTypeVO;
        }).collect(Collectors.toList());
        return TmsResult.success(result);
    }

}
