package net.summerfarm.tms.exceptions;

public enum ErrorCodeEnum {
    UNKNOWN("UNKNOWN", "未知异常:%s"),
    NOT_FIND("NOT_FIND", "没找到对应的数据:%s"),
    FAILED("FAILED", "%s 执行失败"),
    GRAY_ERROR("GRAY_ERROR", "灰度异常:%s"),
    PARAM_ERROR("IND.PARAM_ERROR", "入参[%s]不符合规范"),
    PARAM_NOT_NULL("PARAM_NOT_NULL", "入参[%s]不能为空"),
    PARAM_ILLEGAL("PARAM_ILLEGAL", "参数非法 %s"),
    BIZ_EXCEPTION("BIZ_EXCEPTION", "业务异常"),
    BATCH_ERROR("BATCH_ERROR", "配送状态不支持关闭"),
    ALL_CATEGORY_PICK_BATCH_CLOSE_ERROR("ALL_CATEGORY_PICK_BATCH_CLOSE_ERROR", "提货用车该配送状态不支持关闭"),
    DIST_STATE_ERROR("DIST_STATE_ERROR", "委托单配送状态不支持:%s"),
    DB_DATA_ERROR("DB_DATA_ERROR", "数据不符合预期:%s"),
    DIST_ORDER_NOT_FIND("DIST_ORDER_NOT_FIND", "无有效委托单"),
    ERROR_STATE_EDIT("ERROR_STATE_EDIT", "当前[%s]状态不支持编辑"),
    REPEAT_SITE("REPEAT_SITE", "站点存在重复点位"),
    TIME_ERROR("TIME_ERROR", "节点预计到达时间不可早于之前节点"),
    OUT_TIME_AFTER_ARRIVE_TIME("OUT_TIME_AFTER_ARRIVE_TIME", "到仓时间不能早于前一个节点的出发时间"),
    SAME_OUT_TIME_AFTER_ARRIVE_TIME("SAME_OUT_TIME_AFTER_ARRIVE_TIME", "同一节点到仓时间不能晚于出发时间"),

    SITE_SIZE_ERROR("SITE_SIZE_ERROR", "运输节点至少需要选择两个以上才能推荐"),
    BATCH_STATE_ERROR("BATCH_STATE_ERROR", "调度单状态已变更为:%s,不支持修改"),
    BATCH_BIND_ERROR("BATCH_BIND_ERROR", "调度单状态已变更为:%s,不支持绑定"),
    DIST_BIND_ERROR("DIST_BIND_ERROR", "承运单状态不符合，请重新添加"),
    ESTIMATE_FARE_ERROR("ESTIMATE_FARE_ERROR", "预估运费位数太长"),
    NO_BATCH_ERROR("NO_BATCH_ERROR", "当前没有对应的配送委托单"),
    DRIVER_STATUS_INVALID("DRIVER_STATUS_ERROR", "司机当前状态为无效"),
    MANGAGE_DEFAULT_FAILED("DEFAULT_FAILED", "鲜沐异常:%s"),
    INTERCEPT_ERROR("INTERCEPT_ERROR", "不能发起拦截:%s"),
    COMPLETE_PATH_ERROR("COMPLETE_PATH_ERROR", "完成排线:%s"),
    SITE_ITEM_ERROR("SITE_ITEM_ERROR", "配送货品异常:%s"),
    SITE_ITEM_CODE_ERROR("SITE_ITEM_CODE_ERROR", "扫码异常:%s"),
    BATCH_STATUS_ERROR("BATCH_STATUS_ERROR", "配送路线状态已变更为:%s,不支持智能排线"),
    NO_CHANGE_SPECIAL_SEND("NO_CHANGE_SPECIAL_SEND","已过配送时间,点位不能变更为专车配送"),
    SCAN_CODE_HAVE("SCAN_CODE_HAVE","当前二维码已录入,请扫描其他商品二维码"),
    SCAN_CODE_OVER("SCAN_CODE_OVER","当前商品扫码已超过最大配送数量,请检查!"),
    LOCK_ERROR("LOCK_ERROR","正在处理中,请稍后!"),
    NO_SKU_SCAN_CODE("NO_SKU_SCAN_CODE","该店铺没有此商品信息"),
    SKU_SCAN_CODE_ERROR("SKU_SCAN_CODE_ERROR","扫码数量异常:%s"),
    DELIVERY_SITE_NOT_FOUND("DELIVERY_SITE_NOT_FOUND","存在点位不存在情况,请刷新页面重试"),
    DELIVERY_SITE_INTERCEPTED("DELIVERY_SITE_INTERCEPTED","存在点位[%s]被拦截情况,请刷新页面重试"),
    COMPLETE_PATH_DIST_OPERATE_FAIL("COMPLETE_PATH_DIST_OPERATE_FAIL","已完成排线，操作委托单失败"),
    COMPLETE_DELIVERY_DIST_OPERATE_FAIL("COMPLETE_DELIVERY_DIST_OPERATE_FAIL","已完成配送，操作委托单失败"),
    NEED_SCAN_CODE_OR_WITHOUT_SCAN_ERROR("NEED_SCAN_CODE_OR_WITHOUT_SCAN_ERROR","数据不符:%s"),
    LOGIN_ERROR("LOGIN_ERROR","登录过期请重新登录"),
    ADDRESS_NOT_OPEN("ADDRESS_NOT_OPEN","此地区未开启配送"),
    DUBBO_ERROR("DUBBO_ERROR","调用第三方接口异常:%s"),
    IMPORT_DATA_EMPTY("IMPORT_DATA_EMPTY","暂无记录可导出"),
    ;

    public final String code;
    public final String msg;
    public final boolean needRetry;

    ErrorCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
        this.needRetry = false;
    }

    ErrorCodeEnum(String code, String msg, boolean needRetry) {
        this.code = code;
        this.msg = msg;
        this.needRetry = needRetry;
    }

    public static String formatErrorMsg(ErrorCodeEnum errorCodeEnum, Object... params) {
        return String.format(errorCodeEnum.msg, params);
    }
}
