<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>common</module>
        <module>api</module>
        <module>domain</module>
        <module>application</module>
        <module>infrastructure</module>
        <module>web</module>
        <module>old</module>
        <module>starter</module>
        <module>facade</module>
    </modules>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>net.summerfarm</groupId>
    <artifactId>tms</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>tms</name>
    <description>鲜沐科技配送系统</description>

    <properties>
        <!--    基础配置信息    -->
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!--    依赖版本统一配置、便于管理    -->
        <base.version>1.0-SNAPSHOT</base.version>
        <fastjson.version>1.2.83</fastjson.version>
        <lombok.version>1.18.2</lombok.version>
        <typehandlers.version>1.0.1</typehandlers.version>
        <datatype.version>2.9.2</datatype.version>
        <swagger.version>2.7.0</swagger.version>
        <qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
        <starter.version>2.1.1</starter.version>
        <mysql-connector.version>8.0.23</mysql-connector.version>
        <druid.version>1.1.20</druid.version>
        <redis.version>3.1.0</redis.version>
        <commons-math3.version>3.6.1</commons-math3.version>
        <xstream.version>1.4.7</xstream.version>
        <page.version>1.2.7</page.version>
        <pagehelper.version>5.1.6</pagehelper.version>
        <gauva.version>28.2-jre</gauva.version>
        <hutool.version>5.8.4</hutool.version>
        <common.version>1.5.10-RELEASE</common.version>
        <tms.version>0.0.1-SNAPSHOT</tms.version>
        <tms-api.version>0.0.19-CXH-SNAPSHOT</tms-api.version>
        <tms-common.version>1.0.1-RELEASE</tms-common.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <schedulerx2.version>1.7.4</schedulerx2.version>
        <xianmu-common.version>1.1.5-RELEASE</xianmu-common.version>
        <xianmu-dubbo.version>1.0.9</xianmu-dubbo.version>
        <nacos-config.version>0.2.10</nacos-config.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <warehouse.version>1.2.4</warehouse.version>
        <redisson.version>3.16.4</redisson.version>
        <protobuf.version>3.21.12</protobuf.version>
        <ortools.version>9.4.1874</ortools.version>
        <!-- 此版本兼容easyexcel-->
        <poi.version>4.1.2</poi.version>
        <tms-client.version>1.2.5-cxh-SNAPSHOT</tms-client.version>
        <manage.version>1.0.35-RELEASE</manage.version>
        <ofc.version>1.6.4-RELEASE</ofc.version>
        <xianmu-rocketmq.version>1.2.0</xianmu-rocketmq.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <wms-client.version>1.6.7-RELEASE</wms-client.version>
        <xianmu-task.version>1.0.5</xianmu-task.version>
        <mall-client.version>1.0.20-RELEASE</mall-client.version>
        <xianmu-oss.version>1.0.2</xianmu-oss.version>
        <cosfo-manage-client.version>1.3.8-RELEASE</cosfo-manage-client.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <authentication-client.version>1.1.24</authentication-client.version>
        <authentication-sdk.version>1.1.4</authentication-sdk.version>
        <crm-client.version>1.0.8</crm-client.version>
        <xianmu-robot-util.version>1.0.3</xianmu-robot-util.version>
        <order-center-client.version>1.1.0</order-center-client.version>
        <summerfarm-wnc-client.version>1.4.1-RELEASE</summerfarm-wnc-client.version>
        <goods-center-client.version>1.1.3-RELEASE</goods-center-client.version>
        <oms-client.version>1.0.7-RELEASE</oms-client.version>
        <xianmu-redis.version>1.0.1</xianmu-redis.version>
        <sf-mall-manage-client.version>1.2.1-RELEASE</sf-mall-manage-client.version>
        <usercenter-client.version>1.1.7</usercenter-client.version>
        <common-client.version>1.0.7-RELEASE</common-client.version>
        <xianmu-download-support.version>1.0.2</xianmu-download-support.version>
        <sentinel.version>1.0.3-RELEASE</sentinel.version>
        <pms-client.version>1.4.6-RELEASE</pms-client.version>
        <gaode-support.version>1.0.5-RELEASE</gaode-support.version>
        <xianmu-mybatis-interceptor-support.version>1.0.6-RELEASE</xianmu-mybatis-interceptor-support.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>common-client</artifactId>
                <version>${common-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>xianmu-download-support</artifactId>
                <version>${xianmu-download-support.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-client</artifactId>
                <version>${usercenter-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>sf-mall-manage-client</artifactId>
                <version>${sf-mall-manage-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-redis-support</artifactId>
                <version>${xianmu-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>oms-client</artifactId>
                <version>${oms-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>goods-center-client</artifactId>
                <version>${goods-center-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-robot-util</artifactId>
                <version>${xianmu-robot-util.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-oss-support</artifactId>
                <version>${xianmu-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <version>${xianmu-common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>${xianmu-dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-mybatis-interceptor-support</artifactId>
                <version>${xianmu-mybatis-interceptor-support.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.schedulerx</groupId>
                <artifactId>schedulerx2-spring-boot-starter</artifactId>
                <version>${schedulerx2.version}</version>
                <!--如果用的是logback，需要把log4j和log4j2排除掉 -->
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-api</artifactId>
                <version>${tms-api.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-web</artifactId>
                <version>${tms.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-domain</artifactId>
                <version>${tms.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-facade</artifactId>
                <version>${tms.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-application</artifactId>
                <version>${tms.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>tms-domain</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-infrastructure</artifactId>
                <version>${tms.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>tms-domain</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-common</artifactId>
                <version>${tms-common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-old</artifactId>
                <version>${tms.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>tms-domain</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--分页插件-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${page.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- alibaba json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--  lombok  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!--数据库组件——mysql连接组件-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!--alibaba开源数据库连接池-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${datatype.version}</version>
            </dependency>
            <!--redis依赖-->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${redis.version}</version>
            </dependency>
            <!--  swagger  -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!-- Maven配置 -->
            <dependency>
                <groupId>com.google.ortools</groupId>
                <artifactId>ortools-java</artifactId>
                <version>${ortools.version}</version>
            </dependency>
            <!-- 指定 Protobuf 版本 -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version> <!-- 使用兼容的 Protobuf 版本 -->
            </dependency>
            <!--  七牛上传SDK  -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp</groupId>
                <artifactId>okhttp</artifactId>
                <version>2.7.1</version>
            </dependency>
            <!--   apache数学工具包     -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>${commons-math3.version}</version>
            </dependency>
            <!--    xstream    -->
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>

            <dependency>
                <artifactId>okio</artifactId>
                <groupId>com.squareup.okio</groupId>
                <version>1.17.2</version>
            </dependency>

            <!-- Google guava工具包-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${gauva.version}</version>
            </dependency>


            <dependency>
                <groupId>com.cosfo.summerfarm</groupId>
                <artifactId>saas-to-summerfarm</artifactId>
                <version>1.4.8</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-common</artifactId>
                <version>${common.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>rocketmq-spring-boot-starter</artifactId>
                        <groupId>org.apache.rocketmq</groupId>
                    </exclusion>
                </exclusions>

            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-warehouse</artifactId>
                <version>${warehouse.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>rocketmq-spring-boot-starter</artifactId>
                        <groupId>org.apache.rocketmq</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!--excel等导入导出-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-client</artifactId>
                <version>${tms-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>tms-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>ofc-client</artifactId>
                <version>${ofc.version}</version>
            </dependency>
            <dependency>
                <groupId>net.manage.client</groupId>
                <artifactId>manage-client</artifactId>
                <version>${manage.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>${xianmu-log.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm.wms</groupId>
                <artifactId>summerfarm-wms-client</artifactId>
                <version>${wms-client.version}</version>
            </dependency>
            <!--任务调度-->
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${xianmu-task.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-client</artifactId>
                <version>${mall-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>cosfo-manage-client</artifactId>
                <version>${cosfo-manage-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-client</artifactId>
                <version>${authentication-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>summerfarm-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-sdk</artifactId>
                <version>${authentication-sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>summerfarm-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- POJO转换工具 begin -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- POJO转换工具 end -->
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>crm-client</artifactId>
                <version>${crm-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>order-center-client</artifactId>
                <version>${order-center-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-client</artifactId>
                <version>${summerfarm-wnc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-sentinel-support</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-pms-client</artifactId>
                <version>${pms-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-gaode-support</artifactId>
                <version>${gaode-support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>4.1.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!--  使用阿里云maven仓库  -->
    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <url>https://repo.rdc.aliyun.com/repository/117302-release-WOHwYC/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://repo.rdc.aliyun.com/repository/117302-snapshot-Cov4r3/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
