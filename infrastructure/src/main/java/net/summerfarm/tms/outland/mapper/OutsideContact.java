package net.summerfarm.tms.outland.mapper;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 外部地址表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-67 14:51:43
 */
@Getter
@Setter
@TableName("outside_contact")
public class OutsideContact implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * poi
     */
    @TableField("poi")
    private String poi;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 商户id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 到仓距离
     */
    @TableField("distance")
    private BigDecimal distance;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 联系人
     */
    @TableField("`name`")
    private String name;

    /**
     * 店铺名称
     */
    @TableField("mname")
    private String mname;


}
