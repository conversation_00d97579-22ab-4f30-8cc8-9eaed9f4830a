package net.summerfarm.tms.outland.mapper;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 核心管理员表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-66 14:40:19
 */
@Getter
@Setter
@TableName("admin")
public class Admin implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "admin_id", type = IdType.AUTO)
    private Integer adminId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 登录失败次数，若到达五次则锁定账户；
     */
    @TableField("login_fail_times")
    private Integer loginFailTimes;

    /**
     * 是否禁用
     */
    @TableField("is_disabled")
    private Boolean isDisabled;

    @TableField("username")
    private String username;

    @TableField("`password`")
    private String password;

    @TableField("login_time")
    private LocalDateTime loginTime;

    @TableField("realname")
    private String realname;

    @TableField("gender")
    private Boolean gender;

    @TableField("department")
    private String department;

    @TableField("phone")
    private String phone;

    @TableField("kp")
    private String kp;

    @TableField("saler_id")
    private Integer salerId;

    @TableField("saler_name")
    private String salerName;

    @TableField("contract")
    private String contract;

    @TableField("contract_method")
    private String contractMethod;

    /**
     * 名称备注
     */
    @TableField("name_remakes")
    private String nameRemakes;

    /**
     * 运营
     */
    @TableField("operate_id")
    private Integer operateId;

    /**
     * 报价周期： 0周报价, 1半月报价, 2月报价，3日报价
     */
    @TableField("major_cycle")
    private Integer majorCycle;

    /**
     * 大客户是否提前截单 0 不是 1 是
     */
    @TableField("close_order_type")
    private Integer closeOrderType;

    /**
     * 标识ka大客户状态0（默认）非ka客户或是，1:试样，2:报价，3:试配，4:合作稳定期，5:合作困难期，6:流失，7:暂不合作
     */
    @TableField("cooperation_stage")
    private Integer cooperationStage;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 大客户提前截单时间
     */
    @TableField("close_order_time")
    private String closeOrderTime;

    /**
     * 修改后大客户提前截单时间
     */
    @TableField("update_close_order_time")
    private String updateCloseOrderTime;

    /**
     * 是否开启低价监控：t、开启 f、关闭
     */
    @TableField("low_price_remainder")
    private Boolean lowPriceRemainder;

    /**
     * 低价监控排除城市
     */
    @TableField("not_included_area")
    private String notIncludedArea;

    /**
     * 是否是特殊捡货大客户
     */
    @TableField("sku_sorting")
    private Boolean skuSorting;

    /**
     * 客户类别 0大客户 1普通 2批发客户
     */
    @TableField("admin_type")
    private Integer adminType;

    /**
     * 连锁范围0（NKA）1(LKA) 2(其他连锁)
     */
    @TableField("admin_chain")
    private Integer adminChain;

    /**
     * 品牌等级 0普通 1KA
     */
    @TableField("admin_grade")
    private Integer adminGrade;

    /**
     * 充送开关 0 开启，1 关闭
     */
    @TableField("admin_switch")
    private Integer adminSwitch;

    /**
     * 统一社会信用代码
     */
    @TableField("credit_code")
    private String creditCode;

    /**
     * 营业执照地址
     */
    @TableField("business_license_address")
    private String businessLicenseAddress;

    /**
     * 票到付款 0 是 1 否
     */
    @TableField("bill_to_pay")
    private Integer billToPay;


}
