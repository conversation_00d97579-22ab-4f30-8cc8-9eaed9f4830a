package net.summerfarm.tms.converter;

import net.summerfarm.tms.alert.entity.DeliveryAlertRuleEntity;
import net.summerfarm.tms.dao.TmsCompleteDeliveryRule;

/**
 * Description:配送提醒规则转换器
 * date: 2023/3/21 19:14
 *
 * <AUTHOR>
 */
public class TmsCompleteDeliveryRuleConverter {

    public static TmsCompleteDeliveryRule entity2do(DeliveryAlertRuleEntity deliveryAlertRuleEntity){
        if (deliveryAlertRuleEntity == null){
            return null;
        }
        TmsCompleteDeliveryRule tmsCompleteDeliveryRule = new TmsCompleteDeliveryRule();
        tmsCompleteDeliveryRule.setId(deliveryAlertRuleEntity.getId());
        tmsCompleteDeliveryRule.setStoreNo(deliveryAlertRuleEntity.getStoreNo());
        tmsCompleteDeliveryRule.setRuleName(deliveryAlertRuleEntity.getRuleName());
        tmsCompleteDeliveryRule.setBrandRuleObjectOssKey(deliveryAlertRuleEntity.getBrandRuleObjectOssKey());
        tmsCompleteDeliveryRule.setMerchantRuleObjectOssKey(deliveryAlertRuleEntity.getMerchantRuleObjectOssKey());
        tmsCompleteDeliveryRule.setCreateTime(deliveryAlertRuleEntity.getCreateTime());
        tmsCompleteDeliveryRule.setUpdateTime(deliveryAlertRuleEntity.getUpdateTime());

        return tmsCompleteDeliveryRule;
    }

    public static DeliveryAlertRuleEntity do2entity(TmsCompleteDeliveryRule tmsCompleteDeliveryRule){
        if (tmsCompleteDeliveryRule == null){
            return null;
        }
        DeliveryAlertRuleEntity deliveryAlertRuleEntity = new DeliveryAlertRuleEntity();
        deliveryAlertRuleEntity.setId(tmsCompleteDeliveryRule.getId());
        deliveryAlertRuleEntity.setStoreNo(tmsCompleteDeliveryRule.getStoreNo());
        deliveryAlertRuleEntity.setRuleName(tmsCompleteDeliveryRule.getRuleName());
        deliveryAlertRuleEntity.setBrandRuleObjectOssKey(tmsCompleteDeliveryRule.getBrandRuleObjectOssKey());
        deliveryAlertRuleEntity.setMerchantRuleObjectOssKey(tmsCompleteDeliveryRule.getMerchantRuleObjectOssKey());
//        deliveryAlertRuleVO.setBrandAlertRuleItems();
//        deliveryAlertRuleVO.setMerchantAlertRuleItems();
//        deliveryAlertRuleVO.setDeliveryAlertRuleItems();
        deliveryAlertRuleEntity.setCreateTime(tmsCompleteDeliveryRule.getCreateTime());
        deliveryAlertRuleEntity.setUpdateTime(tmsCompleteDeliveryRule.getUpdateTime());

        return deliveryAlertRuleEntity;
    }
}
