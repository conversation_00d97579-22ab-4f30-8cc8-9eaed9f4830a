package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.config.entity.ConfigEntity;
import net.summerfarm.tms.dao.TmsConfig;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-03-24
 **/
public class TmsConfigConverter {

    /**
     * 实体转entity对象
     *
     * @param tmsConfig
     * @return
     */
    public static ConfigEntity doToEntity(TmsConfig tmsConfig) {
        if (Objects.isNull(tmsConfig)) {
            return null;
        }
        ConfigEntity configEntity = new ConfigEntity();
        configEntity.setId(tmsConfig.getId());
        configEntity.setName(tmsConfig.getName());
        configEntity.setValue(tmsConfig.getValue());
        configEntity.setRemark(tmsConfig.getRemark());
        return configEntity;
    }
}
