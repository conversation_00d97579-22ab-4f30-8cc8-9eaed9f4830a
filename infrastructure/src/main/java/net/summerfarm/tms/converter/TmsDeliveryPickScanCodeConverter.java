package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliveryPickScanCode;
import net.summerfarm.tms.delivery.entity.DeliveryPickScanCodeEntity;

/**
 * Description: 转换类<br/>
 * date: 2024/8/14 15:52<br/>
 *
 * <AUTHOR> />
 */
public class TmsDeliveryPickScanCodeConverter {

    public static DeliveryPickScanCodeEntity do2Entity(TmsDeliveryPickScanCode tmsDeliveryPickScanCode) {
        if(tmsDeliveryPickScanCode == null){
            return null;
        }
        DeliveryPickScanCodeEntity entity = new DeliveryPickScanCodeEntity();

        entity.setId(tmsDeliveryPickScanCode.getId());
        entity.setCreateTime(tmsDeliveryPickScanCode.getCreateTime());
        entity.setUpdateTime(tmsDeliveryPickScanCode.getUpdateTime());
        entity.setPickId(tmsDeliveryPickScanCode.getPickId());
        entity.setDeliveryBatchId(tmsDeliveryPickScanCode.getDeliveryBatchId());
        entity.setSiteId(tmsDeliveryPickScanCode.getSiteId());
        entity.setOutItemId(tmsDeliveryPickScanCode.getOutItemId());
        entity.setItemDesc(tmsDeliveryPickScanCode.getItemDesc());
        entity.setOnlyCode(tmsDeliveryPickScanCode.getOnlyCode());

        return entity;
    }

    public static TmsDeliveryPickScanCode entity2Do(DeliveryPickScanCodeEntity entity) {
        if(entity == null){
            return null;
        }

        TmsDeliveryPickScanCode model = new TmsDeliveryPickScanCode();
        model.setId(entity.getId());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        model.setPickId(entity.getPickId());
        model.setDeliveryBatchId(entity.getDeliveryBatchId());
        model.setSiteId(entity.getSiteId());
        model.setOutItemId(entity.getOutItemId());
        model.setItemDesc(entity.getItemDesc());
        model.setOnlyCode(entity.getOnlyCode());
        return model;
    }
}
