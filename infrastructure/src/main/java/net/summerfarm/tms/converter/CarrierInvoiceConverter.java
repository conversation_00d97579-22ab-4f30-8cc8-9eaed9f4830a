package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.carrier.entity.CarrierInvoiceEntity;
import net.summerfarm.tms.dao.CarrierInvoice;

/**
 * Description: 承运商发票转换
 * date: 2023/10/12 15:31<br/>
 *
 * <AUTHOR> />
 */
public class CarrierInvoiceConverter {

    public static CarrierInvoiceEntity do2Entity(CarrierInvoice carrierInvoice){
        if(carrierInvoice == null){
            return null;
        }
        CarrierInvoiceEntity entity = new CarrierInvoiceEntity();

        entity.setId(carrierInvoice.getId());
        entity.setCarrierId(carrierInvoice.getCarrierId());
        entity.setInvoiceHead(carrierInvoice.getInvoiceHead());
        entity.setTaxNo(carrierInvoice.getTaxNo());
        entity.setCreateTime(carrierInvoice.getCreateTime());
        entity.setUpdateTime(carrierInvoice.getUpdateTime());

        return entity;
    }


    public static CarrierInvoice entity2Do(CarrierInvoiceEntity entity){
        if(entity == null){
            return null;
        }
        CarrierInvoice carrierInvoice = new CarrierInvoice();

        carrierInvoice.setId(entity.getId());
        carrierInvoice.setCarrierId(entity.getCarrierId());
        carrierInvoice.setInvoiceHead(entity.getInvoiceHead());
        carrierInvoice.setTaxNo(entity.getTaxNo());
        carrierInvoice.setCreateTime(entity.getCreateTime());
        carrierInvoice.setUpdateTime(entity.getUpdateTime());

        return carrierInvoice;
    }
}
