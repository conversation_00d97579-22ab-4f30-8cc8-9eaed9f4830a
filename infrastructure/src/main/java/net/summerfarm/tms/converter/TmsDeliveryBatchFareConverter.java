package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliveryBatchFare;
import net.summerfarm.tms.delivery.entity.DeliveryBatchFareEntity;
import net.summerfarm.tms.enums.DeliveryBatchEnums;

/**
 * Description:配送批次费用项PO转换器
 * date: 2023/2/10 18:38
 *
 * <AUTHOR>
 */
public class TmsDeliveryBatchFareConverter {

    public static TmsDeliveryBatchFare entity2fare(DeliveryBatchFareEntity deliveryBatchFareEntity){
        TmsDeliveryBatchFare tmsDeliveryBatchFare = new TmsDeliveryBatchFare();
        if (deliveryBatchFareEntity == null){
            return null;
        }
        tmsDeliveryBatchFare.setId(deliveryBatchFareEntity.getId());
        tmsDeliveryBatchFare.setDeliveryBatchId(deliveryBatchFareEntity.getDeliveryBatchId());
        tmsDeliveryBatchFare.setFareType(deliveryBatchFareEntity.getFareType().getValue());
        tmsDeliveryBatchFare.setAmount(deliveryBatchFareEntity.getAmount());
        tmsDeliveryBatchFare.setCreator(deliveryBatchFareEntity.getCreator());
        tmsDeliveryBatchFare.setCreateTime(deliveryBatchFareEntity.getCreateTime());
        tmsDeliveryBatchFare.setUpdater(deliveryBatchFareEntity.getUpdater());
        tmsDeliveryBatchFare.setUpdateTime(deliveryBatchFareEntity.getUpdateTime());
        return tmsDeliveryBatchFare;
    }

    public static DeliveryBatchFareEntity fare2Entity(TmsDeliveryBatchFare tmsDeliveryBatchFare){
        if (tmsDeliveryBatchFare == null){
            return null;
        }
        DeliveryBatchFareEntity deliveryBatchFareEntity = new DeliveryBatchFareEntity();
        deliveryBatchFareEntity.setId(tmsDeliveryBatchFare.getId());
        deliveryBatchFareEntity.setDeliveryBatchId(tmsDeliveryBatchFare.getDeliveryBatchId());
        //获取费用明细描述
        DeliveryBatchEnums.FareType fareType = DeliveryBatchEnums.FareType.getFareByVal(tmsDeliveryBatchFare.getFareType());
        deliveryBatchFareEntity.setFareType(fareType);
        deliveryBatchFareEntity.setAmount(tmsDeliveryBatchFare.getAmount());
        deliveryBatchFareEntity.setCreator(tmsDeliveryBatchFare.getCreator());
        deliveryBatchFareEntity.setUpdater(tmsDeliveryBatchFare.getUpdater());
        deliveryBatchFareEntity.setCreateTime(tmsDeliveryBatchFare.getCreateTime());
        deliveryBatchFareEntity.setUpdateTime(tmsDeliveryBatchFare.getUpdateTime());
        return deliveryBatchFareEntity;
    }
}
