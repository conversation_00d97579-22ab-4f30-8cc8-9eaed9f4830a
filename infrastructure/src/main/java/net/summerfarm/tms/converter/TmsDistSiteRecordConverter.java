package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.site.entity.SiteRecordEntity;
import net.summerfarm.tms.base.site.param.SiteRecordCommandParam;
import net.summerfarm.tms.dao.TmsDistSiteRecord;

/**
 * Description:点位审核记录转换器
 * date: 2024/1/29 18:40
 *
 * <AUTHOR>
 */
public class TmsDistSiteRecordConverter {

    public static SiteRecordEntity do2Entity(TmsDistSiteRecord tmsDistSiteRecord) {
        if (tmsDistSiteRecord == null){
            return null;
        }
        SiteRecordEntity siteRecordEntity = new SiteRecordEntity();
        siteRecordEntity.setId(tmsDistSiteRecord.getId());
        siteRecordEntity.setSiteId(tmsDistSiteRecord.getSiteId());
        siteRecordEntity.setDeliverySiteId(tmsDistSiteRecord.getDeliverySiteId());
        siteRecordEntity.setNewProvince(tmsDistSiteRecord.getNewProvince());
        siteRecordEntity.setNewCity(tmsDistSiteRecord.getNewCity());
        siteRecordEntity.setNewArea(tmsDistSiteRecord.getNewArea());
        siteRecordEntity.setNewAddress(tmsDistSiteRecord.getNewAddress());
        siteRecordEntity.setOldPoi(tmsDistSiteRecord.getOldPoi());
        siteRecordEntity.setNewPoi(tmsDistSiteRecord.getNewPoi());
        siteRecordEntity.setDistance(tmsDistSiteRecord.getDistance());
        siteRecordEntity.setOperatorId(tmsDistSiteRecord.getOperatorId());
        siteRecordEntity.setCreateTime(tmsDistSiteRecord.getCreateTime());
        siteRecordEntity.setUpdateTime(tmsDistSiteRecord.getUpdateTime());
        siteRecordEntity.setOperator(tmsDistSiteRecord.getOperator());
        return siteRecordEntity;
    }

    public static TmsDistSiteRecord param2Do(SiteRecordCommandParam commandParam) {
        if (commandParam == null){
            return null;
        }
        TmsDistSiteRecord tmsDistSiteRecord = new TmsDistSiteRecord();
        tmsDistSiteRecord.setId(commandParam.getId());
        tmsDistSiteRecord.setSiteId(commandParam.getSiteId());
        tmsDistSiteRecord.setDeliverySiteId(commandParam.getDeliverySiteId());
        tmsDistSiteRecord.setNewProvince(commandParam.getNewProvince());
        tmsDistSiteRecord.setNewCity(commandParam.getNewCity());
        tmsDistSiteRecord.setNewArea(commandParam.getNewArea());
        tmsDistSiteRecord.setNewAddress(commandParam.getNewAddress());
        tmsDistSiteRecord.setOldPoi(commandParam.getOldPoi());
        tmsDistSiteRecord.setNewPoi(commandParam.getNewPoi());
        tmsDistSiteRecord.setDistance(commandParam.getDistance());
        tmsDistSiteRecord.setOperatorId(commandParam.getOperatorId());
        tmsDistSiteRecord.setCreateTime(commandParam.getCreateTime());
        tmsDistSiteRecord.setUpdateTime(commandParam.getUpdateTime());
        tmsDistSiteRecord.setOperator(commandParam.getOperator());
        return tmsDistSiteRecord;

    }
}
