package net.summerfarm.tms.converter.delivery;

import net.summerfarm.tms.delivery.entity.TmsDeliveryBatchExtEntity;
import net.summerfarm.tms.delivery.param.command.TmsDeliveryBatchExtCommandParam;
import net.summerfarm.tms.model.delivery.TmsDeliveryBatchExt;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-05-14 15:11:42
 * @version 1.0
 *
 */
public class TmsDeliveryBatchExtConverter {

    private TmsDeliveryBatchExtConverter() {
        // 无需实现
    }




    public static List<TmsDeliveryBatchExtEntity> toTmsDeliveryBatchExtEntityList(List<TmsDeliveryBatchExt> tmsDeliveryBatchExtList) {
        if (tmsDeliveryBatchExtList == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryBatchExtEntity> tmsDeliveryBatchExtEntityList = new ArrayList<>();
        for (TmsDeliveryBatchExt tmsDeliveryBatchExt : tmsDeliveryBatchExtList) {
            tmsDeliveryBatchExtEntityList.add(toTmsDeliveryBatchExtEntity(tmsDeliveryBatchExt));
        }
        return tmsDeliveryBatchExtEntityList;
}


    public static TmsDeliveryBatchExtEntity toTmsDeliveryBatchExtEntity(TmsDeliveryBatchExt tmsDeliveryBatchExt) {
        if (tmsDeliveryBatchExt == null) {
             return null;
        }
        TmsDeliveryBatchExtEntity tmsDeliveryBatchExtEntity = new TmsDeliveryBatchExtEntity();
        tmsDeliveryBatchExtEntity.setId(tmsDeliveryBatchExt.getId());
        tmsDeliveryBatchExtEntity.setCreateTime(tmsDeliveryBatchExt.getCreateTime());
        tmsDeliveryBatchExtEntity.setUpdateTime(tmsDeliveryBatchExt.getUpdateTime());
        tmsDeliveryBatchExtEntity.setDeliveryBatchId(tmsDeliveryBatchExt.getDeliveryBatchId());
        tmsDeliveryBatchExtEntity.setExtKey(tmsDeliveryBatchExt.getExtKey());
        tmsDeliveryBatchExtEntity.setExtValue(tmsDeliveryBatchExt.getExtValue());
        return tmsDeliveryBatchExtEntity;
    }








    public static TmsDeliveryBatchExt toTmsDeliveryBatchExt(TmsDeliveryBatchExtCommandParam param) {
        if (param == null) {
            return null;
        }
        TmsDeliveryBatchExt tmsDeliveryBatchExt = new TmsDeliveryBatchExt();
        tmsDeliveryBatchExt.setId(param.getId());
        tmsDeliveryBatchExt.setCreateTime(param.getCreateTime());
        tmsDeliveryBatchExt.setUpdateTime(param.getUpdateTime());
        tmsDeliveryBatchExt.setDeliveryBatchId(param.getDeliveryBatchId());
        tmsDeliveryBatchExt.setExtKey(param.getExtKey());
        tmsDeliveryBatchExt.setExtValue(param.getExtValue());
        return tmsDeliveryBatchExt;
    }
}
