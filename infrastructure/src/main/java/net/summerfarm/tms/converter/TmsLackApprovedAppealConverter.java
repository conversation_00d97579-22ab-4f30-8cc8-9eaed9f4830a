package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsLackApprovedAppeal;
import net.summerfarm.tms.lack.entity.LackApprovedAppealEntity;

/**
 * Description: <br/>
 * date: 2023/9/7 17:27<br/>
 *
 * <AUTHOR> />
 */
public class TmsLackApprovedAppealConverter {

    public static LackApprovedAppealEntity model2Entity(TmsLackApprovedAppeal tmsLackApprovedAppeal){
        if(tmsLackApprovedAppeal == null){
            return null;
        }

        LackApprovedAppealEntity lackApprovedAppealEntity = new LackApprovedAppealEntity();

        lackApprovedAppealEntity.setId(tmsLackApprovedAppeal.getId());
        lackApprovedAppealEntity.setCreateTime(tmsLackApprovedAppeal.getCreateTime());
        lackApprovedAppealEntity.setUpdateTime(tmsLackApprovedAppeal.getUpdateTime());
        lackApprovedAppealEntity.setApprovedId(tmsLackApprovedAppeal.getApprovedId());
        lackApprovedAppealEntity.setSide(tmsLackApprovedAppeal.getSide());
        lackApprovedAppealEntity.setDescription(tmsLackApprovedAppeal.getDescription());
        lackApprovedAppealEntity.setCertificate(tmsLackApprovedAppeal.getCertificate());
        lackApprovedAppealEntity.setAppealPeopleName(tmsLackApprovedAppeal.getAppealPeopleName());

        return lackApprovedAppealEntity;
    }



    public static TmsLackApprovedAppeal entity2Model(LackApprovedAppealEntity lackApprovedAppealEntity){
        if(lackApprovedAppealEntity == null){
            return null;
        }

        TmsLackApprovedAppeal tmsLackApprovedAppeal = new TmsLackApprovedAppeal();

        tmsLackApprovedAppeal.setId(lackApprovedAppealEntity.getId());
        tmsLackApprovedAppeal.setCreateTime(lackApprovedAppealEntity.getCreateTime());
        tmsLackApprovedAppeal.setUpdateTime(lackApprovedAppealEntity.getUpdateTime());
        tmsLackApprovedAppeal.setApprovedId(lackApprovedAppealEntity.getApprovedId());
        tmsLackApprovedAppeal.setSide(lackApprovedAppealEntity.getSide());
        tmsLackApprovedAppeal.setDescription(lackApprovedAppealEntity.getDescription());
        tmsLackApprovedAppeal.setCertificate(lackApprovedAppealEntity.getCertificate());
        tmsLackApprovedAppeal.setAppealPeopleName(lackApprovedAppealEntity.getAppealPeopleName());

        return tmsLackApprovedAppeal;
    }
}
