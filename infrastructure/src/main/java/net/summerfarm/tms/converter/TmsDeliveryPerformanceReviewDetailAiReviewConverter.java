package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetailAiReview;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailAiReviewEntity;
import net.summerfarm.tms.performance.param.command.TmsDeliveryPerformanceReviewDetailAiReviewCommandParam;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-09-18 00:16:55
 * @version 1.0
 *
 */
public class TmsDeliveryPerformanceReviewDetailAiReviewConverter {

    private TmsDeliveryPerformanceReviewDetailAiReviewConverter() {
        // 无需实现
    }




    public static List<TmsDeliveryPerformanceReviewDetailAiReviewEntity> toTmsDeliveryPerformanceReviewDetailAiReviewEntityList(List<TmsDeliveryPerformanceReviewDetailAiReview> tmsDeliveryPerformanceReviewDetailAiReviewList) {
        if (tmsDeliveryPerformanceReviewDetailAiReviewList == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryPerformanceReviewDetailAiReviewEntity> tmsDeliveryPerformanceReviewDetailAiReviewEntityList = new ArrayList<>();
        for (TmsDeliveryPerformanceReviewDetailAiReview tmsDeliveryPerformanceReviewDetailAiReview : tmsDeliveryPerformanceReviewDetailAiReviewList) {
            tmsDeliveryPerformanceReviewDetailAiReviewEntityList.add(toTmsDeliveryPerformanceReviewDetailAiReviewEntity(tmsDeliveryPerformanceReviewDetailAiReview));
        }
        return tmsDeliveryPerformanceReviewDetailAiReviewEntityList;
}


    public static TmsDeliveryPerformanceReviewDetailAiReviewEntity toTmsDeliveryPerformanceReviewDetailAiReviewEntity(TmsDeliveryPerformanceReviewDetailAiReview tmsDeliveryPerformanceReviewDetailAiReview) {
        if (tmsDeliveryPerformanceReviewDetailAiReview == null) {
             return null;
        }
        TmsDeliveryPerformanceReviewDetailAiReviewEntity tmsDeliveryPerformanceReviewDetailAiReviewEntity = new TmsDeliveryPerformanceReviewDetailAiReviewEntity();
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setId(tmsDeliveryPerformanceReviewDetailAiReview.getId());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCreateTime(tmsDeliveryPerformanceReviewDetailAiReview.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setUpdateTime(tmsDeliveryPerformanceReviewDetailAiReview.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setPerformanceReviewTaskId(tmsDeliveryPerformanceReviewDetailAiReview.getPerformanceReviewTaskId());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setPerformanceReviewDetailId(tmsDeliveryPerformanceReviewDetailAiReview.getPerformanceReviewDetailId());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCitySignPicResults(tmsDeliveryPerformanceReviewDetailAiReview.getCitySignPicResults());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCityDeliveryPicResults(tmsDeliveryPerformanceReviewDetailAiReview.getCityDeliveryPicResults());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCityProductPicResults(tmsDeliveryPerformanceReviewDetailAiReview.getCityProductPicResults());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCitySignPicPass(tmsDeliveryPerformanceReviewDetailAiReview.getCitySignPicPass());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCityDeliveryPicPass(tmsDeliveryPerformanceReviewDetailAiReview.getCityDeliveryPicPass());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCityProductPicPass(tmsDeliveryPerformanceReviewDetailAiReview.getCityProductPicPass());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setAllPass(tmsDeliveryPerformanceReviewDetailAiReview.getAllPass());

        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCityVehiclePlatePicResults(tmsDeliveryPerformanceReviewDetailAiReview.getCityVehiclePlatePicResults());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCityVehiclePlatePicPass(tmsDeliveryPerformanceReviewDetailAiReview.getCityVehiclePlatePicPass());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCityLoadPicResults(tmsDeliveryPerformanceReviewDetailAiReview.getCityLoadPicResults());
        tmsDeliveryPerformanceReviewDetailAiReviewEntity.setCityLoadPicPass(tmsDeliveryPerformanceReviewDetailAiReview.getCityLoadPicPass());
        return tmsDeliveryPerformanceReviewDetailAiReviewEntity;
    }








    public static TmsDeliveryPerformanceReviewDetailAiReview toTmsDeliveryPerformanceReviewDetailAiReview(TmsDeliveryPerformanceReviewDetailAiReviewCommandParam param) {
        if (param == null) {
            return null;
        }
        TmsDeliveryPerformanceReviewDetailAiReview tmsDeliveryPerformanceReviewDetailAiReview = new TmsDeliveryPerformanceReviewDetailAiReview();
        tmsDeliveryPerformanceReviewDetailAiReview.setId(param.getId());
        tmsDeliveryPerformanceReviewDetailAiReview.setCreateTime(param.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAiReview.setUpdateTime(param.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAiReview.setPerformanceReviewTaskId(param.getPerformanceReviewTaskId());
        tmsDeliveryPerformanceReviewDetailAiReview.setPerformanceReviewDetailId(param.getPerformanceReviewDetailId());
        tmsDeliveryPerformanceReviewDetailAiReview.setCitySignPicResults(param.getCitySignPicResults());
        tmsDeliveryPerformanceReviewDetailAiReview.setCityDeliveryPicResults(param.getCityDeliveryPicResults());
        tmsDeliveryPerformanceReviewDetailAiReview.setCityProductPicResults(param.getCityProductPicResults());
        tmsDeliveryPerformanceReviewDetailAiReview.setCitySignPicPass(param.getCitySignPicPass());
        tmsDeliveryPerformanceReviewDetailAiReview.setCityDeliveryPicPass(param.getCityDeliveryPicPass());
        tmsDeliveryPerformanceReviewDetailAiReview.setCityProductPicPass(param.getCityProductPicPass());
        tmsDeliveryPerformanceReviewDetailAiReview.setAllPass(param.getAllPass());
        tmsDeliveryPerformanceReviewDetailAiReview.setCityVehiclePlatePicResults(param.getCityDeliveryPicResults());
        tmsDeliveryPerformanceReviewDetailAiReview.setCityVehiclePlatePicPass(param.getCityDeliveryPicPass());
        tmsDeliveryPerformanceReviewDetailAiReview.setCityLoadPicResults(param.getCityDeliveryPicResults());
        tmsDeliveryPerformanceReviewDetailAiReview.setCityLoadPicPass(param.getCityDeliveryPicPass());
        return tmsDeliveryPerformanceReviewDetailAiReview;
    }
}
