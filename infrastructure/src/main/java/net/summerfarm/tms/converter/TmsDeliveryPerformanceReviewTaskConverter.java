package net.summerfarm.tms.converter;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewTask;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewTaskEntity;
import net.summerfarm.tms.jsonobject.PerformanceReviewPropertyJson;
import org.springframework.util.StringUtils;

/**
 * Description: <br/>
 * date: 2023/6/30 13:44<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
public class TmsDeliveryPerformanceReviewTaskConverter {

    public static TmsDeliveryPerformanceReviewTask entity2Do(DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity){
        if(deliveryPerformanceReviewTaskEntity == null){
            return null;
        }
        TmsDeliveryPerformanceReviewTask tmsDeliveryPerformanceReviewTask = new TmsDeliveryPerformanceReviewTask();
        tmsDeliveryPerformanceReviewTask.setId(deliveryPerformanceReviewTaskEntity.getId());
        tmsDeliveryPerformanceReviewTask.setCreateTime(deliveryPerformanceReviewTaskEntity.getCreateTime());
        tmsDeliveryPerformanceReviewTask.setUpdateTime(deliveryPerformanceReviewTaskEntity.getUpdateTime());
        tmsDeliveryPerformanceReviewTask.setReviewTaskType(deliveryPerformanceReviewTaskEntity.getReviewTaskType());
        tmsDeliveryPerformanceReviewTask.setName(deliveryPerformanceReviewTaskEntity.getName());
        tmsDeliveryPerformanceReviewTask.setPenaltyStandards(deliveryPerformanceReviewTaskEntity.getPenaltyStandards());

        PerformanceReviewPropertyJson performanceReviewPropertyJson = new PerformanceReviewPropertyJson();

        performanceReviewPropertyJson.setStoreNames(deliveryPerformanceReviewTaskEntity.getStoreNames());
        performanceReviewPropertyJson.setStoreNos(deliveryPerformanceReviewTaskEntity.getStoreNos());
        performanceReviewPropertyJson.setBatchTypeNames(deliveryPerformanceReviewTaskEntity.getBatchTypeNames());
        performanceReviewPropertyJson.setBatchTypes(deliveryPerformanceReviewTaskEntity.getBatchTypes());
        performanceReviewPropertyJson.setBeginDeliveryTime(deliveryPerformanceReviewTaskEntity.getBeginDeliveryTime());
        performanceReviewPropertyJson.setEndDeliveryTime(deliveryPerformanceReviewTaskEntity.getEndDeliveryTime());

        tmsDeliveryPerformanceReviewTask.setPropertyJson(JSON.toJSONString(performanceReviewPropertyJson));

        tmsDeliveryPerformanceReviewTask.setState(deliveryPerformanceReviewTaskEntity.getState());
        tmsDeliveryPerformanceReviewTask.setCreateName(deliveryPerformanceReviewTaskEntity.getCreateName());
        tmsDeliveryPerformanceReviewTask.setReviewMode(deliveryPerformanceReviewTaskEntity.getReviewMode());
        return tmsDeliveryPerformanceReviewTask;
    }


    public static DeliveryPerformanceReviewTaskEntity do2Entity(TmsDeliveryPerformanceReviewTask tmsDeliveryPerformanceReviewTask){
        if(tmsDeliveryPerformanceReviewTask == null){
            return null;
        }
        DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity = new DeliveryPerformanceReviewTaskEntity();
        deliveryPerformanceReviewTaskEntity.setId(tmsDeliveryPerformanceReviewTask.getId());
        deliveryPerformanceReviewTaskEntity.setCreateTime(tmsDeliveryPerformanceReviewTask.getCreateTime());
        deliveryPerformanceReviewTaskEntity.setUpdateTime(tmsDeliveryPerformanceReviewTask.getUpdateTime());
        deliveryPerformanceReviewTaskEntity.setReviewTaskType(tmsDeliveryPerformanceReviewTask.getReviewTaskType());
        deliveryPerformanceReviewTaskEntity.setName(tmsDeliveryPerformanceReviewTask.getName());
        deliveryPerformanceReviewTaskEntity.setPenaltyStandards(tmsDeliveryPerformanceReviewTask.getPenaltyStandards());

        if(!StringUtils.isEmpty(tmsDeliveryPerformanceReviewTask.getPropertyJson())){
            try {
                PerformanceReviewPropertyJson performanceReviewPropertyJson = JSON.parseObject(tmsDeliveryPerformanceReviewTask.getPropertyJson(), PerformanceReviewPropertyJson.class);
                deliveryPerformanceReviewTaskEntity.setStoreNames(performanceReviewPropertyJson.getStoreNames());
                deliveryPerformanceReviewTaskEntity.setStoreNos(performanceReviewPropertyJson.getStoreNos());
                deliveryPerformanceReviewTaskEntity.setBatchTypeNames(performanceReviewPropertyJson.getBatchTypeNames());
                deliveryPerformanceReviewTaskEntity.setBatchTypes(performanceReviewPropertyJson.getBatchTypes());
                deliveryPerformanceReviewTaskEntity.setBeginDeliveryTime(performanceReviewPropertyJson.getBeginDeliveryTime());
                deliveryPerformanceReviewTaskEntity.setEndDeliveryTime(performanceReviewPropertyJson.getEndDeliveryTime());

                tmsDeliveryPerformanceReviewTask.setPropertyJson(JSON.toJSONString(performanceReviewPropertyJson));
            } catch (Exception e) {
                log.error("DeliveryPerformanceReviewTaskEntity do2Entity 数据解析失败:{}",e.getMessage(),e);
            }
        }

        deliveryPerformanceReviewTaskEntity.setState(tmsDeliveryPerformanceReviewTask.getState());
        deliveryPerformanceReviewTaskEntity.setCreateName(tmsDeliveryPerformanceReviewTask.getCreateName());
        deliveryPerformanceReviewTaskEntity.setReviewMode(tmsDeliveryPerformanceReviewTask.getReviewMode());
        return deliveryPerformanceReviewTaskEntity;
    }
}
