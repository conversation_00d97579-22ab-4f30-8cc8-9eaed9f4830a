package net.summerfarm.tms.converter;

import lombok.Data;
import net.summerfarm.tms.base.carrier.entity.CarrierAccountEntity;
import net.summerfarm.tms.dao.CarrierAccount;

/**
 * Description: 承运商账户转换
 * date: 2023/10/12 15:28<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CarrierAccountConverter {

    public static CarrierAccountEntity do2Entity(CarrierAccount carrierAccount){
        if(carrierAccount == null){
            return null;
        }

        CarrierAccountEntity entity = new CarrierAccountEntity();

        entity.setId(carrierAccount.getId());
        entity.setCreateTime(carrierAccount.getCreateTime());
        entity.setUpdateTime(carrierAccount.getUpdateTime());
        entity.setCarrierId(carrierAccount.getCarrierId());
        entity.setPayType(carrierAccount.getPayType());
        entity.setAccountName(carrierAccount.getAccountName());
        entity.setAccountBank(carrierAccount.getAccountBank());
        entity.setAccountAscription(carrierAccount.getAccountAscription());
        entity.setAccount(carrierAccount.getAccount());

        return entity;
    }

    public static CarrierAccount entity2Do(CarrierAccountEntity entity){
        if(entity == null){
            return null;
        }

        CarrierAccount carrierAccount = new CarrierAccount();

        carrierAccount.setId(entity.getId());
        carrierAccount.setCreateTime(entity.getCreateTime());
        carrierAccount.setUpdateTime(entity.getUpdateTime());
        carrierAccount.setCarrierId(entity.getCarrierId());
        carrierAccount.setPayType(entity.getPayType());
        carrierAccount.setAccountName(entity.getAccountName());
        carrierAccount.setAccountBank(entity.getAccountBank());
        carrierAccount.setAccountAscription(entity.getAccountAscription());
        carrierAccount.setAccount(entity.getAccount());

        return carrierAccount;
    }
}
