package net.summerfarm.tms.converter.delivery;

import net.summerfarm.tms.delivery.entity.TmsDeliveryPickShortOrderMappingEntity;
import net.summerfarm.tms.model.delivery.TmsDeliveryPickShortOrderMapping;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-24 14:21:44
 */
public abstract class TmsDeliveryPickShortOrderMappingConverter {

    public static List<TmsDeliveryPickShortOrderMappingEntity> toTmsDeliveryPickShortOrderMappingEntityList(List<TmsDeliveryPickShortOrderMapping> tmsDeliveryPickShortOrderMappingList) {
        if (CollectionUtils.isEmpty(tmsDeliveryPickShortOrderMappingList)) {
            return Collections.emptyList();
        }
        return tmsDeliveryPickShortOrderMappingList.stream().map(TmsDeliveryPickShortOrderMappingConverter::toTmsDeliveryPickShortOrderMappingEntity)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static TmsDeliveryPickShortOrderMappingEntity toTmsDeliveryPickShortOrderMappingEntity(TmsDeliveryPickShortOrderMapping tmsDeliveryPickShortOrderMapping) {
        if (tmsDeliveryPickShortOrderMapping == null) {
            return null;
        }
        TmsDeliveryPickShortOrderMappingEntity tmsDeliveryPickShortOrderMappingEntity = new TmsDeliveryPickShortOrderMappingEntity();
        tmsDeliveryPickShortOrderMappingEntity.setId(tmsDeliveryPickShortOrderMapping.getId());
        tmsDeliveryPickShortOrderMappingEntity.setCreateTime(tmsDeliveryPickShortOrderMapping.getCreateTime());
        tmsDeliveryPickShortOrderMappingEntity.setUpdateTime(tmsDeliveryPickShortOrderMapping.getUpdateTime());
        tmsDeliveryPickShortOrderMappingEntity.setDeliveryBatchId(tmsDeliveryPickShortOrderMapping.getDeliveryBatchId());
        tmsDeliveryPickShortOrderMappingEntity.setOrderNo(tmsDeliveryPickShortOrderMapping.getOrderNo());
        tmsDeliveryPickShortOrderMappingEntity.setOutItemId(tmsDeliveryPickShortOrderMapping.getOutItemId());
        tmsDeliveryPickShortOrderMappingEntity.setItemDesc(tmsDeliveryPickShortOrderMapping.getItemDesc());
        tmsDeliveryPickShortOrderMappingEntity.setShortQuantity(tmsDeliveryPickShortOrderMapping.getShortQuantity());
        tmsDeliveryPickShortOrderMappingEntity.setDriverName(tmsDeliveryPickShortOrderMapping.getDriverName());
        tmsDeliveryPickShortOrderMappingEntity.setDriverPhone(tmsDeliveryPickShortOrderMapping.getDriverPhone());
        tmsDeliveryPickShortOrderMappingEntity.setPathCode(tmsDeliveryPickShortOrderMapping.getPathCode());
        tmsDeliveryPickShortOrderMappingEntity.setPathSequence(tmsDeliveryPickShortOrderMapping.getPathSequence());
        tmsDeliveryPickShortOrderMappingEntity.setOutContactId(tmsDeliveryPickShortOrderMapping.getOutContactId());
        tmsDeliveryPickShortOrderMappingEntity.setOutClientId(tmsDeliveryPickShortOrderMapping.getOutClientId());
        return tmsDeliveryPickShortOrderMappingEntity;
    }

    public static List<TmsDeliveryPickShortOrderMapping> toTmsDeliveryPickShortOrderMappingList(List<TmsDeliveryPickShortOrderMappingEntity> tmsDeliveryPickShortOrderMappingEntitiyList) {
        if (CollectionUtils.isEmpty(tmsDeliveryPickShortOrderMappingEntitiyList)) {
            return Collections.emptyList();
        }
        return tmsDeliveryPickShortOrderMappingEntitiyList.stream().map(TmsDeliveryPickShortOrderMappingConverter::toTmsDeliveryPickShortOrderMapping)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static TmsDeliveryPickShortOrderMapping toTmsDeliveryPickShortOrderMapping(TmsDeliveryPickShortOrderMappingEntity tmsDeliveryPickShortOrderMappingEntity) {
        if (tmsDeliveryPickShortOrderMappingEntity == null) {
            return null;
        }
        TmsDeliveryPickShortOrderMapping deliveryPickShortOrderMapping = new TmsDeliveryPickShortOrderMapping();
        deliveryPickShortOrderMapping.setId(tmsDeliveryPickShortOrderMappingEntity.getId());
        deliveryPickShortOrderMapping.setCreateTime(tmsDeliveryPickShortOrderMappingEntity.getCreateTime());
        deliveryPickShortOrderMapping.setUpdateTime(tmsDeliveryPickShortOrderMappingEntity.getUpdateTime());
        deliveryPickShortOrderMapping.setDeliveryBatchId(tmsDeliveryPickShortOrderMappingEntity.getDeliveryBatchId());
        deliveryPickShortOrderMapping.setOrderNo(tmsDeliveryPickShortOrderMappingEntity.getOrderNo());
        deliveryPickShortOrderMapping.setOutItemId(tmsDeliveryPickShortOrderMappingEntity.getOutItemId());
        deliveryPickShortOrderMapping.setItemDesc(tmsDeliveryPickShortOrderMappingEntity.getItemDesc());
        deliveryPickShortOrderMapping.setShortQuantity(tmsDeliveryPickShortOrderMappingEntity.getShortQuantity());
        deliveryPickShortOrderMapping.setDriverName(tmsDeliveryPickShortOrderMappingEntity.getDriverName());
        deliveryPickShortOrderMapping.setDriverPhone(tmsDeliveryPickShortOrderMappingEntity.getDriverPhone());
        deliveryPickShortOrderMapping.setPathCode(tmsDeliveryPickShortOrderMappingEntity.getPathCode());
        deliveryPickShortOrderMapping.setPathSequence(tmsDeliveryPickShortOrderMappingEntity.getPathSequence());
        deliveryPickShortOrderMapping.setOutContactId(tmsDeliveryPickShortOrderMappingEntity.getOutContactId());
        deliveryPickShortOrderMapping.setOutClientId(tmsDeliveryPickShortOrderMappingEntity.getOutClientId());
        return deliveryPickShortOrderMapping;
    }

}
