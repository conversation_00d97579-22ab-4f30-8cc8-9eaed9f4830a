package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.path.entity.PathCarEntity;
import net.summerfarm.tms.dao.TmsPathCar;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-05-15
 **/
@Mapper(componentModel = Constants.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface TmsPathCarConverter {

    /**
     * entityList --> doList
     * @param pathCarEntities
     * @return
     */
    List<TmsPathCar>  entityToDoList(List<PathCarEntity> pathCarEntities);


    /**
     * doList2EntityList
     * @param pathCarList
     * @return
     */
    List<PathCarEntity> doList2EntityList(List<TmsPathCar> pathCarList);
}
