package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.distance.entity.OutDistanceEntity;
import net.summerfarm.tms.dao.TmsOutDistanceConfig;

/**
 * Description: <br/>
 * date: 2023/7/17 16:49<br/>
 *
 * <AUTHOR> />
 */
public class TmsOutDistanceConfigConverter {

    public static OutDistanceEntity do2Entity(TmsOutDistanceConfig config){
        if(config == null){
            return null;
        }

        OutDistanceEntity outDistanceEntity = new OutDistanceEntity();
        outDistanceEntity.setId(config.getId());
        outDistanceEntity.setStoreNo(config.getStoreNo());
        outDistanceEntity.setOutDistance(config.getOutDistance());
        outDistanceEntity.setState(config.getState());
        outDistanceEntity.setAdminId(config.getAdminId());
        outDistanceEntity.setCreateTime(config.getCreateTime());
        outDistanceEntity.setUpdateTime(config.getUpdateTime());
        return outDistanceEntity;
    }

    public static TmsOutDistanceConfig entity2Do(OutDistanceEntity outDistanceEntity){
        if(outDistanceEntity == null){
            return null;
        }

        TmsOutDistanceConfig config = new TmsOutDistanceConfig();

        config.setId(outDistanceEntity.getId());
        config.setStoreNo(outDistanceEntity.getStoreNo());
        config.setOutDistance(outDistanceEntity.getOutDistance());
        config.setState(outDistanceEntity.getState());
        config.setAdminId(outDistanceEntity.getAdminId());

        return config;
    }
}
