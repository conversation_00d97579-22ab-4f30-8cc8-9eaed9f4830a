package net.summerfarm.tms.converter.tmstracklog;

import net.summerfarm.tms.dao.track.TmsTrackLog;
import net.summerfarm.tms.track.entity.TmsTrackLogEntity;
import net.summerfarm.tms.track.param.command.TmsTrackLogCommandParam;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-08-30 14:11:07
 * @version 1.0
 *
 */
public class TmsTrackLogConverter {

    private TmsTrackLogConverter() {
        // 无需实现
    }




    public static List<TmsTrackLogEntity> toTmsTrackLogEntityList(List<TmsTrackLog> tmsTrackLogList) {
        if (tmsTrackLogList == null) {
            return Collections.emptyList();
        }
        List<TmsTrackLogEntity> tmsTrackLogEntityList = new ArrayList<>();
        for (TmsTrackLog tmsTrackLog : tmsTrackLogList) {
            tmsTrackLogEntityList.add(toTmsTrackLogEntity(tmsTrackLog));
        }
        return tmsTrackLogEntityList;
}


    public static TmsTrackLogEntity toTmsTrackLogEntity(TmsTrackLog tmsTrackLog) {
        if (tmsTrackLog == null) {
             return null;
        }
        TmsTrackLogEntity tmsTrackLogEntity = new TmsTrackLogEntity();
        tmsTrackLogEntity.setId(tmsTrackLog.getId());
        tmsTrackLogEntity.setCreateTime(tmsTrackLog.getCreateTime());
        tmsTrackLogEntity.setUpdateTime(tmsTrackLog.getUpdateTime());
        tmsTrackLogEntity.setBizType(tmsTrackLog.getBizType());
        tmsTrackLogEntity.setBizNo(tmsTrackLog.getBizNo());
        tmsTrackLogEntity.setActionName(tmsTrackLog.getActionName());
        tmsTrackLogEntity.setOperater(tmsTrackLog.getOperater());
        tmsTrackLogEntity.setRemark(tmsTrackLog.getRemark());
        return tmsTrackLogEntity;
    }





    public static TmsTrackLog toTmsTrackLog(TmsTrackLogCommandParam param) {
        if (param == null) {
            return null;
        }
        TmsTrackLog tmsTrackLog = new TmsTrackLog();
        tmsTrackLog.setId(param.getId());
        tmsTrackLog.setCreateTime(param.getCreateTime());
        tmsTrackLog.setUpdateTime(param.getUpdateTime());
        tmsTrackLog.setBizType(param.getBizType());
        tmsTrackLog.setBizNo(param.getBizNo());
        tmsTrackLog.setActionName(param.getActionName());
        tmsTrackLog.setOperater(param.getOperater());
        tmsTrackLog.setRemark(param.getRemark());
        return tmsTrackLog;
    }
}
