package net.summerfarm.tms.converter.deliveryNoteTemplate;

import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateBelongCommandParam;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
public class TmsDeliveryNoteTemplateBelongConverter {

    private TmsDeliveryNoteTemplateBelongConverter() {
        // 无需实现
    }




    public static List<TmsDeliveryNoteTemplateBelongEntity> toTmsDeliveryNoteTemplateBelongEntityList(List<TmsDeliveryNoteTemplateBelong> tmsDeliveryNoteTemplateBelongList) {
        if (tmsDeliveryNoteTemplateBelongList == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryNoteTemplateBelongEntity> tmsDeliveryNoteTemplateBelongEntityList = new ArrayList<>();
        for (TmsDeliveryNoteTemplateBelong tmsDeliveryNoteTemplateBelong : tmsDeliveryNoteTemplateBelongList) {
            tmsDeliveryNoteTemplateBelongEntityList.add(toTmsDeliveryNoteTemplateBelongEntity(tmsDeliveryNoteTemplateBelong));
        }
        return tmsDeliveryNoteTemplateBelongEntityList;
}


    public static TmsDeliveryNoteTemplateBelongEntity toTmsDeliveryNoteTemplateBelongEntity(TmsDeliveryNoteTemplateBelong tmsDeliveryNoteTemplateBelong) {
        if (tmsDeliveryNoteTemplateBelong == null) {
             return null;
        }
        TmsDeliveryNoteTemplateBelongEntity tmsDeliveryNoteTemplateBelongEntity = new TmsDeliveryNoteTemplateBelongEntity();
        tmsDeliveryNoteTemplateBelongEntity.setId(tmsDeliveryNoteTemplateBelong.getId());
        tmsDeliveryNoteTemplateBelongEntity.setCreateTime(tmsDeliveryNoteTemplateBelong.getCreateTime());
        tmsDeliveryNoteTemplateBelongEntity.setUpdateTime(tmsDeliveryNoteTemplateBelong.getUpdateTime());
        tmsDeliveryNoteTemplateBelongEntity.setDeliveryNoteTemplateId(tmsDeliveryNoteTemplateBelong.getDeliveryNoteTemplateId());
        tmsDeliveryNoteTemplateBelongEntity.setTenantId(tmsDeliveryNoteTemplateBelong.getTenantId());
        tmsDeliveryNoteTemplateBelongEntity.setScopeType(tmsDeliveryNoteTemplateBelong.getScopeType());
        tmsDeliveryNoteTemplateBelongEntity.setScopeBusinessId(tmsDeliveryNoteTemplateBelong.getScopeBusinessId());
        tmsDeliveryNoteTemplateBelongEntity.setScopeBusinessName(tmsDeliveryNoteTemplateBelong.getScopeBusinessName());
        tmsDeliveryNoteTemplateBelongEntity.setAppSource(tmsDeliveryNoteTemplateBelong.getAppSource());
        return tmsDeliveryNoteTemplateBelongEntity;
    }








    public static TmsDeliveryNoteTemplateBelong toTmsDeliveryNoteTemplateBelong(TmsDeliveryNoteTemplateBelongCommandParam param) {
        if (param == null) {
            return null;
        }
        TmsDeliveryNoteTemplateBelong tmsDeliveryNoteTemplateBelong = new TmsDeliveryNoteTemplateBelong();
        tmsDeliveryNoteTemplateBelong.setId(param.getId());
        tmsDeliveryNoteTemplateBelong.setCreateTime(param.getCreateTime());
        tmsDeliveryNoteTemplateBelong.setUpdateTime(param.getUpdateTime());
        tmsDeliveryNoteTemplateBelong.setDeliveryNoteTemplateId(param.getDeliveryNoteTemplateId());
        tmsDeliveryNoteTemplateBelong.setTenantId(param.getTenantId());
        tmsDeliveryNoteTemplateBelong.setScopeType(param.getScopeType());
        tmsDeliveryNoteTemplateBelong.setScopeBusinessId(param.getScopeBusinessId());
        tmsDeliveryNoteTemplateBelong.setScopeBusinessName(param.getScopeBusinessName());
        tmsDeliveryNoteTemplateBelong.setAppSource(param.getAppSource());
        return tmsDeliveryNoteTemplateBelong;
    }
}
