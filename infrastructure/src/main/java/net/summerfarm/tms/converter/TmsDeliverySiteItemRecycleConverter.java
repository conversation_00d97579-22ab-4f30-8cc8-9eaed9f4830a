package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliverySiteItemRecycle;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemRecycleEntity;
import net.summerfarm.tms.enums.DeliverySiteItemRecycleEnums;

/**
 * Description:点位配送物品回收DO转换器
 * date: 2023/12/19 17:04
 *
 * <AUTHOR>
 */
public class TmsDeliverySiteItemRecycleConverter {

    public static TmsDeliverySiteItemRecycle entity2Do(DeliverySiteItemRecycleEntity deliverySiteItemRecycleEntity) {
        if (deliverySiteItemRecycleEntity == null) {
            return null;
        }
        TmsDeliverySiteItemRecycle tmsDeliverySiteItemRecycle = new TmsDeliverySiteItemRecycle();
        tmsDeliverySiteItemRecycle.setId(deliverySiteItemRecycleEntity.getId());
        tmsDeliverySiteItemRecycle.setDeliverySiteItemId(deliverySiteItemRecycleEntity.getDeliverySiteItemId());
        tmsDeliverySiteItemRecycle.setDeliverySiteId(deliverySiteItemRecycleEntity.getDeliverySiteId());
        tmsDeliverySiteItemRecycle.setOutItemId(deliverySiteItemRecycleEntity.getOutItemId());
        tmsDeliverySiteItemRecycle.setRecyclePics(deliverySiteItemRecycleEntity.getRecyclePics());
        tmsDeliverySiteItemRecycle.setSpecificationQuantity(deliverySiteItemRecycleEntity.getSpecificationQuantity());
        tmsDeliverySiteItemRecycle.setSpecificationUnit(deliverySiteItemRecycleEntity.getSpecificationUnit());
        tmsDeliverySiteItemRecycle.setBasicSpecQuantity(deliverySiteItemRecycleEntity.getBasicSpecQuantity());
        tmsDeliverySiteItemRecycle.setBasicSpecUnit(deliverySiteItemRecycleEntity.getBasicSpecUnit());
        if (deliverySiteItemRecycleEntity.getReasonType() != null){
            tmsDeliverySiteItemRecycle.setReasonType(deliverySiteItemRecycleEntity.getReasonType().getValue());
        }
        tmsDeliverySiteItemRecycle.setRemark(deliverySiteItemRecycleEntity.getRemark());
        tmsDeliverySiteItemRecycle.setCreateTime(deliverySiteItemRecycleEntity.getCreateTime());
        tmsDeliverySiteItemRecycle.setUpdateTime(deliverySiteItemRecycleEntity.getUpdateTime());
        tmsDeliverySiteItemRecycle.setTemperature(deliverySiteItemRecycleEntity.getTemperature());
        return tmsDeliverySiteItemRecycle;
    }

    public static DeliverySiteItemRecycleEntity do2Entity(TmsDeliverySiteItemRecycle tmsDeliverySiteItemRecycle) {
        if (tmsDeliverySiteItemRecycle == null) {
            return null;
        }
        DeliverySiteItemRecycleEntity deliverySiteItemRecycleEntity = new DeliverySiteItemRecycleEntity();
        deliverySiteItemRecycleEntity.setId(tmsDeliverySiteItemRecycle.getId());
        deliverySiteItemRecycleEntity.setDeliverySiteItemId(tmsDeliverySiteItemRecycle.getDeliverySiteItemId());
        deliverySiteItemRecycleEntity.setDeliverySiteId(tmsDeliverySiteItemRecycle.getDeliverySiteId());
        deliverySiteItemRecycleEntity.setOutItemId(tmsDeliverySiteItemRecycle.getOutItemId());
        deliverySiteItemRecycleEntity.setRecyclePics(tmsDeliverySiteItemRecycle.getRecyclePics());
        deliverySiteItemRecycleEntity.setSpecificationQuantity(tmsDeliverySiteItemRecycle.getSpecificationQuantity());
        deliverySiteItemRecycleEntity.setSpecificationUnit(tmsDeliverySiteItemRecycle.getSpecificationUnit());
        deliverySiteItemRecycleEntity.setBasicSpecQuantity(tmsDeliverySiteItemRecycle.getBasicSpecQuantity());
        deliverySiteItemRecycleEntity.setBasicSpecUnit(tmsDeliverySiteItemRecycle.getBasicSpecUnit());
        if (tmsDeliverySiteItemRecycle.getReasonType() != null){
            DeliverySiteItemRecycleEnums.ReasonType reasonType = DeliverySiteItemRecycleEnums.ReasonType.getTypeByValue(tmsDeliverySiteItemRecycle.getReasonType());
            deliverySiteItemRecycleEntity.setReasonType(reasonType);
        }
        deliverySiteItemRecycleEntity.setRemark(tmsDeliverySiteItemRecycle.getRemark());
        deliverySiteItemRecycleEntity.setCreateTime(tmsDeliverySiteItemRecycle.getCreateTime());
        deliverySiteItemRecycleEntity.setUpdateTime(tmsDeliverySiteItemRecycle.getUpdateTime());
        deliverySiteItemRecycleEntity.setTemperature(tmsDeliverySiteItemRecycle.getTemperature());
        return deliverySiteItemRecycleEntity;

    }
}
