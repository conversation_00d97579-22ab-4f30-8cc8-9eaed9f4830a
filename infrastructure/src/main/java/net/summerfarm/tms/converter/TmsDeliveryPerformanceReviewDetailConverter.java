package net.summerfarm.tms.converter;

import com.alibaba.fastjson.JSON;
import net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetail;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.jsonobject.PerformanceReviewDetailPicJson;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailEntity;
import org.springframework.util.StringUtils;

/**
 * Description: <br/>
 * date: 2023/6/30 17:01<br/>
 *
 * <AUTHOR> />
 */
public class TmsDeliveryPerformanceReviewDetailConverter {

    public static TmsDeliveryPerformanceReviewDetail entity2Do(DeliveryPerformanceReviewDetailEntity deliveryPerformanceReviewDetailEntity){
        if(deliveryPerformanceReviewDetailEntity == null){
            return null;
        }
        TmsDeliveryPerformanceReviewDetail tmsDeliveryPerformanceReviewDetail = new TmsDeliveryPerformanceReviewDetail();
        tmsDeliveryPerformanceReviewDetail.setId(deliveryPerformanceReviewDetailEntity.getId());
        tmsDeliveryPerformanceReviewDetail.setPerformanceReviewTaskId(deliveryPerformanceReviewDetailEntity.getPerformanceReviewTaskId());
        tmsDeliveryPerformanceReviewDetail.setState(deliveryPerformanceReviewDetailEntity.getState());

        PerformanceReviewDetailPicJson performanceReviewDetailPicJson = new PerformanceReviewDetailPicJson();

        performanceReviewDetailPicJson.setCityLoadPics(deliveryPerformanceReviewDetailEntity.getCityLoadPics());
        performanceReviewDetailPicJson.setCityVehiclePlatePics(deliveryPerformanceReviewDetailEntity.getCityVehiclePlatePics());
        performanceReviewDetailPicJson.setCityDeliveryPic(deliveryPerformanceReviewDetailEntity.getCityDeliveryPic());
        performanceReviewDetailPicJson.setCitySignPic(deliveryPerformanceReviewDetailEntity.getCitySignPic());
        performanceReviewDetailPicJson.setCityProductPic(deliveryPerformanceReviewDetailEntity.getCityProductPic());
        performanceReviewDetailPicJson.setTrunkSignPic(deliveryPerformanceReviewDetailEntity.getTrunkSignPic());
        performanceReviewDetailPicJson.setTrunkLoadPics(deliveryPerformanceReviewDetailEntity.getTrunkLoadPics());
        performanceReviewDetailPicJson.setTrunkSealPics(deliveryPerformanceReviewDetailEntity.getTrunkSealPics());
        performanceReviewDetailPicJson.setRefrigeratePics(deliveryPerformanceReviewDetailEntity.getRefrigeratePics());
        performanceReviewDetailPicJson.setFreezePics(deliveryPerformanceReviewDetailEntity.getFreezePics());

        tmsDeliveryPerformanceReviewDetail.setPicJson(JSON.toJSONString(performanceReviewDetailPicJson));

        tmsDeliveryPerformanceReviewDetail.setDeliveryTime(deliveryPerformanceReviewDetailEntity.getDeliveryTime());
        tmsDeliveryPerformanceReviewDetail.setDriverName(deliveryPerformanceReviewDetailEntity.getDriverName());
        tmsDeliveryPerformanceReviewDetail.setDriverPhone(deliveryPerformanceReviewDetailEntity.getDriverPhone());
        tmsDeliveryPerformanceReviewDetail.setCarNumber(deliveryPerformanceReviewDetailEntity.getCarNumber());
        tmsDeliveryPerformanceReviewDetail.setCarType(deliveryPerformanceReviewDetailEntity.getCarType());
        tmsDeliveryPerformanceReviewDetail.setCarStorage(deliveryPerformanceReviewDetailEntity.getCarStorage());
        tmsDeliveryPerformanceReviewDetail.setDeliveryBatchId(deliveryPerformanceReviewDetailEntity.getDeliveryBatchId());
        tmsDeliveryPerformanceReviewDetail.setDeliverySiteId(deliveryPerformanceReviewDetailEntity.getDeliverySiteId());
        tmsDeliveryPerformanceReviewDetail.setSiteId(deliveryPerformanceReviewDetailEntity.getSiteId());
        tmsDeliveryPerformanceReviewDetail.setSiteAddress(deliveryPerformanceReviewDetailEntity.getSiteAddress());
        tmsDeliveryPerformanceReviewDetail.setOuterClientName(deliveryPerformanceReviewDetailEntity.getOuterClientName());
        tmsDeliveryPerformanceReviewDetail.setOuterBrandName(deliveryPerformanceReviewDetailEntity.getOuterBrandName());
        tmsDeliveryPerformanceReviewDetail.setBeginSiteId(deliveryPerformanceReviewDetailEntity.getBeginSiteId());
        tmsDeliveryPerformanceReviewDetail.setBeginSiteName(deliveryPerformanceReviewDetailEntity.getBeginSiteName());
        tmsDeliveryPerformanceReviewDetail.setPathCode(deliveryPerformanceReviewDetailEntity.getPathCode());
        tmsDeliveryPerformanceReviewDetail.setPathName(deliveryPerformanceReviewDetailEntity.getPathName());
        tmsDeliveryPerformanceReviewDetail.setDeliveryBatchType(deliveryPerformanceReviewDetailEntity.getDeliveryBatchType());
        tmsDeliveryPerformanceReviewDetail.setSendWay(deliveryPerformanceReviewDetailEntity.getSendWay());
        tmsDeliveryPerformanceReviewDetail.setOutDistance(deliveryPerformanceReviewDetailEntity.getOutDistance());
        tmsDeliveryPerformanceReviewDetail.setPenaltyMoney(deliveryPerformanceReviewDetailEntity.getPenaltyMoney());
        tmsDeliveryPerformanceReviewDetail.setSitePicReason(deliveryPerformanceReviewDetailEntity.getSitePicReason());
        tmsDeliveryPerformanceReviewDetail.setTemperatureConditions(deliveryPerformanceReviewDetailEntity.getTemperatureConditions());
        tmsDeliveryPerformanceReviewDetail.setSignOutTemperature(deliveryPerformanceReviewDetailEntity.getSignOutTemperature());
        tmsDeliveryPerformanceReviewDetail.setSequence(deliveryPerformanceReviewDetailEntity.getSequence());
        tmsDeliveryPerformanceReviewDetail.setSiteName(deliveryPerformanceReviewDetailEntity.getSiteName());
        tmsDeliveryPerformanceReviewDetail.setSignInStatus(deliveryPerformanceReviewDetailEntity.getSignInStatus());
        tmsDeliveryPerformanceReviewDetail.setSignInRemark(deliveryPerformanceReviewDetailEntity.getSignInRemark());
        tmsDeliveryPerformanceReviewDetail.setNonFruitColdNum(deliveryPerformanceReviewDetailEntity.getNonFruitColdNum());
        tmsDeliveryPerformanceReviewDetail.setNonFruitFreezeNum(deliveryPerformanceReviewDetailEntity.getNonFruitFreezeNum());

        return tmsDeliveryPerformanceReviewDetail;
    }


    public static DeliveryPerformanceReviewDetailEntity do2Entity(TmsDeliveryPerformanceReviewDetail deliveryPerformanceReviewDetail){
        if(deliveryPerformanceReviewDetail == null){
            return null;
        }
        DeliveryPerformanceReviewDetailEntity deliveryPerformanceReviewDetailEntity = new DeliveryPerformanceReviewDetailEntity();

        deliveryPerformanceReviewDetailEntity.setId(deliveryPerformanceReviewDetail.getId());
        deliveryPerformanceReviewDetailEntity.setPerformanceReviewTaskId(deliveryPerformanceReviewDetail.getPerformanceReviewTaskId());
        deliveryPerformanceReviewDetailEntity.setState(deliveryPerformanceReviewDetail.getState());

        String picJson = deliveryPerformanceReviewDetail.getPicJson();
        if(!StringUtils.isEmpty(picJson)){
            PerformanceReviewDetailPicJson performanceReviewDetailPicJson = JSON.parseObject(picJson, PerformanceReviewDetailPicJson.class);

            deliveryPerformanceReviewDetailEntity.setCityLoadPics(performanceReviewDetailPicJson.getCityLoadPics());
            deliveryPerformanceReviewDetailEntity.setCityVehiclePlatePics(performanceReviewDetailPicJson.getCityVehiclePlatePics());
            deliveryPerformanceReviewDetailEntity.setCityDeliveryPic(performanceReviewDetailPicJson.getCityDeliveryPic());
            deliveryPerformanceReviewDetailEntity.setCitySignPic(performanceReviewDetailPicJson.getCitySignPic());
            deliveryPerformanceReviewDetailEntity.setCityProductPic(performanceReviewDetailPicJson.getCityProductPic());
            deliveryPerformanceReviewDetailEntity.setTrunkSignPic(performanceReviewDetailPicJson.getTrunkSignPic());
            deliveryPerformanceReviewDetailEntity.setTrunkLoadPics(performanceReviewDetailPicJson.getTrunkLoadPics());
            deliveryPerformanceReviewDetailEntity.setTrunkSealPics(performanceReviewDetailPicJson.getTrunkSealPics());
            deliveryPerformanceReviewDetailEntity.setRefrigeratePics(performanceReviewDetailPicJson.getRefrigeratePics());
            deliveryPerformanceReviewDetailEntity.setFreezePics(performanceReviewDetailPicJson.getFreezePics());
        }
        deliveryPerformanceReviewDetailEntity.setDeliveryTime(deliveryPerformanceReviewDetail.getDeliveryTime());
        deliveryPerformanceReviewDetailEntity.setDriverName(deliveryPerformanceReviewDetail.getDriverName());
        deliveryPerformanceReviewDetailEntity.setDriverPhone(deliveryPerformanceReviewDetail.getDriverPhone());
        deliveryPerformanceReviewDetailEntity.setCarNumber(deliveryPerformanceReviewDetail.getCarNumber());
        deliveryPerformanceReviewDetailEntity.setCarType(deliveryPerformanceReviewDetail.getCarType());
        deliveryPerformanceReviewDetailEntity.setCarStorage(deliveryPerformanceReviewDetail.getCarStorage());
        deliveryPerformanceReviewDetailEntity.setDeliveryBatchId(deliveryPerformanceReviewDetail.getDeliveryBatchId());
        deliveryPerformanceReviewDetailEntity.setDeliverySiteId(deliveryPerformanceReviewDetail.getDeliverySiteId());
        deliveryPerformanceReviewDetailEntity.setSiteId(deliveryPerformanceReviewDetail.getSiteId());
        deliveryPerformanceReviewDetailEntity.setSiteAddress(deliveryPerformanceReviewDetail.getSiteAddress());
        deliveryPerformanceReviewDetailEntity.setOuterClientName(deliveryPerformanceReviewDetail.getOuterClientName());
        deliveryPerformanceReviewDetailEntity.setOuterBrandName(deliveryPerformanceReviewDetail.getOuterBrandName());
        deliveryPerformanceReviewDetailEntity.setBeginSiteId(deliveryPerformanceReviewDetail.getBeginSiteId());
        deliveryPerformanceReviewDetailEntity.setBeginSiteName(deliveryPerformanceReviewDetail.getBeginSiteName());
        deliveryPerformanceReviewDetailEntity.setPathCode(deliveryPerformanceReviewDetail.getPathCode());
        deliveryPerformanceReviewDetailEntity.setPathName(deliveryPerformanceReviewDetail.getPathName());
        deliveryPerformanceReviewDetailEntity.setDeliveryBatchType(deliveryPerformanceReviewDetail.getDeliveryBatchType());
        deliveryPerformanceReviewDetailEntity.setSendWay(deliveryPerformanceReviewDetail.getSendWay());
        deliveryPerformanceReviewDetailEntity.setOutDistance(deliveryPerformanceReviewDetail.getOutDistance());
        deliveryPerformanceReviewDetailEntity.setPenaltyMoney(deliveryPerformanceReviewDetail.getPenaltyMoney());
        deliveryPerformanceReviewDetailEntity.setSitePicReason(deliveryPerformanceReviewDetail.getSitePicReason());
        deliveryPerformanceReviewDetailEntity.setTemperatureConditions(deliveryPerformanceReviewDetail.getTemperatureConditions());
        deliveryPerformanceReviewDetailEntity.setSignOutTemperature(deliveryPerformanceReviewDetail.getSignOutTemperature());
        deliveryPerformanceReviewDetailEntity.setSequence(deliveryPerformanceReviewDetail.getSequence());
        deliveryPerformanceReviewDetailEntity.setSiteName(deliveryPerformanceReviewDetail.getSiteName());
        deliveryPerformanceReviewDetailEntity.setSignInStatus(deliveryPerformanceReviewDetail.getSignInStatus());
        deliveryPerformanceReviewDetailEntity.setSignInRemark(deliveryPerformanceReviewDetail.getSignInRemark());
        deliveryPerformanceReviewDetailEntity.setNonFruitColdNum(deliveryPerformanceReviewDetail.getNonFruitColdNum());
        deliveryPerformanceReviewDetailEntity.setNonFruitFreezeNum(deliveryPerformanceReviewDetail.getNonFruitFreezeNum());

        return deliveryPerformanceReviewDetailEntity;
    }


    public static DeliveryPerformanceReviewDetailEntity doDetailEntity2Entity(TmsDeliveryPerformanceReviewDetailEntity tmsDeliveryPerformanceReviewDetailEntity){
        if(tmsDeliveryPerformanceReviewDetailEntity == null){
            return null;
        }
        DeliveryPerformanceReviewDetailEntity deliveryPerformanceReviewDetailEntity = new DeliveryPerformanceReviewDetailEntity();

        deliveryPerformanceReviewDetailEntity.setId(tmsDeliveryPerformanceReviewDetailEntity.getId());
        deliveryPerformanceReviewDetailEntity.setPerformanceReviewTaskId(tmsDeliveryPerformanceReviewDetailEntity.getPerformanceReviewTaskId());
        deliveryPerformanceReviewDetailEntity.setState(tmsDeliveryPerformanceReviewDetailEntity.getState());

        String picJson = tmsDeliveryPerformanceReviewDetailEntity.getPicJson();
        if(!StringUtils.isEmpty(picJson)){
            PerformanceReviewDetailPicJson performanceReviewDetailPicJson = JSON.parseObject(picJson, PerformanceReviewDetailPicJson.class);

            deliveryPerformanceReviewDetailEntity.setCityLoadPics(performanceReviewDetailPicJson.getCityLoadPics());
            deliveryPerformanceReviewDetailEntity.setCityVehiclePlatePics(performanceReviewDetailPicJson.getCityVehiclePlatePics());
            deliveryPerformanceReviewDetailEntity.setCityDeliveryPic(performanceReviewDetailPicJson.getCityDeliveryPic());
            deliveryPerformanceReviewDetailEntity.setCitySignPic(performanceReviewDetailPicJson.getCitySignPic());
            deliveryPerformanceReviewDetailEntity.setCityProductPic(performanceReviewDetailPicJson.getCityProductPic());
            deliveryPerformanceReviewDetailEntity.setTrunkSignPic(performanceReviewDetailPicJson.getTrunkSignPic());
            deliveryPerformanceReviewDetailEntity.setTrunkLoadPics(performanceReviewDetailPicJson.getTrunkLoadPics());
            deliveryPerformanceReviewDetailEntity.setTrunkSealPics(performanceReviewDetailPicJson.getTrunkSealPics());
            deliveryPerformanceReviewDetailEntity.setRefrigeratePics(performanceReviewDetailPicJson.getRefrigeratePics());
            deliveryPerformanceReviewDetailEntity.setFreezePics(performanceReviewDetailPicJson.getFreezePics());
        }
        deliveryPerformanceReviewDetailEntity.setDeliveryTime(tmsDeliveryPerformanceReviewDetailEntity.getDeliveryTime());
        deliveryPerformanceReviewDetailEntity.setDriverName(tmsDeliveryPerformanceReviewDetailEntity.getDriverName());
        deliveryPerformanceReviewDetailEntity.setDriverPhone(tmsDeliveryPerformanceReviewDetailEntity.getDriverPhone());
        deliveryPerformanceReviewDetailEntity.setCarNumber(tmsDeliveryPerformanceReviewDetailEntity.getCarNumber());
        deliveryPerformanceReviewDetailEntity.setCarType(tmsDeliveryPerformanceReviewDetailEntity.getCarType());
        deliveryPerformanceReviewDetailEntity.setCarStorage(tmsDeliveryPerformanceReviewDetailEntity.getCarStorage());
        deliveryPerformanceReviewDetailEntity.setDeliveryBatchId(tmsDeliveryPerformanceReviewDetailEntity.getDeliveryBatchId());
        deliveryPerformanceReviewDetailEntity.setDeliverySiteId(tmsDeliveryPerformanceReviewDetailEntity.getDeliverySiteId());
        deliveryPerformanceReviewDetailEntity.setSiteId(tmsDeliveryPerformanceReviewDetailEntity.getSiteId());
        deliveryPerformanceReviewDetailEntity.setSiteAddress(tmsDeliveryPerformanceReviewDetailEntity.getSiteAddress());
        deliveryPerformanceReviewDetailEntity.setOuterClientName(tmsDeliveryPerformanceReviewDetailEntity.getOuterClientName());
        deliveryPerformanceReviewDetailEntity.setOuterBrandName(tmsDeliveryPerformanceReviewDetailEntity.getOuterBrandName());
        deliveryPerformanceReviewDetailEntity.setBeginSiteId(tmsDeliveryPerformanceReviewDetailEntity.getBeginSiteId());
        deliveryPerformanceReviewDetailEntity.setBeginSiteName(tmsDeliveryPerformanceReviewDetailEntity.getBeginSiteName());
        deliveryPerformanceReviewDetailEntity.setPathCode(tmsDeliveryPerformanceReviewDetailEntity.getPathCode());
        deliveryPerformanceReviewDetailEntity.setPathName(tmsDeliveryPerformanceReviewDetailEntity.getPathName());
        deliveryPerformanceReviewDetailEntity.setDeliveryBatchType(tmsDeliveryPerformanceReviewDetailEntity.getDeliveryBatchType());
        deliveryPerformanceReviewDetailEntity.setSendWay(tmsDeliveryPerformanceReviewDetailEntity.getSendWay());
        deliveryPerformanceReviewDetailEntity.setOutDistance(tmsDeliveryPerformanceReviewDetailEntity.getOutDistance());
        deliveryPerformanceReviewDetailEntity.setPenaltyMoney(tmsDeliveryPerformanceReviewDetailEntity.getPenaltyMoney());
        deliveryPerformanceReviewDetailEntity.setSitePicReason(tmsDeliveryPerformanceReviewDetailEntity.getSitePicReason());
        deliveryPerformanceReviewDetailEntity.setTemperatureConditions(tmsDeliveryPerformanceReviewDetailEntity.getTemperatureConditions());
        deliveryPerformanceReviewDetailEntity.setSignOutTemperature(tmsDeliveryPerformanceReviewDetailEntity.getSignOutTemperature());
        deliveryPerformanceReviewDetailEntity.setSequence(tmsDeliveryPerformanceReviewDetailEntity.getSequence());
        deliveryPerformanceReviewDetailEntity.setSiteName(tmsDeliveryPerformanceReviewDetailEntity.getSiteName());
        deliveryPerformanceReviewDetailEntity.setSignInStatus(tmsDeliveryPerformanceReviewDetailEntity.getSignInStatus());
        deliveryPerformanceReviewDetailEntity.setSignInRemark(tmsDeliveryPerformanceReviewDetailEntity.getSignInRemark());
        deliveryPerformanceReviewDetailEntity.setNonFruitColdNum(tmsDeliveryPerformanceReviewDetailEntity.getNonFruitColdNum());
        deliveryPerformanceReviewDetailEntity.setNonFruitFreezeNum(tmsDeliveryPerformanceReviewDetailEntity.getNonFruitFreezeNum());

        return deliveryPerformanceReviewDetailEntity;
    }
}
