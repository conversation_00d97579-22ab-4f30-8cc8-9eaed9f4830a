package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.path.entity.PathQuotationEntity;
import net.summerfarm.tms.dao.TmsPathQuotation;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-05-15
 **/
@Mapper(componentModel = Constants.MapStructConstants.COMPONENT_MODEL_SPRING)
public interface TmsPathQuotationConverter {

    /**
     * entityList --> doList
     * @param pathQuotationEntityList
     * @return
     */
    List<TmsPathQuotation> entityToDoList(List<PathQuotationEntity> pathQuotationEntityList);


    /**
     * doListToEntity
     * @param quotationList
     * @return
     */
    List<PathQuotationEntity> doListToEntity(List<TmsPathQuotation> quotationList);
}
