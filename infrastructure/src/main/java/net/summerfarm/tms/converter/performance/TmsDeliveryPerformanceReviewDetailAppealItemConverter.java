package net.summerfarm.tms.converter.performance;

import net.summerfarm.manage.domain.performance.param.command.TmsDeliveryPerformanceReviewDetailAppealItemCommandParam;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppealItem;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-08-30 14:11:07
 * @version 1.0
 *
 */
public class TmsDeliveryPerformanceReviewDetailAppealItemConverter {


    public static List<DeliveryPerformanceReviewDetailAppealItemEntity> toTmsDeliveryPerformanceReviewDetailAppealItemEntityList(List<TmsDeliveryPerformanceReviewDetailAppealItem> tmsDeliveryPerformanceReviewDetailAppealItemList) {
        if (tmsDeliveryPerformanceReviewDetailAppealItemList == null) {
            return Collections.emptyList();
        }
        List<DeliveryPerformanceReviewDetailAppealItemEntity> tmsDeliveryPerformanceReviewDetailAppealItemEntityList = new ArrayList<>();
        for (TmsDeliveryPerformanceReviewDetailAppealItem tmsDeliveryPerformanceReviewDetailAppealItem : tmsDeliveryPerformanceReviewDetailAppealItemList) {
            tmsDeliveryPerformanceReviewDetailAppealItemEntityList.add(toTmsDeliveryPerformanceReviewDetailAppealItemEntity(tmsDeliveryPerformanceReviewDetailAppealItem));
        }
        return tmsDeliveryPerformanceReviewDetailAppealItemEntityList;
}


    public static DeliveryPerformanceReviewDetailAppealItemEntity toTmsDeliveryPerformanceReviewDetailAppealItemEntity(TmsDeliveryPerformanceReviewDetailAppealItem tmsDeliveryPerformanceReviewDetailAppealItem) {
        if (tmsDeliveryPerformanceReviewDetailAppealItem == null) {
             return null;
        }
        DeliveryPerformanceReviewDetailAppealItemEntity tmsDeliveryPerformanceReviewDetailAppealItemEntity = new DeliveryPerformanceReviewDetailAppealItemEntity();
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setId(tmsDeliveryPerformanceReviewDetailAppealItem.getId());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setCreateTime(tmsDeliveryPerformanceReviewDetailAppealItem.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setUpdateTime(tmsDeliveryPerformanceReviewDetailAppealItem.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setAppealId(tmsDeliveryPerformanceReviewDetailAppealItem.getAppealId());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setDeliverySiteId(tmsDeliveryPerformanceReviewDetailAppealItem.getDeliverySiteId());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setSiteId(tmsDeliveryPerformanceReviewDetailAppealItem.getSiteId());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setSequence(tmsDeliveryPerformanceReviewDetailAppealItem.getSequence());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setSiteAddress(tmsDeliveryPerformanceReviewDetailAppealItem.getSiteAddress());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setNonComplianceSitePicType(tmsDeliveryPerformanceReviewDetailAppealItem.getNonComplianceSitePicType());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setNonCompliancePic(tmsDeliveryPerformanceReviewDetailAppealItem.getNonCompliancePic());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setNonComplianceReason(tmsDeliveryPerformanceReviewDetailAppealItem.getNonComplianceReason());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setAppealPic(tmsDeliveryPerformanceReviewDetailAppealItem.getAppealPic());
        tmsDeliveryPerformanceReviewDetailAppealItemEntity.setAppealReason(tmsDeliveryPerformanceReviewDetailAppealItem.getAppealReason());
        return tmsDeliveryPerformanceReviewDetailAppealItemEntity;
    }





    public static TmsDeliveryPerformanceReviewDetailAppealItem toTmsDeliveryPerformanceReviewDetailAppealItem(DeliveryPerformanceReviewDetailAppealItemEntity param) {
        if (param == null) {
            return null;
        }
        TmsDeliveryPerformanceReviewDetailAppealItem tmsDeliveryPerformanceReviewDetailAppealItem = new TmsDeliveryPerformanceReviewDetailAppealItem();
        tmsDeliveryPerformanceReviewDetailAppealItem.setId(param.getId());
        tmsDeliveryPerformanceReviewDetailAppealItem.setCreateTime(param.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAppealItem.setUpdateTime(param.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAppealItem.setAppealId(param.getAppealId());
        tmsDeliveryPerformanceReviewDetailAppealItem.setDeliverySiteId(param.getDeliverySiteId());
        tmsDeliveryPerformanceReviewDetailAppealItem.setSiteId(param.getSiteId());
        tmsDeliveryPerformanceReviewDetailAppealItem.setSequence(param.getSequence());
        tmsDeliveryPerformanceReviewDetailAppealItem.setSiteAddress(param.getSiteAddress());
        tmsDeliveryPerformanceReviewDetailAppealItem.setNonComplianceSitePicType(param.getNonComplianceSitePicType());
        tmsDeliveryPerformanceReviewDetailAppealItem.setNonCompliancePic(param.getNonCompliancePic());
        tmsDeliveryPerformanceReviewDetailAppealItem.setNonComplianceReason(param.getNonComplianceReason());
        tmsDeliveryPerformanceReviewDetailAppealItem.setAppealPic(param.getAppealPic());
        tmsDeliveryPerformanceReviewDetailAppealItem.setAppealReason(param.getAppealReason());
        return tmsDeliveryPerformanceReviewDetailAppealItem;
    }
}
