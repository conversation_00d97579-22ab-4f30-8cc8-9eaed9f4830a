package net.summerfarm.tms.converter.performance;


import com.alibaba.schedulerx.shade.scala.annotation.meta.param;
import net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppeal;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealEntity;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 *
 */
public class TmsDeliveryPerformanceReviewDetailAppealConverter {



    public static List<DeliveryPerformanceReviewDetailAppealEntity> toTmsDeliveryPerformanceReviewDetailAppealEntityList(List<TmsDeliveryPerformanceReviewDetailAppeal> tmsDeliveryPerformanceReviewDetailAppealList) {
        if (tmsDeliveryPerformanceReviewDetailAppealList == null) {
            return Collections.emptyList();
        }
        List<DeliveryPerformanceReviewDetailAppealEntity> tmsDeliveryPerformanceReviewDetailAppealEntityList = new ArrayList<>();
        for (TmsDeliveryPerformanceReviewDetailAppeal tmsDeliveryPerformanceReviewDetailAppeal : tmsDeliveryPerformanceReviewDetailAppealList) {
            tmsDeliveryPerformanceReviewDetailAppealEntityList.add(toTmsDeliveryPerformanceReviewDetailAppealEntity(tmsDeliveryPerformanceReviewDetailAppeal));
        }
        return tmsDeliveryPerformanceReviewDetailAppealEntityList;
}


    public static DeliveryPerformanceReviewDetailAppealEntity toTmsDeliveryPerformanceReviewDetailAppealEntity(TmsDeliveryPerformanceReviewDetailAppeal tmsDeliveryPerformanceReviewDetailAppeal) {
        if (tmsDeliveryPerformanceReviewDetailAppeal == null) {
             return null;
        }
        DeliveryPerformanceReviewDetailAppealEntity tmsDeliveryPerformanceReviewDetailAppealEntity = new DeliveryPerformanceReviewDetailAppealEntity();
        tmsDeliveryPerformanceReviewDetailAppealEntity.setId(tmsDeliveryPerformanceReviewDetailAppeal.getId());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setCreateTime(tmsDeliveryPerformanceReviewDetailAppeal.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setUpdateTime(tmsDeliveryPerformanceReviewDetailAppeal.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setPerformanceReviewTaskId(tmsDeliveryPerformanceReviewDetailAppeal.getPerformanceReviewTaskId());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setDelPerfDetailReviewId(tmsDeliveryPerformanceReviewDetailAppeal.getDelPerfDetailReviewId());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setDeliveryBatchId(tmsDeliveryPerformanceReviewDetailAppeal.getDeliveryBatchId());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setStatus(tmsDeliveryPerformanceReviewDetailAppeal.getStatus());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setStoreNo(tmsDeliveryPerformanceReviewDetailAppeal.getStoreNo());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setStoreName(tmsDeliveryPerformanceReviewDetailAppeal.getStoreName());
        tmsDeliveryPerformanceReviewDetailAppealEntity.setAppealFailReason(tmsDeliveryPerformanceReviewDetailAppeal.getAppealFailReason());

        return tmsDeliveryPerformanceReviewDetailAppealEntity;
    }


    public static TmsDeliveryPerformanceReviewDetailAppeal toTmsDeliveryPerformanceReviewDetailAppeal(DeliveryPerformanceReviewDetailAppealEntity param) {
        if (param == null) {
            return null;
        }
        TmsDeliveryPerformanceReviewDetailAppeal tmsDeliveryPerformanceReviewDetailAppeal = new TmsDeliveryPerformanceReviewDetailAppeal();
        tmsDeliveryPerformanceReviewDetailAppeal.setId(param.getId());
        tmsDeliveryPerformanceReviewDetailAppeal.setCreateTime(param.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAppeal.setUpdateTime(param.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAppeal.setPerformanceReviewTaskId(param.getPerformanceReviewTaskId());
        tmsDeliveryPerformanceReviewDetailAppeal.setDelPerfDetailReviewId(param.getDelPerfDetailReviewId());
        tmsDeliveryPerformanceReviewDetailAppeal.setDeliveryBatchId(param.getDeliveryBatchId());
        tmsDeliveryPerformanceReviewDetailAppeal.setStatus(param.getStatus());
        tmsDeliveryPerformanceReviewDetailAppeal.setStoreNo(param.getStoreNo());
        tmsDeliveryPerformanceReviewDetailAppeal.setStoreName(param.getStoreName());
        tmsDeliveryPerformanceReviewDetailAppeal.setAppealFailReason(param.getAppealFailReason());
        return tmsDeliveryPerformanceReviewDetailAppeal;
    }
}
