package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsLackApprovedResponsible;
import net.summerfarm.tms.lack.entity.LackApprovedResponsibleEntity;

/**
 * Description: <br/>
 * date: 2023/9/11 13:55<br/>
 *
 * <AUTHOR> />
 */
public class TmsLackApprovedResponsibleConverter {

    public static TmsLackApprovedResponsible model2Entity(LackApprovedResponsibleEntity lackApprovedResponsibleEntity){
        if(lackApprovedResponsibleEntity == null){
            return null;
        }
        TmsLackApprovedResponsible tmsLackApprovedResponsible = new TmsLackApprovedResponsible();
        tmsLackApprovedResponsible.setId(lackApprovedResponsibleEntity.getId());
        tmsLackApprovedResponsible.setCreateTime(lackApprovedResponsibleEntity.getCreateTime());
        tmsLackApprovedResponsible.setUpdateTime(lackApprovedResponsibleEntity.getUpdateTime());
        tmsLackApprovedResponsible.setApprovedId(lackApprovedResponsibleEntity.getApprovedId());
        tmsLackApprovedResponsible.setResponsible(lackApprovedResponsibleEntity.getResponsible());
        tmsLackApprovedResponsible.setBuyOut(lackApprovedResponsibleEntity.getBuyOut());
        tmsLackApprovedResponsible.setBuyOutMoney(lackApprovedResponsibleEntity.getBuyOutMoney());

        return tmsLackApprovedResponsible;
    }


    public static LackApprovedResponsibleEntity model2Entity(TmsLackApprovedResponsible tmsLackApprovedResponsible){
        if(tmsLackApprovedResponsible == null){
            return null;
        }
        LackApprovedResponsibleEntity lackApprovedResponsibleEntity = new LackApprovedResponsibleEntity();
        lackApprovedResponsibleEntity.setId(tmsLackApprovedResponsible.getId());
        lackApprovedResponsibleEntity.setCreateTime(tmsLackApprovedResponsible.getCreateTime());
        lackApprovedResponsibleEntity.setUpdateTime(tmsLackApprovedResponsible.getUpdateTime());
        lackApprovedResponsibleEntity.setApprovedId(tmsLackApprovedResponsible.getApprovedId());
        lackApprovedResponsibleEntity.setResponsible(tmsLackApprovedResponsible.getResponsible());
        lackApprovedResponsibleEntity.setBuyOut(tmsLackApprovedResponsible.getBuyOut());
        lackApprovedResponsibleEntity.setBuyOutMoney(tmsLackApprovedResponsible.getBuyOutMoney());

        return lackApprovedResponsibleEntity;
    }
}
