package net.summerfarm.tms.converter;

import net.summerfarm.tms.alert.entity.DeliveryAlertEntity;
import net.summerfarm.tms.dao.CompleteDelivery;

/**
 * Description:配送提醒转换器
 * date: 2023/3/21 19:14
 *
 * <AUTHOR>
 */
public class CompleteDeliveryConverter {

    public static CompleteDelivery entity2do(DeliveryAlertEntity deliveryAlertEntity){
        if (deliveryAlertEntity == null){
            return null;
        }
        CompleteDelivery completeDelivery = new CompleteDelivery();
        completeDelivery.setId(deliveryAlertEntity.getId());
        completeDelivery.setStoreNo(deliveryAlertEntity.getStoreNo());
//        completeDelivery.setAreaNo();
        completeDelivery.setCompleteDeliveryTime(deliveryAlertEntity.getCompleteDeliveryTime());
        completeDelivery.setStatus(deliveryAlertEntity.getStatus());
        completeDelivery.setUpdater(deliveryAlertEntity.getUpdater());
        completeDelivery.setUpdateTime(deliveryAlertEntity.getUpdateTime());
        completeDelivery.setCreator(deliveryAlertEntity.getCreator());
        completeDelivery.setCreateTime(deliveryAlertEntity.getCreateTime());
        completeDelivery.setRegion(deliveryAlertEntity.getRegion());
        completeDelivery.setCity(deliveryAlertEntity.getCity());

        return completeDelivery;
    }

    public static DeliveryAlertEntity do2Entity(CompleteDelivery completeDelivery){
        if (completeDelivery == null){
            return null;
        }
        DeliveryAlertEntity deliveryAlertEntity = new DeliveryAlertEntity();
        deliveryAlertEntity.setId(completeDelivery.getId());
        deliveryAlertEntity.setRegion(completeDelivery.getRegion());
        deliveryAlertEntity.setStoreNo(completeDelivery.getStoreNo());
//        deliveryAlertEntity.setStoreName();
        deliveryAlertEntity.setCity(completeDelivery.getCity());
        deliveryAlertEntity.setCompleteDeliveryTime(completeDelivery.getCompleteDeliveryTime());
        deliveryAlertEntity.setStatus(completeDelivery.getStatus());
        deliveryAlertEntity.setUpdater(completeDelivery.getUpdater());
        deliveryAlertEntity.setUpdateTime(completeDelivery.getUpdateTime());
        deliveryAlertEntity.setCreator(completeDelivery.getCreator());
        deliveryAlertEntity.setCreateTime(completeDelivery.getCreateTime());
        return deliveryAlertEntity;
    }
}
