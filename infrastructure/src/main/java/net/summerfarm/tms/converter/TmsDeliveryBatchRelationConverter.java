package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliveryBatchRelation;
import net.summerfarm.tms.delivery.entity.DeliveryBatchRelationEntity;

/**
 * Description:配送批次关联关系DO转换器
 * date: 2023/8/8 11:10
 *
 * <AUTHOR>
 */
public class TmsDeliveryBatchRelationConverter {

    public static TmsDeliveryBatchRelation entity2do(DeliveryBatchRelationEntity deliveryBatchRelationEntity){
        TmsDeliveryBatchRelation tmsDeliveryBatchRelation = new TmsDeliveryBatchRelation();
        if (deliveryBatchRelationEntity == null){
            return null;
        }
        tmsDeliveryBatchRelation.setBatchId(deliveryBatchRelationEntity.getBatchId());
        tmsDeliveryBatchRelation.setRelateBatchId(deliveryBatchRelationEntity.getRelateBatchId());
        tmsDeliveryBatchRelation.setCreator(deliveryBatchRelationEntity.getCreator());
        return tmsDeliveryBatchRelation;
    }

    public static DeliveryBatchRelationEntity do2Entity(TmsDeliveryBatchRelation tmsDeliveryBatchRelation){
        if (tmsDeliveryBatchRelation == null){
            return null;
        }
        DeliveryBatchRelationEntity deliveryBatchRelationEntity = new DeliveryBatchRelationEntity();
        deliveryBatchRelationEntity.setId(tmsDeliveryBatchRelation.getId());
        deliveryBatchRelationEntity.setBatchId(tmsDeliveryBatchRelation.getBatchId());
        deliveryBatchRelationEntity.setRelateBatchId(tmsDeliveryBatchRelation.getRelateBatchId());
        deliveryBatchRelationEntity.setCreator(tmsDeliveryBatchRelation.getCreator());
        deliveryBatchRelationEntity.setCreateTime(tmsDeliveryBatchRelation.getCreateTime());
        return deliveryBatchRelationEntity;
    }
}
