package net.summerfarm.tms.converter.deliveryNoteTemplate;

import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplate;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateCommandParam;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
public class TmsDeliveryNoteTemplateConverter {

    private TmsDeliveryNoteTemplateConverter() {
        // 无需实现
    }




    public static List<TmsDeliveryNoteTemplateEntity> toTmsDeliveryNoteTemplateEntityList(List<TmsDeliveryNoteTemplate> tmsDeliveryNoteTemplateList) {
        if (tmsDeliveryNoteTemplateList == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryNoteTemplateEntity> tmsDeliveryNoteTemplateEntityList = new ArrayList<>();
        for (TmsDeliveryNoteTemplate tmsDeliveryNoteTemplate : tmsDeliveryNoteTemplateList) {
            tmsDeliveryNoteTemplateEntityList.add(toTmsDeliveryNoteTemplateEntity(tmsDeliveryNoteTemplate));
        }
        return tmsDeliveryNoteTemplateEntityList;
}


    public static TmsDeliveryNoteTemplateEntity toTmsDeliveryNoteTemplateEntity(TmsDeliveryNoteTemplate tmsDeliveryNoteTemplate) {
        if (tmsDeliveryNoteTemplate == null) {
             return null;
        }
        TmsDeliveryNoteTemplateEntity tmsDeliveryNoteTemplateEntity = new TmsDeliveryNoteTemplateEntity();
        tmsDeliveryNoteTemplateEntity.setId(tmsDeliveryNoteTemplate.getId());
        tmsDeliveryNoteTemplateEntity.setCreateTime(tmsDeliveryNoteTemplate.getCreateTime());
        tmsDeliveryNoteTemplateEntity.setUpdateTime(tmsDeliveryNoteTemplate.getUpdateTime());
        tmsDeliveryNoteTemplateEntity.setDeliveryNoteName(tmsDeliveryNoteTemplate.getDeliveryNoteName());
        tmsDeliveryNoteTemplateEntity.setAppSource(tmsDeliveryNoteTemplate.getAppSource());
        tmsDeliveryNoteTemplateEntity.setUseState(tmsDeliveryNoteTemplate.getUseState());
        tmsDeliveryNoteTemplateEntity.setLastOperatorName(tmsDeliveryNoteTemplate.getLastOperatorName());
        tmsDeliveryNoteTemplateEntity.setFrontPageStr(tmsDeliveryNoteTemplate.getFrontPageStr());
        tmsDeliveryNoteTemplateEntity.setShowPriceFlag(tmsDeliveryNoteTemplate.getShowPriceFlag());
        tmsDeliveryNoteTemplateEntity.setScopeType(tmsDeliveryNoteTemplate.getScopeType());
        tmsDeliveryNoteTemplateEntity.setBelongBusinessName(tmsDeliveryNoteTemplate.getBelongBusinessName());
        tmsDeliveryNoteTemplateEntity.setShowPriceTemplateOssUrl(tmsDeliveryNoteTemplate.getShowPriceTemplateOssUrl());
        tmsDeliveryNoteTemplateEntity.setNoShowPriceTemplateOssUrl(tmsDeliveryNoteTemplate.getNoShowPriceTemplateOssUrl());
        return tmsDeliveryNoteTemplateEntity;
    }








    public static TmsDeliveryNoteTemplate toTmsDeliveryNoteTemplate(TmsDeliveryNoteTemplateCommandParam param) {
        if (param == null) {
            return null;
        }
        TmsDeliveryNoteTemplate tmsDeliveryNoteTemplate = new TmsDeliveryNoteTemplate();
        tmsDeliveryNoteTemplate.setId(param.getId());
        tmsDeliveryNoteTemplate.setCreateTime(param.getCreateTime());
        tmsDeliveryNoteTemplate.setUpdateTime(param.getUpdateTime());
        tmsDeliveryNoteTemplate.setDeliveryNoteName(param.getDeliveryNoteName());
        tmsDeliveryNoteTemplate.setAppSource(param.getAppSource());
        tmsDeliveryNoteTemplate.setUseState(param.getUseState());
        tmsDeliveryNoteTemplate.setLastOperatorName(param.getLastOperatorName());
        tmsDeliveryNoteTemplate.setFrontPageStr(param.getFrontPageStr());
        tmsDeliveryNoteTemplate.setShowPriceFlag(param.getShowPriceFlag());
        tmsDeliveryNoteTemplate.setScopeType(param.getScopeType());
        tmsDeliveryNoteTemplate.setBelongBusinessName(param.getBelongBusinessName());
        tmsDeliveryNoteTemplate.setShowPriceTemplateOssUrl(param.getShowPriceTemplateOssUrl());
        tmsDeliveryNoteTemplate.setNoShowPriceTemplateOssUrl(param.getNoShowPriceTemplateOssUrl());
        return tmsDeliveryNoteTemplate;
    }
}
