package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsTrunkCityDistRelationship;
import net.summerfarm.tms.dist.entity.TrunkCityDistRelationshipEntity;

/**
 * Description: 关系转换类<br/>
 * date: 2024/4/15 11:17<br/>
 *
 * <AUTHOR> />
 */
public class TmsTrunkCityDistRelationshipConverter {

    public static TmsTrunkCityDistRelationship entity2Model(TrunkCityDistRelationshipEntity entity) {
        if(entity == null){
            return null;
        }
        TmsTrunkCityDistRelationship relationship = new TmsTrunkCityDistRelationship();
        relationship.setId(entity.getId());
        relationship.setTrunkDistId(entity.getTrunkDistId());
        relationship.setCityDistId(entity.getCityDistId());
        relationship.setOuterOrderId(entity.getOuterOrderId());

        return relationship;
    }

    public static TrunkCityDistRelationshipEntity model2Entity(TmsTrunkCityDistRelationship relationship) {
        if(relationship == null){
            return null;
        }

        TrunkCityDistRelationshipEntity model = new TrunkCityDistRelationshipEntity();

        model.setId(relationship.getId());
        model.setCreateTime(relationship.getCreateTime());
        model.setUpdateTime(relationship.getUpdateTime());
        model.setTrunkDistId(relationship.getTrunkDistId());
        model.setCityDistId(relationship.getCityDistId());
        model.setOuterOrderId(relationship.getOuterOrderId());

        return model;
    }
}
