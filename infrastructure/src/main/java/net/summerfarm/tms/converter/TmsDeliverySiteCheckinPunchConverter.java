package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliverySiteCheckinPunch;
import net.summerfarm.tms.delivery.entity.DeliverySiteCheckinPunchEntity;

/**
 * Description: <br/>
 * date: 2023/10/26 17:36<br/>
 *
 * <AUTHOR> />
 */
public class TmsDeliverySiteCheckinPunchConverter {

    public static DeliverySiteCheckinPunchEntity model2Entity(TmsDeliverySiteCheckinPunch model){
        if(model == null){
            return null;
        }
        DeliverySiteCheckinPunchEntity entity = new DeliverySiteCheckinPunchEntity();

        entity.setId(model.getId());
        entity.setCreateTime(model.getCreateTime());
        entity.setUpdateTime(model.getUpdateTime());
        entity.setDeliverySiteId(model.getDeliverySiteId());
        entity.setPunchRange(model.getPunchRange());
        entity.setPunchAddress(model.getPunchAddress());
        entity.setPunchPoi(model.getPunchPoi());
        entity.setDistanceToSite(model.getDistanceToSite());
        entity.setExceedReason(model.getExceedReason());

        return entity;
    }


    public static TmsDeliverySiteCheckinPunch entity2Model(DeliverySiteCheckinPunchEntity entity){
        if(entity == null){
            return null;
        }
        TmsDeliverySiteCheckinPunch model = new TmsDeliverySiteCheckinPunch();

        model.setId(entity.getId());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        model.setDeliverySiteId(entity.getDeliverySiteId());
        model.setPunchRange(entity.getPunchRange());
        model.setPunchAddress(entity.getPunchAddress());
        model.setPunchPoi(entity.getPunchPoi());
        model.setDistanceToSite(entity.getDistanceToSite());
        model.setExceedReason(entity.getExceedReason());

        return model;
    }
}
