package net.summerfarm.tms.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.lack.LackApprovedRepository;
import net.summerfarm.tms.base.MqEvent;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.DeliveryBatchDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.lack.entity.LackApprovedEntity;
import net.summerfarm.tms.message.out.StockInStoreMessage;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@MqListener(consumerGroup = MqConstants.ConsumeGroup.GID_TMS, topic = MqConstants.Topic.TMS_LIST,
        consumeThreadMin = 2,
        consumeThreadMax = 2)
public class WmsOutStockMsgListener extends AbstractMqListener<MqEvent> {

    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    private LackApprovedRepository lackApprovedRepository;
    @Resource
    private DeliveryBatchDomainService deliveryBatchDomainService;

    @Override
    public void process(MqEvent mqEvent) {
        log.info("rocketmq receive：{}", JSON.toJSONString(mqEvent));

        String msg = getMsg(mqEvent);
        if (MqConstants.Event.WAREHOUSE_TASK_COMPLETE == mqEvent.getEvent()) {

            JSONObject jsonObject = JSONObject.parseObject(msg);
            String outerOrderId = (String) jsonObject.get("outerOrderId");
            Integer source = (Integer) jsonObject.get("source");
            DistOrderSourceEnum sourceEnum = DistOrderSourceEnum.getDistOrderSourceByCode(source);
            //查询委托单信息
            DistOrderEntity distOrderEntity = distOrderRepository.queryByUk(outerOrderId, sourceEnum, null, null);
            if (distOrderEntity == null) {
                log.info("outerOrderId:{},source:{},收到WMS任务完成通知,仓库未发起预约干线物流", outerOrderId, sourceEnum.getName());
                return;
            }
            if (distOrderEntity.isInvalid()) {
                log.warn("承运单:{} 已经失效,忽略WMS任务:{} 完成消息", distOrderEntity.getDistId(), outerOrderId);
                return;
            }
            if (distOrderEntity.isComplete()) {
                log.warn("distId:{},收到WMS任务:{} 完成重复通知,委托单已完成", distOrderEntity.getDistId(), outerOrderId);
                return;
            }
            //查询关联调度单是否完成
            Boolean isBatchFinish = deliveryBatchDomainService.queryBatchFinishByDistId(distOrderEntity.getDistId());
            if (!isBatchFinish) {
                log.info("distId:{},关联调度单未完成", distOrderEntity.getDistId());
                return;
            }
            log.info("distId:{},关联调度单已完成,委托单已完成", distOrderEntity.getDistId());
            distOrderEntity.complete();
            distOrderEntity.setUpdateTime(null);
            //更新委托单信息
            distOrderRepository.update(distOrderEntity);
        }else if(MqConstants.Event.WAREHOUSE_IN_STOCK_COMPLETE == mqEvent.getEvent()){

            //更新缺货入库信息
            StockInStoreMessage stockInStoreMessage = JSONObject.parseObject(JSON.toJSONString(mqEvent.getData()), StockInStoreMessage.class);
            Integer operationType = stockInStoreMessage.getOperationType();
            //缺货入库
            if(Objects.equals(operationType,20)){
                if(stockInStoreMessage.getSourceId() == null){
                    log.info("缺货入库:sourceId为空");
                    return;
                }
                LackApprovedEntity lackApprovedEntity = lackApprovedRepository.query(Long.parseLong(stockInStoreMessage.getSourceId()));
                if(lackApprovedEntity == null){
                    log.info("未查询到缺货入库信息：id为：{}",stockInStoreMessage.getSourceId());
                    LogConfigHolder.removeInboundFlag();
                    return;
                }
                if(CollectionUtils.isEmpty(stockInStoreMessage.getTaskId())){
                    log.info("未查询到缺货入库信息：taskId为空");
                    LogConfigHolder.removeInboundFlag();
                    return;
                }
                lackApprovedEntity.setStockTaskId(stockInStoreMessage.getTaskId().get(0));
                lackApprovedRepository.update(lackApprovedEntity);
            }
        }
    }

    private String getMsg(MqEvent mqEvent) {
        String msg;
        if (mqEvent.getData() instanceof String) {
            msg = String.valueOf(mqEvent.getData());
        } else {
            msg = JSONObject.toJSONString(mqEvent.getData());
        }
        return msg;
    }
}
