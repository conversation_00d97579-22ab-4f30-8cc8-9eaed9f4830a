package net.summerfarm.tms.utils;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-04-26 23:14
 */
public interface ExBaseMapper<T> extends BaseMapper<T> {

  /**
   * 以下定义的 4个 default method, copy from {@link com.baomidou.mybatisplus.extension.toolkit.ChainWrappers}
   */
  default QueryChainWrapper<T> queryChain() {
    return new QueryChainWrapper<>(this);
  }

  default LambdaQueryChainWrapper<T> lambdaQueryChain() {
    return new LambdaQueryChainWrapper<>(this);
  }

  default UpdateChainWrapper<T> updateChain() {
    return new UpdateChainWrapper<>(this);
  }

  default LambdaUpdateChainWrapper<T> lambdaUpdateChain() {
    return new LambdaUpdateChainWrapper<>(this);
  }

  /**
   * @param entityList
   * @return
   */
  int insertBatchSomeColumn(List<T> entityList);

  /**
   * @param entity
   * @return
   */
  int alwaysUpdateSomeColumnById(@Param(Constants.ENTITY) T entity);

  /**
   * @param entity
   * @return
   */
  int deleteByIdWithFill(T entity);

}
