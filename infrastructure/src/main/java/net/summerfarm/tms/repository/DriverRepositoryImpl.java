package net.summerfarm.tms.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import net.summerfarm.tms.base.car.CarRepository;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.driver.DriverRepository;
import net.summerfarm.tms.base.driver.entity.DriverAccountEntity;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.driver.enums.BusinessTypeEnum;
import net.summerfarm.tms.base.driver.enums.StateEnum;
import net.summerfarm.tms.converter.TmsDriverCarCarrierConverter;
import net.summerfarm.tms.converter.TmsDriverConverter;
import net.summerfarm.tms.dao.TmsDriver;
import net.summerfarm.tms.dao.TmsDriverAccount;
import net.summerfarm.tms.dao.TmsDriverCarCarrierMapping;
import net.summerfarm.tms.enums.DriverEnums;
import net.summerfarm.tms.enums.TmsDriverStatus;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.mapper.TmsDriverAccountMapper;
import net.summerfarm.tms.mapper.TmsDriverCarCarrierMappingMapper;
import net.summerfarm.tms.mapper.TmsDriverMapper;
import net.summerfarm.tms.query.base.driver.DriverQuery;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/7/13 15:09<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DriverRepositoryImpl implements DriverRepository {

    @Autowired
    private TmsDriverMapper tmsDriverMapper;

    @Autowired
    private TmsDriverAccountMapper tmsDriverAccountMapper;

    @Autowired
    private TmsDriverCarCarrierMappingMapper tmsDriverCarCarrierMappingMapper;

    @Autowired
    private CarRepository carRepository;

    @Override
    public Long save(DriverEntity driverEntity) {
        Long cityWarehouseSiteId = driverEntity.getCityWarehouseSiteId();
        Integer businessType = driverEntity.getBusinessType();

        LocalDateTime now = LocalDateTime.now();

        TmsDriver tmsDriver = TmsDriverConverter.entity2TmsDriver(driverEntity);
        tmsDriver.setCreateTime(now);
        tmsDriverMapper.insert(tmsDriver);

        TmsDriverCarCarrierMapping tmsDriverCarCarrierMapping = TmsDriverCarCarrierConverter.entity2CityDriverCarCarrier(driverEntity);
        tmsDriverCarCarrierMapping.setCreateTime(now);
        tmsDriverCarCarrierMapping.setWarehouseSiteId(cityWarehouseSiteId);
        tmsDriverCarCarrierMapping.setTmsDriverId(tmsDriver.getId());
        tmsDriverCarCarrierMapping.setState(StateEnum.valid.getCode());

        //如果是干线城配的话需要生成多条映射关系
        if (BusinessTypeEnum.trunkAndCity.getCode().equals(businessType)) {
            tmsDriverCarCarrierMapping.setBusinessType(BusinessTypeEnum.city.getCode());
            tmsDriverCarCarrierMappingMapper.insert(tmsDriverCarCarrierMapping);
        } else if (BusinessTypeEnum.city.getCode().equals(businessType)) {
            //tmsDriverCarCarrierMapping.setBusinessType(driverDO.getBusinessType());
            tmsDriverCarCarrierMappingMapper.insert(tmsDriverCarCarrierMapping);
        }

        saveDriverAccount(driverEntity, tmsDriver);

        return tmsDriver.getId();
    }

    private void saveDriverAccount(DriverEntity driverEntity, TmsDriver tmsDriver) {
        if (Objects.nonNull(driverEntity.getDriverAccountEntity())) {
            DriverAccountEntity driverAccountEntity = driverEntity.getDriverAccountEntity();
            TmsDriverAccount tmsDriverAccount = new TmsDriverAccount();
            tmsDriverAccount.setDriverId(tmsDriver.getId());
            tmsDriverAccount.setAccount(driverAccountEntity.getAccount());
            tmsDriverAccount.setAccountBank(driverAccountEntity.getAccountBank());
            tmsDriverAccount.setAccountAscription(driverAccountEntity.getAccountAscription());
            tmsDriverAccount.setPayType(driverAccountEntity.getPayType());
            tmsDriverAccount.setAccountName(driverAccountEntity.getAccountName());
            tmsDriverAccountMapper.insert(tmsDriverAccount);
        }
    }

    @Override
    public void edit(DriverEntity driverEntity) {
        TmsDriver otherTmsDriver = tmsDriverMapper.selectOtherByPhone(driverEntity.getId(), driverEntity.getPhone());
        if (otherTmsDriver != null) {
            throw new TmsRuntimeException("已存在此手机号");
        }
        TmsDriver toTmsDriver = TmsDriverConverter.entity2TmsDriver(driverEntity);
        TmsDriverCarCarrierMapping tmsDriverCarCarrierMapping = TmsDriverCarCarrierConverter.entity2CityDriverCarCarrier(driverEntity);

        Integer newBusinessType = toTmsDriver.getBusinessType();
        Long warehouseSiteId = tmsDriverCarCarrierMapping.getWarehouseSiteId();

        TmsDriver tmsDriver = tmsDriverMapper.selectByPrimaryKey(toTmsDriver.getId());
        Integer oldBusinessType = tmsDriver.getBusinessType();

        LocalDateTime now = LocalDateTime.now();
        tmsDriver.setUpdateTime(now);
        tmsDriver.setAdminId(toTmsDriver.getAdminId());
        tmsDriver.setBusinessType(newBusinessType);
        tmsDriver.setName(toTmsDriver.getName());
        tmsDriver.setPhone(toTmsDriver.getPhone());
        tmsDriver.setCooperationCycle(toTmsDriver.getCooperationCycle());
        tmsDriver.setStatus(toTmsDriver.getStatus());
        tmsDriver.setIdCard(toTmsDriver.getIdCard());
        tmsDriver.setIdCardFrontPic(toTmsDriver.getIdCardFrontPic());
        tmsDriver.setIdCardBehindPic(toTmsDriver.getIdCardBehindPic());
        tmsDriver.setDriverPics(toTmsDriver.getDriverPics());
        tmsDriver.setCitySiteId(toTmsDriver.getCitySiteId());
        tmsDriver.setKeepTemperatureMethodAudit(toTmsDriver.getKeepTemperatureMethodAudit());
        if (toTmsDriver.getCitySiteId() != null) {
            tmsDriver.setCityCarrierId(toTmsDriver.getCityCarrierId());
        }

        tmsDriverMapper.updateByPrimaryKey(tmsDriver);

        if (!oldBusinessType.equals(newBusinessType)) {
            //如果之前是干线，需要新增映射关系
            if (oldBusinessType.equals(BusinessTypeEnum.trunk.getCode())) {
                tmsDriverCarCarrierMapping.setId(null);
                tmsDriverCarCarrierMapping.setCreateTime(now);
                tmsDriverCarCarrierMapping.setWarehouseSiteId(warehouseSiteId);
                tmsDriverCarCarrierMapping.setTmsDriverId(tmsDriver.getId());
                tmsDriverCarCarrierMapping.setState(StateEnum.valid.getCode());
                tmsDriverCarCarrierMapping.setBusinessType(BusinessTypeEnum.city.getCode());
                tmsDriverCarCarrierMappingMapper.insert(tmsDriverCarCarrierMapping);
            }
            //之前是城配，现在变成干线，需要删除映射
            if ((oldBusinessType.equals(BusinessTypeEnum.trunkAndCity.getCode()) && newBusinessType.equals(BusinessTypeEnum.trunk.getCode())) ||
                    (oldBusinessType.equals(BusinessTypeEnum.city.getCode()) && newBusinessType.equals(BusinessTypeEnum.trunk.getCode()))) {
                TmsDriverCarCarrierMapping dCarCarr = new TmsDriverCarCarrierMapping(tmsDriver.getId(), StateEnum.valid.getCode(), BusinessTypeEnum.city.getCode());
                TmsDriverCarCarrierMapping driverCarCarrierData = tmsDriverCarCarrierMappingMapper.selectByDriverCarCarrierMapping(dCarCarr);
                driverCarCarrierData.setState(StateEnum.invalid.getCode());
                driverCarCarrierData.setUpdateTime(now);
                tmsDriverCarCarrierMappingMapper.updateByPrimaryKeySelective(driverCarCarrierData);
            }
        } else {
            if (tmsDriverCarCarrierMapping.getId() != null) {
                TmsDriverCarCarrierMapping dCarCarr = tmsDriverCarCarrierMappingMapper.selectByPrimaryKey(tmsDriverCarCarrierMapping.getId());
                dCarCarr.setTmsDriverId(tmsDriver.getId());
                dCarCarr.setState(StateEnum.valid.getCode());
                dCarCarr.setBusinessType(BusinessTypeEnum.city.getCode());
                dCarCarr.setCarrierId(tmsDriverCarCarrierMapping.getCarrierId());
                dCarCarr.setWarehouseSiteId(warehouseSiteId);
                dCarCarr.setTmsCarId(tmsDriverCarCarrierMapping.getTmsCarId());
                //修改城配信息
                tmsDriverCarCarrierMappingMapper.updateByPrimaryKey(dCarCarr);
            }
        }
        // 删除司机打款信息
        tmsDriverAccountMapper.delete(new LambdaQueryWrapper<TmsDriverAccount>()
                .eq(TmsDriverAccount::getDriverId, tmsDriver.getId()));
        // 保存司机打款信息
        saveDriverAccount(driverEntity, tmsDriver);

    }

    @Override
    public int findByPhone(String phone) {
        TmsDriver tmsDriver = new TmsDriver();
        tmsDriver.setPhone(phone);

        TmsDriver tmsDriverData = tmsDriverMapper.selectByTmsDriver(tmsDriver);
        return tmsDriverData != null ? 1 : 0;
    }

    @Override
    public PageInfo<DriverEntity> queryPage(DriverQuery driverQuery) {
        TmsDriver tmsDriver = new TmsDriver();
        BeanUtils.copyProperties(driverQuery, tmsDriver);
        PageHelper.startPage(driverQuery.getPageIndex(), driverQuery.getPageSize());
        List<TmsDriver> tmsDrivers = tmsDriverMapper.selectListByTmsDriver(driverQuery);

        //转化
        PageInfo<TmsDriver> pageInfo = PageInfoHelper.createPageInfo(tmsDrivers);
        List<DriverEntity> driverEntityList = tmsDrivers.stream().map(TmsDriverConverter::tmsDriver2Entity).collect(Collectors.toList());

        PageInfo<DriverEntity> driverEntityPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, driverEntityPageInfo);
        driverEntityPageInfo.setList(driverEntityList);

        return driverEntityPageInfo;
    }

    @Override
    public DriverEntity driverDetail(Long id) {

        TmsDriver tmsDriver = tmsDriverMapper.selectByPrimaryKey(id);
        DriverEntity driverEntity = TmsDriverConverter.tmsDriver2Entity(tmsDriver);
        List<TmsDriverAccount> tmsDriverAccounts = tmsDriverAccountMapper.selectList(new LambdaQueryWrapper<TmsDriverAccount>()
                .eq(TmsDriverAccount::getDriverId, id));
        if (!CollectionUtils.isEmpty(tmsDriverAccounts)) {
            TmsDriverAccount tmsDriverAccount = tmsDriverAccounts.get(0);
            DriverAccountEntity driverAccountEntity = new DriverAccountEntity();
            driverAccountEntity.setAccount(tmsDriverAccount.getAccount());
            driverAccountEntity.setAccountName(tmsDriverAccount.getAccountName());
            driverAccountEntity.setAccountAscription(tmsDriverAccount.getAccountAscription());
            driverAccountEntity.setAccountBank(tmsDriverAccount.getAccountBank());
            driverAccountEntity.setPayType(tmsDriverAccount.getPayType());
            driverEntity.setDriverAccountEntity(driverAccountEntity);
        }
        return driverEntity;
    }

    @Override
    public List<DriverEntity> getDriverByCarId(Long carId) {
        TmsDriverCarCarrierMapping mapping = new TmsDriverCarCarrierMapping();
        mapping.setState(StateEnum.valid.getCode());
        mapping.setTmsCarId(carId);
        //获取映射关系
        List<TmsDriverCarCarrierMapping> dCarCarrDatas = tmsDriverCarCarrierMappingMapper.selectListByDriverCarCarrierMapping(mapping);

        List<DriverEntity> driverEntityList = new ArrayList<>();

        dCarCarrDatas.stream().filter(mappingData -> mappingData.getTmsDriverId() != null).forEach(dCarCarrData -> {
            //获取司机信息
            TmsDriver tmsDriver = tmsDriverMapper.selectByPrimaryKey(dCarCarrData.getTmsDriverId());
            DriverEntity driverEntity = TmsDriverConverter.tmsDriver2Entity(tmsDriver);

            driverEntityList.add(driverEntity);
        });

        return driverEntityList;
    }

    @Override
    public Map<Long, List<DriverEntity>> getDriverByCarIds(Set<Long> ids) {
        Map<Long, List<DriverEntity>> response = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ids)) {
            return response;
        }
        List<TmsDriverCarCarrierMapping> tmsDriverCarCarrierMappings = tmsDriverCarCarrierMappingMapper
                .selectList(new LambdaQueryWrapper<TmsDriverCarCarrierMapping>()
                        .eq(TmsDriverCarCarrierMapping::getState, StateEnum.valid.getCode())
                        .in(TmsDriverCarCarrierMapping::getTmsCarId, ids));
        if (CollectionUtils.isEmpty(tmsDriverCarCarrierMappings)) {
            return response;
        }
        Set<Long> driverIds = tmsDriverCarCarrierMappings.stream()
                .filter(a -> Objects.nonNull(a.getTmsDriverId()))
                .map(TmsDriverCarCarrierMapping::getTmsDriverId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(driverIds)) {
            return response;
        }
        List<TmsDriver> tmsDrivers = tmsDriverMapper.selectBatchIds(driverIds);
        if (CollectionUtils.isEmpty(tmsDrivers)) {
            return response;
        }
        Map<Long, TmsDriver> driverMap = tmsDrivers.stream()
                .collect(Collectors.toMap(TmsDriver::getId, Function.identity(), (a, b) -> a));
        Map<Long, List<TmsDriverCarCarrierMapping>> collect = tmsDriverCarCarrierMappings.stream()
                .collect(Collectors.groupingBy(TmsDriverCarCarrierMapping::getTmsCarId));
        collect.forEach((carId, mappingList) -> {
            List<DriverEntity> valueList = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(mappingList)) {
                for (TmsDriverCarCarrierMapping tmsDriverCarCarrierMapping : mappingList) {
                    valueList.add(TmsDriverConverter.tmsDriver2Entity(driverMap.get(tmsDriverCarCarrierMapping.getTmsDriverId())));
                }
                response.put(carId, valueList);
            }
        });

        return response;
    }

    @Override
    public void deleteBindRelation(Long cityMappingId) {
        TmsDriverCarCarrierMapping tmsDriverCarCarrierMapping = tmsDriverCarCarrierMappingMapper.selectByPrimaryKey(cityMappingId);
        tmsDriverCarCarrierMapping.setTmsCarId(null);
        tmsDriverCarCarrierMapping.setUpdateTime(LocalDateTime.now());
        tmsDriverCarCarrierMappingMapper.updateByPrimaryKey(tmsDriverCarCarrierMapping);
    }

    @Override
    public Boolean isDriverById(Long id) {
        TmsDriver tmsDriver = tmsDriverMapper.selectByPrimaryKey(id);
        return tmsDriver != null;
    }

    @Override
    public DriverEntity getDriverCityMapping(Long driverId) {
        TmsDriverCarCarrierMapping dCarCarrData = tmsDriverCarCarrierMappingMapper.selectByDriverCarCarrierMapping
                (new TmsDriverCarCarrierMapping(driverId, StateEnum.valid.getCode(), BusinessTypeEnum.city.getCode()));

        return TmsDriverCarCarrierConverter.cityDriverCarCarrier2Entity(dCarCarrData);
    }

    @Override
    public List<DriverEntity> queryMappingListByDriverIdList(List<Long> driverIdList) {
        if(CollectionUtils.isEmpty(driverIdList)){
            return new ArrayList<>();
        }
        List<TmsDriverCarCarrierMapping> tmsDriverCarCarrierMappings = tmsDriverCarCarrierMappingMapper.selectList(new LambdaQueryWrapper<TmsDriverCarCarrierMapping>()
                .eq(TmsDriverCarCarrierMapping::getState, StateEnum.valid.getCode())
                .eq(TmsDriverCarCarrierMapping::getBusinessType, BusinessTypeEnum.city.getCode())
                .in(TmsDriverCarCarrierMapping::getTmsDriverId, driverIdList)
        );
        return tmsDriverCarCarrierMappings.stream().map(TmsDriverCarCarrierConverter::cityDriverCarCarrier2Entity).collect(Collectors.toList());
    }

    @Override
    public List<DriverEntity> listAllByCitySiteId(Long siteId) {
        //根据城配仓点位查询对应有效的司机信息
        TmsDriverCarCarrierMapping tmsDriverCarCarrierMapping = new TmsDriverCarCarrierMapping();
        tmsDriverCarCarrierMapping.setWarehouseSiteId(siteId);
        tmsDriverCarCarrierMapping.setState(StateEnum.valid.getCode());
        tmsDriverCarCarrierMapping.setBusinessType(BusinessTypeEnum.city.getCode());
        List<TmsDriverCarCarrierMapping> tmsDriverCarCarrierMappings = tmsDriverCarCarrierMappingMapper.selectListByDriverCarCarrierMapping(tmsDriverCarCarrierMapping);

        Map<Long, Long> driverCarMap = tmsDriverCarCarrierMappings.stream().filter(driverCar -> driverCar.getTmsDriverId() != null && driverCar.getTmsCarId() != null)
                .collect(Collectors.toMap(TmsDriverCarCarrierMapping::getTmsDriverId, TmsDriverCarCarrierMapping::getTmsCarId));
        List<Long> driverIds = tmsDriverCarCarrierMappings.stream().map(TmsDriverCarCarrierMapping::getTmsDriverId).collect(Collectors.toList());
        List<Long> carIds = tmsDriverCarCarrierMappings.stream().map(TmsDriverCarCarrierMapping::getTmsCarId).collect(Collectors.toList());

        if (driverIds.size() == 0) {
            return new ArrayList<DriverEntity>();
        }
        List<TmsDriver> tmsDrivers = tmsDriverMapper.selectByIdList(driverIds);
        List<CarEntity> tmsCars = carRepository.getCarByIdList(carIds);

        Map<Long, List<CarEntity>> carIdTmsCarMap = tmsCars.stream().collect(Collectors.groupingBy(CarEntity::getId));

        List<DriverEntity> driverEntityList = tmsDrivers.stream().map(TmsDriverConverter::tmsDriver2Entity).collect(Collectors.toList());

        for (DriverEntity driverEntity : driverEntityList) {
            Long carId = driverCarMap.get(driverEntity.getId());
            List<CarEntity> carEntities = carIdTmsCarMap.get(carId);
            if (!CollectionUtils.isEmpty(carEntities)) {
                driverEntity.setCityCarEntity(carEntities.get(0));
            }
        }
        return driverEntityList;
    }

    @Override
    public List<DriverEntity> searchByNameOrPhone(String nameOrPhone) {
        List<TmsDriver> tmsDrivers = tmsDriverMapper.selectList(new LambdaQueryWrapper<TmsDriver>()
                .eq(TmsDriver::getStatus, TmsDriverStatus.valid.getCode())
                .in(TmsDriver::getBusinessType, Arrays.asList(BusinessTypeEnum.trunk.getCode(), BusinessTypeEnum.trunkAndCity.getCode()))
                .and(i -> i.like(TmsDriver::getName, nameOrPhone).or(s -> s.like(TmsDriver::getPhone, nameOrPhone))));

        return tmsDrivers.stream().map(TmsDriverConverter::tmsDriver2Entity).collect(Collectors.toList());
    }

    @Override
    public List<DriverEntity> queryList(DriverQuery driverQuery) {
        List<TmsDriver> tmsDrivers = tmsDriverMapper.selectList(new LambdaQueryWrapper<TmsDriver>()
                .eq(driverQuery.getId() != null, TmsDriver::getId, driverQuery.getId())
                .eq(!StringUtils.isEmpty(driverQuery.getPhone()), TmsDriver::getPhone, driverQuery.getPhone())
                .eq(driverQuery.getCooperationCycle() != null, TmsDriver::getCooperationCycle, driverQuery.getCooperationCycle())
                .eq(driverQuery.getStatus() != null, TmsDriver::getStatus, driverQuery.getStatus())
                .in(driverQuery.getBusinessType() != null, TmsDriver::getBusinessType, Arrays.asList(driverQuery.getBusinessType(), BusinessTypeEnum.trunkAndCity.getCode()))
                .eq(driverQuery.getCitySiteId() != null, TmsDriver::getCitySiteId, driverQuery.getCitySiteId())
                .eq(driverQuery.getCityCarrierId() != null, TmsDriver::getCityCarrierId, driverQuery.getCityCarrierId())
                .like(!StringUtils.isEmpty(driverQuery.getName()), TmsDriver::getName, driverQuery.getName())
                .in(!CollectionUtils.isEmpty(driverQuery.getIds()), TmsDriver::getId, driverQuery.getIds())
                .in(!CollectionUtils.isEmpty(driverQuery.getCitySiteIds()), TmsDriver::getCitySiteId, driverQuery.getCitySiteIds())
        );
        List<DriverEntity> driverEntityList = tmsDrivers.stream().map(TmsDriverConverter::tmsDriver2Entity).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmsDrivers)) {
            return driverEntityList;
        }
        List<TmsDriverAccount> tmsDriverAccounts = tmsDriverAccountMapper.selectList(new LambdaQueryWrapper<TmsDriverAccount>().in(TmsDriverAccount::getDriverId,
                tmsDrivers.stream().map(TmsDriver::getId).collect(Collectors.toSet())));
        if (CollectionUtils.isEmpty(tmsDriverAccounts)) {
            return driverEntityList;
        }
        Map<Long, List<TmsDriverAccount>> driverAccountMap = tmsDriverAccounts.stream().collect(Collectors.groupingBy(TmsDriverAccount::getDriverId));
        for (DriverEntity driverEntity : driverEntityList) {
            List<TmsDriverAccount> driverAccountList = driverAccountMap.get(driverEntity.getId());
            if (CollectionUtils.isEmpty(driverAccountList)) {
                continue;
            }
            TmsDriverAccount tmsDriverAccount = driverAccountList.get(0);
            DriverAccountEntity driverAccountEntity = new DriverAccountEntity();
            driverAccountEntity.setPayType(tmsDriverAccount.getPayType());
            driverAccountEntity.setAccountName(tmsDriverAccount.getAccountName());
            driverAccountEntity.setAccountBank(tmsDriverAccount.getAccountBank());
            driverAccountEntity.setAccountAscription(tmsDriverAccount.getAccountAscription());
            driverAccountEntity.setAccount(tmsDriverAccount.getAccount());
            driverEntity.setDriverAccountEntity(driverAccountEntity);
        }
        return driverEntityList;

    }

    @Override
    public DriverEntity query(Long driverId) {
        return TmsDriverConverter.tmsDriver2Entity(tmsDriverMapper.selectByPrimaryKey(driverId));
    }

    @Override
    public DriverEntity queryWithCityCar(DriverQuery driverQuery) {
        TmsDriver tmsDriver = tmsDriverMapper.selectOne(new LambdaQueryWrapper<TmsDriver>()
                .eq(driverQuery.getId() != null, TmsDriver::getId, driverQuery.getId()
                )
        );

        TmsDriverCarCarrierMapping tmsDriverCarCarrierMapping = tmsDriverCarCarrierMappingMapper.selectOne(new LambdaQueryWrapper<TmsDriverCarCarrierMapping>()
                .eq(driverQuery.getId() != null, TmsDriverCarCarrierMapping::getTmsDriverId, driverQuery.getId())
                .eq(TmsDriverCarCarrierMapping::getState, StateEnum.valid.getCode())
                .in(TmsDriverCarCarrierMapping::getBusinessType, Arrays.asList(BusinessTypeEnum.city.getCode(), BusinessTypeEnum.trunkAndCity.getCode()))
        );

        DriverEntity driverEntity = TmsDriverConverter.tmsDriver2Entity(tmsDriver);
        if(tmsDriverCarCarrierMapping != null){
            driverEntity.setCityCarId(tmsDriverCarCarrierMapping.getTmsCarId());
            driverEntity.setCityMappingId(tmsDriverCarCarrierMapping.getCarrierId());
        }

        return driverEntity;
    }
}
