package net.summerfarm.tms.repository.delivery;

import net.summerfarm.tms.converter.delivery.TmsDeliveryBatchExtConverter;
import net.summerfarm.tms.delivery.entity.TmsDeliveryBatchExtEntity;
import net.summerfarm.tms.delivery.param.command.TmsDeliveryBatchExtCommandParam;
import net.summerfarm.tms.delivery.repository.TmsDeliveryBatchExtCommandRepository;
import net.summerfarm.tms.mapper.delivery.TmsDeliveryBatchExtMapper;
import net.summerfarm.tms.model.delivery.TmsDeliveryBatchExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-05-14 15:11:42
* @version 1.0
*
*/
@Repository
public class TmsDeliveryBatchExtCommandRepositoryImpl implements TmsDeliveryBatchExtCommandRepository {

    @Autowired
    private TmsDeliveryBatchExtMapper tmsDeliveryBatchExtMapper;
    @Override
    public TmsDeliveryBatchExtEntity insertSelective(TmsDeliveryBatchExtCommandParam param) {
        TmsDeliveryBatchExt tmsDeliveryBatchExt = TmsDeliveryBatchExtConverter.toTmsDeliveryBatchExt(param);
        tmsDeliveryBatchExtMapper.insertSelective(tmsDeliveryBatchExt);
        return TmsDeliveryBatchExtConverter.toTmsDeliveryBatchExtEntity(tmsDeliveryBatchExt);
    }

    @Override
    public int updateSelectiveById(TmsDeliveryBatchExtCommandParam param){
        return tmsDeliveryBatchExtMapper.updateSelectiveById(TmsDeliveryBatchExtConverter.toTmsDeliveryBatchExt(param));
    }


    @Override
    public int remove(Long id) {
        return tmsDeliveryBatchExtMapper.remove(id);
    }
}