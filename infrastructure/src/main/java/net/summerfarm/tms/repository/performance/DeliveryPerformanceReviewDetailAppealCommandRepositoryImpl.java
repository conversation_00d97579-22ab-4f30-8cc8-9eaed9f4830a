package net.summerfarm.tms.repository.performance;

import com.alibaba.fastjson.JSON;
import net.summerfarm.tms.converter.performance.TmsDeliveryPerformanceReviewDetailAppealConverter;
import net.summerfarm.tms.converter.performance.TmsDeliveryPerformanceReviewDetailAppealItemConverter;
import net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppeal;
import net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppealItem;
import net.summerfarm.tms.mapper.performance.TmsDeliveryPerformanceReviewDetailAppealMapper;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealEntity;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailAppealCommandRepository;
import net.summerfarm.tms.track.param.command.TmsTrackLogCommandParam;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.tms.enums.TmsTrackLogEnums.BizType.APPEAL;

/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:06
* @version 1.0
*
*/
@Repository
public class DeliveryPerformanceReviewDetailAppealCommandRepositoryImpl implements DeliveryPerformanceReviewDetailAppealCommandRepository {

    @Resource
    private TmsDeliveryPerformanceReviewDetailAppealMapper tmsDeliveryPerformanceReviewDetailAppealMapper;

    @Override
    public List<DeliveryPerformanceReviewDetailAppealEntity> createBatch(List<DeliveryPerformanceReviewDetailAppealEntity> appealSaveList) {
        if(CollectionUtils.isEmpty(appealSaveList)){
            return appealSaveList;
        }
        // 申诉表
        List<TmsDeliveryPerformanceReviewDetailAppeal> appealList = appealSaveList.stream().map(TmsDeliveryPerformanceReviewDetailAppealConverter::toTmsDeliveryPerformanceReviewDetailAppeal).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(appealList, TmsDeliveryPerformanceReviewDetailAppeal.class);

        // 设置Id
        Map<Long, TmsDeliveryPerformanceReviewDetailAppeal> detailAppealMap = appealList.stream().collect(Collectors.toMap(TmsDeliveryPerformanceReviewDetailAppeal::getDelPerfDetailReviewId, Function.identity()));
        Map<Long, DeliveryPerformanceReviewDetailAppealEntity> detailAppealEntityMap = appealSaveList.stream().collect(Collectors.toMap(DeliveryPerformanceReviewDetailAppealEntity::getDelPerfDetailReviewId, Function.identity()));
        detailAppealMap.forEach((detailId, dbAppeal) -> {
            DeliveryPerformanceReviewDetailAppealEntity appealEntity = detailAppealEntityMap.get(detailId);
            List<DeliveryPerformanceReviewDetailAppealItemEntity> appealItemEntities = appealEntity.getDeliveryPerformanceReviewDetailAppealItemEntities();
            appealItemEntities.forEach(appealItemEntity -> {
                appealItemEntity.setAppealId(dbAppeal.getId());
            });
        });
        // 申诉项保存
        List<DeliveryPerformanceReviewDetailAppealItemEntity> appealItemEntityList = appealSaveList.stream().map(DeliveryPerformanceReviewDetailAppealEntity::getDeliveryPerformanceReviewDetailAppealItemEntities).flatMap(List::stream).collect(Collectors.toList());
        List<TmsDeliveryPerformanceReviewDetailAppealItem> appealItemList = appealItemEntityList.stream().map(TmsDeliveryPerformanceReviewDetailAppealItemConverter::toTmsDeliveryPerformanceReviewDetailAppealItem).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(appealItemList,TmsDeliveryPerformanceReviewDetailAppealItem.class);

        return TmsDeliveryPerformanceReviewDetailAppealConverter.toTmsDeliveryPerformanceReviewDetailAppealEntityList(appealList);

    }

    @Override
    public void updateStatus(DeliveryPerformanceReviewDetailAppealEntity appealEntity, Integer status) {
        if(appealEntity == null){
            return;
        }

        TmsDeliveryPerformanceReviewDetailAppeal tmsDeliveryPerformanceReviewDetailAppeal = new TmsDeliveryPerformanceReviewDetailAppeal();
        tmsDeliveryPerformanceReviewDetailAppeal.setId(appealEntity.getId());
        tmsDeliveryPerformanceReviewDetailAppeal.setStatus(status);
        tmsDeliveryPerformanceReviewDetailAppealMapper.updateById(tmsDeliveryPerformanceReviewDetailAppeal);
    }

    @Override
    public void update(DeliveryPerformanceReviewDetailAppealEntity appealEntity) {
        if(appealEntity == null){
            return;
        }
        tmsDeliveryPerformanceReviewDetailAppealMapper.updateById(TmsDeliveryPerformanceReviewDetailAppealConverter.toTmsDeliveryPerformanceReviewDetailAppeal(appealEntity));
    }

    @Override
    public void batchUpdate(List<DeliveryPerformanceReviewDetailAppealEntity> appealEntities) {
        if(CollectionUtils.isEmpty(appealEntities)){
            return;
        }

        List<TmsDeliveryPerformanceReviewDetailAppeal> appealList = appealEntities.stream().map(TmsDeliveryPerformanceReviewDetailAppealConverter::toTmsDeliveryPerformanceReviewDetailAppeal).collect(Collectors.toList());
        appealList.sort(Comparator.comparing(TmsDeliveryPerformanceReviewDetailAppeal::getId));

        MybatisPlusUtil.updateBatch(appealList, TmsDeliveryPerformanceReviewDetailAppeal.class );
    }
}