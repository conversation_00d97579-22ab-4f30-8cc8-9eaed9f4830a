package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.NonNull;
import net.summerfarm.tms.base.carrier.CarrierRepository;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.path.PathQuotationRepository;
import net.summerfarm.tms.base.path.entity.PathQuotationEntity;
import net.summerfarm.tms.converter.TmsPathQuotationConverter;
import net.summerfarm.tms.dao.TmsPathQuotation;
import net.summerfarm.tms.enums.CarStorageEnum;
import net.summerfarm.tms.enums.CarTypeEnum;
import net.summerfarm.tms.mapper.TmsPathQuotationMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-05-15
 **/
@Service
public class PathQuotationRepositoryImpl implements PathQuotationRepository {
	@Resource
	private TmsPathQuotationMapper tmsPathQuotationMapper;
	@Resource
	private CarrierRepository carrierRepository;
	@Resource
	private TmsPathQuotationConverter tmsPathQuotationConverter;

	@Override
	public void removeByPathId(Long pathId) {
		tmsPathQuotationMapper.delete(new LambdaQueryWrapper<TmsPathQuotation>()
				.eq(TmsPathQuotation::getPathId, pathId));

	}

	@Override
	public List<PathQuotationEntity> queryByPathId(@NonNull Long pathId) {
		List<TmsPathQuotation> tmsPathQuotationList = tmsPathQuotationMapper
				.selectList(new LambdaQueryWrapper<TmsPathQuotation>()
						.eq(TmsPathQuotation::getPathId, pathId));
		List<PathQuotationEntity> pathQuotationEntities = tmsPathQuotationConverter.doListToEntity(tmsPathQuotationList);
		if (CollectionUtils.isEmpty(pathQuotationEntities)) {
			return null;
		}
		Set<Long> carrierIdList = pathQuotationEntities.stream()
				.map(PathQuotationEntity::getCarrierId).collect(Collectors.toSet());
		if (CollectionUtils.isNotEmpty(carrierIdList)) {
			List<CarrierEntity> carrierList = carrierRepository.getListByIdList(carrierIdList);
			if (CollectionUtils.isEmpty(carrierList)) {
				return pathQuotationEntities;
			}
			Map<Long, CarrierEntity> carrierMap = carrierList.stream()
					.collect(Collectors.toMap(CarrierEntity::getId, Function.identity()));
			for (PathQuotationEntity tmsPathQuotation : pathQuotationEntities) {
				if (!Objects.isNull(tmsPathQuotation.getCarType())) {
					tmsPathQuotation.setCarTypeDesc(CarTypeEnum.getCarTypeByCode(tmsPathQuotation.getCarType()).getDesc());
				}
				if (!Objects.isNull(tmsPathQuotation.getStorage())) {
					tmsPathQuotation.setStorageDesc(CarStorageEnum.getCarStorageByCode(tmsPathQuotation.getStorage()).getName());
				}
				CarrierEntity carrierEntity = carrierMap.get(tmsPathQuotation.getCarrierId());
				if (carrierEntity == null) {
					continue;
				}
				tmsPathQuotation.setCarrierName(carrierEntity.getCarrierName());
			}
		}

		return pathQuotationEntities;
	}
}
