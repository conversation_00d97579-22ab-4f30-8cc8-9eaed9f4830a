package net.summerfarm.tms.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.base.site.entity.SiteRecordEntity;
import net.summerfarm.tms.base.site.param.SiteRecordCommandParam;
import net.summerfarm.tms.converter.SiteConverter;
import net.summerfarm.tms.converter.TmsDistSiteRecordConverter;
import net.summerfarm.tms.dao.TmsDistSite;
import net.summerfarm.tms.dao.TmsDistSiteRecord;
import net.summerfarm.tms.dist.query.SiteTypeDetailAddressBusinessNoQuery;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.mapper.TmsDistSiteMapper;
import net.summerfarm.tms.mapper.TmsDistSiteRecordMapper;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/7/13 16:07<br/>
 *
 * <AUTHOR> />
 */
@Service
public class SiteRepositoryImpl implements SiteRepository {

    @Resource
    private TmsDistSiteMapper tmsDistSiteMapper;
    @Resource
    private TmsDistSiteRecordMapper tmsDistSiteRecordMapper;

    @Override
    public Long getCitySiteIdByOutBusinessNo(Integer storeNo) {
        TmsDistSite tmsDistSite = new TmsDistSite();
        tmsDistSite.setType(SiteTypeEnum.store.getCode());
        tmsDistSite.setOutBusinessNo(String.valueOf(storeNo));
        TmsDistSite tmsDistSiteInfo = tmsDistSiteMapper.selectByTmsDistSite(tmsDistSite);
        if (tmsDistSiteInfo != null) {
            return tmsDistSiteInfo.getId();
        }
        throw new TmsRuntimeException("不存在此城配仓");
    }

    @Override
    public SiteEntity query(Long siteId) {
        if (siteId == null) {
            return null;
        }
        return SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectByPrimaryKey(siteId));
    }

    @Override
    public List<SiteEntity> getSiteByOutBNosAndType(List<String> outBusinessNoList, Integer type) {
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectByOutBusNoAndType(outBusinessNoList, type);
        return tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
    }

    @Override
    public void batchUpdateSite(List<SiteEntity> siteEntities) {
        List<TmsDistSite> tmsDistSiteList = siteEntities.stream().map(SiteConverter::entity2tmsDistSite).collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        for (TmsDistSite tmsDistSite : tmsDistSiteList) {
            tmsDistSite.setUpdateTime(now);
            tmsDistSiteMapper.updateByPrimaryKey(tmsDistSite);
        }
    }

    @Override
    public List<SiteEntity> siteSearch(SiteQuery siteQuery) {
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectByQuery(siteQuery);
        //拼接省市区详细地址字段fullAddress
        List<SiteEntity> siteEntities = tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
        siteEntities.forEach(r -> r.setFullAddress(r.getProvince() + r.getCity() + r.getArea() + r.getAddress()));
        return siteEntities;
    }

    @Override
    public PageInfo<SiteEntity> siteSearchPage(SiteQuery siteQuery) {
        PageHelper.startPage(siteQuery.getPageIndex(), siteQuery.getPageSize());
        List<SiteEntity> siteEntities = siteSearch(siteQuery);
        return PageInfoHelper.createPageInfo(siteEntities);
    }

    @Override
    public Long siteAdd(SiteEntity siteEntity) {
        TmsDistSite tmsDistSite = SiteConverter.entity2tmsDistSite(siteEntity);
        if (Objects.nonNull(siteEntity.getType()) && (siteEntity.getType() == TmsSiteTypeEnum.STORE.getCode() || siteEntity.getType() == TmsSiteTypeEnum.WAREHOUSE.getCode())) {
            TmsDistSite warehouseSite = tmsDistSiteMapper.selectOne(new LambdaQueryWrapper<TmsDistSite>().eq(TmsDistSite::getOutBusinessNo, tmsDistSite.getOutBusinessNo())
                    .eq(TmsDistSite::getType, tmsDistSite.getType()));
            if (warehouseSite == null){
                throw new TmsRuntimeException("起始点位不存在");
            }
            if (StrUtil.isBlank(siteEntity.getPoi())){
                siteEntity.setPoi(warehouseSite.getPoi());
            }
            if (warehouseSite.getContactPerson() == null || warehouseSite.getPhone() == null) {
                warehouseSite.setContactPerson(tmsDistSite.getContactPerson());
                warehouseSite.setPhone(tmsDistSite.getPhone());
                tmsDistSiteMapper.updateById(warehouseSite);
            }
            return warehouseSite.getId();
        }
        //判断地址是否已经存在
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>()
                .eq(Objects.nonNull(siteEntity.getId()), TmsDistSite::getId, siteEntity.getId())
                .eq(Objects.nonNull(siteEntity.getType()),TmsDistSite::getType, siteEntity.getType())
                .eq(Objects.nonNull(tmsDistSite.getProvice()),TmsDistSite::getProvice, siteEntity.getProvince())
                .eq(Objects.nonNull(tmsDistSite.getCity()),TmsDistSite::getCity, siteEntity.getCity())
                .eq(Objects.nonNull(tmsDistSite.getArea()),TmsDistSite::getArea, siteEntity.getArea())
                .eq(Objects.nonNull(tmsDistSite.getAddress()),TmsDistSite::getAddress, siteEntity.getAddress())
                .eq(Objects.nonNull(tmsDistSite.getPhone()),TmsDistSite::getPhone, siteEntity.getPhone())
                .eq(Objects.nonNull(tmsDistSite.getName()),TmsDistSite::getName, siteEntity.getName())
                .eq(Objects.equals(siteEntity.getType(), TmsSiteTypeEnum.SPECIFIED.getCode())
                        && Objects.nonNull(tmsDistSite.getContactPerson()),TmsDistSite::getContactPerson, siteEntity.getContactPerson())
                .orderByDesc(TmsDistSite::getId)
        );
        if (tmsDistSites.size() > 0) {
            TmsDistSite existedSite = tmsDistSites.get(0);
            boolean isSetOutBusinessNo = StrUtil.isBlank(existedSite.getOutBusinessNo()) && StrUtil.isNotBlank(siteEntity.getOutBusinessNo());
            if (isSetOutBusinessNo){
                existedSite.setOutBusinessNo(siteEntity.getOutBusinessNo());
            }
            boolean isPoiChange = !Objects.equals(existedSite.getPoi(), siteEntity.getPoi());
            if (isPoiChange){
                existedSite.setPoi(siteEntity.getPoi());
            }
            boolean isSitePicsChange = !Objects.equals(existedSite.getSitePics(), siteEntity.getSitePics());
            if (isSitePicsChange){
                existedSite.setSitePics(siteEntity.getSitePics());
            }
            boolean isSiteUseChange = !Objects.equals(existedSite.getSiteUse(), siteEntity.getSiteUse());
            if (isSiteUseChange){
                existedSite.setSiteUse(siteEntity.getSiteUse());
            }
            boolean isContactPersonChange = !Objects.equals(existedSite.getContactPerson(), siteEntity.getContactPerson());
            if (isContactPersonChange){
                existedSite.setContactPerson(siteEntity.getContactPerson());
            }
            boolean isOutBusinessNoChange = !Objects.equals(existedSite.getOutBusinessNo(), siteEntity.getOutBusinessNo());
            if (isOutBusinessNoChange){
                existedSite.setOutBusinessNo(siteEntity.getOutBusinessNo());
            }
            if (isPoiChange || isSitePicsChange || isContactPersonChange || isOutBusinessNoChange){
                tmsDistSiteMapper.updateById(existedSite);
            }
            return existedSite.getId();
        }
        tmsDistSite.setCreateTime(LocalDateTime.now());

        tmsDistSiteMapper.insert(tmsDistSite);

        return tmsDistSite.getId();
    }

    @Override
    public SiteEntity query(SiteQuery siteQuery) {
        TmsDistSite tmsDistSite = tmsDistSiteMapper.selectOne(
                new LambdaQueryWrapper<TmsDistSite>()
                        .eq(!StringUtils.isEmpty(siteQuery.getOutBusinessNo()),TmsDistSite::getOutBusinessNo, siteQuery.getOutBusinessNo())
                        .eq(siteQuery.getType() != null,TmsDistSite::getType, siteQuery.getType())
                        .orderByDesc(TmsDistSite::getId)
                        .last("limit 1")
        );
        return SiteConverter.tmsDistSite2Entity(tmsDistSite);
    }

    @Override
    public SiteEntity siteDetail(Long id, Integer type) {
        TmsDistSite tmsDistSite;
        if (Objects.nonNull(type) && (type == TmsSiteTypeEnum.STORE.getCode() || type == TmsSiteTypeEnum.WAREHOUSE.getCode())) {
            tmsDistSite = tmsDistSiteMapper.selectOne(new LambdaQueryWrapper<TmsDistSite>().eq(TmsDistSite::getType, type).eq(TmsDistSite::getOutBusinessNo, id));

        }else {
            tmsDistSite = tmsDistSiteMapper.selectOne(new LambdaQueryWrapper<TmsDistSite>().eq(TmsDistSite::getId, id));
        }
        return SiteConverter.tmsDistSite2Entity(tmsDistSite);
    }

    @Override
    public void checkDuplicate(SiteEntity siteEntity) {
        //首先判断简称是否重复
        List<TmsDistSite> nameResult = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>()
                        .eq(Objects.nonNull(siteEntity.getType()),TmsDistSite::getType, siteEntity.getType())
                        .eq(Objects.nonNull(siteEntity.getName()), TmsDistSite::getName, siteEntity.getName()));
        if (!CollectionUtils.isEmpty(nameResult)){
            throw new TmsRuntimeException("地址简称已存在!");
        }
        //其次判断地址是否重复
        List<TmsDistSite> addressResult = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>()
                        .eq(Objects.nonNull(siteEntity.getType()),TmsDistSite::getType, siteEntity.getType())
                        .eq(Objects.nonNull(siteEntity.getProvince()),TmsDistSite::getProvice, siteEntity.getProvince())
                        .eq(Objects.nonNull(siteEntity.getCity()),TmsDistSite::getCity, siteEntity.getCity())
                        .eq(Objects.nonNull(siteEntity.getArea()),TmsDistSite::getArea, siteEntity.getArea())
                        .eq(Objects.nonNull(siteEntity.getAddress()),TmsDistSite::getAddress, siteEntity.getAddress()));
        if (!CollectionUtils.isEmpty(addressResult)){
            throw new TmsRuntimeException("详细地址已存在!");
        }
    }

    @Override
    public List<SiteEntity> queryPrimaryIdList(Collection<Long> siteIdList) {
        if (CollectionUtils.isEmpty(siteIdList)) {
            return new ArrayList<>();
        }

        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>().in(TmsDistSite::getId, siteIdList));
        return tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
    }

    @Override
    public List<SiteEntity> queryList(SiteQuery siteQuery) {

        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>()
                .eq(siteQuery.getType() != null, TmsDistSite::getType, siteQuery.getType())
                .eq(!StringUtils.isEmpty(siteQuery.getOutBusinessNo()), TmsDistSite::getOutBusinessNo, siteQuery.getOutBusinessNo())
                .eq(siteQuery.getState() != null, TmsDistSite::getState, siteQuery.getState())
                .eq(siteQuery.getId() != null, TmsDistSite::getId, siteQuery.getId())
                .in(!CollectionUtils.isEmpty(siteQuery.getOutBusinessNos()), TmsDistSite::getOutBusinessNo, siteQuery.getOutBusinessNos())
        );

        return tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
    }

    @Override
    public List<SiteEntity> queryListByRegion(List<String> regions) {
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectByRegion(regions);
        return tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
    }

    @Override
    public Map<Long, SiteEntity> queryMapByIds(List<Long> siteIds) {
        if (CollectionUtils.isEmpty(siteIds)) {
            return Maps.newHashMap();
        }
        List<SiteEntity> siteEntities = this.queryPrimaryIdList(siteIds);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(siteEntities)) {
            return Maps.newHashMap();
        }
        return siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));

    }

    @Override
    public Map<String, SiteEntity> queryStoreMap(List<String> storeNoList) {
        if (CollectionUtils.isEmpty(storeNoList)) {
            return Collections.emptyMap();
        }
        return getStringSiteEntityMap(storeNoList,SiteTypeEnum.store);
    }

    @Override
    public Map<String, SiteEntity> queryWarehouseMap(List<String> warehouseNoList) {
        if (CollectionUtils.isEmpty(warehouseNoList)) {
            return Collections.emptyMap();
        }
        return getStringSiteEntityMap(warehouseNoList,SiteTypeEnum.warehouse);
    }

    private Map<String, SiteEntity> getStringSiteEntityMap(List<String> warehouseNoList,SiteTypeEnum siteTypeEnum) {
        List<SiteEntity> tmsDistSites = this.getSiteByOutBNosAndType(warehouseNoList, siteTypeEnum.getCode());
        if (CollectionUtils.isEmpty(tmsDistSites)) {
            return Collections.emptyMap();
        }
        return tmsDistSites.stream().collect(Collectors.toMap(SiteEntity::getOutBusinessNo, Function.identity(), (a, b) -> a));
    }

    @Override
    public String queryStoreName(Object storeNo) {
        if(storeNo == null){
            return null;
        }
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>()
                .eq(TmsDistSite::getOutBusinessNo, storeNo)
                .eq(TmsDistSite::getType, SiteTypeEnum.store.getCode())
                .last("limit 1")
        );
        if(CollectionUtils.isEmpty(tmsDistSites)){
            return null;
        }
        return tmsDistSites.get(0).getName();
    }

    @Override
    public String queryWarehouseName(Object warehouseNo) {
        if(warehouseNo == null){
            return null;
        }
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>()
                .eq(TmsDistSite::getOutBusinessNo, warehouseNo)
                .eq(TmsDistSite::getType, SiteTypeEnum.warehouse.getCode())
                .last("limit 1")
        );
        if(CollectionUtils.isEmpty(tmsDistSites)){
            return null;
        }
        return tmsDistSites.get(0).getName();
    }

    @Override
    public SiteEntity queryForceMasterByOutBusinessNoType(String outBusinessNo, Integer type) {
        return SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.queryForceMasterByOutBusinessNoType(outBusinessNo,type));
    }

    @Override
    public void save(SiteEntity siteEntity) {
        TmsDistSite tmsDistSite = SiteConverter.entity2tmsDistSite(siteEntity);
        tmsDistSite.setCreateTime(LocalDateTime.now());
        tmsDistSiteMapper.insert(tmsDistSite);
    }

    @Override
    public void update(SiteEntity siteEntity) {
        tmsDistSiteMapper.updateById(SiteConverter.entity2tmsDistSite(siteEntity));
    }

    @Override
    public void updatePoiOutBusinessNo(Long siteId, String poi, String outBusinessNo) {
        if(siteId == null || StringUtil.isBlank(poi)){
            return;
        }
        TmsDistSite tmsDistSite = new TmsDistSite();
        tmsDistSite.setId(siteId);
        tmsDistSite.setPoi(poi);
        if(StringUtil.isNotBlank(outBusinessNo)){
            tmsDistSite.setOutBusinessNo(outBusinessNo);
        }
        tmsDistSiteMapper.updateById(tmsDistSite);
    }

    @Override
    public SiteEntity queryWithRecord(Long siteId) {
        if (siteId == null){
            return null;
        }
        TmsDistSite tmsDistSite = tmsDistSiteMapper.selectById(siteId);
        SiteEntity siteEntity = SiteConverter.tmsDistSite2Entity(tmsDistSite);
        if (siteEntity == null){
            return null;
        }
        List<TmsDistSiteRecord> tmsDistSiteRecords = tmsDistSiteRecordMapper.selectList(new LambdaQueryWrapper<TmsDistSiteRecord>().eq(TmsDistSiteRecord::getSiteId, tmsDistSite.getId()));
        List<SiteRecordEntity> siteRecordEntities = Optional.ofNullable(tmsDistSiteRecords).orElse(Lists.newArrayList()).stream().map(TmsDistSiteRecordConverter::do2Entity).collect(Collectors.toList());
        siteEntity.setSiteRecords(siteRecordEntities);
        return siteEntity;
    }

    @Override
    public Long saveRecord(SiteRecordCommandParam param) {
        TmsDistSiteRecord tmsDistSiteRecord = TmsDistSiteRecordConverter.param2Do(param);
        if (tmsDistSiteRecord == null){
            return null;
        }
        tmsDistSiteRecordMapper.insert(tmsDistSiteRecord);
        return tmsDistSiteRecord.getId();
    }

    @Override
    public Map<Long, SiteEntity> querySiteStoreMapByOutBusinessNos(List<String> popStoreNosList) {
        if(CollectionUtils.isEmpty(popStoreNosList)){
            return Collections.emptyMap();
        }
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>()
                .in(!CollectionUtils.isEmpty(popStoreNosList), TmsDistSite::getOutBusinessNo, popStoreNosList)
                .eq(TmsDistSite::getType, SiteTypeEnum.store.getCode())
        );
        List<SiteEntity> siteEntities = tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
        return siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));
    }

    @Override
    public PageInfo<SiteEntity> queryPage(SiteQuery query) {
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.queryList(query);
        PageInfo pageInfo = PageInfoHelper.createPageInfo(tmsDistSites);
        pageInfo.setList(tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList()));
        return pageInfo;
    }

    @Override
    public SiteEntity queryByTypeAndDetailAddress(Integer type, String province, String city, String area, String address) {
        //其次判断地址是否重复
        TmsDistSite addressResult = tmsDistSiteMapper.selectOne(new LambdaQueryWrapper<TmsDistSite>()
                .eq(Objects.nonNull(type),TmsDistSite::getType, type)
                .eq(Objects.nonNull(province),TmsDistSite::getProvice, province)
                .eq(Objects.nonNull(city),TmsDistSite::getCity, city)
                .eq(Objects.nonNull(area),TmsDistSite::getArea, area)
                .eq(Objects.nonNull(address),TmsDistSite::getAddress, address)
                .last("limit 1"));

        return SiteConverter.tmsDistSite2Entity(addressResult);
    }

    @Override
    public SiteEntity querySite(SiteTypeDetailAddressBusinessNoQuery query) {
        TmsDistSite distSite = tmsDistSiteMapper.selectOne(new LambdaQueryWrapper<TmsDistSite>()
               .eq(Objects.nonNull(query.getType()),TmsDistSite::getType, query.getType())
               .eq(Objects.nonNull(query.getProvice()),TmsDistSite::getProvice, query.getProvice())
                .eq(Objects.nonNull(query.getCity()),TmsDistSite::getCity, query.getCity())
                .eq(Objects.nonNull(query.getArea()),TmsDistSite::getArea, query.getArea())
                .eq(Objects.nonNull(query.getAddress()),TmsDistSite::getAddress, query.getAddress())
                .eq(Objects.nonNull(query.getPhone()),TmsDistSite::getPhone, query.getPhone())
                .eq(Objects.nonNull(query.getName()),TmsDistSite::getName, query.getName())
                .eq(Objects.nonNull(query.getOutBusinessNo()),TmsDistSite::getOutBusinessNo, query.getOutBusinessNo())
               .last("limit 1"));

        return SiteConverter.tmsDistSite2Entity(distSite);
    }
}
