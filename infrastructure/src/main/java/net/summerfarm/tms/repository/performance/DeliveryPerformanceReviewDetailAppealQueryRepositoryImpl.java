package net.summerfarm.tms.repository.performance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.tms.converter.performance.TmsDeliveryPerformanceReviewDetailAppealConverter;
import net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppeal;
import net.summerfarm.tms.delivery.entity.BoardEntity;
import net.summerfarm.tms.mapper.performance.TmsDeliveryPerformanceReviewDetailAppealMapper;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealEntity;
import net.summerfarm.tms.performance.entity.dataobject.ReviewDetailAppealPageObj;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailAppealQueryRepository;
import net.summerfarm.tms.query.performance.AppealQuery;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:06
* @version 1.0
*
*/
@Repository
public class DeliveryPerformanceReviewDetailAppealQueryRepositoryImpl implements DeliveryPerformanceReviewDetailAppealQueryRepository {


    @Resource
    private TmsDeliveryPerformanceReviewDetailAppealMapper tmsDeliveryPerformanceReviewDetailAppealMapper;

    @Override
    public DeliveryPerformanceReviewDetailAppealEntity selectById(Long id) {
        return TmsDeliveryPerformanceReviewDetailAppealConverter.toTmsDeliveryPerformanceReviewDetailAppealEntity(tmsDeliveryPerformanceReviewDetailAppealMapper.selectById(id));
    }

    @Override
    public List<DeliveryPerformanceReviewDetailAppealEntity> queryAppealListByDeliveryPerReviewDetailIdsAndTaskIds(List<Long> deliveryPerReviewDetailIds, List<Long> performanceReviewTaskIds) {
        if(CollectionUtils.isEmpty(deliveryPerReviewDetailIds) || CollectionUtils.isEmpty(performanceReviewTaskIds)){
            return Collections.emptyList();
        }

        List<TmsDeliveryPerformanceReviewDetailAppeal> appealList = tmsDeliveryPerformanceReviewDetailAppealMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPerformanceReviewDetailAppeal>()
                .in(TmsDeliveryPerformanceReviewDetailAppeal::getDelPerfDetailReviewId, deliveryPerReviewDetailIds)
                .in(TmsDeliveryPerformanceReviewDetailAppeal::getPerformanceReviewTaskId, performanceReviewTaskIds)
        );

        return appealList.stream().map(TmsDeliveryPerformanceReviewDetailAppealConverter::toTmsDeliveryPerformanceReviewDetailAppealEntity).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryPerformanceReviewDetailAppealEntity> queryAppealListByBatchIdsAndTaskIds(List<Long> deliveryBatchIdList, List<Long> performanceReviewTaskIds) {
        if(CollectionUtils.isEmpty(deliveryBatchIdList) || CollectionUtils.isEmpty(performanceReviewTaskIds)){
            return Collections.emptyList();
        }

        List<TmsDeliveryPerformanceReviewDetailAppeal> appealList = tmsDeliveryPerformanceReviewDetailAppealMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPerformanceReviewDetailAppeal>()
                .in(TmsDeliveryPerformanceReviewDetailAppeal::getDeliveryBatchId, deliveryBatchIdList)
                .in(TmsDeliveryPerformanceReviewDetailAppeal::getPerformanceReviewTaskId, performanceReviewTaskIds)
        );

        return appealList.stream().map(TmsDeliveryPerformanceReviewDetailAppealConverter::toTmsDeliveryPerformanceReviewDetailAppealEntity).collect(Collectors.toList());
    }

    @Override
    public PageInfo<ReviewDetailAppealPageObj> queryReviewDetailAppealPage(AppealQuery query) {
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        List<ReviewDetailAppealPageObj> reviewDetailAppealPageObjs = tmsDeliveryPerformanceReviewDetailAppealMapper.queryReviewDetailAppealPage(query);

        return PageInfoHelper.createPageInfo(reviewDetailAppealPageObjs);
    }

    @Override
    public ReviewDetailAppealPageObj queryDetailById(Long id) {
        if(id == null){
            return null;
        }
        List<ReviewDetailAppealPageObj> reviewDetailAppealPageObjs = tmsDeliveryPerformanceReviewDetailAppealMapper.queryReviewDetailAppealPage(AppealQuery.builder().id(id).build());
        if(CollectionUtils.isEmpty(reviewDetailAppealPageObjs)){
            return null;
        }
        return reviewDetailAppealPageObjs.get(0);
    }

    @Override
    public List<DeliveryPerformanceReviewDetailAppealEntity> queryAppealListByIds(List<Long> appealIds) {
        if(CollectionUtils.isEmpty(appealIds)){
            return Collections.emptyList();
        }

        List<TmsDeliveryPerformanceReviewDetailAppeal> appealList = tmsDeliveryPerformanceReviewDetailAppealMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPerformanceReviewDetailAppeal>()
                .in(TmsDeliveryPerformanceReviewDetailAppeal::getId, appealIds));

        return appealList.stream().map(TmsDeliveryPerformanceReviewDetailAppealConverter::toTmsDeliveryPerformanceReviewDetailAppealEntity).collect(Collectors.toList());
    }
}