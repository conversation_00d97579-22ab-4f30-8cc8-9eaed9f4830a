package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.path.PathCarRepository;
import net.summerfarm.tms.base.path.PathQuotationRepository;
import net.summerfarm.tms.base.path.PathRepository;
import net.summerfarm.tms.base.path.entity.PathCarEntity;
import net.summerfarm.tms.base.path.entity.PathQuotationEntity;
import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.base.path.entity.TmsPathSectionEntity;
import net.summerfarm.tms.converter.PathConverter;
import net.summerfarm.tms.converter.TmsPathCarConverter;
import net.summerfarm.tms.converter.TmsPathQuotationConverter;
import net.summerfarm.tms.converter.TmsPathSectionConverter;
import net.summerfarm.tms.dao.TmsPath;
import net.summerfarm.tms.dao.TmsPathCar;
import net.summerfarm.tms.dao.TmsPathQuotation;
import net.summerfarm.tms.dao.TmsPathSection;
import net.summerfarm.tms.enums.PathSectionEnums;
import net.summerfarm.tms.enums.TmsPathTypeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.mapper.TmsPathMapper;
import net.summerfarm.tms.mapper.TmsPathSectionMapper;
import net.summerfarm.tms.query.path.PathQuery;
import net.summerfarm.tms.query.path.QueryPathFilterDTO;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PathRepositoryImpl implements PathRepository {
    @Resource
    TmsPathMapper tmsPathMapper;
    @Resource
    TmsPathSectionMapper tmsPathSectionMapper;
    @Resource
    TmsPathQuotationConverter tmsPathQuotationConverter;
    @Resource
    TmsPathCarConverter tmsPathCarConverter;
    @Resource
    PathQuotationRepository pathQuotationRepository;
    @Resource
    PathCarRepository pathCarRepository;

    @Override
    public PageInfo<TmsPathEntity> queryPage(PathQuery pathQuery) {
        LambdaQueryWrapper<TmsPath> pathQueryWrapper = new LambdaQueryWrapper<>();
        pathQueryWrapper.eq(pathQuery.getBeginSiteId() != null, TmsPath::getBeginSiteId, pathQuery.getBeginSiteId());
        pathQueryWrapper.eq(pathQuery.getEndSiteId() != null, TmsPath::getEndSiteId, pathQuery.getEndSiteId());
        pathQueryWrapper.eq(pathQuery.getStatus() != null, TmsPath::getStatus, pathQuery.getStatus());
        pathQueryWrapper.eq(pathQuery.getAutoSwitch() != null, TmsPath::getAutoSwitch, pathQuery.getAutoSwitch());
        pathQueryWrapper.eq(pathQuery.getType() != null, TmsPath::getType, pathQuery.getType());
        pathQueryWrapper.eq(pathQuery.getCarrierId() != null, TmsPath::getCarrierId, pathQuery.getCarrierId());
        pathQueryWrapper.eq(pathQuery.getDriverId() != null, TmsPath::getDriverId, pathQuery.getDriverId());
        pathQueryWrapper.eq(pathQuery.getCarId() != null, TmsPath::getCarId, pathQuery.getCarId());
        pathQueryWrapper.in(CollectionUtils.isNotEmpty(pathQuery.getPathIdList()), TmsPath::getId, pathQuery.getPathIdList());

        if (pathQuery.isNeedPagination()) {
            PageHelper.startPage(pathQuery.getPageIndex(), pathQuery.getPageSize());
        }
        List<TmsPath> tmsPaths = tmsPathMapper.selectList(pathQueryWrapper);
        PageInfo pageInfo = PageInfoHelper.createPageInfo(tmsPaths);

        List<TmsPathEntity> tmsPathEntities = tmsPaths.stream().map(PathConverter::do2Entity).collect(Collectors.toList());
        tmsPathEntities.forEach(tmsPathEntity -> {
            TmsPathEntity existEntity = queryDetail(tmsPathEntity.getPathId());
            tmsPathEntity.setTmsPathSectionList(existEntity.getTmsPathSectionList());
            tmsPathEntity.setTmsPathCarList(existEntity.getTmsPathCarList());
            tmsPathEntity.setTmsPathQuotationList(existEntity.getTmsPathQuotationList());
        });

        pageInfo.setList(tmsPathEntities);

        return pageInfo;
    }

    @Override
    public TmsPathEntity query(Long id) {
        return PathConverter.do2Entity(tmsPathMapper.selectById(id));
    }

    @Override
    public TmsPathEntity queryDetail(Long id) {
        TmsPathEntity tmsPathEntity = query(id);
        List<TmsPathSection> tmsPathSectionList = tmsPathSectionMapper.selectList(
                new LambdaQueryWrapper<TmsPathSection>().eq(TmsPathSection::getPathId, id)
        );
        for (TmsPathSection tmsPathSection : tmsPathSectionList) {
            if (tmsPathSection.getSequence() == null) {
                tmsPathSection.setSequence(0);
            }
        }
        List<TmsPathSectionEntity> tmsPathSectionEntities = tmsPathSectionList.stream()
                .map(PathConverter::section2Entity)
                .sorted(Comparator.comparingInt(TmsPathSectionEntity::getSequence))
                .collect(Collectors.toList());
        tmsPathEntity.setTmsPathSectionList(tmsPathSectionEntities);

        List<PathQuotationEntity> pathQuotationEntities = pathQuotationRepository.queryByPathId(id);
        tmsPathEntity.setTmsPathQuotationList(pathQuotationEntities);

        List<PathCarEntity> pathCarEntities = pathCarRepository.queryByPathId(id);
        tmsPathEntity.setTmsPathCarList(pathCarEntities);

        return tmsPathEntity;
    }

    @Override
    public Boolean updateStatus(Long pathId, Integer newStatus, Integer updaterId, String updater) {
        TmsPath tmsPath = new TmsPath();
        tmsPath.setId(pathId);
        tmsPath.setStatus(newStatus);
        tmsPath.setUpdater(updater);
        if (Objects.nonNull(updaterId)) {
            tmsPath.setUpdaterId(updaterId.toString());

        }
        tmsPathMapper.updateById(tmsPath);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TmsPathEntity temporarySave(TmsPathEntity tmsPathEntity) {
        TmsPath tmsPath = PathConverter.entity2Do(tmsPathEntity);
        tmsPathMapper.insert(tmsPath);
        tmsPathEntity.setPathId(tmsPath.getId());
        if(CollectionUtils.isNotEmpty(tmsPathEntity.getTmsPathSectionList())){
            for (TmsPathSectionEntity tmsPathSectionEntity : tmsPathEntity.getTmsPathSectionList()) {
                tmsPathSectionMapper.insert(PathConverter.entity2SectionDo(tmsPathSectionEntity, tmsPathEntity));
            }
        }

        return tmsPathEntity;
    }

    @Override
    public TmsPathEntity getPrePath(Long beginSiteId, Long endSiteId, TmsPathTypeEnum typeEnum) {
        if(beginSiteId == null || endSiteId == null || typeEnum == null){
            return null;
        }
        TmsPathSection tmsPathSection = tmsPathSectionMapper.querySectionByBeginEndIdType(beginSiteId,endSiteId,typeEnum.getCode());
        if (tmsPathSection == null || tmsPathSection.getPathId() == null){
            return null;
        }
        return query(tmsPathSection.getPathId());
    }

    @Override
    public void temporaryUpdate(TmsPathEntity tmsPathEntity) {
        TmsPath tmsPath = PathConverter.entity2Do(tmsPathEntity);
        tmsPathMapper.updateById(tmsPath);

        /*List<TmsPathSectionEntity> tmsPathSectionList = tmsPathEntity.getTmsPathSectionList();
        if(!CollectionUtils.isEmpty(tmsPathSectionList)){
            for (TmsPathSectionEntity tmsPathSectionEntity : tmsPathSectionList) {
                //是否存在，存在更新，不存在新增
                TmsPathSection tmsPathSection = tmsPathSectionMapper.selectOne(new LambdaQueryWrapper<TmsPathSection>()
                        .eq(tmsPathSectionEntity.getBeginSiteId() != null, TmsPathSection::getBeginSiteId, tmsPathSectionEntity.getBeginSiteId())
                        .eq(tmsPathSectionEntity.getEndSiteId() != null, TmsPathSection::getEndSiteId, tmsPathSectionEntity.getEndSiteId())
                        .eq(tmsPathEntity.getType() != null, TmsPathSection::getType, tmsPathEntity.getType())
                        .last("limit 1")
                );
                if(tmsPathSection == null){
                    //新增
                    tmsPathSectionMapper.insert(PathConverter.entity2SectionDo(tmsPathSectionEntity, tmsPathEntity));
                }else{
                    //更新
                    tmsPathSectionEntity.setId(tmsPathSection.getId());
                    tmsPathSectionMapper.updateById(PathConverter.entity2SectionDo(tmsPathSectionEntity, tmsPathEntity));
                }
            }
        }*/
    }

    @Override
    public TmsPathEntity query(PathQuery pathQuery) {
        LambdaQueryWrapper<TmsPath> pathQueryWrapper = new LambdaQueryWrapper<>();
        pathQueryWrapper.eq(pathQuery.getBeginSiteId() != null, TmsPath::getBeginSiteId, pathQuery.getBeginSiteId());
        pathQueryWrapper.eq(pathQuery.getPathCode() != null, TmsPath::getPathCode, pathQuery.getPathCode());
        TmsPath tmsPath = tmsPathMapper.selectOne(pathQueryWrapper.last("limit 1"));
        return PathConverter.do2Entity(tmsPath);
    }

    @Override
    public List<TmsPathEntity> queryList(PathQuery pathQuery) {
        List<TmsPath> tmsPaths = tmsPathMapper.selectList(new LambdaQueryWrapper<TmsPath>()
                .in(!CollectionUtils.isEmpty(pathQuery.getPathIdList()), TmsPath::getId, pathQuery.getPathIdList())
                .eq(pathQuery.getStatus() != null, TmsPath::getStatus, pathQuery.getStatus())
        );
        return tmsPaths.stream().map(PathConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<TmsPathEntity> queryListByIdList(List<Long> pathIdList) {
        if (CollectionUtils.isEmpty(pathIdList)) {
            return Collections.emptyList();
        }
        return tmsPathMapper.selectList(new LambdaQueryWrapper<TmsPath>().in(TmsPath::getId, pathIdList))
                .stream().map(PathConverter::do2Entity).collect(Collectors.toList());
    }


    @Override
    public void batchSaveOrUpdate(List<TmsPathEntity> pathEntities) {
        if (CollectionUtils.isEmpty(pathEntities)) {
            return;
        }
        List<TmsPath> tmsPaths = tmsPathMapper.selectList(new LambdaQueryWrapper<TmsPath>().in(TmsPath::getBeginSiteId,
                pathEntities.stream().map(TmsPathEntity::getBeginSiteId).collect(Collectors.toSet()))
                .in(TmsPath::getType, 
                        pathEntities.stream().map(TmsPathEntity::getType).collect(Collectors.toSet())));

        List<TmsPathSectionEntity> tmsPathSectionEntityList = pathEntities.stream().flatMap(t -> t.getTmsPathSectionList().stream()).collect(Collectors.toList());

        List<TmsPathSection> existTmsPathSectionList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tmsPathSectionEntityList)) {
            existTmsPathSectionList = tmsPathSectionMapper.selectList(new LambdaQueryWrapper<TmsPathSection>()
                    .in(TmsPathSection::getBeginSiteId,
                            tmsPathSectionEntityList.stream().map(TmsPathSectionEntity::getBeginSiteId).collect(Collectors.toSet()))
                    .in(TmsPathSection::getEndSiteId,
                            tmsPathSectionEntityList.stream().map(TmsPathSectionEntity::getEndSiteId).collect(Collectors.toSet()))
                    .in(TmsPathSection::getType, 
                            tmsPathSectionEntityList.stream().map(TmsPathSectionEntity::getType).collect(Collectors.toSet())));
        }

        List<TmsPathSection> insertPathSectionList = Lists.newArrayList();
        List<TmsPathSection> updatePathSectionList = Lists.newArrayList();
        for (TmsPathEntity pathEntity : pathEntities) {
            TmsPath path = null;
            if (CollectionUtils.isEmpty(tmsPaths)) {
                //需要新增
                path = PathConverter.entity2Do(pathEntity);
                tmsPathMapper.insert(path);
            } else {
                Optional<TmsPath> firstPathOptional = tmsPaths.stream().filter(a -> Objects.equals(pathEntity.getType(), a.getType())
                        && Objects.equals(pathEntity.getPathCode(), a.getPathCode()) 
                        && Objects.equals(pathEntity.getBeginSiteId(),a.getBeginSiteId())).findFirst();
                if (!firstPathOptional.isPresent()) {
                    path = PathConverter.entity2Do(pathEntity);
                    tmsPathMapper.insert(path);
                } else {
                    path = firstPathOptional.get();
                }
            }
            List<TmsPathSectionEntity> tmsPathSectionList = pathEntity.getTmsPathSectionList();
            if (!CollectionUtils.isEmpty(tmsPathSectionList)) {
                for (TmsPathSectionEntity tmsPathSectionEntity : tmsPathSectionList) {
                    if (CollectionUtils.isEmpty(existTmsPathSectionList)) {
                        tmsPathSectionEntity.setPathId(path.getId());
                        //新增
                        insertPathSectionList.add(TmsPathSectionConverter.entity2Do(tmsPathSectionEntity));
                    } else {
                        //是否存在，存在更新，不存在新增
                        Optional<TmsPathSection> existPathSectionOptional = existTmsPathSectionList.stream().filter(a -> Objects.equals(a.getBeginSiteId(), tmsPathSectionEntity.getBeginSiteId())
                                && Objects.equals(a.getEndSiteId(), tmsPathSectionEntity.getEndSiteId())
                                && Objects.equals(a.getType(), tmsPathSectionEntity.getType())).findFirst();
                        tmsPathSectionEntity.setPathId(path.getId());

                        if (!existPathSectionOptional.isPresent()) {
                            //新增
                            insertPathSectionList.add(TmsPathSectionConverter.entity2Do(tmsPathSectionEntity));
                        } else {
                            //更新
                            tmsPathSectionEntity.setId(existPathSectionOptional.get().getId());
                            updatePathSectionList.add(TmsPathSectionConverter.entity2Do(tmsPathSectionEntity));
                        }
                    }


                }
            }
        }
        MybatisPlusUtil.createBatch(insertPathSectionList, TmsPathSection.class);
        MybatisPlusUtil.updateBatch(updatePathSectionList, TmsPathSection.class);
    }

    @Override
    public TmsPathEntity save(TmsPathEntity tmsPathEntity) {
        TmsPath tmsPath = PathConverter.entity2Do(tmsPathEntity);
        tmsPathMapper.insert(tmsPath);
        tmsPathEntity.setPathId(tmsPath.getId());
        createPathItemInfo(tmsPathEntity, tmsPath);
        return tmsPathEntity;

    }

    private void createPathItemInfo(TmsPathEntity tmsPathEntity, TmsPath tmsPath) {
        if (CollectionUtils.isNotEmpty(tmsPathEntity.getTmsPathSectionList())) {
            List<TmsPathSection> pathSectionList = Lists.newArrayList();
            for (TmsPathSectionEntity tmsPathSectionEntity : tmsPathEntity.getTmsPathSectionList()) {
                pathSectionList.add(PathConverter.entity2SectionDo(tmsPathSectionEntity, tmsPathEntity));
            }
            MybatisPlusUtil.createBatch(pathSectionList, TmsPathSection.class);
        }
        if (CollectionUtils.isNotEmpty(tmsPathEntity.getTmsPathCarList())) {
            tmsPathEntity.getTmsPathCarList().forEach(a -> a.setPathId(tmsPath.getId()));
            MybatisPlusUtil.createBatch(tmsPathCarConverter.entityToDoList(tmsPathEntity.getTmsPathCarList()), TmsPathCar.class);
        }
        if (CollectionUtils.isNotEmpty(tmsPathEntity.getTmsPathQuotationList())) {
            tmsPathEntity.getTmsPathQuotationList().forEach(a -> a.setPathId(tmsPath.getId()));
            MybatisPlusUtil.createBatch(tmsPathQuotationConverter.entityToDoList(tmsPathEntity.getTmsPathQuotationList()), TmsPathQuotation.class);
        }
    }

    @Override
    public TmsPathEntity update(TmsPathEntity tmsPathEntity) {
        TmsPath tmsPath = PathConverter.entity2Do(tmsPathEntity);
        tmsPathMapper.updateById(tmsPath);
        createPathItemInfo(tmsPathEntity, tmsPath);
        return tmsPathEntity;
    }

    @Override
    public TmsPathEntity queryByCode(String pathCode, Long id) {
        List<TmsPath> tmsPaths = tmsPathMapper.selectList(new LambdaQueryWrapper<TmsPath>()
                .eq(TmsPath::getPathCode, pathCode)
                .ne(!Objects.isNull(id), TmsPath::getId, id));
        if (CollectionUtils.isNotEmpty(tmsPaths)) {
            return PathConverter.do2Entity(tmsPaths.get(0));
        }
        return null;
    }

    @Override
    public PageInfo<TmsPathEntity> queryList(QueryPathFilterDTO filterDTO) {
        PageHelper.startPage(filterDTO.getPageIndex(), filterDTO.getPageSize());
        List<TmsPathEntity> tmsPaths = tmsPathMapper.queryList(filterDTO);
        PageInfo<TmsPathEntity> pageInfo = PageInfoHelper.createPageInfo(tmsPaths);
        return pageInfo;
    }


}
