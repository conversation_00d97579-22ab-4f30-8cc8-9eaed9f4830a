package net.summerfarm.tms.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.TmsDeliveryPerformanceReviewDetailAiReviewConverter;
import net.summerfarm.tms.mapper.TmsDeliveryPerformanceReviewDetailAiReviewMapper;
import net.summerfarm.tms.performance.TmsDeliveryPerformanceReviewDetailAiReviewQueryRepository;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailAiReviewEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAiReviewQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2024-09-18 00:16:55
* @version 1.0
*
*/
@Repository
public class TmsDeliveryPerformanceReviewDetailAiReviewQueryRepositoryImpl implements TmsDeliveryPerformanceReviewDetailAiReviewQueryRepository {

    @Autowired
    private TmsDeliveryPerformanceReviewDetailAiReviewMapper tmsDeliveryPerformanceReviewDetailAiReviewMapper;


    @Override
    public PageInfo<TmsDeliveryPerformanceReviewDetailAiReviewEntity> getPage(TmsDeliveryPerformanceReviewDetailAiReviewQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<TmsDeliveryPerformanceReviewDetailAiReviewEntity> entities = tmsDeliveryPerformanceReviewDetailAiReviewMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public TmsDeliveryPerformanceReviewDetailAiReviewEntity selectById(Long id) {
        return TmsDeliveryPerformanceReviewDetailAiReviewConverter.toTmsDeliveryPerformanceReviewDetailAiReviewEntity(tmsDeliveryPerformanceReviewDetailAiReviewMapper.selectById(id));
    }


    @Override
    public List<TmsDeliveryPerformanceReviewDetailAiReviewEntity> selectByCondition(TmsDeliveryPerformanceReviewDetailAiReviewQueryParam param) {
        return TmsDeliveryPerformanceReviewDetailAiReviewConverter.toTmsDeliveryPerformanceReviewDetailAiReviewEntityList(tmsDeliveryPerformanceReviewDetailAiReviewMapper.selectByCondition(param));
    }

    @Override
    public Map<Long, TmsDeliveryPerformanceReviewDetailAiReviewEntity> mapByReviewDetailIdList(List<Long> reviewDetailIdList) {
        if (CollectionUtils.isEmpty(reviewDetailIdList)){
            return new HashMap<>(1);
        }

        TmsDeliveryPerformanceReviewDetailAiReviewQueryParam queryParam = new TmsDeliveryPerformanceReviewDetailAiReviewQueryParam();
        queryParam.setPerformanceReviewDetailIdList(reviewDetailIdList);

        List<TmsDeliveryPerformanceReviewDetailAiReviewEntity> list = selectByCondition(queryParam);
        return list.stream().collect(Collectors.toMap(
                TmsDeliveryPerformanceReviewDetailAiReviewEntity::getPerformanceReviewDetailId,
                Function.identity(),
                (a, b) -> a
        ));
    }

}