package net.summerfarm.tms.repository.command;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.converter.TmsTrunkCityDistRelationshipConverter;
import net.summerfarm.tms.dao.TmsTrunkCityDistRelationship;
import net.summerfarm.tms.dist.TrunkCityDistRelationshipCommandRepository;
import net.summerfarm.tms.dist.entity.TrunkCityDistRelationshipEntity;
import net.summerfarm.tms.mapper.TmsTrunkCityDistRelationshipMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/4/12 18:50<br/>
 *
 * <AUTHOR> />
 */
@Service
public class TrunkCityDistRelationshipCommandRepositoryImpl implements TrunkCityDistRelationshipCommandRepository {

    @Resource
    private TmsTrunkCityDistRelationshipMapper tmsTrunkCityDistRelationshipMapper;

    @Override
    public void save(TrunkCityDistRelationshipEntity relationshipEntity) {
        if(relationshipEntity == null){
            return;
        }
        tmsTrunkCityDistRelationshipMapper.insert(TmsTrunkCityDistRelationshipConverter.entity2Model(relationshipEntity));
    }

    @Override
    public void deleteByOuterOrderId(String outerOrderId) {
        if(StringUtils.isEmpty(outerOrderId)){
            return;
        }
        tmsTrunkCityDistRelationshipMapper.delete(new LambdaQueryWrapper<TmsTrunkCityDistRelationship>()
                .eq(TmsTrunkCityDistRelationship::getOuterOrderId, outerOrderId));

    }
}
