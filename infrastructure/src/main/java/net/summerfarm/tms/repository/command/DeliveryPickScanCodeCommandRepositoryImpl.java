package net.summerfarm.tms.repository.command;

import net.summerfarm.tms.converter.TmsDeliveryPickScanCodeConverter;
import net.summerfarm.tms.delivery.entity.DeliveryPickScanCodeEntity;
import net.summerfarm.tms.mapper.TmsDeliveryPickScanCodeMapper;
import net.summerfarm.tms.pick.repository.DeliveryPickScanCodeCommandRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description: 拣货扫码操作类<br/>
 * date: 2024/8/14 15:14<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliveryPickScanCodeCommandRepositoryImpl implements DeliveryPickScanCodeCommandRepository {

    @Resource
    private TmsDeliveryPickScanCodeMapper tmsDeliveryPickScanCodeMapper;

    @Override
    public DeliveryPickScanCodeEntity save(DeliveryPickScanCodeEntity entity) {
        if(entity == null){
            return entity;
        }
        tmsDeliveryPickScanCodeMapper.insert(TmsDeliveryPickScanCodeConverter.entity2Do(entity));
        return entity;
    }
}
