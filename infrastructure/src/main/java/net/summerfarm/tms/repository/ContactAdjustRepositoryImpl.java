package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.base.site.ContactAdjustRepository;
import net.summerfarm.tms.base.site.entity.ContactAdjustEntity;
import net.summerfarm.tms.base.site.param.ContactAdjustCommandParam;
import net.summerfarm.tms.converter.ContactAdjustConverter;
import net.summerfarm.tms.dao.ContactAdjust;
import net.summerfarm.tms.mapper.TmsContactAdjustMapper;
import net.summerfarm.tms.query.site.ContactAdjustQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/1/5 11:28<br/>
 *
 * <AUTHOR> />
 */
@Service
public class ContactAdjustRepositoryImpl implements ContactAdjustRepository {

    @Resource
    private TmsContactAdjustMapper tmsContactAdjustMapper;

    @Override
    public ContactAdjustEntity query(ContactAdjustQuery contactAdjustQuery) {
        ContactAdjust contactAdjust = tmsContactAdjustMapper.selectOne(new LambdaQueryWrapper<ContactAdjust>()
                .eq(ContactAdjust::getContactId, contactAdjustQuery.getContactId())
                .orderByDesc(ContactAdjust::getId)
                .last("limit 1"));

        return ContactAdjustConverter.do2Entity(contactAdjust);
    }

    @Override
    public void save(ContactAdjustCommandParam param) {
        if (param == null){
            return;
        }
        ContactAdjust contactAdjust = ContactAdjustConverter.param2Do(param);
        tmsContactAdjustMapper.insert(contactAdjust);
    }
}
