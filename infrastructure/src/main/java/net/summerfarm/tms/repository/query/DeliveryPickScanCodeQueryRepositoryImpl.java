package net.summerfarm.tms.repository.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.tms.converter.TmsDeliveryPickScanCodeConverter;
import net.summerfarm.tms.dao.TmsDeliveryPickScanCode;
import net.summerfarm.tms.delivery.entity.DeliveryPickScanCodeEntity;
import net.summerfarm.tms.mapper.TmsDeliveryPickScanCodeMapper;
import net.summerfarm.tms.pick.repository.DeliveryPickScanCodeQueryRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 拣货查询服务<br/>
 * date: 2024/8/14 15:13<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliveryPickScanCodeQueryRepositoryImpl implements DeliveryPickScanCodeQueryRepository {

    @Resource
    private TmsDeliveryPickScanCodeMapper tmsDeliveryPickScanCodeMapper;

    @Override
    public DeliveryPickScanCodeEntity queryByOnlyCode(String onlyCode) {
        if(StringUtils.isBlank(onlyCode)){
            return null;
        }
        TmsDeliveryPickScanCode tmsDeliveryPickScanCode = tmsDeliveryPickScanCodeMapper.selectOne(new LambdaQueryWrapper<TmsDeliveryPickScanCode>()
                .eq(TmsDeliveryPickScanCode::getOnlyCode, onlyCode)
                .last("limit 1")
        );

        return TmsDeliveryPickScanCodeConverter.do2Entity(tmsDeliveryPickScanCode);
    }

    @Override
    public List<DeliveryPickScanCodeEntity> queryByDeliveryBatchId(Long deliveryBatchId) {
        if (deliveryBatchId == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryPickScanCode> tmsDeliveryPickScanCodes = tmsDeliveryPickScanCodeMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPickScanCode>()
                .eq(TmsDeliveryPickScanCode::getDeliveryBatchId, deliveryBatchId)
        );
        if (CollectionUtils.isEmpty(tmsDeliveryPickScanCodes)) {
            return Collections.emptyList();
        }
        return tmsDeliveryPickScanCodes.stream().map(TmsDeliveryPickScanCodeConverter::do2Entity).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
