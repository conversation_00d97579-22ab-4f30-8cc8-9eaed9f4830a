package net.summerfarm.tms.repository.delivery;

import net.summerfarm.tms.converter.delivery.TmsDeliveryPickShortOrderMappingConverter;
import net.summerfarm.tms.delivery.entity.TmsDeliveryPickShortOrderMappingEntity;
import net.summerfarm.tms.delivery.repository.TmsDeliveryPickShortOrderMappingCommandRepository;
import net.summerfarm.tms.mapper.delivery.TmsDeliveryPickShortOrderMappingMapper;
import net.summerfarm.tms.model.delivery.TmsDeliveryPickShortOrderMapping;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-24 14:21:44
 */
@Repository
public class TmsDeliveryPickShortOrderMappingCommandRepositoryImpl implements TmsDeliveryPickShortOrderMappingCommandRepository {

    @Autowired
    private TmsDeliveryPickShortOrderMappingMapper tmsDeliveryPickShortOrderMappingMapper;

    @Override
    public int batchInsert(List<TmsDeliveryPickShortOrderMappingEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return 0;
        }
        List<TmsDeliveryPickShortOrderMapping> tmsDeliveryPickShortOrderMappingList = TmsDeliveryPickShortOrderMappingConverter.toTmsDeliveryPickShortOrderMappingList(entityList);
        return tmsDeliveryPickShortOrderMappingMapper.batchInsert(tmsDeliveryPickShortOrderMappingList);
    }

}