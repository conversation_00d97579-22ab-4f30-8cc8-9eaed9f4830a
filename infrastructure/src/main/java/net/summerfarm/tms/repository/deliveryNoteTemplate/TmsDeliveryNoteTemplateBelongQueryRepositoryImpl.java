package net.summerfarm.tms.repository.deliveryNoteTemplate;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongConverter;
import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateBelongQueryParam;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateBelongQueryRepository;
import net.summerfarm.tms.mapper.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
@Repository
public class TmsDeliveryNoteTemplateBelongQueryRepositoryImpl implements TmsDeliveryNoteTemplateBelongQueryRepository {

    @Autowired
    private TmsDeliveryNoteTemplateBelongMapper tmsDeliveryNoteTemplateBelongMapper;

    @Override
    public TmsDeliveryNoteTemplateBelongEntity selectById(Long id) {
        return TmsDeliveryNoteTemplateBelongConverter.toTmsDeliveryNoteTemplateBelongEntity(tmsDeliveryNoteTemplateBelongMapper.selectById(id));
    }


    @Override
    public List<TmsDeliveryNoteTemplateBelongEntity> selectByCondition(TmsDeliveryNoteTemplateBelongQueryParam param) {
        return TmsDeliveryNoteTemplateBelongConverter.toTmsDeliveryNoteTemplateBelongEntityList(tmsDeliveryNoteTemplateBelongMapper.selectByCondition(param));
    }

    @Override
    public List<TmsDeliveryNoteTemplateBelongEntity> selectListByCondition(TmsDeliveryNoteTemplateBelongQueryParam param) {
        List<TmsDeliveryNoteTemplateBelong> tmsDeliveryNoteTemplateBelongList = tmsDeliveryNoteTemplateBelongMapper.selectList(new LambdaQueryWrapper<TmsDeliveryNoteTemplateBelong>()
                .in(!CollectionUtils.isEmpty(param.getScopeBusinessIds()), TmsDeliveryNoteTemplateBelong::getScopeBusinessId, param.getScopeBusinessIds())
        );

        return TmsDeliveryNoteTemplateBelongConverter.toTmsDeliveryNoteTemplateBelongEntityList(tmsDeliveryNoteTemplateBelongList);
    }
}