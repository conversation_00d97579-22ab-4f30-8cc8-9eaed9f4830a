package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.converter.TmsDeliveryBatchFareConverter;
import net.summerfarm.tms.dao.TmsDeliveryBatchFare;
import net.summerfarm.tms.delivery.DeliveryBatchFareRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryBatchFareEntity;
import net.summerfarm.tms.mapper.TmsDeliveryBatchFareMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:配送批次费用项 仓库实现类
 * date: 2023/5/30 15:14
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class DeliveryBatchFareRepositoryImpl implements DeliveryBatchFareRepository {

    private final TmsDeliveryBatchFareMapper tmsDeliveryBatchFareMapper;

    @Override
    public List<DeliveryBatchFareEntity> queryList(Long deliveryBatchId) {
        List<TmsDeliveryBatchFare> tmsDeliveryBatchFares = tmsDeliveryBatchFareMapper.selectList(new LambdaQueryWrapper<TmsDeliveryBatchFare>()
                .eq(TmsDeliveryBatchFare::getDeliveryBatchId, deliveryBatchId));
        return tmsDeliveryBatchFares.stream().map(TmsDeliveryBatchFareConverter::fare2Entity).collect(Collectors.toList());
    }

    @Override
    public void saveOrUpdate(List<DeliveryBatchFareEntity> newDeliveryBatchFareEntities, Long deliveryBatchId) {
        if (newDeliveryBatchFareEntities == null || deliveryBatchId == null){
            return;
        }
        //根据配送批次ID查询配送批次费用信息
        List<TmsDeliveryBatchFare> existedTmsDeliveryBatchFares = tmsDeliveryBatchFareMapper.selectList(new LambdaQueryWrapper<TmsDeliveryBatchFare>()
                .eq(TmsDeliveryBatchFare::getDeliveryBatchId, deliveryBatchId));
        if (!CollectionUtils.isEmpty(existedTmsDeliveryBatchFares)){
            List<Long> fareIds = existedTmsDeliveryBatchFares.stream().map(TmsDeliveryBatchFare::getId).collect(Collectors.toList());
            tmsDeliveryBatchFareMapper.deleteBatchIds(fareIds);
        }
        if (CollectionUtils.isEmpty(newDeliveryBatchFareEntities)){
            return;
        }
        List<TmsDeliveryBatchFare> newTmsDeliveryBatchFares = newDeliveryBatchFareEntities.stream().map(TmsDeliveryBatchFareConverter::entity2fare).collect(Collectors.toList());
        tmsDeliveryBatchFareMapper.insertBatch(newTmsDeliveryBatchFares);
    }

}
