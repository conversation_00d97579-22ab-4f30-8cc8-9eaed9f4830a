package net.summerfarm.tms.repository.performance;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.performance.TmsDeliveryPerformanceReviewDetailAppealItemConverter;
import net.summerfarm.tms.mapper.performance.TmsDeliveryPerformanceReviewDetailAppealItemMapper;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAppealItemQueryParam;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailAppealItemQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:07
* @version 1.0
*
*/
@Repository
public class DeliveryPerformanceReviewDetailAppealItemQueryRepositoryImpl implements DeliveryPerformanceReviewDetailAppealItemQueryRepository {

    @Resource
    private TmsDeliveryPerformanceReviewDetailAppealItemMapper tmsDeliveryPerformanceReviewDetailAppealItemMapper;


    @Override
    public DeliveryPerformanceReviewDetailAppealItemEntity selectById(Long id) {
        return TmsDeliveryPerformanceReviewDetailAppealItemConverter.toTmsDeliveryPerformanceReviewDetailAppealItemEntity(tmsDeliveryPerformanceReviewDetailAppealItemMapper.selectById(id));
    }


    @Override
    public List<DeliveryPerformanceReviewDetailAppealItemEntity> selectByCondition(TmsDeliveryPerformanceReviewDetailAppealItemQueryParam param) {
        return TmsDeliveryPerformanceReviewDetailAppealItemConverter.toTmsDeliveryPerformanceReviewDetailAppealItemEntityList(tmsDeliveryPerformanceReviewDetailAppealItemMapper.selectByCondition(param));
    }

}