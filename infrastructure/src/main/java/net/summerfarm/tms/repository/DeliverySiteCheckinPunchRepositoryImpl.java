package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.converter.TmsDeliverySiteCheckinPunchConverter;
import net.summerfarm.tms.dao.TmsDeliverySiteCheckinPunch;
import net.summerfarm.tms.delivery.DeliverySiteCheckinPunchRepository;
import net.summerfarm.tms.delivery.entity.DeliverySiteCheckinPunchEntity;
import net.summerfarm.tms.mapper.TmsDeliverySiteCheckinPunchMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/10/25 18:35<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliverySiteCheckinPunchRepositoryImpl implements DeliverySiteCheckinPunchRepository {

    @Resource
    private TmsDeliverySiteCheckinPunchMapper tmsDeliverySiteCheckinPunchMapper;

    @Override
    public DeliverySiteCheckinPunchEntity queryByDeliverySiteId(Long deliverySiteId) {
        TmsDeliverySiteCheckinPunch tmsDeliverySiteCheckinPunch = tmsDeliverySiteCheckinPunchMapper.selectOne(new LambdaQueryWrapper<TmsDeliverySiteCheckinPunch>()
                .eq(TmsDeliverySiteCheckinPunch::getDeliverySiteId, deliverySiteId)
        );
        return TmsDeliverySiteCheckinPunchConverter.model2Entity(tmsDeliverySiteCheckinPunch);
    }

    @Override
    public void save(DeliverySiteCheckinPunchEntity entity) {
        TmsDeliverySiteCheckinPunch tmsDeliverySiteCheckinPunch = TmsDeliverySiteCheckinPunchConverter.entity2Model(entity);
        tmsDeliverySiteCheckinPunchMapper.insert(tmsDeliverySiteCheckinPunch);
    }
}
