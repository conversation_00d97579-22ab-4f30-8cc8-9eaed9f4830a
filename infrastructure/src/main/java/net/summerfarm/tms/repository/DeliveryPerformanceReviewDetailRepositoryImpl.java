package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.TmsDeliveryPerformanceReviewDetailConverter;
import net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetail;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailRepository;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.mapper.TmsDeliveryPerformanceReviewDetailMapper;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailQueryParam;
import net.summerfarm.tms.query.delivery.PerformanceReviewDetailQuery;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import net.summerfarm.tms.utils.PageInfoHelper;
import net.xianmu.common.input.PageSortInput;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/6/28 16:13<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliveryPerformanceReviewDetailRepositoryImpl implements DeliveryPerformanceReviewDetailRepository {
    @Resource
    private TmsDeliveryPerformanceReviewDetailMapper tmsDeliveryPerformanceReviewDetailMapper;

    @Override
    public void saveBatch(List<DeliveryPerformanceReviewDetailEntity> deliveryPerformanceReviewDetailEntities) {
        List<TmsDeliveryPerformanceReviewDetail> tmsDeliveryPerformanceReviewDetails = deliveryPerformanceReviewDetailEntities.stream().map(TmsDeliveryPerformanceReviewDetailConverter::entity2Do).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(tmsDeliveryPerformanceReviewDetails)){
            return;
        }
        MybatisPlusUtil.createBatch(tmsDeliveryPerformanceReviewDetails, TmsDeliveryPerformanceReviewDetail.class);
    }

    @Override
    public long queryCountByTaskIdAndState(Long performanceReviewTaskId, Integer state) {
        Long num = tmsDeliveryPerformanceReviewDetailMapper.selectCount(new LambdaQueryWrapper<TmsDeliveryPerformanceReviewDetail>()
                .eq(TmsDeliveryPerformanceReviewDetail::getPerformanceReviewTaskId, performanceReviewTaskId)
                .eq(TmsDeliveryPerformanceReviewDetail::getState, state)
        );
        return num == null ? 0 : num;
    }

    @Override
    public PageInfo<DeliveryPerformanceReviewDetailEntity> queryListPage(PerformanceReviewDetailQuery performanceReviewDetailQuery) {
        TmsDeliveryPerformanceReviewDetailQueryParam queryParam = getTmsDeliveryPerformanceReviewDetailQueryParam(performanceReviewDetailQuery);
        // endregion
        PageInfo<TmsDeliveryPerformanceReviewDetailEntity> pageInfo = this.getPage(queryParam);

        List<TmsDeliveryPerformanceReviewDetailEntity> tmsDeliveryPerformanceReviewDetails = pageInfo.getList();

        PageInfo<DeliveryPerformanceReviewDetailEntity> deliveryPerformanceReviewDetailEntityPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo,deliveryPerformanceReviewDetailEntityPageInfo);
        deliveryPerformanceReviewDetailEntityPageInfo.setList(tmsDeliveryPerformanceReviewDetails.stream()
                .map(TmsDeliveryPerformanceReviewDetailConverter::doDetailEntity2Entity)
                .collect(Collectors.toList()));

        return deliveryPerformanceReviewDetailEntityPageInfo;
    }

    private static TmsDeliveryPerformanceReviewDetailQueryParam getTmsDeliveryPerformanceReviewDetailQueryParam(PerformanceReviewDetailQuery performanceReviewDetailQuery) {

        TmsDeliveryPerformanceReviewDetailQueryParam queryParam = new TmsDeliveryPerformanceReviewDetailQueryParam();
        queryParam.setPageIndex(performanceReviewDetailQuery.getPageIndex());
        queryParam.setPageSize(performanceReviewDetailQuery.getPageSize());

        queryParam.setPerformanceReviewTaskId(performanceReviewDetailQuery.getPerformanceReviewTaskId());
        queryParam.setBeginSiteIdList(performanceReviewDetailQuery.getBeginSiteIds());
        queryParam.setDriverNameLike(performanceReviewDetailQuery.getDriverName());
        queryParam.setStateList(performanceReviewDetailQuery.getStates());
        queryParam.setDeliveryTimeBegin(performanceReviewDetailQuery.getBeginDeliveryTime());
        queryParam.setDeliveryTimeEnd(performanceReviewDetailQuery.getEndDeliveryTime());
        queryParam.setOuterClientNameLike(performanceReviewDetailQuery.getClientName());
        queryParam.setAiAllPass(performanceReviewDetailQuery.getAiAllPass());
        queryParam.setSignInStatus(performanceReviewDetailQuery.getSignInStatus());
        queryParam.setTemperatureCondition(performanceReviewDetailQuery.getTemperatureCondition());

        // region sort
        List<PageSortInput> sortList = new ArrayList<>();

        PageSortInput pageSortInput1 = new PageSortInput();
        pageSortInput1.setSortBy("t.delivery_time");
        pageSortInput1.setOrderBy("asc");
        sortList.add(pageSortInput1);

        PageSortInput pageSortInput2 = new PageSortInput();
        pageSortInput2.setSortBy("t.begin_site_name");
        pageSortInput2.setOrderBy("asc");
        sortList.add(pageSortInput2);

        PageSortInput pageSortInput3 = new PageSortInput();
        pageSortInput3.setSortBy("t.path_code");
        pageSortInput3.setOrderBy("asc");
        sortList.add(pageSortInput3);

        PageSortInput pageSortInput4 = new PageSortInput();
        pageSortInput4.setSortBy("t.sequence");
        pageSortInput4.setOrderBy("asc");
        sortList.add(pageSortInput4);

        queryParam.setSortList(sortList);
        return queryParam;
    }

    @Override
    public PageInfo<DeliveryPerformanceReviewDetailEntity> queryReviewDetailPageGgBatchId(PerformanceReviewDetailQuery performanceReviewDetailQuery) {
        PageHelper.startPage(performanceReviewDetailQuery.getPageIndex(), performanceReviewDetailQuery.getPageSize());
        List<TmsDeliveryPerformanceReviewDetail> tmsDeliveryPerformanceReviewDetails = tmsDeliveryPerformanceReviewDetailMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPerformanceReviewDetail>()
                .eq(performanceReviewDetailQuery.getPerformanceReviewTaskId() != null,TmsDeliveryPerformanceReviewDetail::getPerformanceReviewTaskId, performanceReviewDetailQuery.getPerformanceReviewTaskId())
                .in(CollectionUtils.isNotEmpty(performanceReviewDetailQuery.getBatchTypes()), TmsDeliveryPerformanceReviewDetail::getDeliveryBatchType, performanceReviewDetailQuery.getBatchTypes())
                .in(CollectionUtils.isNotEmpty(performanceReviewDetailQuery.getStates()), TmsDeliveryPerformanceReviewDetail::getState, performanceReviewDetailQuery.getStates())
                .ge(performanceReviewDetailQuery.getBeginDeliveryTime() != null, TmsDeliveryPerformanceReviewDetail::getDeliveryTime, performanceReviewDetailQuery.getBeginDeliveryTime())
                .le(performanceReviewDetailQuery.getEndDeliveryTime() != null, TmsDeliveryPerformanceReviewDetail::getDeliveryTime, performanceReviewDetailQuery.getEndDeliveryTime())
                .groupBy(TmsDeliveryPerformanceReviewDetail::getDeliveryBatchId)
                .orderByAsc(TmsDeliveryPerformanceReviewDetail::getBeginSiteId)
                .orderByAsc(TmsDeliveryPerformanceReviewDetail::getDeliveryTime)
                .orderByDesc(TmsDeliveryPerformanceReviewDetail::getDeliveryBatchId)
        );
        PageInfo pageInfo = PageInfoHelper.createPageInfo(tmsDeliveryPerformanceReviewDetails);

        PageInfo<DeliveryPerformanceReviewDetailEntity> deliveryPerformanceReviewDetailEntityPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo,deliveryPerformanceReviewDetailEntityPageInfo);
        deliveryPerformanceReviewDetailEntityPageInfo.setList(tmsDeliveryPerformanceReviewDetails.stream()
                .map(TmsDeliveryPerformanceReviewDetailConverter::do2Entity).collect(Collectors.toList()));

        return deliveryPerformanceReviewDetailEntityPageInfo;
    }

    @Override
    public List<DeliveryPerformanceReviewDetailEntity> queryByBatchIdsAndTaskIds(List<Long> deliveryBatchIdList,List<Long> taskIds) {
        List<TmsDeliveryPerformanceReviewDetail> tmsDeliveryPerformanceReviewDetails = tmsDeliveryPerformanceReviewDetailMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPerformanceReviewDetail>()
                .in(TmsDeliveryPerformanceReviewDetail::getDeliveryBatchId, deliveryBatchIdList)
                .in(TmsDeliveryPerformanceReviewDetail::getPerformanceReviewTaskId, taskIds)
        );

        return tmsDeliveryPerformanceReviewDetails.stream().map(TmsDeliveryPerformanceReviewDetailConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public DeliveryPerformanceReviewDetailEntity queryById(Long performanceReviewDetailId) {
        if(performanceReviewDetailId == null){
            return null;
        }
        return TmsDeliveryPerformanceReviewDetailConverter.do2Entity(tmsDeliveryPerformanceReviewDetailMapper.selectById(performanceReviewDetailId));
    }

    @Override
    public void update(List<DeliveryPerformanceReviewDetailEntity> detailEntities) {
        if(CollectionUtils.isEmpty(detailEntities)){
            return;
        }
        List<TmsDeliveryPerformanceReviewDetail> reviewDetails = detailEntities.stream().map(TmsDeliveryPerformanceReviewDetailConverter::entity2Do).collect(Collectors.toList());
        //批量更新
        MybatisPlusUtil.updateBatch(reviewDetails, TmsDeliveryPerformanceReviewDetail.class);
    }

    @Override
    public long queryTrunkCountByTaskIdAndState(Long performanceReviewTaskId, Integer state) {
        return tmsDeliveryPerformanceReviewDetailMapper.queryTrunkCountByTaskIdAndState(performanceReviewTaskId,state);
    }

    @Override
    public List<DeliveryPerformanceReviewDetailEntity> queryList(PerformanceReviewDetailQuery performanceReviewDetailQuery) {
        List<TmsDeliveryPerformanceReviewDetail> tmsDeliveryPerformanceReviewDetails = tmsDeliveryPerformanceReviewDetailMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPerformanceReviewDetail>()
                .eq(performanceReviewDetailQuery.getPerformanceReviewTaskId() != null,TmsDeliveryPerformanceReviewDetail::getPerformanceReviewTaskId, performanceReviewDetailQuery.getPerformanceReviewTaskId())
                .in(CollectionUtils.isNotEmpty(performanceReviewDetailQuery.getBatchTypes()), TmsDeliveryPerformanceReviewDetail::getDeliveryBatchType, performanceReviewDetailQuery.getBatchTypes())
                .in(CollectionUtils.isNotEmpty(performanceReviewDetailQuery.getStates()), TmsDeliveryPerformanceReviewDetail::getState, performanceReviewDetailQuery.getStates())
                .ge(performanceReviewDetailQuery.getBeginDeliveryTime() != null, TmsDeliveryPerformanceReviewDetail::getDeliveryTime, performanceReviewDetailQuery.getBeginDeliveryTime())
                .le(performanceReviewDetailQuery.getEndDeliveryTime() != null, TmsDeliveryPerformanceReviewDetail::getDeliveryTime, performanceReviewDetailQuery.getEndDeliveryTime())
                .groupBy(performanceReviewDetailQuery.getBatchGroupBy() != null && performanceReviewDetailQuery.getBatchGroupBy(),TmsDeliveryPerformanceReviewDetail::getDeliveryBatchId)
                .orderByAsc(TmsDeliveryPerformanceReviewDetail::getBeginSiteId)
                .orderByAsc(TmsDeliveryPerformanceReviewDetail::getDeliveryTime)
                .orderByDesc(TmsDeliveryPerformanceReviewDetail::getDeliveryBatchId)
        );

        return tmsDeliveryPerformanceReviewDetails.stream().map(TmsDeliveryPerformanceReviewDetailConverter::do2Entity).collect(Collectors.toList());
    }
    @Override
    public PageInfo<TmsDeliveryPerformanceReviewDetailEntity> getPage(TmsDeliveryPerformanceReviewDetailQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<TmsDeliveryPerformanceReviewDetailEntity> entities = tmsDeliveryPerformanceReviewDetailMapper.getPage(param);
        return PageInfo.of(entities);
    }

}
