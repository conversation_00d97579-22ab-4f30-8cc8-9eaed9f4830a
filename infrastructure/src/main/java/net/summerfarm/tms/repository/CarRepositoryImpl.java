package net.summerfarm.tms.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.car.CarRepository;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.converter.TmsCarConverter;
import net.summerfarm.tms.dao.TmsCar;
import net.summerfarm.tms.enums.CarStatusEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.mapper.TmsCarMapper;
import net.summerfarm.tms.query.base.car.CarQuery;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/7/12 15:42<br/>
 *
 * <AUTHOR> />
 */
@Service
public class CarRepositoryImpl implements CarRepository {

    @Autowired
    private TmsCarMapper tmsCarMapper;

    @Override
    public void save(CarEntity carEntity) {
        TmsCar tmsCar = TmsCarConverter.entity2TmsCar(carEntity);
        tmsCar.setCreateTime(LocalDateTime.now());
        tmsCarMapper.insertSelective(tmsCar);
    }

    @Override
    public PageInfo<CarEntity> queryPage(CarQuery carQuery) {

        int pageIndex = carQuery.getPageIndex();
        int pageSize = carQuery.getPageSize();

        PageHelper.startPage(pageIndex, pageSize);
        List<TmsCar> carQueryPageList = tmsCarMapper.selectByQuery(carQuery);
        PageInfo pageInfo = PageInfoHelper.createPageInfo(carQueryPageList);
        List<CarEntity> carEntities = carQueryPageList.stream().map(TmsCarConverter::tmsCar2Entity).collect(Collectors.toList());
        pageInfo.setList(carEntities);

        return pageInfo;
    }

    @Override
    public List<CarEntity> queryList(CarQuery carQuery) {
        List<TmsCar> carList = tmsCarMapper.selectByQuery(carQuery);
        return carList.stream().map(TmsCarConverter::tmsCar2Entity).collect(Collectors.toList());
    }

    @Override
    public CarEntity getCarDetail(long id) {
        return TmsCarConverter.tmsCar2Entity(tmsCarMapper.selectById(id));
    }

    @Override
    public List<CarEntity> getCarByCarNumber(String carNumber) {
        TmsCar tmsCar = new TmsCar();
        tmsCar.setCarNumber(carNumber);
        tmsCar.setStatus(CarStatusEnum.valid.getCode());
        List<TmsCar> tmsCarList = tmsCarMapper.selectByTmsCar(tmsCar);

        List<CarEntity> carDos = new ArrayList<>();
        tmsCarList.forEach(car -> carDos.add(TmsCarConverter.tmsCar2Entity(car)));

        return carDos;
    }

    @Override
    public Long selectCarCountByNumber(String carNumber) {
        if(StrUtil.isBlank(carNumber)){
            throw new TmsRuntimeException("车牌号不能为空");
        }
        return tmsCarMapper.selectCount(new LambdaQueryWrapper<TmsCar>().eq(TmsCar::getCarNumber, carNumber));
    }

    @Override
    public int findCarById(Long id) {
        TmsCar tmsCar = tmsCarMapper.selectByPrimaryKey(id);
        return tmsCar == null ? 0 : 1;
    }

    @Override
    public void update(CarEntity carEntity) {
        TmsCar tmsCar = TmsCarConverter.entity2TmsCar(carEntity);
        tmsCar.setUpdateTime(LocalDateTime.now());
        tmsCarMapper.updateByPrimaryKey(tmsCar);
    }

    @Override
    public List<CarEntity> getCarByIdList(List<Long> carIds) {
        List<TmsCar> tmsCarList = tmsCarMapper.selectByIdList(carIds);
        return tmsCarList.stream().map(TmsCarConverter::tmsCar2Entity).collect(Collectors.toList());
    }

    @Override
    public List<CarEntity> queryPrimaryIdList(List<Long> carIdList) {
        if(CollectionUtils.isEmpty(carIdList)){
            return new ArrayList<>();
        }
        List<TmsCar> tmsCarList = tmsCarMapper.selectByIdList(carIdList);
        return tmsCarList.stream().map(TmsCarConverter::tmsCar2Entity).collect(Collectors.toList());
    }
}
