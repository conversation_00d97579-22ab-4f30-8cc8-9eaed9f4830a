package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.converter.TmsLackApprovedResponsibleConverter;
import net.summerfarm.tms.dao.TmsLackApprovedResponsible;
import net.summerfarm.tms.lack.LackResponsibleRepository;
import net.summerfarm.tms.lack.entity.LackApprovedResponsibleEntity;
import net.summerfarm.tms.mapper.TmsLackApprovedResponsibleMapper;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/9/7 16:50<br/>
 *
 * <AUTHOR> />
 */
@Service
public class LackResponsibleRepositoryImpl implements LackResponsibleRepository {

    @Resource
    private TmsLackApprovedResponsibleMapper tmsLackApprovedResponsibleMapper;

    @Override
    public void saveBatch(List<LackApprovedResponsibleEntity> lackApprovedResponsibleEntities) {
        if(CollectionUtils.isEmpty(lackApprovedResponsibleEntities)){
            return;
        }
        List<TmsLackApprovedResponsible> responsibles = lackApprovedResponsibleEntities.stream().map(TmsLackApprovedResponsibleConverter::model2Entity).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(responsibles,TmsLackApprovedResponsible.class);
    }

    @Override
    public List<LackApprovedResponsibleEntity> queryListByApprovedId(Long lackApprovedId) {
        if(lackApprovedId == null){
            return Collections.emptyList();
        }
        List<TmsLackApprovedResponsible> responsibles = tmsLackApprovedResponsibleMapper.selectList(new LambdaQueryWrapper<TmsLackApprovedResponsible>().eq(TmsLackApprovedResponsible::getApprovedId, lackApprovedId));
        return responsibles.stream().map(TmsLackApprovedResponsibleConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public List<LackApprovedResponsibleEntity> queryListByApprovedIds(List<Long> approvedIdList) {
        if(CollectionUtils.isEmpty(approvedIdList)){
            return Collections.emptyList();
        }
        List<TmsLackApprovedResponsible> responsibles = tmsLackApprovedResponsibleMapper.selectList(new LambdaQueryWrapper<TmsLackApprovedResponsible>().in(TmsLackApprovedResponsible::getApprovedId, approvedIdList));
        return responsibles.stream().map(TmsLackApprovedResponsibleConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public List<LackApprovedResponsibleEntity> queryAllList() {
        List<TmsLackApprovedResponsible> responsibles = tmsLackApprovedResponsibleMapper.selectList(new LambdaQueryWrapper<>());
        return responsibles.stream().map(TmsLackApprovedResponsibleConverter::model2Entity).collect(Collectors.toList());
    }
}
