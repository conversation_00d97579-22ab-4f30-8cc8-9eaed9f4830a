package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import lombok.NonNull;
import net.summerfarm.tms.base.car.CarRepository;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.carrier.CarrierRepository;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.driver.DriverRepository;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.path.PathCarRepository;
import net.summerfarm.tms.base.path.entity.PathCarEntity;
import net.summerfarm.tms.converter.TmsPathCarConverter;
import net.summerfarm.tms.dao.TmsPathCar;
import net.summerfarm.tms.mapper.TmsPathCarMapper;
import net.summerfarm.tms.query.base.driver.DriverQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-05-15
 **/
@Service
public class PathCarRepositoryImpl implements PathCarRepository {
	@Resource
	private TmsPathCarMapper tmsPathCarMapper;
	@Resource
	private TmsPathCarConverter tmsPathCarConverter;
	@Resource
	private DriverRepository driverRepository;
	@Resource
	private CarRepository carRepository;
	@Resource
	private CarrierRepository carrierRepository;

	@Override
	public void removeByPathId(Long pathId) {
		tmsPathCarMapper.delete(new LambdaQueryWrapper<TmsPathCar>().eq(TmsPathCar::getPathId, pathId));
	}

	@Override
	public List<PathCarEntity> queryByPathId(@NonNull Long pathId) {
		List<TmsPathCar> tmsPathCarList = tmsPathCarMapper.selectList(new LambdaQueryWrapper<TmsPathCar>()
				.eq(TmsPathCar::getPathId, pathId));
		if (CollectionUtils.isEmpty(tmsPathCarList)) {
			return null;
		}
		List<PathCarEntity> pathCarEntities = tmsPathCarConverter.doList2EntityList(tmsPathCarList);
		Map<Long, CarrierEntity> carrierMap = getCarrierMap(pathCarEntities);
		Map<Long, CarEntity> carMap = getCarMap(pathCarEntities);
		Map<Long, DriverEntity> driverMap = getDriverMap(pathCarEntities);
		for (PathCarEntity pathCarEntity : pathCarEntities) {
			CarrierEntity carrierEntity = carrierMap.get(pathCarEntity.getCarrierId());
			CarEntity carEntity = carMap.get(pathCarEntity.getCarId());
			DriverEntity driverEntity = driverMap.get(pathCarEntity.getDriverId());
			if (Objects.nonNull(carrierEntity)) {
				pathCarEntity.setCarrierName(carrierEntity.getCarrierName());
			}
			if (Objects.nonNull(carEntity)) {
				pathCarEntity.setCarNumber(carEntity.getCarNumber());
				pathCarEntity.setCarType(carEntity.getCarTypeEnum().getCode());
				pathCarEntity.setCarTypeDesc(carEntity.getCarTypeEnum().getDesc());
				pathCarEntity.setStorage(carEntity.getCarStorageEnum().getCode());
				pathCarEntity.setStorageDesc(carEntity.getCarStorageEnum().getName());
			}
			if (Objects.nonNull(driverEntity)) {
				pathCarEntity.setDriverName(driverEntity.getName());
				pathCarEntity.setDriverPhone(driverEntity.getPhone());
			}


		}

		return pathCarEntities;
	}

	private Map<Long, CarrierEntity> getCarrierMap(List<PathCarEntity> pathCarEntities) {
		Set<Long> carrierIdList = pathCarEntities.stream().map(PathCarEntity::getCarrierId).collect(Collectors.toSet());

		if (CollectionUtils.isEmpty(carrierIdList)) {
			return Maps.newHashMap();
		}
		List<CarrierEntity> carrierList = carrierRepository.getListByIdList(carrierIdList);
		if (CollectionUtils.isEmpty(carrierList)) {
			return Maps.newHashMap();
		}
		return carrierList.stream().collect(Collectors.toMap(CarrierEntity::getId, Function.identity()));
	}

	private Map<Long, CarEntity> getCarMap(List<PathCarEntity> pathCarEntities) {
		List<Long> carIdList = pathCarEntities.stream().map(PathCarEntity::getCarId).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(carIdList)) {
			return Maps.newHashMap();
		}
		List<CarEntity> carList = carRepository.getCarByIdList(carIdList);
		if (CollectionUtils.isEmpty(carList)) {
			return Maps.newHashMap();
		}
		return carList.stream().collect(Collectors.toMap(CarEntity::getId, Function.identity()));
	}

	private Map<Long, DriverEntity> getDriverMap(List<PathCarEntity> driverEntityList) {
		Set<Long> driverIdList = driverEntityList.stream().map(PathCarEntity::getDriverId).collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(driverIdList)) {
			return Maps.newHashMap();
		}
		DriverQuery driverQuery = new DriverQuery();
		driverQuery.setIds(driverIdList);
		List<DriverEntity> driverEntities = driverRepository.queryList(driverQuery);
		if (CollectionUtils.isEmpty(driverEntities)) {
			return Maps.newHashMap();
		}
		return driverEntities.stream().collect(Collectors.toMap(DriverEntity::getId, Function.identity()));
	}
}
