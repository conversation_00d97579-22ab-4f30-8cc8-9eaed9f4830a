package net.summerfarm.tms.repository;

import net.summerfarm.tms.converter.TmsDeliveryItemConverter;
import net.summerfarm.tms.dao.TmsDeliveryItem;
import net.summerfarm.tms.delivery.DeliveryItemRepository;
import net.summerfarm.tms.delivery.entity.DeliveryItemEntity;
import net.summerfarm.tms.mapper.TmsDeliveryItemMapper;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/11/7 14:51<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliveryItemRepositoryImpl implements DeliveryItemRepository {

    @Resource
    private TmsDeliveryItemMapper tmsDeliveryItemMapper;

    @Override
    public void update(DeliveryItemEntity deliveryItemEntity) {
        TmsDeliveryItem tmsDeliveryItem = TmsDeliveryItemConverter.entity2Do(deliveryItemEntity);
        tmsDeliveryItemMapper.updateById(tmsDeliveryItem);
    }

    @Override
    public void save(DeliveryItemEntity deliveryItemEntity) {
        TmsDeliveryItem tmsDeliveryItem = TmsDeliveryItemConverter.entity2Do(deliveryItemEntity);
        tmsDeliveryItemMapper.insert(tmsDeliveryItem);
        deliveryItemEntity.setId(tmsDeliveryItem.getId());
    }

    @Override
    public void saveBatch(ArrayList<DeliveryItemEntity> saveDeliveryItemList) {
        if(CollectionUtils.isEmpty(saveDeliveryItemList)){
            return;
        }
        List<TmsDeliveryItem> tmsDeliveryItems = saveDeliveryItemList.stream().map(TmsDeliveryItemConverter::entity2Do).collect(Collectors.toList());
        for (TmsDeliveryItem tmsDeliveryItem : tmsDeliveryItems) {
            tmsDeliveryItem.setPlanReceiptCount(tmsDeliveryItem.getPlanReceiptCount() == null ? 0 : tmsDeliveryItem.getPlanReceiptCount());
            tmsDeliveryItem.setRealReceiptCount(tmsDeliveryItem.getRealReceiptCount() == null ? 0 : tmsDeliveryItem.getRealReceiptCount());
            tmsDeliveryItem.setShortCount(tmsDeliveryItem.getShortCount() == null ? 0 : tmsDeliveryItem.getShortCount());
            tmsDeliveryItem.setInterceptCount(tmsDeliveryItem.getInterceptCount() == null ? 0 : tmsDeliveryItem.getInterceptCount());
            tmsDeliveryItem.setRejectCount(tmsDeliveryItem.getRejectCount() == null ? 0 : tmsDeliveryItem.getRejectCount());
            tmsDeliveryItem.setScanCount(tmsDeliveryItem.getScanCount() == null ? 0 : tmsDeliveryItem.getScanCount());
            tmsDeliveryItem.setNoscanCount(tmsDeliveryItem.getNoscanCount() == null ? 0 : tmsDeliveryItem.getNoscanCount());
        }
        tmsDeliveryItemMapper.saveBatch(tmsDeliveryItems);
    }

    @Override
    public void batchUpdate(List<DeliveryItemEntity> deliveryItemEntities) {
        if(CollectionUtils.isEmpty(deliveryItemEntities)){
            return;
        }
        List<TmsDeliveryItem> tmsDeliveryItems = deliveryItemEntities.stream().map(TmsDeliveryItemConverter::entity2Do).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(tmsDeliveryItems,TmsDeliveryItem.class);
    }
}
