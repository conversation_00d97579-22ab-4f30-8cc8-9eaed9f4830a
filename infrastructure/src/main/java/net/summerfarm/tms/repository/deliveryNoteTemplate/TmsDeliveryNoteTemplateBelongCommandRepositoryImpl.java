package net.summerfarm.tms.repository.deliveryNoteTemplate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.converter.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongConverter;
import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateBelongCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateBelongCommandRepository;
import net.summerfarm.tms.mapper.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongMapper;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
@Repository
public class TmsDeliveryNoteTemplateBelongCommandRepositoryImpl implements TmsDeliveryNoteTemplateBelongCommandRepository {

    @Autowired
    private TmsDeliveryNoteTemplateBelongMapper tmsDeliveryNoteTemplateBelongMapper;
    @Override
    public TmsDeliveryNoteTemplateBelongEntity insertSelective(TmsDeliveryNoteTemplateBelongCommandParam param) {
        TmsDeliveryNoteTemplateBelong tmsDeliveryNoteTemplateBelong = TmsDeliveryNoteTemplateBelongConverter.toTmsDeliveryNoteTemplateBelong(param);
        tmsDeliveryNoteTemplateBelongMapper.insertSelective(tmsDeliveryNoteTemplateBelong);
        return TmsDeliveryNoteTemplateBelongConverter.toTmsDeliveryNoteTemplateBelongEntity(tmsDeliveryNoteTemplateBelong);
    }

    @Override
    public int updateSelectiveById(TmsDeliveryNoteTemplateBelongCommandParam param){
        return tmsDeliveryNoteTemplateBelongMapper.updateSelectiveById(TmsDeliveryNoteTemplateBelongConverter.toTmsDeliveryNoteTemplateBelong(param));
    }


    @Override
    public int remove(Long id) {
        return tmsDeliveryNoteTemplateBelongMapper.remove(id);
    }

    @Override
    public void removeByTemplateId(Long templateId) {
        if(templateId == null){
            return;
        }
        tmsDeliveryNoteTemplateBelongMapper.delete(new LambdaQueryWrapper<TmsDeliveryNoteTemplateBelong>().eq(TmsDeliveryNoteTemplateBelong::getDeliveryNoteTemplateId, templateId));
    }

    @Override
    public void batchInsert(List<TmsDeliveryNoteTemplateBelongCommandParam> belongCommandParamList) {
        if(CollectionUtils.isEmpty(belongCommandParamList)){
            return;
        }
        List<TmsDeliveryNoteTemplateBelong> tmsDeliveryNoteTemplateBelongList = belongCommandParamList.stream().map(TmsDeliveryNoteTemplateBelongConverter::toTmsDeliveryNoteTemplateBelong).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(tmsDeliveryNoteTemplateBelongList, TmsDeliveryNoteTemplateBelong.class);
    }
}