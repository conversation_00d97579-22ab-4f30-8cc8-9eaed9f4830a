package net.summerfarm.tms.repository.performance;

import net.summerfarm.manage.domain.performance.param.command.TmsDeliveryPerformanceReviewDetailAppealItemCommandParam;
import net.summerfarm.tms.converter.performance.TmsDeliveryPerformanceReviewDetailAppealItemConverter;
import net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppealItem;
import net.summerfarm.tms.mapper.performance.TmsDeliveryPerformanceReviewDetailAppealItemMapper;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailAppealItemCommandRepository;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-08-30 14:11:07
* @version 1.0
*
*/
@Repository
public class DeliveryPerformanceReviewDetailAppealItemCommandRepositoryImpl implements DeliveryPerformanceReviewDetailAppealItemCommandRepository {

    @Resource
    private TmsDeliveryPerformanceReviewDetailAppealItemMapper tmsDeliveryPerformanceReviewDetailAppealItemMapper;

    @Override
    public void update(List<DeliveryPerformanceReviewDetailAppealItemEntity> deliveryPerformanceReviewDetailAppealItemEntities) {
        if(CollectionUtils.isEmpty(deliveryPerformanceReviewDetailAppealItemEntities)){
            return;
        }
        List<TmsDeliveryPerformanceReviewDetailAppealItem> items = deliveryPerformanceReviewDetailAppealItemEntities.stream().map(TmsDeliveryPerformanceReviewDetailAppealItemConverter::toTmsDeliveryPerformanceReviewDetailAppealItem).collect(Collectors.toList());
        items.sort(Comparator.comparing(TmsDeliveryPerformanceReviewDetailAppealItem::getId));
        MybatisPlusUtil.updateBatch(items,TmsDeliveryPerformanceReviewDetailAppealItem.class);
    }
}