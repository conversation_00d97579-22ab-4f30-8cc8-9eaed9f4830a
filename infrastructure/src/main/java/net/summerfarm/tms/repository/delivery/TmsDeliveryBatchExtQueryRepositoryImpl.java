package net.summerfarm.tms.repository.delivery;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.prometheus.client.Collector;
import net.summerfarm.tms.converter.delivery.TmsDeliveryBatchExtConverter;
import net.summerfarm.tms.delivery.entity.TmsDeliveryBatchExtEntity;
import net.summerfarm.tms.delivery.param.query.TmsDeliveryBatchExtQueryParam;
import net.summerfarm.tms.delivery.repository.TmsDeliveryBatchExtQueryRepository;
import net.summerfarm.tms.enums.DeliveryBatchExtEnum;
import net.summerfarm.tms.mapper.delivery.TmsDeliveryBatchExtMapper;
import net.summerfarm.tms.model.delivery.TmsDeliveryBatchExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2025-05-14 15:11:42
* @version 1.0
*
*/
@Repository
public class TmsDeliveryBatchExtQueryRepositoryImpl implements TmsDeliveryBatchExtQueryRepository {

    @Autowired
    private TmsDeliveryBatchExtMapper tmsDeliveryBatchExtMapper;


    @Override
    public PageInfo<TmsDeliveryBatchExtEntity> getPage(TmsDeliveryBatchExtQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<TmsDeliveryBatchExtEntity> entities = tmsDeliveryBatchExtMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public TmsDeliveryBatchExtEntity selectById(Long id) {
        return TmsDeliveryBatchExtConverter.toTmsDeliveryBatchExtEntity(tmsDeliveryBatchExtMapper.selectById(id));
    }


    @Override
    public List<TmsDeliveryBatchExtEntity> selectByCondition(TmsDeliveryBatchExtQueryParam param) {
        return TmsDeliveryBatchExtConverter.toTmsDeliveryBatchExtEntityList(tmsDeliveryBatchExtMapper.selectByCondition(param));
    }

    @Override
    public String queryKeepTemperatureMethodPicsByBatchId(Long deliveryBatchId) {
        if(deliveryBatchId == null){
            return "";
        }
        TmsDeliveryBatchExtQueryParam queryParam = new TmsDeliveryBatchExtQueryParam();
        queryParam.setDeliveryBatchId(deliveryBatchId);
        queryParam.setExtKey(DeliveryBatchExtEnum.KEEP_TEMPERATURE_METHOD_PICS.getCode());
        List<TmsDeliveryBatchExtEntity> tmsDeliveryBatchExtEntities = this.selectByCondition(queryParam);

        if(CollectionUtils.isEmpty(tmsDeliveryBatchExtEntities)){
            return "";
        }

        return tmsDeliveryBatchExtEntities.get(0).getExtValue();
    }

    @Override
    public Map<Long, String> queryKeepTemperatureMethodPicsByBatchIds(List<Long> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return Collections.emptyMap();
        }
        TmsDeliveryBatchExtQueryParam queryParam = new TmsDeliveryBatchExtQueryParam();
        queryParam.setBatchIds(batchIds);
        queryParam.setExtKey(DeliveryBatchExtEnum.KEEP_TEMPERATURE_METHOD_PICS.getCode());
        List<TmsDeliveryBatchExt> tmsDeliveryBatchExts = tmsDeliveryBatchExtMapper.selectByCondition(queryParam);
        if (CollectionUtils.isEmpty(tmsDeliveryBatchExts)) {
            return Collections.emptyMap();
        }

        return tmsDeliveryBatchExts.stream()
                .collect(Collectors.toMap(TmsDeliveryBatchExt::getDeliveryBatchId, TmsDeliveryBatchExt::getExtValue, (key1, key2) -> key1));
    }
}