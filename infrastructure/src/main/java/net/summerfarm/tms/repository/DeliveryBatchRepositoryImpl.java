package net.summerfarm.tms.repository;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.converter.*;
import net.summerfarm.tms.dao.*;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliverySectionRepository;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.delivery.param.command.TmsDeliveryBatchExtCommandParam;
import net.summerfarm.tms.delivery.repository.TmsDeliveryBatchExtCommandRepository;
import net.summerfarm.tms.delivery.vo.BatchCarLoadStaticVO;
import net.summerfarm.tms.delivery.vo.BatchDetailLoadStaticVO;
import net.summerfarm.tms.delivery.vo.BatchLoadRatioVO;
import net.summerfarm.tms.enums.DeliveryBatchEnums;
import net.summerfarm.tms.delivery.entity.BoardEntity;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliverySectionEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.enums.DeliveryBatchExtEnum;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.jsonobject.DeliveryBatchLoadRatioCalculateJson;
import net.summerfarm.tms.mapper.*;
import net.summerfarm.tms.query.board.BoardQuery;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliverySectionQuery;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import net.summerfarm.tms.utils.PageInfoHelper;
import net.xianmu.gaode.support.enums.DriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.enums.XMDriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.service.LbsGaoDeService;
import net.xianmu.gaode.support.service.input.PathSectionInput;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import net.xianmu.gaode.support.service.vo.PathSection;
import net.xianmu.gaode.support.service.vo.PathSectionDetail;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/13 18:15<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class DeliveryBatchRepositoryImpl implements DeliveryBatchRepository {

    @Resource
    private TmsDeliveryBatchMapper tmsDeliveryBatchMapper;
    @Resource
    private TmsDeliveryOrderMapper tmsDeliveryOrderMapper;
    @Resource
    private TmsDriverMapper tmsDriverMapper;
    @Resource
    private TmsCarMapper tmsCarMapper;
    @Resource
    private CarrierMapper carrierMapper;
    @Resource
    private TmsDeliverySiteMapper tmsDeliverySiteMapper;
    @Resource
    private DeliverySectionRepository deliverySectionRepository;
    @Resource
    private TmsDistSiteMapper tmsDistSiteMapper;
    @Resource
    private TmsDeliveryBatchRelationMapper tmsDeliveryBatchRelationMapper;
    @Resource
    private TmsDeliverySectionMapper tmsDeliverySectionMapper;
    @Resource
    private LbsGaoDeService lbsGaoDeService;
    @Resource
    private TmsDeliveryBatchExtCommandRepository tmsDeliveryBatchExtCommandRepository;

    @Override
    public void save(DeliveryBatchEntity deliveryBatchEntity) {
        TmsDeliveryBatch tmsDeliveryBatch = TmsDeliveryBatchConverter.entityToTmsDeliveryBatch(deliveryBatchEntity);
        if (!CollectionUtils.isEmpty(deliveryBatchEntity.getDeliverySiteList())) {
            //开始点位
            tmsDeliveryBatch.setBeginSiteId(deliveryBatchEntity.getDeliverySiteList().get(0).getSiteId());
            //结束点位
            tmsDeliveryBatch.setEndSiteId(deliveryBatchEntity.getDeliverySiteList().get(deliveryBatchEntity.getDeliverySiteList().size() - 1).getSiteId());
        }
        Long pathId = deliveryBatchEntity.getPathId();
        if (pathId != null) {
            //开始点位
            DeliveryBatchEntity existedDeliveryBatch = getDeliveryBatch(tmsDeliveryBatch.getBeginSiteId(), deliveryBatchEntity.getDeliveryTime(),
                    pathId, null);
            if (existedDeliveryBatch != null) {
                deliveryBatchEntity.setId(existedDeliveryBatch.getId());
                return;
            }
        }
        //配送批次表的保存
        tmsDeliveryBatchMapper.insert(tmsDeliveryBatch);
        deliveryBatchEntity.setId(tmsDeliveryBatch.getId());
    }


    @Override
    public void saveByPath(DeliveryBatchEntity deliveryBatchEntity) {
        TmsDeliveryBatch tmsDeliveryBatch = TmsDeliveryBatchConverter.entityToTmsDeliveryBatch(deliveryBatchEntity);
        if (!CollectionUtils.isEmpty(deliveryBatchEntity.getDeliverySiteList())) {
            //开始点位
            tmsDeliveryBatch.setBeginSiteId(deliveryBatchEntity.getDeliverySiteList().get(0).getSiteId());
            //结束点位
            tmsDeliveryBatch.setEndSiteId(deliveryBatchEntity.getDeliverySiteList().get(deliveryBatchEntity.getDeliverySiteList().size() - 1).getSiteId());
        }
        Long pathId = deliveryBatchEntity.getPathId();
        //开始点位
        if (!Objects.isNull(deliveryBatchEntity.getCarId())) {
            DeliveryBatchEntity existedDeliveryBatch = getDeliveryBatch(tmsDeliveryBatch.getBeginSiteId(), deliveryBatchEntity.getDeliveryTime(),
                    pathId, deliveryBatchEntity.getCarId());
            if (existedDeliveryBatch != null) {
                throw new TmsRuntimeException("系统中已经存在相同车次相同配送日期的调度单");
            }
        }
        //配送批次表的保存
        tmsDeliveryBatchMapper.insert(tmsDeliveryBatch);
        deliveryBatchEntity.setId(tmsDeliveryBatch.getId());
    }


    @Override
    public DeliveryBatchEntity query(Long deliveryBatchId) {
        return TmsDeliveryBatchConverter.tmsDeliveryBatchToEntity(tmsDeliveryBatchMapper.selectById(deliveryBatchId));
    }

    @Override
    public DeliveryBatchEntity getBatchDetail(Long deliveryBatchId) {
        DeliveryBatchEntity deliveryBatchEntity = TmsDeliveryBatchConverter.tmsDeliveryBatchToEntity(tmsDeliveryBatchMapper.selectById(deliveryBatchId));
        getBaseDeliveryInfo(deliveryBatchEntity);
        return deliveryBatchEntity;
    }

    /**
     * 获取批次的基础信息，车辆、司机、承运商信息
     */
    private void getBaseDeliveryInfo(DeliveryBatchEntity deliveryBatchEntity) {
        if (deliveryBatchEntity.getDriverId() != null) {
            TmsDriver tmsDriver = tmsDriverMapper.selectById(deliveryBatchEntity.getDriverId());
            deliveryBatchEntity.setDriverEntity(TmsDriverConverter.tmsDriver2Entity(tmsDriver));
        }
        if (deliveryBatchEntity.getCarrierId() != null) {
            Carrier carrier = carrierMapper.selectById(deliveryBatchEntity.getCarrierId());
            deliveryBatchEntity.setCarrierEntity(CarrierConverter.do2Entity(carrier));
        }
        if (deliveryBatchEntity.getCarId() != null) {
            TmsCar tmsCar = tmsCarMapper.selectById(deliveryBatchEntity.getCarId());
            deliveryBatchEntity.setCarEntity(TmsCarConverter.tmsCar2Entity(tmsCar));
        }
    }

    @Override
    public void update(DeliveryBatchEntity deliveryBatchEntity) {
        TmsDeliveryBatch tmsDeliveryBatch = TmsDeliveryBatchConverter.entityToTmsDeliveryBatch(deliveryBatchEntity);
        if(!CollectionUtils.isEmpty(deliveryBatchEntity.getDeliverySiteList())){
            //开始点位
            tmsDeliveryBatch.setBeginSiteId(deliveryBatchEntity.getDeliverySiteList().get(0).getSiteId());
            //结束点位
            tmsDeliveryBatch.setEndSiteId(deliveryBatchEntity.getDeliverySiteList().get(deliveryBatchEntity.getDeliverySiteList().size() - 1).getSiteId());
        }
        tmsDeliveryBatchMapper.updateById(tmsDeliveryBatch);
    }

    @Override
    public PageInfo<DeliveryBatchEntity> queryPage(DeliveryBatchQuery deliveryBatchQuery) {
        int pageIndex = deliveryBatchQuery.getPageIndex();
        int pageSize = deliveryBatchQuery.getPageSize();
        if (deliveryBatchQuery.getBeginDeliveryTime() != null) {
            LocalDateTime beginDeliveryTime = LocalDateTime.of(deliveryBatchQuery.getBeginDeliveryTime().toLocalDate(), LocalTime.MIN);
            deliveryBatchQuery.setBeginDeliveryTime(beginDeliveryTime);
        }
        if (deliveryBatchQuery.getEndDeliveryTime() != null) {
            LocalDateTime endDeliveryTime = LocalDateTime.of(deliveryBatchQuery.getEndDeliveryTime().toLocalDate(), Constants.localTimeMaxTime);
            deliveryBatchQuery.setEndDeliveryTime(endDeliveryTime);
        }
        if (deliveryBatchQuery.getBeginTime() != null) {
            LocalDateTime beginTime = LocalDateTime.of(deliveryBatchQuery.getBeginTime().toLocalDate(), LocalTime.MIN);
            deliveryBatchQuery.setBeginTime(beginTime);
        }
        if (deliveryBatchQuery.getEndTime() != null) {
            LocalDateTime endTime = LocalDateTime.of(deliveryBatchQuery.getEndTime().toLocalDate(), Constants.localTimeMaxTime);
            deliveryBatchQuery.setEndTime(endTime);
        }

        if (deliveryBatchQuery.isNeedPagination()) {
            PageHelper.startPage(pageIndex, pageSize);
            if (deliveryBatchQuery.orderBys() == null){
                PageHelper.orderBy("id desc");
            }else {
                PageHelper.orderBy(deliveryBatchQuery.orderBys());
            }
        }
        List<TmsDeliveryBatch> tmsDeliveryBatches = tmsDeliveryBatchMapper.selectList(new LambdaQueryWrapper<TmsDeliveryBatch>()
                .eq(deliveryBatchQuery.getPathCode() != null, TmsDeliveryBatch::getPathCode, deliveryBatchQuery.getPathCode())
                .eq(deliveryBatchQuery.getBatchId() != null, TmsDeliveryBatch::getId, deliveryBatchQuery.getBatchId())
                .eq(deliveryBatchQuery.getPathId() != null, TmsDeliveryBatch::getPathId, deliveryBatchQuery.getPathId())
                .eq(deliveryBatchQuery.getBeginSiteId() != null, TmsDeliveryBatch::getBeginSiteId, deliveryBatchQuery.getBeginSiteId())
                .eq(deliveryBatchQuery.getEndSiteId() != null, TmsDeliveryBatch::getEndSiteId, deliveryBatchQuery.getEndSiteId())
                .eq(deliveryBatchQuery.getDeliveryBatchType() != null, TmsDeliveryBatch::getType, deliveryBatchQuery.getDeliveryBatchType())
                .eq(deliveryBatchQuery.getDeliveryBatchStatus() != null, TmsDeliveryBatch::getStatus, deliveryBatchQuery.getDeliveryBatchStatus())
                .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchStatusList()), TmsDeliveryBatch::getStatus, deliveryBatchQuery.getDeliveryBatchStatusList())
                .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchTypeList()), TmsDeliveryBatch::getType, deliveryBatchQuery.getDeliveryBatchTypeList())
                .eq(deliveryBatchQuery.getCarrierId() != null, TmsDeliveryBatch::getCarrierId, deliveryBatchQuery.getCarrierId())
                .eq(deliveryBatchQuery.getDriverId() != null, TmsDeliveryBatch::getDriverId, deliveryBatchQuery.getDriverId())
                .eq(deliveryBatchQuery.getCarId() != null, TmsDeliveryBatch::getCarId, deliveryBatchQuery.getCarId())
                .eq(deliveryBatchQuery.getDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getDeliveryTime())
                .eq(deliveryBatchQuery.getCarryType() != null, TmsDeliveryBatch::getCarryType, deliveryBatchQuery.getCarryType())
                .ge(deliveryBatchQuery.getBeginDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getBeginDeliveryTime())
                .le(deliveryBatchQuery.getEndDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getEndDeliveryTime())
                .ge(deliveryBatchQuery.getBeginTime() != null, TmsDeliveryBatch::getPlanBeginTime, deliveryBatchQuery.getBeginTime())
                .le(deliveryBatchQuery.getEndTime() != null, TmsDeliveryBatch::getPlanBeginTime, deliveryBatchQuery.getEndTime())
//                .orderByDesc(TmsDeliveryBatch::getId)
        );
        PageInfo tmsDeliveryBatchePageInfo = PageInfoHelper.createPageInfo(tmsDeliveryBatches);

        List<DeliveryBatchEntity> deliveryBatchEntityList = tmsDeliveryBatches.stream().map(TmsDeliveryBatchConverter::tmsDeliveryBatchToEntity).collect(Collectors.toList());
        tmsDeliveryBatchePageInfo.setList(deliveryBatchEntityList);

        return tmsDeliveryBatchePageInfo;
    }

    @Override
    public List<DeliveryBatchEntity> getDeliveryBatchByDistId(Long distId) {
        //先查询配送单
        List<TmsDeliveryOrder> tmsDeliveryOrders = tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>().eq(TmsDeliveryOrder::getDistOrderId, distId));
        //在查询对应的调度单
        List<Long> batchIdList = tmsDeliveryOrders.stream().map(TmsDeliveryOrder::getBatchId).collect(Collectors.toList());
        List<TmsDeliveryBatch> tmsDeliveryBatches = new ArrayList<>();
        if (!CollectionUtils.isEmpty(batchIdList)) {
            tmsDeliveryBatches = tmsDeliveryBatchMapper.selectList(new LambdaQueryWrapper<TmsDeliveryBatch>().in(TmsDeliveryBatch::getId, batchIdList));
        }
        return tmsDeliveryBatches.stream().map(TmsDeliveryBatchConverter::tmsDeliveryBatchToEntity).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryBatchEntity> queryListWithDriver(DeliveryBatchQuery deliveryBatchQuery) {
        LambdaQueryWrapper<TmsDeliveryBatch> tmsDeliveryBatchLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tmsDeliveryBatchLambdaQueryWrapper
                .eq(deliveryBatchQuery.getPathId() != null , TmsDeliveryBatch::getPathId ,deliveryBatchQuery.getPathId())
                .eq(deliveryBatchQuery.getBeginSiteId() != null, TmsDeliveryBatch::getBeginSiteId, deliveryBatchQuery.getBeginSiteId())
                .eq(deliveryBatchQuery.getDeliveryBatchType() != null, TmsDeliveryBatch::getType, deliveryBatchQuery.getDeliveryBatchType())
                .eq(deliveryBatchQuery.getType() != null, TmsDeliveryBatch::getType, deliveryBatchQuery.getType())
                .ge(deliveryBatchQuery.getDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getDeliveryTime() != null ? deliveryBatchQuery.getDeliveryTime().toLocalDate().atStartOfDay() : null)
                .lt(deliveryBatchQuery.getDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getDeliveryTime() != null ? LocalDateTime.of(deliveryBatchQuery.getDeliveryTime().toLocalDate(), Constants.localTimeMaxTime) : null
                );
        List<TmsDeliveryBatch> tmsDeliveryBatches = tmsDeliveryBatchMapper.selectList(tmsDeliveryBatchLambdaQueryWrapper);

        List<DeliveryBatchEntity> deliveryBatchEntityList = tmsDeliveryBatches.stream().map(TmsDeliveryBatchConverter::tmsDeliveryBatchToEntity).collect(Collectors.toList());

        deliveryBatchEntityList.forEach(this::getBaseDeliveryInfo);

        return deliveryBatchEntityList;
    }

    @Override
    public DeliveryBatchEntity query(DeliveryBatchQuery deliveryBatchQuery) {
        TmsDeliveryBatch tmsDeliveryBatch = tmsDeliveryBatchMapper.selectOne(new LambdaQueryWrapper<TmsDeliveryBatch>()
                .eq(deliveryBatchQuery.getBeginSiteId() != null, TmsDeliveryBatch::getBeginSiteId, deliveryBatchQuery.getBeginSiteId())
                .eq(deliveryBatchQuery.getDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getDeliveryTime())
                .eq(!StringUtils.isEmpty(deliveryBatchQuery.getPathCode()), TmsDeliveryBatch::getPathCode, deliveryBatchQuery.getPathCode())
                .eq(deliveryBatchQuery.getDeliveryBatchType() != null, TmsDeliveryBatch::getType, deliveryBatchQuery.getDeliveryBatchType())
                .eq(deliveryBatchQuery.getDriverId() != null, TmsDeliveryBatch::getDriverId, deliveryBatchQuery.getDriverId())
                .eq(deliveryBatchQuery.getBatchId() != null, TmsDeliveryBatch::getId, deliveryBatchQuery.getBatchId())
                .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchStatusList()),TmsDeliveryBatch::getStatus,deliveryBatchQuery.getDeliveryBatchStatusList())
                .last("limit 1")
        );
        return TmsDeliveryBatchConverter.tmsDeliveryBatchToEntity(tmsDeliveryBatch);
    }

    @Override
    public void saveBatch(DeliveryBatchEntity deliveryBatchEntity) {
        LocalDateTime now = LocalDateTime.now();
        TmsDeliveryBatch tmsDeliveryBatch = TmsDeliveryBatchConverter.entityToTmsDeliveryBatch(deliveryBatchEntity);
        if(CollectionUtils.isEmpty(deliveryBatchEntity.getDeliverySiteList())){
            //开始点位
            tmsDeliveryBatch.setBeginSiteId(deliveryBatchEntity.getDeliverySiteList().get(0).getSiteId());
            //结束点位
            tmsDeliveryBatch.setEndSiteId(deliveryBatchEntity.getDeliverySiteList().get(deliveryBatchEntity.getDeliverySiteList().size() - 1).getSiteId());
        }
        tmsDeliveryBatch.setCreateTime(now);

        List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
        //开始点位
        Long beginSiteId = deliverySiteList.get(0).getSiteId();
        tmsDeliveryBatch.setBeginSiteId(beginSiteId);
        DeliveryBatchEntity existedDeliveryBatch = getDeliveryBatch(beginSiteId, deliveryBatchEntity.getDeliveryTime(),
                deliveryBatchEntity.getPathId(), null);
        if (existedDeliveryBatch != null) {
            deliveryBatchEntity.setId(existedDeliveryBatch.getId());
            return;
        }
        //配送批次表的保存
        tmsDeliveryBatchMapper.insert(tmsDeliveryBatch);
        deliveryBatchEntity.setId(tmsDeliveryBatch.getId());
    }

    @Override
    public void remove(Long deliveryBatchId, Long beginSiteId,  Long endSiteId) {
        //查询该配送批次是否还有其他配送点位
        Long otherSiteCount = tmsDeliverySiteMapper.selectCount(new LambdaQueryWrapper<TmsDeliverySite>()
                .eq(TmsDeliverySite::getDeliveryBatchId, deliveryBatchId)
                .ne(TmsDeliverySite::getSiteId, endSiteId));
        if (otherSiteCount > 1) {
            //除了初始城配仓点位还有其他点位 不能删除该批次
            return;
        }
        //默认批次不删
        TmsDeliveryBatch tmsDeliveryBatch = tmsDeliveryBatchMapper.selectById(deliveryBatchId);
        if(Objects.equals(tmsDeliveryBatch.getPathId(), -1)){
            return;
        }

        //删除初始城配仓点位
        tmsDeliverySiteMapper.delete(new LambdaQueryWrapper<TmsDeliverySite>()
                .eq(TmsDeliverySite::getDeliveryBatchId, deliveryBatchId)
                .eq(TmsDeliverySite::getSiteId, beginSiteId));
        //删除配送批次
        tmsDeliveryBatchMapper.deleteById(deliveryBatchId);
    }

    private DeliveryBatchEntity getDeliveryBatch(Long beginSiteId, LocalDateTime deliveryTime, Long pathId, Long carId) {
        List<TmsDeliveryBatch> tmsDeliveryBatchList = tmsDeliveryBatchMapper.selectList(new LambdaQueryWrapper<>(TmsDeliveryBatch.class)
                .eq(TmsDeliveryBatch::getBeginSiteId, beginSiteId)
                .eq(TmsDeliveryBatch::getDeliveryTime, deliveryTime)
                .eq(TmsDeliveryBatch::getPathId, pathId)
                .eq(Objects.nonNull(carId), TmsDeliveryBatch::getCarId, carId)
                .orderByDesc(TmsDeliveryBatch::getId));
        if (CollectionUtils.isEmpty(tmsDeliveryBatchList)) {
            return null;
        }
        return TmsDeliveryBatchConverter.tmsDeliveryBatchToEntity(tmsDeliveryBatchList.get(0));
    }

    @Override
    public List<DeliveryBatchEntity> queryList(DeliveryBatchQuery deliveryBatchQuery) {
        List<TmsDeliveryBatch> tmsDeliveryBatches = tmsDeliveryBatchMapper.selectList(new LambdaQueryWrapper<TmsDeliveryBatch>()
                .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchTypeList()),TmsDeliveryBatch::getType,deliveryBatchQuery.getDeliveryBatchTypeList())
                .eq(deliveryBatchQuery.getDeliveryBatchStatus() != null, TmsDeliveryBatch::getStatus, deliveryBatchQuery.getDeliveryBatchStatus())
                .eq(deliveryBatchQuery.getDeliveryTime() != null ,TmsDeliveryBatch::getDeliveryTime,deliveryBatchQuery.getDeliveryTime())
                .eq(deliveryBatchQuery.getBeginSiteId() != null ,TmsDeliveryBatch::getBeginSiteId,deliveryBatchQuery.getBeginSiteId())
                .eq(deliveryBatchQuery.getEndSiteId() != null ,TmsDeliveryBatch::getEndSiteId,deliveryBatchQuery.getEndSiteId())
                .ge(deliveryBatchQuery.getBeginDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getBeginDeliveryTime())
                .lt(deliveryBatchQuery.getEndDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getEndDeliveryTime())
                .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchStatusList()), TmsDeliveryBatch::getStatus, deliveryBatchQuery.getDeliveryBatchStatusList())
                .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getBatchIds()), TmsDeliveryBatch::getId, deliveryBatchQuery.getBatchIds())
                .eq(deliveryBatchQuery.getDeliveryBatchType() != null, TmsDeliveryBatch::getType, deliveryBatchQuery.getDeliveryBatchType())
                .eq(deliveryBatchQuery.getType() != null, TmsDeliveryBatch::getType, deliveryBatchQuery.getType())
                .eq(deliveryBatchQuery.getDriverId() != null, TmsDeliveryBatch::getDriverId, deliveryBatchQuery.getDriverId())
        );

        return tmsDeliveryBatches.stream().map(TmsDeliveryBatchConverter::tmsDeliveryBatchToEntity).collect(Collectors.toList());
    }

    @Override
    public void calculateDistance(List<WaypointsInput> waypointsInputList, Long batchId, DeliverySectionEnums.Type type, XMDriverRoutPlanStrategyEnum combinationStrategy) {
        if(batchId == null){
            return;
        }
        //保存路段信息
        PathSection pathSection = this.queryGaoDePathSection(waypointsInputList, batchId, combinationStrategy);

        List<PathSectionDetail> pathSectionDetailList = pathSection.getPathSectionDetailList();
        List<DeliverySectionEntity> deliverySectionEntities = new ArrayList<>();
        for (PathSectionDetail pathSectionDTO : pathSectionDetailList) {
            DeliverySectionEntity deliverySectionEntity = new DeliverySectionEntity();
            deliverySectionEntity.setBatchId(batchId);
            deliverySectionEntity.setBeginSiteEntity(SiteEntity.builder().id(pathSectionDTO.getBeginSiteId()).build());
            deliverySectionEntity.setEndSiteEntity(SiteEntity.builder().id(pathSectionDTO.getEndSiteId()).build());
            deliverySectionEntity.setType(type.getValue());
            deliverySectionEntity.setDistance(pathSectionDTO.getDistance());

            deliverySectionEntities.add(deliverySectionEntity);
        }

        if(type == DeliverySectionEnums.Type.complete_path){
            UpdateWrapper<TmsDeliveryBatch> updateWrapper = new UpdateWrapper();
            updateWrapper.eq("id", batchId);

            //实际公里数（转为公里数）
            BigDecimal totalDistance = pathSection.getTotalDistance() == null ? new BigDecimal(0) : pathSection.getTotalDistance();
            updateWrapper.set("plan_total_distance",totalDistance.divide(new BigDecimal(1000)).setScale(2,BigDecimal.ROUND_HALF_UP));
            tmsDeliveryBatchMapper.update(null,updateWrapper);

            List<DeliverySectionEntity> deliverySectionEntityList = deliverySectionRepository.queryList(DeliverySectionQuery.builder()
                    .batchId(batchId)
                    .type(DeliverySectionEnums.Type.complete_path.getValue()).build());

            if(deliverySectionEntityList.size() > 0){
                deliverySectionRepository.removeList(deliverySectionEntityList);
            }

            deliverySectionRepository.saveBatch(deliverySectionEntities);
        }

        if(type == DeliverySectionEnums.Type.INTELLIGENCE_PATH){
            UpdateWrapper<TmsDeliveryBatch> updateWrapper = new UpdateWrapper();
            updateWrapper.eq("id", batchId);

            //实际公里数（转为公里数）
            BigDecimal totalDistance = pathSection.getTotalDistance() == null ? new BigDecimal(0) : pathSection.getTotalDistance();
            updateWrapper.set("intelligence_total_distance",totalDistance.divide(new BigDecimal(1000)).setScale(2,BigDecimal.ROUND_HALF_UP));
            tmsDeliveryBatchMapper.update(null,updateWrapper);

            List<DeliverySectionEntity> deliverySectionEntityList = deliverySectionRepository.queryList(DeliverySectionQuery.builder()
                    .batchId(batchId)
                    .type(DeliverySectionEnums.Type.INTELLIGENCE_PATH.getValue()).build());

            if(deliverySectionEntityList.size() > 0){
                deliverySectionRepository.removeList(deliverySectionEntityList);
            }

            deliverySectionRepository.saveBatch(deliverySectionEntities);
        }

        if(type == DeliverySectionEnums.Type.FINISH_SEND){
            UpdateWrapper<TmsDeliveryBatch> updateWrapper = new UpdateWrapper();
            updateWrapper.eq("id", batchId);
            BigDecimal totalDistance = pathSection.getTotalDistance() == null ? new BigDecimal(0) : pathSection.getTotalDistance();
            updateWrapper.set("real_total_distance",totalDistance.divide(new BigDecimal(1000)).setScale(2,BigDecimal.ROUND_HALF_UP));
            tmsDeliveryBatchMapper.update(null,updateWrapper);

            List<DeliverySectionEntity> deliverySectionEntityList = deliverySectionRepository.queryList(DeliverySectionQuery.builder()
                    .batchId(batchId)
                            .type(DeliverySectionEnums.Type.FINISH_SEND.getValue()).build());

            if(deliverySectionEntityList.size() > 0){
                deliverySectionRepository.removeList(deliverySectionEntityList);
            }
            deliverySectionRepository.saveBatch(deliverySectionEntities);
        }

        if(type == DeliverySectionEnums.Type.ANT_ALGORITHM){
            UpdateWrapper<TmsDeliveryBatch> updateWrapper = new UpdateWrapper();
            updateWrapper.eq("id", batchId);
            //实际公里数（转为公里数）
            BigDecimal totalDistance = pathSection.getTotalDistance() == null ? new BigDecimal(0) : pathSection.getTotalDistance();
            updateWrapper.set("ant_distance",totalDistance.divide(new BigDecimal(1000)).setScale(2,BigDecimal.ROUND_HALF_UP));
            tmsDeliveryBatchMapper.update(null,updateWrapper);

            List<DeliverySectionEntity> deliverySectionEntityList = deliverySectionRepository.queryList(DeliverySectionQuery.builder()
                    .batchId(batchId)
                    .type(DeliverySectionEnums.Type.ANT_ALGORITHM.getValue()).build());

            if(deliverySectionEntityList.size() > 0){
                deliverySectionRepository.removeList(deliverySectionEntityList);
            }

            deliverySectionRepository.saveBatch(deliverySectionEntities);
        }

        if(type == DeliverySectionEnums.Type.GREEDY_ALGORITHM){
            List<DeliverySectionEntity> deliverySectionEntityList = deliverySectionRepository.queryList(DeliverySectionQuery.builder()
                    .batchId(batchId)
                    .type(DeliverySectionEnums.Type.GREEDY_ALGORITHM.getValue()).build());

            if(deliverySectionEntityList.size() > 0){
                deliverySectionRepository.removeList(deliverySectionEntityList);
            }

            deliverySectionRepository.saveBatch(deliverySectionEntities);

            BigDecimal totalDistance = pathSection.getTotalDistance() == null ? new BigDecimal(0) : pathSection.getTotalDistance();
            BigDecimal totalDistanceDecimal = totalDistance.divide(new BigDecimal(1000)).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 保存总距离
            TmsDeliveryBatchExtCommandParam extCommandParam = new TmsDeliveryBatchExtCommandParam();
            extCommandParam.setDeliveryBatchId(batchId);
            extCommandParam.setExtKey(DeliveryBatchExtEnum.GREEDY_TOTAL_DISTANCE.getCode());
            extCommandParam.setExtValue(totalDistanceDecimal.toString());
            tmsDeliveryBatchExtCommandRepository.insertSelective(extCommandParam);
        }

        if(type == DeliverySectionEnums.Type.SPECIAL_SEND_PATH){
            List<DeliverySectionEntity> deliverySectionEntityList = deliverySectionRepository.queryList(DeliverySectionQuery.builder()
                    .batchId(batchId)
                    .type(DeliverySectionEnums.Type.SPECIAL_SEND_PATH.getValue()).build());

            if(deliverySectionEntityList.size() > 0){
                deliverySectionRepository.removeList(deliverySectionEntityList);
            }
            deliverySectionRepository.saveBatch(deliverySectionEntities);
        }
    }

    private PathSection queryGaoDePathSection(List<WaypointsInput> waypointsInputList, Long batchId, XMDriverRoutPlanStrategyEnum combinationStrategy) {
        PathSectionInput input = new PathSectionInput();
        input.setStrategyEunm(DriverRoutPlanStrategyEnum.DISTANCE_FIRST);
        input.setWaypointsInputList(waypointsInputList);
        input.setXmDriverRoutPlanStrategyEnum(combinationStrategy);
        PathSection pathSection = lbsGaoDeService.waypointsPathSection(input);
        log.info("批次id:{},高德计算距离:{},poi点位:{}", batchId, pathSection.getTotalDistance(), JSON.toJSONString(waypointsInputList));
        return pathSection;
    }

    @Override
    public PathSection queryPathGaoDeDistance(List<DeliverySiteEntity> deliverySiteEntities, Long batchId) {
        if (CollectionUtils.isEmpty(deliverySiteEntities)){
            return null;
        }
        List<WaypointsInput> waypointsInputList = deliverySiteEntities.stream().map(site -> {
            WaypointsInput waypointsInput = new WaypointsInput();
            waypointsInput.setPoi(site.getSiteEntity().getPoi());
            waypointsInput.setSiteId(site.getSiteEntity().getId());
            return waypointsInput;
        }).collect(Collectors.toList());

        return this.queryGaoDePathSection(waypointsInputList, batchId, XMDriverRoutPlanStrategyEnum.COMBINATION_STRATEGY);
    }

    @Override
    public void updatePlanDistance(DeliveryBatchEntity deliveryBatchEntity) {
        UpdateWrapper<TmsDeliveryBatch> updateWrapper = new UpdateWrapper();
        updateWrapper.eq("id",deliveryBatchEntity.getId());

        //实际公里数（转为公里数）
        updateWrapper.set("plan_total_distance",deliveryBatchEntity.getPlanTotalDistance());
        tmsDeliveryBatchMapper.update(null,updateWrapper);
    }

    @Override
    public DeliveryBatchEntity queryWithDriver(Long deliveryBatchId) {
        DeliveryBatchEntity deliveryBatchEntity = TmsDeliveryBatchConverter.tmsDeliveryBatchToEntity(tmsDeliveryBatchMapper.selectById(deliveryBatchId));
        if (deliveryBatchEntity.getDriverId() != null) {
            TmsDriver tmsDriver = tmsDriverMapper.selectById(deliveryBatchEntity.getDriverId());
            deliveryBatchEntity.setDriverEntity(TmsDriverConverter.tmsDriver2Entity(tmsDriver));
        }
        return deliveryBatchEntity;
    }

    @Override
    public DeliveryBatchEntity queryWithSite(Long deliveryBatchId) {
        DeliveryBatchEntity deliveryBatchEntity = this.query(deliveryBatchId);
        if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            return null;
        }
        this.querySite(Collections.singletonList(deliveryBatchEntity));
        return deliveryBatchEntity;
    }

    @Override
    public List<DeliveryBatchEntity> queryWithSiteWithBase(DeliveryBatchQuery deliveryBatchQuery) {
        List<TmsDeliveryBatch> tmsDeliveryBatches = tmsDeliveryBatchMapper.selectList(new LambdaQueryWrapper<TmsDeliveryBatch>()
                        .eq(deliveryBatchQuery.getPathCode() != null, TmsDeliveryBatch::getPathCode, deliveryBatchQuery.getPathCode())
                        .eq(deliveryBatchQuery.getBatchId() != null, TmsDeliveryBatch::getId, deliveryBatchQuery.getBatchId())
                        .eq(deliveryBatchQuery.getPathId() != null, TmsDeliveryBatch::getPathId, deliveryBatchQuery.getPathId())
                        .eq(deliveryBatchQuery.getBeginSiteId() != null, TmsDeliveryBatch::getBeginSiteId, deliveryBatchQuery.getBeginSiteId())
                        .eq(deliveryBatchQuery.getEndSiteId() != null, TmsDeliveryBatch::getEndSiteId, deliveryBatchQuery.getEndSiteId())
                        .eq(deliveryBatchQuery.getDeliveryBatchType() != null, TmsDeliveryBatch::getType, deliveryBatchQuery.getDeliveryBatchType())
                        .eq(deliveryBatchQuery.getDeliveryBatchStatus() != null, TmsDeliveryBatch::getStatus, deliveryBatchQuery.getDeliveryBatchStatus())
                        .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchStatusList()), TmsDeliveryBatch::getStatus, deliveryBatchQuery.getDeliveryBatchStatusList())
                        .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getDeliveryBatchTypeList()), TmsDeliveryBatch::getType, deliveryBatchQuery.getDeliveryBatchTypeList())
                        .eq(deliveryBatchQuery.getCarrierId() != null, TmsDeliveryBatch::getCarrierId, deliveryBatchQuery.getCarrierId())
                        .eq(deliveryBatchQuery.getDriverId() != null, TmsDeliveryBatch::getDriverId, deliveryBatchQuery.getDriverId())
                        .eq(deliveryBatchQuery.getCarId() != null, TmsDeliveryBatch::getCarId, deliveryBatchQuery.getCarId())
                        .eq(deliveryBatchQuery.getDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getDeliveryTime())
                        .ge(deliveryBatchQuery.getBeginDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getBeginDeliveryTime())
                        .le(deliveryBatchQuery.getEndDeliveryTime() != null, TmsDeliveryBatch::getDeliveryTime, deliveryBatchQuery.getEndDeliveryTime())
                        .ge(deliveryBatchQuery.getBeginTime() != null, TmsDeliveryBatch::getPlanBeginTime, deliveryBatchQuery.getBeginTime())
                        .le(deliveryBatchQuery.getEndTime() != null, TmsDeliveryBatch::getPlanBeginTime, deliveryBatchQuery.getEndTime())
                        .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getBeginSiteIds()),TmsDeliveryBatch::getBeginSiteId,deliveryBatchQuery.getBeginSiteIds())
                        .ge(deliveryBatchQuery.getBeginFinishTime() != null, TmsDeliveryBatch::getFinishDeliveryTime, deliveryBatchQuery.getBeginFinishTime())
                        .le(deliveryBatchQuery.getEndFinishTime() != null, TmsDeliveryBatch::getFinishDeliveryTime, deliveryBatchQuery.getEndFinishTime())
                        .in(!CollectionUtils.isEmpty(deliveryBatchQuery.getBatchIds()),TmsDeliveryBatch::getId,deliveryBatchQuery.getBatchIds())
        );

        List<DeliveryBatchEntity> deliveryBatchEntityList = tmsDeliveryBatches.stream().map(TmsDeliveryBatchConverter::tmsDeliveryBatchToEntity).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(tmsDeliveryBatches)){
            return deliveryBatchEntityList;
        }
        //查询点位信息
        this.querySite(deliveryBatchEntityList);
        //查询司机
        this.queryDriver(deliveryBatchEntityList);
        //查询车辆信息
        this.queryCar(deliveryBatchEntityList);
        //查询承运商信息
        this.queryCarrier(deliveryBatchEntityList);
        return deliveryBatchEntityList;
    }

    @Override
    public List<DeliveryBatchEntity> queryWithSite(DeliveryBatchQuery deliveryBatchQuery) {
        List<DeliveryBatchEntity> deliveryBatchEntityList = this.queryList(deliveryBatchQuery);
        if (CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return new ArrayList<>();
        }
        this.querySite(deliveryBatchEntityList);
        this.queryCar(deliveryBatchEntityList);

        return deliveryBatchEntityList;
    }

    @Override
    public List<DeliveryBatchEntity> queryListWithBatchDriver(DeliveryBatchQuery deliveryBatchQuery) {
        List<DeliveryBatchEntity> deliveryBatchEntityList = this.queryList(deliveryBatchQuery);
        if (CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return new ArrayList<>();
        }
        this.queryDriver(deliveryBatchEntityList);
        return deliveryBatchEntityList;
    }

    private void queryCarrier(List<DeliveryBatchEntity> deliveryBatchEntityList) {
        List<Long> carrierIds = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getCarrierId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(carrierIds)){
            return;
        }
        List<Carrier> carriers = carrierMapper.selectBatchIds(carrierIds);
        if(CollectionUtils.isEmpty(carriers)){
            return;
        }
        List<CarrierEntity> carrierEntities = carriers.stream().map(CarrierConverter::do2Entity).collect(Collectors.toList());
        Map<Long, CarrierEntity> carrierIdMap = carrierEntities.stream().collect(Collectors.toMap(CarrierEntity::getId, Function.identity()));
        deliveryBatchEntityList.forEach(batch ->{
            if(carrierIdMap.get(batch.getCarrierId()) != null){
                batch.setCarrierEntity(carrierIdMap.get(batch.getCarrierId()));
            }
        });
    }

    private void queryCar(List<DeliveryBatchEntity> deliveryBatchEntityList) {
        List<Long> carIds = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getCarId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(carIds)){
            return;
        }
        List<TmsCar> tmsCars = tmsCarMapper.selectBatchIds(carIds);
        if(CollectionUtils.isEmpty(tmsCars)){
            return;
        }
        List<CarEntity> carEntities = tmsCars.stream().map(TmsCarConverter::tmsCar2Entity).collect(Collectors.toList());
        Map<Long, CarEntity> carIdMap = carEntities.stream().collect(Collectors.toMap(CarEntity::getId, Function.identity()));
        deliveryBatchEntityList.forEach(batch ->{
            if(carIdMap.get(batch.getCarId()) != null){
                batch.setCarEntity(carIdMap.get(batch.getCarId()));
            }
        });
    }

    private void queryDriver(List<DeliveryBatchEntity> deliveryBatchEntityList) {
        List<Long> driverIds = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getDriverId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(driverIds)){
            return;
        }
        List<TmsDriver> tmsDrivers = tmsDriverMapper.selectBatchIds(driverIds);
        if(CollectionUtils.isEmpty(tmsDrivers)){
            return;
        }
        List<DriverEntity> driverEntities = tmsDrivers.stream().map(TmsDriverConverter::tmsDriver2Entity).collect(Collectors.toList());
        Map<Long, DriverEntity> driverIdMap = driverEntities.stream().collect(Collectors.toMap(DriverEntity::getId, Function.identity()));
        deliveryBatchEntityList.forEach(batch ->{
            if(driverIdMap.get(batch.getDriverId()) != null){
                batch.setDriverEntity(driverIdMap.get(batch.getDriverId()));
            }
        });
    }

    private void querySite(List<DeliveryBatchEntity> deliveryBatchEntityList) {
        List<Long> deliveryBatchIds = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(deliveryBatchIds)){
            return;
        }
        List<TmsDeliverySite> tmsDeliverySites = tmsDeliverySiteMapper.selectList(new LambdaQueryWrapper<TmsDeliverySite>()
                .in(TmsDeliverySite::getDeliveryBatchId, deliveryBatchIds)
        );
        if(CollectionUtils.isEmpty(tmsDeliverySites)){
            return;
        }
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectBatchIds(tmsDeliverySites.stream().map(TmsDeliverySite::getSiteId).collect(Collectors.toList()));
        List<SiteEntity> siteEntities = tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
        Map<Long, SiteEntity> siteMap = siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));
        Map<Long, List<TmsDeliverySite>> batchSiteListMap = tmsDeliverySites.stream().collect(Collectors.groupingBy(TmsDeliverySite::getDeliveryBatchId));

        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            Long beginSiteId = deliveryBatchEntity.getBeginSiteId();
            deliveryBatchEntity.setBeginSiteEntity(siteMap.get(beginSiteId));
            
            if(!CollectionUtils.isEmpty(batchSiteListMap.get(deliveryBatchEntity.getId()))){
                List<TmsDeliverySite> tmsDeliverySiteList = batchSiteListMap.get(deliveryBatchEntity.getId());
                tmsDeliverySiteList.sort(Comparator.comparing(TmsDeliverySite::getId));
                deliveryBatchEntity.setDeliverySiteList(tmsDeliverySiteList.stream().map(TmsDeliverySiteConverter::tmsDeliverySiteToSiteEntity).collect(Collectors.toList()));

                List<Long> siteIdList = tmsDeliverySiteList.stream().map(TmsDeliverySite::getSiteId).collect(Collectors.toList());
                if(!Objects.equals(deliveryBatchEntity.getType(), DeliveryBatchTypeEnum.city.getCode())){
                    StringJoiner pathNameSJoiner = new StringJoiner("-");
                    for (Long siteId : siteIdList) {
                        if(siteMap.get(siteId) != null){
                            pathNameSJoiner.add(siteMap.get(siteId).getSiteName());
                        }
                    }
                    deliveryBatchEntity.setPathName(pathNameSJoiner.toString());
                }
            }
            for (DeliverySiteEntity deliverySiteEntity : deliveryBatchEntity.getDeliverySiteList()) {
                SiteEntity siteEntity = siteMap.get(deliverySiteEntity.getSiteId());
                if(siteEntity != null){
                    deliverySiteEntity.setSiteEntity(siteEntity);
                }
            }
        }
    }

    @Override
    public List<BoardEntity> queryBoardData(BoardQuery boardQuery) {
        return tmsDeliveryBatchMapper.queryBoardData(boardQuery);
    }

    @Override
    public DeliveryBatchEntity queryByIdForceMaster(Long batchId) {
        return TmsDeliveryBatchConverter.tmsDeliveryBatchToEntity(tmsDeliveryBatchMapper.queryByIdForceMaster(batchId));
    }

    @Override
    public List<DeliveryBatchRelationEntity> queryBatchRelationList(Long batchId) {
        return this.queryBatchRelationList(batchId, false);
    }

    @Override
    public List<DeliveryBatchRelationEntity> queryBatchRelationList(Long batchId, boolean isForceMaster) {
        if (batchId == null){
            return Collections.emptyList();
        }
        List<DeliveryBatchRelationEntity> allRelations;
        if (isForceMaster){
            allRelations = tmsDeliveryBatchRelationMapper.selectBatchRelationListForceMaster(batchId);
        }else {
            List<TmsDeliveryBatchRelation> mainRelations = tmsDeliveryBatchRelationMapper.selectList(new LambdaQueryWrapper<TmsDeliveryBatchRelation>()
                    .eq(TmsDeliveryBatchRelation::getBatchId, batchId)
                    .or().eq(TmsDeliveryBatchRelation::getRelateBatchId, batchId));
            allRelations = mainRelations.stream().map(TmsDeliveryBatchRelationConverter::do2Entity).collect(Collectors.toList());
        }
        allRelations.forEach(e -> e.resetRelateBatchId(batchId));
        return allRelations;
    }

    @Override
    public List<DeliveryBatchRelationEntity> queryBatchRelationListWithDetail(Long batchId) {
        List<DeliveryBatchRelationEntity> deliveryBatchRelationEntities = this.queryBatchRelationList(batchId);
        if (CollectionUtils.isEmpty(deliveryBatchRelationEntities)){
            return Collections.emptyList();
        }
        List<Long> batchIds = deliveryBatchRelationEntities.stream().map(DeliveryBatchRelationEntity::getDeliveryBatchId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchIds)){
            return Collections.emptyList();
        }
        List<DeliveryBatchEntity> deliveryBatchEntityList = this.queryWithSite(DeliveryBatchQuery.builder().batchIds(batchIds).build());
        Map<Long, DeliveryBatchEntity> batchEntityMap = deliveryBatchEntityList.stream().collect(Collectors.toMap(DeliveryBatchEntity::getId, Function.identity()));
        for (DeliveryBatchRelationEntity deliveryBatchRelationEntity : deliveryBatchRelationEntities) {
            DeliveryBatchEntity deliveryBatchEntity = batchEntityMap.get(deliveryBatchRelationEntity.getDeliveryBatchId());
            if (deliveryBatchEntity == null){
                continue;
            }
            deliveryBatchRelationEntity.setType(DeliveryBatchTypeEnum.code2Enum(deliveryBatchEntity.getType()));
            deliveryBatchRelationEntity.setClasses(DeliveryBatchEnums.Classes.code2Enum(deliveryBatchEntity.getClasses()));
            deliveryBatchRelationEntity.setPathName(deliveryBatchEntity.getPathName());
            deliveryBatchRelationEntity.setCarEntity(deliveryBatchEntity.getCarEntity());
        }
        return deliveryBatchRelationEntities;
    }

    @Override
    public int saveBatchRelation(List<DeliveryBatchRelationEntity> deliveryBatchRelationEntities) {
        if (CollectionUtils.isEmpty(deliveryBatchRelationEntities)){
            return 0;
        }
        List<TmsDeliveryBatchRelation> tmsDeliveryBatchRelations = deliveryBatchRelationEntities.stream().map(TmsDeliveryBatchRelationConverter::entity2do).collect(Collectors.toList());
        return tmsDeliveryBatchRelationMapper.insertBatch(tmsDeliveryBatchRelations);
    }

    @Override
    public int saveOrUpdateBatchRelation(List<DeliveryBatchRelationEntity> newDeliveryBatchRelationEntities, Long deliveryBatchId) {
        if (newDeliveryBatchRelationEntities == null){
            return 0;
        }
        newDeliveryBatchRelationEntities = newDeliveryBatchRelationEntities.stream().filter(e -> !Objects.equals(Constants.SYSTEM, e.getCreator())).collect(Collectors.toList());

        List<DeliveryBatchRelationEntity> existedBatchRelationEntities = this.queryBatchRelationList(deliveryBatchId);
        existedBatchRelationEntities = existedBatchRelationEntities.stream().filter(e -> !Objects.equals(Constants.SYSTEM, e.getCreator())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(existedBatchRelationEntities)){
            return this.saveBatchRelation(newDeliveryBatchRelationEntities);
        }
        List<Long> existedIds = existedBatchRelationEntities.stream().map(DeliveryBatchRelationEntity::getId).collect(Collectors.toList());
        this.removeBatchRelation(existedIds);
        return this.saveBatchRelation(newDeliveryBatchRelationEntities);
    }

    @Override
    public int removeBatchRelation(List<Long> relationIds) {
        if (CollectionUtils.isEmpty(relationIds)){
            return 0;
        }
        return tmsDeliveryBatchRelationMapper.deleteBatchIds(relationIds);
    }

    @Override
    public void updateBatchLoadRatioById(BatchLoadRatioVO batchLoadRatioVO) {
        if (batchLoadRatioVO == null || batchLoadRatioVO.getBatchId() == null){
            return;
        }
        //更新装载率相关数据
        TmsDeliveryBatch deliveryBatch = new TmsDeliveryBatch();
        deliveryBatch.setVolumeLoadRatio(batchLoadRatioVO.getVolumeLoadRatio());
        deliveryBatch.setWeightLoadRatio(batchLoadRatioVO.getWeightLoadRatio());
        deliveryBatch.setQuantityLoadRatio(batchLoadRatioVO.getQuantityLoadRatio());
        DeliveryBatchLoadRatioCalculateJson json = new DeliveryBatchLoadRatioCalculateJson();
        if(batchLoadRatioVO.getBatchCarLoadStaticVO() != null){
            json.setCarLoadVolume(batchLoadRatioVO.getBatchCarLoadStaticVO().getLoadVolume());
            json.setCarLoadWeight(batchLoadRatioVO.getBatchCarLoadStaticVO().getLoadWeight().multiply(BigDecimal.valueOf(1000)));
            json.setCarLoadQuantity(batchLoadRatioVO.getBatchCarLoadStaticVO().getLoadQuantity());
        }
        if(batchLoadRatioVO.getBatchDetailLoadStaticVO() != null){
            json.setTransportationLoadVolume(batchLoadRatioVO.getBatchDetailLoadStaticVO().getLoadVolume());
            json.setTransportationLoadWeight(batchLoadRatioVO.getBatchDetailLoadStaticVO().getLoadWeight());
            json.setTransportationLoadQuantity(batchLoadRatioVO.getBatchDetailLoadStaticVO().getLoadQuantity());
        }

        deliveryBatch.setLoadRatioCalculateJson(JSON.toJSONString(json));
        tmsDeliveryBatchMapper.update(deliveryBatch, new LambdaUpdateWrapper<TmsDeliveryBatch>().eq(TmsDeliveryBatch::getId,batchLoadRatioVO.getBatchId()));
    }

    @Override
    public BatchLoadRatioVO queryBatchLoadData(Long batchId) {
        if (batchId == null){
            return null;
        }
        DeliveryBatchEntity deliveryBatchEntity = this.queryByIdForceMaster(batchId);
        if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            return null;
        }
        //调度单已关闭
        if (deliveryBatchEntity.isClose()){
            //装载率置空
            return new BatchLoadRatioVO(batchId, null, null);
        }
        List<DeliveryBatchRelationEntity> deliveryBatchRelations = this.queryBatchRelationList(batchId, true);
        //查询所有关联调度单ID+当前调度单ID
        List<Long> relateBatchIds = Lists.newArrayList(batchId);
        if (!CollectionUtils.isEmpty(deliveryBatchRelations)){
            //查询所有关联调度单ID
            relateBatchIds.addAll(deliveryBatchRelations.stream().map(DeliveryBatchRelationEntity::getDeliveryBatchId).collect(Collectors.toList()));
        }
        //所有关联调度单+当前调度单运输明细总体积、重量、件数「承运单去重」/所有关联调度单+当前调度单车辆装载体积、重量、件数之和，百分数，小数点后四舍五入保留两位
        //查询所有关联调度单+当前调度单的所有运输明细进行去重
        BatchDetailLoadStaticVO batchDetailLoadStaticVO = tmsDeliveryOrderMapper.selectBatchLoadDetail(relateBatchIds);
        //查询所有关联调度单+当前调度单的所有车辆信息
        BatchCarLoadStaticVO batchCarLoadStaticVO = tmsDeliveryBatchMapper.selectBatchCarLoad(relateBatchIds);
        return new BatchLoadRatioVO(batchId, batchDetailLoadStaticVO, batchCarLoadStaticVO);
    }

    @Override
    public DeliveryBatchEntity queryByUk(Long pathId, LocalDateTime deliveryTime, Long beginSiteId, Long carId) {
        if(pathId == null || deliveryTime == null || beginSiteId == null || carId == null){
            return null;
        }
        TmsDeliveryBatch tmsDeliveryBatch = tmsDeliveryBatchMapper.selectOne(new LambdaQueryWrapper<TmsDeliveryBatch>()
                .eq(TmsDeliveryBatch::getPathId, pathId)
                .eq(TmsDeliveryBatch::getDeliveryTime, deliveryTime)
                .eq(TmsDeliveryBatch::getBeginSiteId, beginSiteId)
                .eq(TmsDeliveryBatch::getCarId, carId)
        );
        return TmsDeliveryBatchConverter.tmsDeliveryBatchToEntity(tmsDeliveryBatch);
    }

    @Override
    public void batchUpdate(List<DeliveryBatchEntity> batchEntities) {
        if(CollectionUtils.isEmpty(batchEntities)){
            return;
        }
        List<TmsDeliveryBatch> tmsDeliveryBatches = batchEntities.stream().map(TmsDeliveryBatchConverter::entityToTmsDeliveryBatch).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(tmsDeliveryBatches,TmsDeliveryBatch.class);
    }
}
