package net.summerfarm.tms.repository.track;

import net.summerfarm.tms.converter.tmstracklog.TmsTrackLogConverter;
import net.summerfarm.tms.dao.track.TmsTrackLog;
import net.summerfarm.tms.mapper.track.TmsTrackLogMapper;
import net.summerfarm.tms.track.entity.TmsTrackLogEntity;
import net.summerfarm.tms.track.param.command.TmsTrackLogCommandParam;
import net.summerfarm.tms.track.repository.TmsTrackLogCommandRepository;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-09-12 17:25:29
* @version 1.0
*
*/
@Repository
public class TmsTrackLogCommandRepositoryImpl implements TmsTrackLogCommandRepository {

    @Resource
    private TmsTrackLogMapper tmsTrackLogMapper;

    @Override
    public TmsTrackLogEntity insertSelective(TmsTrackLogCommandParam param) {
        TmsTrackLog tmsTrackLog = TmsTrackLogConverter.toTmsTrackLog(param);
        tmsTrackLogMapper.insertSelective(tmsTrackLog);
        return TmsTrackLogConverter.toTmsTrackLogEntity(tmsTrackLog);
    }

    @Override
    public int updateSelectiveById(TmsTrackLogCommandParam param){
        return tmsTrackLogMapper.updateSelectiveById(TmsTrackLogConverter.toTmsTrackLog(param));
    }


    @Override
    public int remove(Long id) {
        return tmsTrackLogMapper.remove(id);
    }

    @Override
    public void batchInsert(List<TmsTrackLogCommandParam> tmsTrackLogCommandParams) {
        if(CollectionUtils.isEmpty(tmsTrackLogCommandParams)){
            return;
        }

        List<TmsTrackLog> tmsTrackLogs = tmsTrackLogCommandParams.stream().map(TmsTrackLogConverter::toTmsTrackLog).collect(Collectors.toList());
        MybatisPlusUtil.createBatch(tmsTrackLogs, TmsTrackLog.class);
    }
}