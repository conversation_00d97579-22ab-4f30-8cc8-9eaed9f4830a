package net.summerfarm.tms.repository.track;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.tmstracklog.TmsTrackLogConverter;
import net.summerfarm.tms.mapper.track.TmsTrackLogMapper;
import net.summerfarm.tms.track.entity.TmsTrackLogEntity;
import net.summerfarm.tms.track.param.query.TmsTrackLogQueryParam;
import net.summerfarm.tms.track.repository.TmsTrackLogQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-09-12 17:25:29
* @version 1.0
*
*/
@Repository
public class TmsTrackLogQueryRepositoryImpl implements TmsTrackLogQueryRepository {

    @Resource
    private TmsTrackLogMapper tmsTrackLogMapper;


    @Override
    public PageInfo<TmsTrackLogEntity> getPage(TmsTrackLogQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<TmsTrackLogEntity> entities = tmsTrackLogMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public TmsTrackLogEntity selectById(Long id) {
        return TmsTrackLogConverter.toTmsTrackLogEntity(tmsTrackLogMapper.selectById(id));
    }


    @Override
    public List<TmsTrackLogEntity> selectByCondition(TmsTrackLogQueryParam param) {
        return TmsTrackLogConverter.toTmsTrackLogEntityList(tmsTrackLogMapper.selectByCondition(param));
    }

}