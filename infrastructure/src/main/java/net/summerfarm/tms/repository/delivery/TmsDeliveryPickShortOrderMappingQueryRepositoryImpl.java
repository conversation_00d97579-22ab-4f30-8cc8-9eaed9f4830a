package net.summerfarm.tms.repository.delivery;

import net.summerfarm.tms.converter.delivery.TmsDeliveryPickShortOrderMappingConverter;
import net.summerfarm.tms.delivery.entity.TmsDeliveryPickShortOrderMappingEntity;
import net.summerfarm.tms.delivery.repository.TmsDeliveryPickShortOrderMappingQueryRepository;
import net.summerfarm.tms.mapper.delivery.TmsDeliveryPickShortOrderMappingMapper;
import net.summerfarm.tms.model.delivery.TmsDeliveryPickShortOrderMapping;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
public class TmsDeliveryPickShortOrderMappingQueryRepositoryImpl implements TmsDeliveryPickShortOrderMappingQueryRepository {

    @Autowired
    private TmsDeliveryPickShortOrderMappingMapper tmsDeliveryPickShortOrderMappingMapper;

    @Override
    public List<TmsDeliveryPickShortOrderMappingEntity> listByDeliveryBatchId(Long deliveryBatchId) {
        if (deliveryBatchId == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryPickShortOrderMapping> tmsDeliveryPickShortOrderMappingList = tmsDeliveryPickShortOrderMappingMapper.listByDeliveryBatchId(deliveryBatchId);
        if (CollectionUtils.isEmpty(tmsDeliveryPickShortOrderMappingList)) {
            return Collections.emptyList();
        }
        return TmsDeliveryPickShortOrderMappingConverter.toTmsDeliveryPickShortOrderMappingEntityList(tmsDeliveryPickShortOrderMappingList);
    }

}
