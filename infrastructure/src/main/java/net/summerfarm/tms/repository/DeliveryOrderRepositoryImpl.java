package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.converter.TmsDeliveryItemConverter;
import net.summerfarm.tms.converter.TmsDeliveryOrderConverter;
import net.summerfarm.tms.dao.TmsDeliveryItem;
import net.summerfarm.tms.dao.TmsDeliveryOrder;
import net.summerfarm.tms.dao.TmsDeliverySite;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliveryOrderRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.delivery.entity.query.DeliveryOrderRelatedQuery;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.mapper.TmsDeliveryItemMapper;
import net.summerfarm.tms.mapper.TmsDeliveryOrderMapper;
import net.summerfarm.tms.mapper.TmsDeliverySiteMapper;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/13 18:20<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliveryOrderRepositoryImpl implements DeliveryOrderRepository {
    @Resource
    private TmsDeliveryOrderMapper tmsDeliveryOrderMapper;
    @Resource
    private TmsDeliveryItemMapper tmsDeliveryItemMapper;
    @Resource
    private TmsDeliverySiteMapper tmsDeliverySiteMapper;
    @Resource
    private SiteRepository siteRepository;
    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;

    @Override
    public PageInfo<DeliveryOrderEntity> queryPage(DeliveryOrderQuery deliveryOrderQuery) {
        // 使用MyBatis方式实现分页查询
        PageHelper.startPage(deliveryOrderQuery.getPageIndex(), deliveryOrderQuery.getPageSize());
        List<TmsDeliveryOrder> tmsDeliveryOrders = tmsDeliveryOrderMapper.queryPageByCondition(deliveryOrderQuery);
        PageInfo<TmsDeliveryOrder> pageInfo = PageInfoHelper.createPageInfo(tmsDeliveryOrders);

        // 转换为实体对象
        List<DeliveryOrderEntity> deliveryOrderEntities = tmsDeliveryOrders.stream()
                .map(TmsDeliveryOrderConverter::do2Entity)
                .collect(Collectors.toList());

        // 构建返回结果
        PageInfo<DeliveryOrderEntity> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        resultPageInfo.setList(deliveryOrderEntities);

        return resultPageInfo;
    }

    @Override
    public DeliveryOrderEntity queryDetail(Long id) {
        return null;
    }

    @Override
    public List<DeliveryOrderEntity> queryListWithDetail(DeliveryOrderQuery deliveryOrderQuery) {
        return null;
    }

    @Override
    public DeliveryOrderEntity queryWithItem(Long id) {
        return null;
    }

    @Override
    public List<DeliveryOrderEntity> queryListWithItem(DeliveryOrderQuery deliveryOrderQuery) {
        List<DeliveryOrderEntity> deliveryOrderEntityList = queryList(deliveryOrderQuery);
        if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
            return deliveryOrderEntityList;
        }
        List<Long> deliveryOrderIdList = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(deliveryOrderIdList)){
            return deliveryOrderEntityList;
        }
        List<TmsDeliveryItem> tmsDeliveryItems = tmsDeliveryItemMapper.selectList(new LambdaQueryWrapper<TmsDeliveryItem>().in(TmsDeliveryItem::getDeliveryOrderId, deliveryOrderIdList));
        if(CollectionUtils.isEmpty(tmsDeliveryItems)){
            return deliveryOrderEntityList;
        }
        Map<Long, List<TmsDeliveryItem>> distId2TmsDeliveryItemListMap = tmsDeliveryItems.stream().collect(Collectors.groupingBy(TmsDeliveryItem::getDeliveryOrderId, Collectors.toList()));

        deliveryOrderEntityList.forEach(deliveryOrderEntity -> {
            List<TmsDeliveryItem> orderDeliveryItems = distId2TmsDeliveryItemListMap.get(deliveryOrderEntity.getId());
            if(CollectionUtils.isEmpty(orderDeliveryItems)){
                return;
            }
            deliveryOrderEntity.setDeliveryItemEntityList(orderDeliveryItems.stream().map(TmsDeliveryItemConverter::do2Entity).collect(Collectors.toList()));
        });

        return deliveryOrderEntityList;
    }

    @Override
    public void batchRemoveByDistId(Long distId) {
        LambdaQueryWrapper<TmsDeliveryOrder> lambdaQueryWrapper = new LambdaQueryWrapper<TmsDeliveryOrder>()
                .eq(TmsDeliveryOrder::getDistOrderId, distId);
        tmsDeliveryOrderMapper.delete(lambdaQueryWrapper);
    }

    @Override
    public void saveOrUpdate(DeliveryOrderEntity deliveryOrderEntity) {
        // 先根据Id查询后更新
        if (deliveryOrderEntity.getId() != null) {
            TmsDeliveryOrder tmsDeliveryOrderDb = tmsDeliveryOrderMapper.selectById(deliveryOrderEntity.getId());
            if (tmsDeliveryOrderDb != null) {
                TmsDeliveryOrder tmsDeliveryOrder = TmsDeliveryOrderConverter.entity2Do(deliveryOrderEntity);
                tmsDeliveryOrder.setId(tmsDeliveryOrder.getId());
                tmsDeliveryOrderMapper.updateById(tmsDeliveryOrder);
                return;
            }
        }

        // 根据业务条件查询后更新
        List<TmsDeliveryOrder> tmsDeliveryOrders = tmsDeliveryOrderMapper.selectList(
                new LambdaQueryWrapper<TmsDeliveryOrder>()
                        .eq(TmsDeliveryOrder::getDistOrderId, deliveryOrderEntity.getDistOrderId())
                        .eq(TmsDeliveryOrder::getBeginSiteId, deliveryOrderEntity.getBeginSiteId())
                        .eq(TmsDeliveryOrder::getEndSiteId, deliveryOrderEntity.getEndSiteId())
                        .eq(deliveryOrderEntity.getDeliveryBatchId() != null,
                                TmsDeliveryOrder::getBatchId, deliveryOrderEntity.getDeliveryBatchId())
                        .isNull(deliveryOrderEntity.getDeliveryBatchId() == null, TmsDeliveryOrder::getBatchId)
                        .eq(TmsDeliveryOrder::getType,deliveryOrderEntity.getType()));
        TmsDeliveryOrder tmsDeliveryOrder = TmsDeliveryOrderConverter.entity2Do(deliveryOrderEntity);
        if (tmsDeliveryOrders.isEmpty()) {
            tmsDeliveryOrderMapper.insert(tmsDeliveryOrder);
        } else {
            tmsDeliveryOrder.setId(tmsDeliveryOrders.get(0).getId());
            tmsDeliveryOrderMapper.updateById(tmsDeliveryOrder);
        }
        deliveryOrderEntity.setId(tmsDeliveryOrder.getId());
    }

    @Override
    public void update(DeliveryOrderEntity deliveryOrderEntity) {
        TmsDeliveryOrder tmsDeliveryOrder = TmsDeliveryOrderConverter.entity2Do(deliveryOrderEntity);
        tmsDeliveryOrderMapper.updateById(tmsDeliveryOrder);
        if(!CollectionUtils.isEmpty(deliveryOrderEntity.getDeliveryItemEntityList())){
            List<TmsDeliveryItem> tmsDeliveryItems = deliveryOrderEntity.getDeliveryItemEntityList().stream().map(TmsDeliveryItemConverter::entity2Do).collect(Collectors.toList());
            for (TmsDeliveryItem tmsDeliveryItem : tmsDeliveryItems) {
                tmsDeliveryItemMapper.updateById(tmsDeliveryItem);
            }
        }
    }

    @Override
    public void updateDeliveryBatchId(List<DeliveryOrderEntity> deliveryOrderEntities, Long deliveryBatchId) {
        if(CollectionUtils.isEmpty(deliveryOrderEntities) || deliveryBatchId == null){
            return;
        }
        deliveryOrderEntities = deliveryOrderEntities.stream()
                .filter(deliveryOrderEntity -> deliveryOrderEntity.getId() != null)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(deliveryOrderEntities)){
            return;
        }
        List<Long> deliveryOrderIds = deliveryOrderEntities.stream().map(DeliveryOrderEntity::getId).collect(Collectors.toList());
        //批量更新
        tmsDeliveryOrderMapper.update(null, new LambdaUpdateWrapper<TmsDeliveryOrder>()
                .in(TmsDeliveryOrder::getId,deliveryOrderIds)
                .set(TmsDeliveryOrder::getBatchId,deliveryBatchId)
        );
    }

    @Override
    public void saveBatch(List<DeliveryOrderEntity> deliveryOrderEntities) {
        if (CollectionUtils.isEmpty(deliveryOrderEntities)){
            return;
        }
        List<TmsDeliveryOrder> tmsDeliveryOrderList = deliveryOrderEntities.stream().map(TmsDeliveryOrderConverter::entity2Do).collect(Collectors.toList());
        tmsDeliveryOrderMapper.insertBatch(tmsDeliveryOrderList);
    }

    @Override
    public void remove(Long distId) {
        tmsDeliveryOrderMapper.delete(new LambdaQueryWrapper<TmsDeliveryOrder>().eq(TmsDeliveryOrder::getDistOrderId, distId));
    }

    @Override
    public List<DeliveryOrderEntity> queryList(DeliveryOrderQuery deliveryOrderQuery) {
        LambdaQueryWrapper<TmsDeliveryOrder> tmsDeliveryOrderQueryWrapper = new LambdaQueryWrapper<>();
        tmsDeliveryOrderQueryWrapper
                .eq(deliveryOrderQuery.getBatchId() != null, TmsDeliveryOrder::getBatchId, deliveryOrderQuery.getBatchId())
                .eq(deliveryOrderQuery.getType() != null, TmsDeliveryOrder::getType, deliveryOrderQuery.getType())
                .in(!CollectionUtils.isEmpty(deliveryOrderQuery.getBatchIds()), TmsDeliveryOrder::getBatchId, deliveryOrderQuery.getBatchIds())
                .eq(deliveryOrderQuery.getDistOrderId() != null, TmsDeliveryOrder::getDistOrderId, deliveryOrderQuery.getDistOrderId())
                .eq(deliveryOrderQuery.getBeginSiteId() != null, TmsDeliveryOrder::getBeginSiteId, deliveryOrderQuery.getBeginSiteId())
                .eq(deliveryOrderQuery.getEndSiteId() != null, TmsDeliveryOrder::getEndSiteId, deliveryOrderQuery.getEndSiteId())
                .eq(deliveryOrderQuery.getExpectBeginTime() != null, TmsDeliveryOrder::getDeliveryTime, deliveryOrderQuery.getExpectBeginTime())
                .ge(deliveryOrderQuery.getDeliveryTime() != null, TmsDeliveryOrder::getDeliveryTime, deliveryOrderQuery.getDeliveryTime() != null ? deliveryOrderQuery.getDeliveryTime().atStartOfDay() : null)
                .lt(deliveryOrderQuery.getDeliveryTime() != null, TmsDeliveryOrder::getDeliveryTime, deliveryOrderQuery.getDeliveryTime() != null ? LocalDateTime.of(deliveryOrderQuery.getDeliveryTime(), Constants.localTimeMaxTime) : null)
                .ge(deliveryOrderQuery.getBeginDeliveryTime() != null, TmsDeliveryOrder::getDeliveryTime, deliveryOrderQuery.getBeginDeliveryTime())
                .le(deliveryOrderQuery.getEndDeliveryTime() != null, TmsDeliveryOrder::getDeliveryTime, deliveryOrderQuery.getEndDeliveryTime())
                .ne(deliveryOrderQuery.getNeState() != null,TmsDeliveryOrder::getState, deliveryOrderQuery.getNeState())
                .in(!CollectionUtils.isEmpty(deliveryOrderQuery.getDistIdList()),TmsDeliveryOrder::getDistOrderId, deliveryOrderQuery.getDistIdList())
                .notIn(!CollectionUtils.isEmpty(deliveryOrderQuery.getNotInDeliveryOrderIds()), TmsDeliveryOrder::getId, deliveryOrderQuery.getNotInDeliveryOrderIds())
                .in(!CollectionUtils.isEmpty(deliveryOrderQuery.getSourceList()),TmsDeliveryOrder::getSource,deliveryOrderQuery.getSourceList())
                .eq(!StringUtils.isEmpty(deliveryOrderQuery.getOutOrderId()), TmsDeliveryOrder::getOutOrderId, deliveryOrderQuery.getOutOrderId())
                .in(!CollectionUtils.isEmpty(deliveryOrderQuery.getBeginSiteIds()),TmsDeliveryOrder::getBeginSiteId, deliveryOrderQuery.getBeginSiteIds())
                .in(!CollectionUtils.isEmpty(deliveryOrderQuery.getDeliveryIds()),TmsDeliveryOrder::getId, deliveryOrderQuery.getDeliveryIds())
                .in(!CollectionUtils.isEmpty(deliveryOrderQuery.getEndSiteIds()),TmsDeliveryOrder::getEndSiteId, deliveryOrderQuery.getEndSiteIds())
        ;
        List<TmsDeliveryOrder> tmsDeliveryOrders =  tmsDeliveryOrderMapper.selectList(tmsDeliveryOrderQueryWrapper);
        return tmsDeliveryOrders.stream()
                .map(TmsDeliveryOrderConverter::do2Entity)
                .collect(Collectors.toList());
    }

    @Override
    public DeliveryOrderEntity query(Long id) {
        TmsDeliveryOrder tmsDeliveryOrder = tmsDeliveryOrderMapper.selectById(id);
        return TmsDeliveryOrderConverter.do2Entity(tmsDeliveryOrder);
    }

    @Override
    public DeliveryOrderEntity queryByUk(Long distId, Long beginSiteId, Long endSiteId, Long batchId, Integer type) {
        LambdaQueryWrapper<TmsDeliveryOrder> tmsDeliveryOrderQueryWrapper = new LambdaQueryWrapper<>();
        tmsDeliveryOrderQueryWrapper
                .eq(TmsDeliveryOrder::getBatchId, batchId)
                .eq(TmsDeliveryOrder::getDistOrderId, distId)
                .eq(TmsDeliveryOrder::getBeginSiteId, beginSiteId)
                .eq(TmsDeliveryOrder::getEndSiteId, endSiteId)
                .eq(TmsDeliveryOrder::getType, type);
        TmsDeliveryOrder tmsDeliveryOrder = tmsDeliveryOrderMapper.selectOne(tmsDeliveryOrderQueryWrapper);
        return TmsDeliveryOrderConverter.do2Entity(tmsDeliveryOrder);
    }

    @Override
    public List<DeliveryOrderEntity> queryListWithSiteName(DeliveryOrderQuery deliveryOrderQuery) {
        List<DeliveryOrderEntity> deliveryOrderEntityList = queryList(deliveryOrderQuery);
        for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntityList) {
            SiteEntity beginSiteEntity = siteRepository.query(deliveryOrderEntity.getBeginSiteId());
            deliveryOrderEntity.setBeginSiteName(beginSiteEntity.getSiteName());
            deliveryOrderEntity.setBeginSiteFullAddress(beginSiteEntity.getFullAddress());
            SiteEntity endSiteEntity = siteRepository.query(deliveryOrderEntity.getEndSiteId());
            deliveryOrderEntity.setEndSiteName(endSiteEntity.getSiteName());
            deliveryOrderEntity.setEndSiteFullAddress(endSiteEntity.getFullAddress());
        }
        return deliveryOrderEntityList;
    }

    @Override
    public void updateBatchId(DeliveryOrderEntity deliveryOrderEntity, Long batchId) {
        deliveryOrderEntity.setDeliveryBatchId(batchId);
        TmsDeliveryOrder tmsDeliveryOrder = TmsDeliveryOrderConverter.entity2Do(deliveryOrderEntity);
        tmsDeliveryOrderMapper.updateById(tmsDeliveryOrder);
    }

    @Override
    public void save(DeliveryOrderEntity orderEntity) {
        TmsDeliveryOrder tmsDeliveryOrder = TmsDeliveryOrderConverter.entity2Do(orderEntity);
        tmsDeliveryOrderMapper.insert(tmsDeliveryOrder);
    }

    @Override
    public void removeByPrimaryId(Long deliveryOrderId) {
        tmsDeliveryOrderMapper.deleteById(deliveryOrderId);
    }

    @Override
    public List<DeliveryOrderEntity> queryListWithBatchDetail(DeliveryOrderQuery deliveryOrderQuery) {
        List<TmsDeliveryOrder> tmsDeliveryOrders =  tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>()
                .in(!CollectionUtils.isEmpty(deliveryOrderQuery.getDistIdList()),TmsDeliveryOrder::getDistOrderId, deliveryOrderQuery.getDistIdList())
        );
        List<DeliveryOrderEntity> deliveryOrderEntities = tmsDeliveryOrders.stream()
                .map(TmsDeliveryOrderConverter::do2Entity)
                .collect(Collectors.toList());

        List<Long> deliveryBatchIds = tmsDeliveryOrders.stream().map(TmsDeliveryOrder::getBatchId).collect(Collectors.toList());
        //查询相关批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder().batchIds(deliveryBatchIds).build());
        if(!CollectionUtils.isEmpty(deliveryBatchEntityList)){
            List<SiteEntity> siteEntities = siteRepository.queryPrimaryIdList(deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getBeginSiteId).collect(Collectors.toList()));
            Map<Long, SiteEntity> siteEntityMap = siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));
            for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
                if(siteEntityMap.get(deliveryBatchEntity.getBeginSiteId()) != null){
                    deliveryBatchEntity.setBeginSiteEntity(siteEntityMap.get(deliveryBatchEntity.getBeginSiteId()));
                }
            }
            Map<Long, DeliveryBatchEntity> deliveryBatchEntityMap = deliveryBatchEntityList.stream().collect(Collectors.toMap(DeliveryBatchEntity::getId, Function.identity()));
            for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntities) {
                if(deliveryBatchEntityMap.get(deliveryOrderEntity.getDeliveryBatchId()) != null){
                    deliveryOrderEntity.setDeliveryBatchEntity(deliveryBatchEntityMap.get(deliveryOrderEntity.getDeliveryBatchId()));
                }
            }
        }
        return deliveryOrderEntities;
    }

    @Override
    public Boolean diffBatchHandle(LocalDateTime deliveryTime) {
        List<TmsDeliveryOrder> dataWithDiffBatchList = tmsDeliveryOrderMapper.queryListWithDiffBatch(deliveryTime);
        if (CollectionUtils.isEmpty(dataWithDiffBatchList)){
            return Boolean.FALSE;
        }
        Boolean result = Boolean.FALSE;
        for (TmsDeliveryOrder dataWithDiffBatch : dataWithDiffBatchList) {
            List<TmsDeliveryOrder> tmsDeliveryOrdersWithDiffBatch = tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>()
                    .eq(TmsDeliveryOrder::getBeginSiteId, dataWithDiffBatch.getBeginSiteId())
                    .eq(TmsDeliveryOrder::getEndSiteId, dataWithDiffBatch.getEndSiteId())
                    .eq(TmsDeliveryOrder::getDeliveryTime, dataWithDiffBatch.getDeliveryTime()));
            if (CollectionUtils.isEmpty(tmsDeliveryOrdersWithDiffBatch)){
                continue;
            }
            List<TmsDeliveryOrder> deliveryOrderWithNoSite = new ArrayList<>();
            //有效批次ID
            Long deliveryBatchId = null;
            for (TmsDeliveryOrder deliveryOrder : tmsDeliveryOrdersWithDiffBatch) {
                //已完成排线不处理
                DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryOrder.getBatchId());
                if (deliveryBatchEntity == null || deliveryBatchEntity.isCompletePath()){
                    continue;
                }
                //查询该配送单所在批次是否有挂点位
                TmsDeliverySite tmsDeliverySite = tmsDeliverySiteMapper.selectOne(new LambdaQueryWrapper<TmsDeliverySite>()
                        .eq(TmsDeliverySite::getDeliveryBatchId, deliveryOrder.getBatchId())
                        .eq(TmsDeliverySite::getSiteId, deliveryOrder.getEndSiteId()));
                if (tmsDeliverySite == null){
                    deliveryOrderWithNoSite.add(deliveryOrder);
                }else {
                    //配送单上批次挂了对应点位为有效批次ID
                    deliveryBatchId = tmsDeliverySite.getDeliveryBatchId();
                }
            }
            if (deliveryBatchId != null){
                for (TmsDeliveryOrder tmsDeliveryOrder : deliveryOrderWithNoSite) {
                    result = Boolean.TRUE;
                    tmsDeliveryOrder.setBatchId(deliveryBatchId);
                    tmsDeliveryOrderMapper.updateById(tmsDeliveryOrder);
                }
            }
        }
        return result;
    }

    @Override
    public void batchUpdate(List<DeliveryOrderEntity> deliveryOrderEntities) {
        if(CollectionUtils.isEmpty(deliveryOrderEntities)){
            return;
        }
        List<TmsDeliveryOrder> tmsDeliveryOrders = deliveryOrderEntities.stream().map(TmsDeliveryOrderConverter::entity2Do).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(tmsDeliveryOrders,TmsDeliveryOrder.class);
    }

    @Override
    public List<DeliveryOrderEntity> selectListByCondition(DeliveryOrderRelatedQuery deliveryOrderRelatedQuery) {
        if (Objects.isNull(deliveryOrderRelatedQuery) || StringUtil.isBlank(deliveryOrderRelatedQuery.getCarNumber())
            || Objects.isNull(deliveryOrderRelatedQuery.getDeliveryTime()) || Objects.isNull(deliveryOrderRelatedQuery.getOutOrderSource()))  {
            return Lists.newArrayList();
        }
        List<TmsDeliveryOrder> deliveryOrderList = tmsDeliveryOrderMapper.selectListByCondition(deliveryOrderRelatedQuery);
        return deliveryOrderList.stream().map(TmsDeliveryOrderConverter::do2Entity).collect(Collectors.toList());
    }
}
