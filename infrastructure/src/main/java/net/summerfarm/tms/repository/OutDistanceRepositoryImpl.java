package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.tms.base.distance.OutDistanceRepository;
import net.summerfarm.tms.base.distance.dto.OutDistanceConfigVO;
import net.summerfarm.tms.base.distance.entity.OutDistanceEntity;
import net.summerfarm.tms.converter.TmsOutDistanceConfigConverter;
import net.summerfarm.tms.dao.TmsOutDistanceConfig;
import net.summerfarm.tms.enums.TmsOutDistanceEnums;
import net.summerfarm.tms.mapper.TmsOutDistanceConfigNewMapper;
import net.summerfarm.tms.query.base.distance.OutDistanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/7/14 14:43<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
public class OutDistanceRepositoryImpl implements OutDistanceRepository {

    private final TmsOutDistanceConfigNewMapper tmsOutDistanceConfigMapper;

    @Override
    public PageInfo<OutDistanceEntity> queryPage(OutDistanceQuery outDistanceQuery) {
        PageHelper.startPage(outDistanceQuery.getPageIndex(), outDistanceQuery.getPageSize());
        List<OutDistanceEntity> outDistanceEntityList = tmsOutDistanceConfigMapper.queryList(outDistanceQuery);
        return net.summerfarm.tms.utils.PageInfoHelper.createPageInfo(outDistanceEntityList);
    }

    @Override
    public OutDistanceEntity queryByStoreNo(Integer storeNo) {
        TmsOutDistanceConfig tmsOutDistanceConfig = tmsOutDistanceConfigMapper.selectOne(new LambdaQueryWrapper<TmsOutDistanceConfig>()
                .eq(TmsOutDistanceConfig::getStoreNo, storeNo)
                .last("limit 1")
        );

        return TmsOutDistanceConfigConverter.do2Entity(tmsOutDistanceConfig);
    }

    @Override
    public void save(OutDistanceEntity outDistanceEntity) {
        TmsOutDistanceConfig tmsOutDistanceConfig = new TmsOutDistanceConfig();

        tmsOutDistanceConfig.setOutDistance(outDistanceEntity.getOutDistance());
        tmsOutDistanceConfig.setStoreNo(outDistanceEntity.getStoreNo());
        tmsOutDistanceConfig.setState(outDistanceEntity.getState());
        tmsOutDistanceConfig.setAdminId(outDistanceEntity.getAdminId());

        tmsOutDistanceConfigMapper.insert(tmsOutDistanceConfig);
    }

    @Override
    public OutDistanceEntity queryById(Long id) {
        if(id == null){
            return null;
        }
        return TmsOutDistanceConfigConverter.do2Entity(tmsOutDistanceConfigMapper.selectById(id));
    }

    @Override
    public void update(OutDistanceEntity outDistanceEntity) {
        if(outDistanceEntity == null){
            return;
        }
        tmsOutDistanceConfigMapper.updateById(TmsOutDistanceConfigConverter.entity2Do(outDistanceEntity));
    }
}
