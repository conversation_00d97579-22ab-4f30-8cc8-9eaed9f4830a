package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.TmsDeliveryPerformanceReviewDetailConverter;
import net.summerfarm.tms.converter.TmsDeliveryPerformanceReviewTaskConverter;
import net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetail;
import net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewTask;
import net.summerfarm.tms.mapper.TmsDeliveryPerformanceReviewDetailMapper;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewTaskRepository;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewTaskEntity;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.mapper.TmsDeliveryPerformanceReviewTaskMapper;
import net.summerfarm.tms.query.delivery.PerformanceReviewTaskQuery;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/6/28 16:10<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliveryPerformanceReviewTaskRepositoryImpl implements DeliveryPerformanceReviewTaskRepository {

    @Resource
    private TmsDeliveryPerformanceReviewTaskMapper tmsDeliveryPerformanceReviewTaskMapper;
    @Resource
    private TmsDeliveryPerformanceReviewDetailMapper tmsDeliveryPerformanceReviewDetailMapper;

    @Override
    public Long save(DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity) {
        TmsDeliveryPerformanceReviewTask tmsDeliveryPerformanceReviewTask = TmsDeliveryPerformanceReviewTaskConverter.entity2Do(deliveryPerformanceReviewTaskEntity);
        if(tmsDeliveryPerformanceReviewTask == null){
            throw new TmsRuntimeException("生成履约审核保存数据不能为空");
        }
        tmsDeliveryPerformanceReviewTaskMapper.insert(tmsDeliveryPerformanceReviewTask);
        return tmsDeliveryPerformanceReviewTask.getId();
    }

    @Override
    public PageInfo<DeliveryPerformanceReviewTaskEntity> queryPageList(PerformanceReviewTaskQuery performanceReviewTaskQuery) {
        PageHelper.startPage(performanceReviewTaskQuery.getPageIndex(), performanceReviewTaskQuery.getPageSize());
        LambdaQueryWrapper<TmsDeliveryPerformanceReviewTask> query = new LambdaQueryWrapper<TmsDeliveryPerformanceReviewTask>()
                .eq(performanceReviewTaskQuery.getId() != null, TmsDeliveryPerformanceReviewTask::getId, performanceReviewTaskQuery.getId())
                .like(!StringUtils.isEmpty(performanceReviewTaskQuery.getName()), TmsDeliveryPerformanceReviewTask::getName, performanceReviewTaskQuery.getName())
                .in(!CollectionUtils.isEmpty(performanceReviewTaskQuery.getReviewTaskTypeList()), TmsDeliveryPerformanceReviewTask::getReviewTaskType, performanceReviewTaskQuery.getReviewTaskTypeList())
                .in(!CollectionUtils.isEmpty(performanceReviewTaskQuery.getStateList()), TmsDeliveryPerformanceReviewTask::getState, performanceReviewTaskQuery.getStateList())
                .ge(performanceReviewTaskQuery.getBeginCreateTime() != null, TmsDeliveryPerformanceReviewTask::getCreateTime, performanceReviewTaskQuery.getBeginCreateTime())
                .le(performanceReviewTaskQuery.getEndCreateTime() != null, TmsDeliveryPerformanceReviewTask::getCreateTime, performanceReviewTaskQuery.getEndCreateTime())
                .orderByDesc(TmsDeliveryPerformanceReviewTask::getId);
        if (performanceReviewTaskQuery.getReviewMode() != null){
            query.eq(TmsDeliveryPerformanceReviewTask::getReviewMode, performanceReviewTaskQuery.getReviewMode());
        }
        List<TmsDeliveryPerformanceReviewTask> tmsDeliveryPerformanceReviewTasks = tmsDeliveryPerformanceReviewTaskMapper.selectList(query);

        PageInfo pageInfo = PageInfoHelper.createPageInfo(tmsDeliveryPerformanceReviewTasks);
        List<DeliveryPerformanceReviewTaskEntity> deliveryPerformanceReviewTaskEntities = tmsDeliveryPerformanceReviewTasks.stream().map(TmsDeliveryPerformanceReviewTaskConverter::do2Entity).collect(Collectors.toList());
        pageInfo.setList(deliveryPerformanceReviewTaskEntities);

        return pageInfo;
    }

    @Override
    public DeliveryPerformanceReviewTaskEntity queryById(Long performanceReviewTaskId) {
        if(performanceReviewTaskId == null){
            return null;
        }
        return TmsDeliveryPerformanceReviewTaskConverter.do2Entity(tmsDeliveryPerformanceReviewTaskMapper.selectById(performanceReviewTaskId));
    }

    @Override
    public List<DeliveryPerformanceReviewTaskEntity> queryByIdList(List<Long> performanceReviewTaskId) {
        if(CollectionUtils.isEmpty(performanceReviewTaskId)){
            return new ArrayList<>();
        }

        List<TmsDeliveryPerformanceReviewTask> list = tmsDeliveryPerformanceReviewTaskMapper.selectBatchIds(performanceReviewTaskId);
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }

        return list.stream().map(TmsDeliveryPerformanceReviewTaskConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public Map<Long, DeliveryPerformanceReviewTaskEntity> mapByIdList(List<Long> performanceReviewTaskId) {
        List<DeliveryPerformanceReviewTaskEntity> list = queryByIdList(performanceReviewTaskId);
        return list.stream().collect(Collectors.toMap(DeliveryPerformanceReviewTaskEntity::getId,
                Function.identity(), (a, b) -> a));
    }

    @Override
    public void update(DeliveryPerformanceReviewTaskEntity deliveryPerformanceReviewTaskEntity) {
        tmsDeliveryPerformanceReviewTaskMapper.updateById(TmsDeliveryPerformanceReviewTaskConverter.entity2Do(deliveryPerformanceReviewTaskEntity));
    }

    @Override
    public List<DeliveryPerformanceReviewTaskEntity> queryWithDetail(List<Long> performanceReviewTaskIds) {
        if(CollectionUtils.isEmpty(performanceReviewTaskIds)){
            return Collections.emptyList();
        }
        //查询
        List<TmsDeliveryPerformanceReviewTask> tasks = tmsDeliveryPerformanceReviewTaskMapper.selectBatchIds(performanceReviewTaskIds);
        List<TmsDeliveryPerformanceReviewDetail> detailList = tmsDeliveryPerformanceReviewDetailMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPerformanceReviewDetail>()
                .in(TmsDeliveryPerformanceReviewDetail::getPerformanceReviewTaskId, performanceReviewTaskIds)
        );

        List<DeliveryPerformanceReviewDetailEntity> detailEntities = detailList.stream().map(TmsDeliveryPerformanceReviewDetailConverter::do2Entity).collect(Collectors.toList());

        //根据任务ID进行分组
        Map<Long, List<DeliveryPerformanceReviewDetailEntity>> taskDetailMap = detailEntities.stream().collect(Collectors.groupingBy(DeliveryPerformanceReviewDetailEntity::getPerformanceReviewTaskId));

        List<DeliveryPerformanceReviewTaskEntity> taskEntities = tasks.stream().map(TmsDeliveryPerformanceReviewTaskConverter::do2Entity).collect(Collectors.toList());
        taskEntities.forEach(task ->{
            List<DeliveryPerformanceReviewDetailEntity> deliveryPerformanceReviewDetailEntities = taskDetailMap.get(task.getId());
            if(!CollectionUtils.isEmpty(deliveryPerformanceReviewDetailEntities)){
                task.setDeliveryPerformanceReviewDetailEntities(deliveryPerformanceReviewDetailEntities);
            }
        });

        return taskEntities;
    }
}
