package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import net.summerfarm.tms.converter.TmsDeliverySiteItemCodeConverter;
import net.summerfarm.tms.converter.TmsDeliverySiteItemConverter;
import net.summerfarm.tms.converter.TmsDeliverySiteItemRecycleConverter;
import net.summerfarm.tms.dao.TmsDeliverySiteItem;
import net.summerfarm.tms.dao.TmsDeliverySiteItemCode;
import net.summerfarm.tms.dao.TmsDeliverySiteItemRecycle;
import net.summerfarm.tms.delivery.DeliverySiteItemRepository;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemCodeEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemEntity;
import net.summerfarm.tms.enums.DeliverySiteItemTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.mapper.TmsDeliverySiteItemCodeMapper;
import net.summerfarm.tms.mapper.TmsDeliverySiteItemMapper;
import net.summerfarm.tms.mapper.TmsDeliverySiteItemRecycleMapper;
import net.summerfarm.tms.query.delivery.DeliverySiteItemQuery;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/13 18:16<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliverySiteItemRepositoryImpl implements DeliverySiteItemRepository {

    @Resource
    private TmsDeliverySiteItemMapper tmsDeliverySiteItemMapper;
    @Resource
    private TmsDeliverySiteItemCodeMapper tmsDeliverySiteItemCodeMapper;
    @Resource
    private TmsDeliverySiteItemRecycleMapper tmsDeliverySiteItemRecycleMapper;

    @Override
    public void update(DeliverySiteItemEntity deliverySiteItemEntity) {
        if (deliverySiteItemEntity.getId() == null) {
            List<TmsDeliverySiteItem> tmsDeliverySiteItems = tmsDeliverySiteItemMapper.selectList(
                    new LambdaQueryWrapper<TmsDeliverySiteItem>()
                            .eq(TmsDeliverySiteItem::getDeliverySiteId, deliverySiteItemEntity.getDeliverySiteId())
                            .eq(TmsDeliverySiteItem::getOutItemId, deliverySiteItemEntity.getOutItemId())
                            .eq(TmsDeliverySiteItem::getType, DeliverySiteItemTypeEnum.DELIVERY.getCode())
            );
            TmsAssert.isTrue(tmsDeliverySiteItems.size() == 1, ErrorCodeEnum.DB_DATA_ERROR, "货品没找到或有多个");
            deliverySiteItemEntity.setId(tmsDeliverySiteItems.get(0).getId());
        }
        tmsDeliverySiteItemMapper.updateById(TmsDeliverySiteItemConverter.entity2Do(deliverySiteItemEntity));
    }

    @Override
    public List<DeliverySiteItemEntity> queryList(DeliverySiteItemQuery deliverySiteItemQuery) {
        List<TmsDeliverySiteItem> tmsDeliverySiteItems = tmsDeliverySiteItemMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItem>()
                .eq(deliverySiteItemQuery.getDeliverySiteId() != null, TmsDeliverySiteItem::getDeliverySiteId, deliverySiteItemQuery.getDeliverySiteId())
                .eq(deliverySiteItemQuery.getType() != null, TmsDeliverySiteItem::getType, deliverySiteItemQuery.getType())
                .in(!CollectionUtils.isEmpty(deliverySiteItemQuery.getDeliverySiteIds()), TmsDeliverySiteItem::getDeliverySiteId, deliverySiteItemQuery.getDeliverySiteIds())
        );

        return tmsDeliverySiteItems.stream().map(TmsDeliverySiteItemConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public DeliverySiteItemEntity queryByUk(Long deliverySiteId, String outItemId,Integer type) {
        List<TmsDeliverySiteItem> tmsDeliverySiteItems = tmsDeliverySiteItemMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItem>()
                .eq(TmsDeliverySiteItem::getDeliverySiteId, deliverySiteId)
                .eq(TmsDeliverySiteItem::getOutItemId, outItemId)
                .eq(TmsDeliverySiteItem::getType, type)
                .orderByDesc(TmsDeliverySiteItem::getId));
        if (tmsDeliverySiteItems.isEmpty()) {
            return null;
        }
        return TmsDeliverySiteItemConverter.do2Entity(tmsDeliverySiteItems.get(0));
    }

    @Override
    public void save(DeliverySiteItemEntity deliverySiteItemEntity) {
        tmsDeliverySiteItemMapper.insert(TmsDeliverySiteItemConverter.entity2Do(deliverySiteItemEntity));
    }

    @Override
    public void saveBatch(ArrayList<DeliverySiteItemEntity> deliverySiteItemEntities) {
        if(CollectionUtils.isEmpty(deliverySiteItemEntities)){
            return;
        }
        List<TmsDeliverySiteItem> tmsDeliverySiteItems = deliverySiteItemEntities.stream().map(TmsDeliverySiteItemConverter::entity2Do).collect(Collectors.toList());
        for (TmsDeliverySiteItem tmsDeliverySiteItem : tmsDeliverySiteItems) {
            tmsDeliverySiteItem.setPlanReceiptCount(tmsDeliverySiteItem.getPlanReceiptCount() == null ? 0 : tmsDeliverySiteItem.getPlanReceiptCount());
            tmsDeliverySiteItem.setRealReceiptCount(tmsDeliverySiteItem.getRealReceiptCount() == null ? 0 : tmsDeliverySiteItem.getRealReceiptCount());
            tmsDeliverySiteItem.setShortCount(tmsDeliverySiteItem.getShortCount() == null ? 0 : tmsDeliverySiteItem.getShortCount());
            tmsDeliverySiteItem.setInterceptCount(tmsDeliverySiteItem.getInterceptCount() == null ? 0 : tmsDeliverySiteItem.getInterceptCount());
            tmsDeliverySiteItem.setRejectCount(tmsDeliverySiteItem.getRejectCount() == null ? 0 : tmsDeliverySiteItem.getRejectCount());
            tmsDeliverySiteItem.setScanCount(tmsDeliverySiteItem.getScanCount() == null ? 0 : tmsDeliverySiteItem.getScanCount());
            tmsDeliverySiteItem.setNoscanCount(tmsDeliverySiteItem.getNoscanCount() == null ? 0 : tmsDeliverySiteItem.getScanCount());
        }
        tmsDeliverySiteItemMapper.saveBatch(tmsDeliverySiteItems);
    }

    @Override
    public List<DeliverySiteItemEntity> queryWithCodeList(DeliverySiteItemQuery deliverySiteItemQuery) {
        List<TmsDeliverySiteItem> tmsDeliverySiteItems = tmsDeliverySiteItemMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItem>()
                .eq(deliverySiteItemQuery.getDeliverySiteId() != null, TmsDeliverySiteItem::getDeliverySiteId, deliverySiteItemQuery.getDeliverySiteId())
                .eq(deliverySiteItemQuery.getType() != null, TmsDeliverySiteItem::getType, deliverySiteItemQuery.getType())
                .in(!CollectionUtils.isEmpty(deliverySiteItemQuery.getDeliverySiteIds()), TmsDeliverySiteItem::getDeliverySiteId, deliverySiteItemQuery.getDeliverySiteIds())
        );

        //扫码信息
        List<TmsDeliverySiteItemCode> tmsDeliverySiteItemCodes = tmsDeliverySiteItemCodeMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItemCode>()
                .eq(deliverySiteItemQuery.getDeliverySiteId() != null, TmsDeliverySiteItemCode::getDeliverySiteId, deliverySiteItemQuery.getDeliverySiteId())
                .in(!CollectionUtils.isEmpty(deliverySiteItemQuery.getDeliverySiteIds()), TmsDeliverySiteItemCode::getDeliverySiteId, deliverySiteItemQuery.getDeliverySiteIds())
        );
        List<DeliverySiteItemCodeEntity> deliverySiteItemCodeEntityList = tmsDeliverySiteItemCodes.stream().map(TmsDeliverySiteItemCodeConverter::do2Entity).collect(Collectors.toList());

        List<DeliverySiteItemEntity> deliverySiteItemEntities = tmsDeliverySiteItems.stream().map(TmsDeliverySiteItemConverter::do2Entity).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(deliverySiteItemCodeEntityList)){
            Map<Long, List<DeliverySiteItemCodeEntity>> deliverySiteItemIdCodeMap = deliverySiteItemCodeEntityList.stream().collect(Collectors.groupingBy(DeliverySiteItemCodeEntity::getDeliverySiteItemId));
            for (DeliverySiteItemEntity deliverySiteItemEntity : deliverySiteItemEntities) {
                if(!CollectionUtils.isEmpty(deliverySiteItemIdCodeMap.get(deliverySiteItemEntity.getId()))){
                    deliverySiteItemEntity.setDeliverySiteItemCodeEntityList(deliverySiteItemIdCodeMap.get(deliverySiteItemEntity.getId()));
                }
            }
        }

        return deliverySiteItemEntities;
    }

    @Override
    public List<DeliverySiteItemEntity> queryListWithCodeWithRecycle(DeliverySiteItemQuery deliverySiteItemQuery) {
        List<DeliverySiteItemEntity> deliverySiteItemEntities = this.queryWithCodeList(deliverySiteItemQuery);
        //获取回收点位物品ID
        List<Long> siteItemIds = deliverySiteItemEntities.stream().filter(item -> Objects.equals(item.getType(), DeliverySiteItemTypeEnum.RECYCLE.getCode())).map(DeliverySiteItemEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(siteItemIds)){
            return deliverySiteItemEntities;
        }
        List<TmsDeliverySiteItemRecycle> tmsDeliverySiteItemRecycles = tmsDeliverySiteItemRecycleMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItemRecycle>()
                .in(TmsDeliverySiteItemRecycle::getDeliverySiteItemId, siteItemIds));
        if (CollectionUtils.isEmpty(tmsDeliverySiteItemRecycles)){
            return deliverySiteItemEntities;
        }
        Map<Long, TmsDeliverySiteItemRecycle> deliverySiteItemIdRecycleMap = tmsDeliverySiteItemRecycles.stream().collect(Collectors.toMap(TmsDeliverySiteItemRecycle::getDeliverySiteItemId, Function.identity(), (oldData, newData) -> newData));
        for (DeliverySiteItemEntity deliverySiteItemEntity : deliverySiteItemEntities) {
            TmsDeliverySiteItemRecycle tmsDeliverySiteItemRecycle = deliverySiteItemIdRecycleMap.get(deliverySiteItemEntity.getId());
            if(tmsDeliverySiteItemRecycle == null){
                continue;
            }
            deliverySiteItemEntity.setDeliverySiteItemRecycleEntity(TmsDeliverySiteItemRecycleConverter.do2Entity(tmsDeliverySiteItemRecycle));
        }
        return deliverySiteItemEntities;
    }

    @Override
    public Boolean siteItemStatusDataInit() {
        List<Integer> deliveryShortInitIds = tmsDeliverySiteItemMapper.selectDeliveryShortInitIds();
        List<Integer> deliveryRecycleAbnormalInitIds =tmsDeliverySiteItemMapper.selectRecycleAbnormalInitIds();
        if (CollectionUtils.isEmpty(deliveryShortInitIds) && CollectionUtils.isEmpty(deliveryRecycleAbnormalInitIds)){
            return Boolean.TRUE;
        }
        if (!CollectionUtils.isEmpty(deliveryShortInitIds)){
            tmsDeliverySiteItemMapper.update(null, new LambdaUpdateWrapper<TmsDeliverySiteItem>()
                    .in(TmsDeliverySiteItem::getId, deliveryShortInitIds)
                    .set(TmsDeliverySiteItem::getStatus, 1));
        }
        if (!CollectionUtils.isEmpty(deliveryRecycleAbnormalInitIds)){
            tmsDeliverySiteItemMapper.update(null, new LambdaUpdateWrapper<TmsDeliverySiteItem>()
                    .in(TmsDeliverySiteItem::getId, deliveryRecycleAbnormalInitIds)
                    .set(TmsDeliverySiteItem::getStatus, 1));
        }
        return Boolean.FALSE;
    }

    @Override
    public void batchUpdate(List<DeliverySiteItemEntity> needUpdateItemEntityList) {
        if(CollectionUtils.isEmpty(needUpdateItemEntityList)){
            return;
        }
        List<TmsDeliverySiteItem> tmsDeliverySiteItems = needUpdateItemEntityList.stream().map(TmsDeliverySiteItemConverter::entity2Do).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(tmsDeliverySiteItems,TmsDeliverySiteItem.class);
    }
}
