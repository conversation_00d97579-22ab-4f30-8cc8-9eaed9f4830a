package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.converter.TmsLackApprovedAppealConverter;
import net.summerfarm.tms.dao.TmsLackApprovedAppeal;
import net.summerfarm.tms.lack.LackAppealRepository;
import net.summerfarm.tms.lack.entity.LackApprovedAppealEntity;
import net.summerfarm.tms.mapper.TmsLackApprovedAppealMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/9/7 16:40<br/>
 *
 * <AUTHOR> />
 */
@Service
public class LackAppealRepositoryImpl implements LackAppealRepository {

    @Resource
    private TmsLackApprovedAppealMapper tmsLackApprovedAppealMapper;

    @Override
    public List<LackApprovedAppealEntity> queryByApprovedIds(List<Long> lackGoodsApprovedIds) {
        if(CollectionUtils.isEmpty(lackGoodsApprovedIds)){
            return Collections.emptyList();
        }
        List<TmsLackApprovedAppeal> tmsLackApprovedAppeals = tmsLackApprovedAppealMapper.selectList(new LambdaQueryWrapper<TmsLackApprovedAppeal>()
                .in(TmsLackApprovedAppeal::getApprovedId, lackGoodsApprovedIds)
        );

        return tmsLackApprovedAppeals.stream().map(TmsLackApprovedAppealConverter::model2Entity).collect(Collectors.toList());
    }

    @Override
    public void appealSave(LackApprovedAppealEntity lackApprovedAppealEntity) {
        if(lackApprovedAppealEntity == null){
            return;
        }
        tmsLackApprovedAppealMapper.insert(TmsLackApprovedAppealConverter.entity2Model(lackApprovedAppealEntity));
    }
}
