package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.base.config.TmsConfigRepository;
import net.summerfarm.tms.base.config.entity.ConfigEntity;
import net.summerfarm.tms.converter.TmsConfigConverter;
import net.summerfarm.tms.dao.TmsConfig;
import net.summerfarm.tms.enums.TmsConfigKeyEnum;
import net.summerfarm.tms.mapper.TmsConfigMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023-03-24
 **/
@Service
public class TmsConfigRepositoryImpl implements TmsConfigRepository {
    @Resource
    private TmsConfigMapper tmsConfigMapper;

    @Override
    public ConfigEntity queryByKey(TmsConfigKeyEnum key) {
        TmsConfig tmsConfig = tmsConfigMapper.selectOne(new LambdaQueryWrapper<TmsConfig>()
                .eq(TmsConfig::getName, key.name()));


        return TmsConfigConverter.doToEntity(tmsConfig);
    }
}
