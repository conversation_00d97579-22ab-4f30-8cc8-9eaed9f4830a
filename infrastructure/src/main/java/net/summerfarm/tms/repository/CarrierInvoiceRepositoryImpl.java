package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.base.carrier.CarrierInvoiceRepository;
import net.summerfarm.tms.base.carrier.entity.CarrierInvoiceEntity;
import net.summerfarm.tms.converter.CarrierInvoiceConverter;
import net.summerfarm.tms.dao.CarrierInvoice;
import net.summerfarm.tms.mapper.CarrierInvoiceMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/10/12 15:16<br/>
 *
 * <AUTHOR> />
 */
@Repository
public class CarrierInvoiceRepositoryImpl implements CarrierInvoiceRepository {

    @Resource
    private CarrierInvoiceMapper carrierInvoiceMapper;


    @Override
    public List<CarrierInvoiceEntity> queryByCarrierIdList(List<Long> carrierIdList) {
        if(CollectionUtils.isEmpty(carrierIdList)){
            return Lists.newArrayList();
        }
        //查询承运商发票信息
        List<CarrierInvoice> carrierInvoices = carrierInvoiceMapper.selectList(new LambdaQueryWrapper<CarrierInvoice>()
                .in(CarrierInvoice::getCarrierId, carrierIdList));

        return carrierInvoices.stream().map(CarrierInvoiceConverter::do2Entity).collect(Collectors.toList());
    }
}
