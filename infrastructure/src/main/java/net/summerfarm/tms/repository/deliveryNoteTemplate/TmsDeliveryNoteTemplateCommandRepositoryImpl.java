package net.summerfarm.tms.repository.deliveryNoteTemplate;

import net.summerfarm.tms.converter.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongConverter;
import net.summerfarm.tms.converter.deliveryNoteTemplate.TmsDeliveryNoteTemplateConverter;
import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplate;
import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateBelongCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateCommandRepository;
import net.summerfarm.tms.mapper.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongMapper;
import net.summerfarm.tms.mapper.deliveryNoteTemplate.TmsDeliveryNoteTemplateMapper;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
@Repository
public class TmsDeliveryNoteTemplateCommandRepositoryImpl implements TmsDeliveryNoteTemplateCommandRepository {

    @Autowired
    private TmsDeliveryNoteTemplateMapper tmsDeliveryNoteTemplateMapper;
    @Autowired
    private TmsDeliveryNoteTemplateBelongMapper tmsDeliveryNoteTemplateBelongMapper;

    @Override
    public TmsDeliveryNoteTemplateEntity insertSelective(TmsDeliveryNoteTemplateCommandParam param) {
        TmsDeliveryNoteTemplate tmsDeliveryNoteTemplate = TmsDeliveryNoteTemplateConverter.toTmsDeliveryNoteTemplate(param);
        tmsDeliveryNoteTemplateMapper.insert(tmsDeliveryNoteTemplate);
        List<TmsDeliveryNoteTemplateBelongCommandParam> belongCommandParamList = param.getBelongCommandParamList();
        if(CollectionUtils.isEmpty(belongCommandParamList)){
            List<TmsDeliveryNoteTemplateBelong> tmsDeliveryNoteTemplateBelongList = belongCommandParamList.stream().map(TmsDeliveryNoteTemplateBelongConverter::toTmsDeliveryNoteTemplateBelong).collect(Collectors.toList());
            MybatisPlusUtil.createBatch(tmsDeliveryNoteTemplateBelongList, TmsDeliveryNoteTemplateBelong.class);
        }

        return TmsDeliveryNoteTemplateConverter.toTmsDeliveryNoteTemplateEntity(tmsDeliveryNoteTemplate);
    }

    @Override
    public void updateSelectiveById(TmsDeliveryNoteTemplateCommandParam param){
        tmsDeliveryNoteTemplateMapper.updateSelectiveById(TmsDeliveryNoteTemplateConverter.toTmsDeliveryNoteTemplate(param));
    }


    @Override
    public int remove(Long id) {
        return tmsDeliveryNoteTemplateMapper.remove(id);
    }
}