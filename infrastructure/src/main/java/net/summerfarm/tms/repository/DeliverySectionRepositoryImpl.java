package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.converter.SiteConverter;
import net.summerfarm.tms.converter.TmsDeliverySectionConverter;
import net.summerfarm.tms.dao.TmsDeliverySection;
import net.summerfarm.tms.dao.TmsDistSite;
import net.summerfarm.tms.delivery.DeliverySectionRepository;
import net.summerfarm.tms.delivery.entity.DeliverySectionEntity;
import net.summerfarm.tms.mapper.TmsDeliverySectionMapper;
import net.summerfarm.tms.mapper.TmsDistSiteMapper;
import net.summerfarm.tms.query.delivery.DeliverySectionQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/12/8 14:07<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DeliverySectionRepositoryImpl implements DeliverySectionRepository {
    @Resource
    private TmsDeliverySectionMapper tmsDeliverySectionMapper;
    @Resource
    private TmsDistSiteMapper tmsDistSiteMapper;

    @Override
    public void save(DeliverySectionEntity deliverySectionEntity) {
        tmsDeliverySectionMapper.insert(TmsDeliverySectionConverter.entity2Do(deliverySectionEntity));
    }

    @Override
    public void saveBatch(List<DeliverySectionEntity> deliverySectionEntities) {
        if(CollectionUtils.isEmpty(deliverySectionEntities)){
            return;
        }
        List<TmsDeliverySection> tmsDeliverySections = deliverySectionEntities.stream().map(TmsDeliverySectionConverter::entity2Do).collect(Collectors.toList());
        tmsDeliverySectionMapper.saveBatch(tmsDeliverySections);
    }

    @Override
    public List<DeliverySectionEntity> queryList(DeliverySectionQuery deliverySectionQuery) {
        List<TmsDeliverySection> tmsDeliverySections = tmsDeliverySectionMapper.selectList(new LambdaQueryWrapper<TmsDeliverySection>()
                .eq(deliverySectionQuery.getBatchId() != null, TmsDeliverySection::getBatchId, deliverySectionQuery.getBatchId())
                .eq(deliverySectionQuery.getType() != null, TmsDeliverySection::getType, deliverySectionQuery.getType())
                .in(!CollectionUtils.isEmpty(deliverySectionQuery.getEndSiteIds()),TmsDeliverySection::getEndSiteId,deliverySectionQuery.getEndSiteIds())
                .in(!CollectionUtils.isEmpty(deliverySectionQuery.getTypes()),TmsDeliverySection::getType,deliverySectionQuery.getTypes())
        );

        return tmsDeliverySections.stream().map(TmsDeliverySectionConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public void removeList(List<DeliverySectionEntity> deliverySectionEntityList) {
        if(CollectionUtils.isEmpty(deliverySectionEntityList)){
            return;
        }
        tmsDeliverySectionMapper.deleteBatchIds(deliverySectionEntityList.stream().map(DeliverySectionEntity::getId).collect(Collectors.toList()));
    }

    @Override
    public List<DeliverySectionEntity> queryListWithSite(DeliverySectionQuery deliverySectionQuery) {
        List<TmsDeliverySection> tmsDeliverySections = tmsDeliverySectionMapper.selectList(new LambdaQueryWrapper<TmsDeliverySection>()
                .eq(deliverySectionQuery.getBatchId() != null, TmsDeliverySection::getBatchId, deliverySectionQuery.getBatchId())
                .eq(deliverySectionQuery.getType() != null, TmsDeliverySection::getType, deliverySectionQuery.getType())
                .in(!CollectionUtils.isEmpty(deliverySectionQuery.getEndSiteIds()),TmsDeliverySection::getEndSiteId,deliverySectionQuery.getEndSiteIds())
        );

        List<DeliverySectionEntity> deliverySectionEntityList = tmsDeliverySections.stream().map(TmsDeliverySectionConverter::do2Entity).collect(Collectors.toList());

        List<Long> siteIds = tmsDeliverySections.stream().map(TmsDeliverySection::getBeginSiteId).collect(Collectors.toList());
        siteIds.addAll(tmsDeliverySections.stream().map(TmsDeliverySection::getEndSiteId).collect(Collectors.toList()));

        if(!CollectionUtils.isEmpty(siteIds)){
            List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>().in(TmsDistSite::getId, siteIds));
            Map<Long, TmsDistSite> idMap = tmsDistSites.stream().collect(Collectors.toMap(TmsDistSite::getId, Function.identity()));
            for (DeliverySectionEntity deliverySectionEntity : deliverySectionEntityList) {
                TmsDistSite beginSite = idMap.get(deliverySectionEntity.getBeginSiteEntity().getId());
                TmsDistSite endSite = idMap.get(deliverySectionEntity.getEndSiteEntity().getId());

                deliverySectionEntity.setBeginSiteEntity(SiteConverter.tmsDistSite2Entity(beginSite));
                deliverySectionEntity.setEndSiteEntity(SiteConverter.tmsDistSite2Entity(endSite));
            }
        }

        return deliverySectionEntityList;
    }

    @Override
    public List<DeliverySectionEntity> queryListByBatchIdsAndType(List<Long> deliveryBatchIdList, Integer type) {
        List<TmsDeliverySection> tmsDeliverySections = tmsDeliverySectionMapper.selectList(new LambdaQueryWrapper<TmsDeliverySection>()
                .in(TmsDeliverySection::getBatchId, deliveryBatchIdList)
                .eq(TmsDeliverySection::getType, type)
        );

        return tmsDeliverySections.stream().map(TmsDeliverySectionConverter::do2Entity).collect(Collectors.toList());
    }
}
