package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.alert.DeliveryAlertRuleRepository;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleEntity;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleGroupEntity;
import net.summerfarm.tms.alert.entity.DeliveryAlertRuleItemVO;
import net.summerfarm.tms.converter.TmsCompleteDeliveryRuleConverter;
import net.summerfarm.tms.converter.TmsCompleteDeliveryRuleItemConverter;
import net.summerfarm.tms.dao.TmsCompleteDeliveryRule;
import net.summerfarm.tms.dao.TmsCompleteDeliveryRuleItem;
import net.summerfarm.tms.enums.DeliveryAlertEnums;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.mapper.TmsCompleteDeliveryRuleItemMapper;
import net.summerfarm.tms.mapper.TmsCompleteDeliveryRuleMapper;
import net.summerfarm.tms.query.alert.DeliveryAlertRuleQuery;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:配送提醒仓库实现类
 * date: 2023/3/21 15:13
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class DeliveryAlertRuleRepositoryImpl implements DeliveryAlertRuleRepository {

    private final TmsCompleteDeliveryRuleMapper tmsCompleteDeliveryRuleMapper;

    private final TmsCompleteDeliveryRuleItemMapper tmsCompleteDeliveryRuleItemMapper;

    @Override
    public List<DeliveryAlertRuleEntity> queryList(DeliveryAlertRuleQuery deliveryAlertRuleQuery) {
        List<TmsCompleteDeliveryRule> deliveryRules = tmsCompleteDeliveryRuleMapper.selectList(new LambdaQueryWrapper<TmsCompleteDeliveryRule>()
                .eq(deliveryAlertRuleQuery.getStoreNo() != null, TmsCompleteDeliveryRule::getStoreNo, deliveryAlertRuleQuery.getStoreNo())
                .ne(deliveryAlertRuleQuery.getNotEqualId() != null, TmsCompleteDeliveryRule::getId, deliveryAlertRuleQuery.getNotEqualId()));
        return deliveryRules.stream().map(TmsCompleteDeliveryRuleConverter::do2entity).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryAlertRuleEntity> queryListWithItems(DeliveryAlertRuleQuery deliveryAlertRuleQuery) {
        List<DeliveryAlertRuleEntity> deliveryAlertRuleEntities = this.queryList(deliveryAlertRuleQuery);
        if (CollectionUtils.isEmpty(deliveryAlertRuleEntities)){
            return new ArrayList<>();
        }
        for (DeliveryAlertRuleEntity deliveryAlertRuleEntity : deliveryAlertRuleEntities) {
            List<TmsCompleteDeliveryRuleItem> tmsCompleteDeliveryRuleItems = tmsCompleteDeliveryRuleItemMapper.selectList(new LambdaQueryWrapper<TmsCompleteDeliveryRuleItem>()
                    .eq(TmsCompleteDeliveryRuleItem::getRuleId, deliveryAlertRuleEntity.getId()));
            List<DeliveryAlertRuleItemVO> deliveryAlertRuleItemVOList = tmsCompleteDeliveryRuleItems.stream().map(item -> {
                DeliveryAlertRuleItemVO deliveryAlertRuleItemVO = TmsCompleteDeliveryRuleItemConverter.do2vo(item);
                deliveryAlertRuleItemVO.setRuleName(deliveryAlertRuleEntity.getRuleName());
                return deliveryAlertRuleItemVO;
            }).collect(Collectors.toList());
            deliveryAlertRuleEntity.deliveryAlertRuleItemsInit(deliveryAlertRuleItemVOList);
        }
        return deliveryAlertRuleEntities;
    }

    @Override
    public PageInfo<DeliveryAlertRuleGroupEntity> queryPage(DeliveryAlertRuleQuery deliveryAlertRuleQuery) {
        PageHelper.startPage(deliveryAlertRuleQuery.getPageIndex(), deliveryAlertRuleQuery.getPageSize());
        List<DeliveryAlertRuleGroupEntity> deliveryAlertRuleGroupEntityList = tmsCompleteDeliveryRuleMapper.queryPage(deliveryAlertRuleQuery);
        for (DeliveryAlertRuleGroupEntity deliveryAlertRuleGroupEntity : deliveryAlertRuleGroupEntityList) {
            List<TmsCompleteDeliveryRule> deliveryRules = tmsCompleteDeliveryRuleMapper.selectList(new LambdaQueryWrapper<TmsCompleteDeliveryRule>()
                    .eq(TmsCompleteDeliveryRule::getStoreNo, deliveryAlertRuleGroupEntity.getStoreNo()));
            List<DeliveryAlertRuleEntity> deliveryAlertRuleEntityList = deliveryRules.stream().map(TmsCompleteDeliveryRuleConverter::do2entity).collect(Collectors.toList());
            deliveryAlertRuleGroupEntity.setDeliveryAlertRules(deliveryAlertRuleEntityList);
        }
        return PageInfoHelper.createPageInfo(deliveryAlertRuleGroupEntityList);
    }

    @Override
    public void saveOrUpdate(DeliveryAlertRuleGroupEntity deliveryAlertRuleGroupEntity) {
        List<TmsCompleteDeliveryRule> existedCompleteDeliveryRules = tmsCompleteDeliveryRuleMapper.selectList(new LambdaQueryWrapper<TmsCompleteDeliveryRule>()
                .eq(TmsCompleteDeliveryRule::getStoreNo, deliveryAlertRuleGroupEntity.getStoreNo()));
        if (!CollectionUtils.isEmpty(existedCompleteDeliveryRules)){
            List<Long> existedIds = existedCompleteDeliveryRules.stream().map(TmsCompleteDeliveryRule::getId).collect(Collectors.toList());
            tmsCompleteDeliveryRuleMapper.deleteBatchIds(existedIds);
            tmsCompleteDeliveryRuleItemMapper.deleteBatchIds(existedIds);
        }
        for (DeliveryAlertRuleEntity deliveryAlertRuleEntity : deliveryAlertRuleGroupEntity.getDeliveryAlertRules()) {
            this.save(deliveryAlertRuleEntity);
        }

    }

    @Override
    public void remove(Integer storeNo) {
        List<TmsCompleteDeliveryRule> existedCompleteDeliveryRules = tmsCompleteDeliveryRuleMapper.selectList(new LambdaQueryWrapper<TmsCompleteDeliveryRule>()
                .eq(TmsCompleteDeliveryRule::getStoreNo, storeNo));
        if (CollectionUtils.isEmpty(existedCompleteDeliveryRules)){
            throw new TmsRuntimeException("无可删除的规则组");
        }
        List<Long> existedIds = existedCompleteDeliveryRules.stream().map(TmsCompleteDeliveryRule::getId).collect(Collectors.toList());
        tmsCompleteDeliveryRuleMapper.deleteBatchIds(existedIds);
        tmsCompleteDeliveryRuleItemMapper.delete(new LambdaQueryWrapper<TmsCompleteDeliveryRuleItem>().in(TmsCompleteDeliveryRuleItem::getRuleId, existedIds));
    }

    @Override
    public DeliveryAlertRuleItemVO queryByUk(Integer storeNo, DeliveryAlertEnums.Channel channel, DeliveryAlertEnums.Type type, String bizNo) {
        TmsCompleteDeliveryRuleItem tmsCompleteDeliveryRuleItem = tmsCompleteDeliveryRuleItemMapper.selectOne(new LambdaQueryWrapper<TmsCompleteDeliveryRuleItem>()
                .eq(TmsCompleteDeliveryRuleItem::getStoreNo, storeNo)
                .eq(TmsCompleteDeliveryRuleItem::getChannel, channel.getValue())
                .eq(TmsCompleteDeliveryRuleItem::getType, type.getValue())
                .eq(TmsCompleteDeliveryRuleItem::getBizNo, bizNo));
        return TmsCompleteDeliveryRuleItemConverter.do2vo(tmsCompleteDeliveryRuleItem);
    }

    @Override
    public List<DeliveryAlertRuleItemVO> queryList(Integer storeNo, DeliveryAlertEnums.Type type, List<String> bizNos) {
        List<TmsCompleteDeliveryRuleItem> tmsCompleteDeliveryRuleItems = tmsCompleteDeliveryRuleItemMapper.selectList(new LambdaQueryWrapper<TmsCompleteDeliveryRuleItem>()
                .eq(TmsCompleteDeliveryRuleItem::getStoreNo, storeNo)
                .eq(TmsCompleteDeliveryRuleItem::getType, type.getValue())
                .in(TmsCompleteDeliveryRuleItem::getBizNo, bizNos));

        return tmsCompleteDeliveryRuleItems.stream().map(TmsCompleteDeliveryRuleItemConverter::do2vo).collect(Collectors.toList());
    }

    @Override
    public void save(DeliveryAlertRuleEntity deliveryAlertRuleEntity) {
        TmsCompleteDeliveryRule tmsCompleteDeliveryRule = TmsCompleteDeliveryRuleConverter.entity2do(deliveryAlertRuleEntity);
        tmsCompleteDeliveryRuleMapper.insert(tmsCompleteDeliveryRule);
        this.saveBatchRuleItem(deliveryAlertRuleEntity.getDeliveryAlertRuleItems(), tmsCompleteDeliveryRule.getId());
    }

    @Override
    public void update(DeliveryAlertRuleEntity deliveryAlertRuleEntity) {
        //更新规则
        TmsCompleteDeliveryRule tmsCompleteDeliveryRule = TmsCompleteDeliveryRuleConverter.entity2do(deliveryAlertRuleEntity);
        tmsCompleteDeliveryRuleMapper.updateById(tmsCompleteDeliveryRule);
        //规则项先删后增
        tmsCompleteDeliveryRuleItemMapper.delete(new LambdaQueryWrapper<TmsCompleteDeliveryRuleItem>().eq(TmsCompleteDeliveryRuleItem::getRuleId, tmsCompleteDeliveryRule.getId()));
        this.saveBatchRuleItem(deliveryAlertRuleEntity.getDeliveryAlertRuleItems(), tmsCompleteDeliveryRule.getId());
    }

    private void saveBatchRuleItem(List<DeliveryAlertRuleItemVO> deliveryAlertRuleItems, Long ruleId) {
        if (CollectionUtils.isEmpty(deliveryAlertRuleItems)){
            return;
        }
        if (ruleId == null){
            throw new TmsRuntimeException("配送提醒规则项缺少关联ruleId");
        }
        List<TmsCompleteDeliveryRuleItem> tmsCompleteDeliveryRuleItems = new ArrayList<>();
        for (DeliveryAlertRuleItemVO deliveryAlertRuleItemVO : deliveryAlertRuleItems) {
            TmsCompleteDeliveryRuleItem tmsCompleteDeliveryRuleItem = TmsCompleteDeliveryRuleItemConverter.vo2do(deliveryAlertRuleItemVO);
            tmsCompleteDeliveryRuleItem.setRuleId(ruleId);
            tmsCompleteDeliveryRuleItems.add(tmsCompleteDeliveryRuleItem);
        }
        tmsCompleteDeliveryRuleItemMapper.insertBatch(tmsCompleteDeliveryRuleItems);
    }

    @Override
    public void removeById(Long ruleId) {
        tmsCompleteDeliveryRuleMapper.deleteById(ruleId);
        tmsCompleteDeliveryRuleItemMapper.delete(new LambdaQueryWrapper<TmsCompleteDeliveryRuleItem>().eq(TmsCompleteDeliveryRuleItem::getRuleId, ruleId));
    }

    @Override
    public DeliveryAlertRuleEntity query(Long id) {
        TmsCompleteDeliveryRule tmsCompleteDeliveryRule = tmsCompleteDeliveryRuleMapper.selectById(id);
        return TmsCompleteDeliveryRuleConverter.do2entity(tmsCompleteDeliveryRule);
    }
}
