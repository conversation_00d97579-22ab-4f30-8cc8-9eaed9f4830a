package net.summerfarm.tms.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.converter.TmsDeliveryCostConverter;
import net.summerfarm.tms.converter.TmsDeliveryCostRelationConverter;
import net.summerfarm.tms.cost.DeliveryCostRepository;
import net.summerfarm.tms.cost.entity.DeliveryCostEntity;
import net.summerfarm.tms.cost.entity.DeliveryCostRelationEntity;
import net.summerfarm.tms.dao.TmsDeliveryCost;
import net.summerfarm.tms.dao.TmsDeliveryCostRecord;
import net.summerfarm.tms.dao.TmsDeliveryCostRelation;
import net.summerfarm.tms.enums.DeliveryCostEnums;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.mapper.TmsDeliveryCostMapper;
import net.summerfarm.tms.mapper.TmsDeliveryCostRecordMapper;
import net.summerfarm.tms.mapper.TmsDeliveryCostRelationMapper;
import net.summerfarm.tms.query.delivery.DeliveryCostQuery;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:配送成本仓库实现类
 * date: 2023/2/10 18:29
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class DeliveryCostRepositoryImpl implements DeliveryCostRepository {

    private final TmsDeliveryCostMapper tmsDeliveryCostMapper;
    private final TmsDeliveryCostRecordMapper tmsDeliveryCostRecordMapper;
    private final TmsDeliveryCostRelationMapper tmsDeliveryCostRelationMapper;

    @Override
    public List<DeliveryCostEntity> queryList(DeliveryCostQuery deliveryCostQuery) {
        List<TmsDeliveryCost> tmsDeliveryCosts = tmsDeliveryCostMapper.selectList(new LambdaQueryWrapper<TmsDeliveryCost>()
                .eq(deliveryCostQuery.getDistOrderId() != null, TmsDeliveryCost::getDistOrderId, deliveryCostQuery.getDistOrderId())
                .eq(StrUtil.isNotBlank(deliveryCostQuery.getOutItemId()), TmsDeliveryCost::getOutItemId, deliveryCostQuery.getOutItemId()));
        return tmsDeliveryCosts.stream().map(TmsDeliveryCostConverter::tmsDeliveryCost2Entity).collect(Collectors.toList());
    }

    @Override
    public void saveOrUpdate(List<DeliveryCostEntity> newDeliveryCostEntities, DeliveryCostEnums.ChangeType changeType) {
        if (CollectionUtils.isEmpty(newDeliveryCostEntities)){
            return;
        }
        Long distOrderId = newDeliveryCostEntities.get(0).getDistOrderId();
        List<TmsDeliveryCost> newTmsDeliveryCosts = newDeliveryCostEntities.stream().map(TmsDeliveryCostConverter::entity2TmsDeliveryCost).collect(Collectors.toList());
        //根据委托单ID查询成本批次信息
        List<TmsDeliveryCost> existedTmsDeliveryCosts = tmsDeliveryCostMapper.selectList(new LambdaQueryWrapper<TmsDeliveryCost>()
                .eq(TmsDeliveryCost::getDistOrderId, distOrderId));
        if (CollectionUtils.isEmpty(existedTmsDeliveryCosts)){
            newTmsDeliveryCosts.forEach(tmsDeliveryCostMapper::insert);
            return;
        }
        //根据ID排序
        existedTmsDeliveryCosts = existedTmsDeliveryCosts.stream().sorted(Comparator.comparing(TmsDeliveryCost::getId)).collect(Collectors.toList());
        Map<String, TmsDeliveryCost> ukCostMap = newTmsDeliveryCosts.stream().collect(Collectors.toMap(TmsDeliveryCost::buildUk, Function.identity()));

        for (TmsDeliveryCost existedDeliveryCost : existedTmsDeliveryCosts) {
            TmsDeliveryCost newTmsDeliveryCost = ukCostMap.get(existedDeliveryCost.buildUk());
            //已经存在运输成本的sku未获取到新的运输成本
            BigDecimal newAverageCost = newTmsDeliveryCost == null ? BigDecimal.ZERO : newTmsDeliveryCost.getAverageCost();
            Integer newQuantity = newTmsDeliveryCost == null ? 0 : newTmsDeliveryCost.getQuantity();
            if(newAverageCost.compareTo(existedDeliveryCost.getAverageCost()) == 0){
                //成本相同不处理
                continue;
            }
            TmsDeliveryCostRecord record = new TmsDeliveryCostRecord();
            record.setDeliveryCostId(existedDeliveryCost.getId());
            record.setChangeType(changeType.getValue());
            record.setCostBefore(existedDeliveryCost.getAverageCost());
            record.setCostAfter(newAverageCost);
            //成本变更日志记录
            tmsDeliveryCostRecordMapper.insert(record);
            existedDeliveryCost.setAverageCost(newAverageCost);
            existedDeliveryCost.setQuantity(newQuantity);
            tmsDeliveryCostMapper.updateById(existedDeliveryCost);
        }
    }

    @Override
    public List<DeliveryCostEntity> queryCostList(DeliveryCostQuery deliveryCostQuery) {
        //根据外部批次ID查询成本批次关系
        List<TmsDeliveryCostRelation> tmsDeliveryCostRelations = tmsDeliveryCostRelationMapper.selectList(new LambdaQueryWrapper<TmsDeliveryCostRelation>().
                in(TmsDeliveryCostRelation::getOutBatchId, deliveryCostQuery.getOutBatchIds()));
        List<DeliveryCostEntity> invalidDeliveryCostEntities = new ArrayList<>();
        if (CollectionUtils.isEmpty(tmsDeliveryCostRelations) || tmsDeliveryCostRelations.size() != deliveryCostQuery.getOutBatchIds().size()){
            //已存在的成本批次ID集合
            Set<String> existedOutBatchIds = tmsDeliveryCostRelations.stream().map(TmsDeliveryCostRelation::getOutBatchId).collect(Collectors.toSet());
            //未入库成本批次ID集合
            Set<String> invalidOutBatchIds = deliveryCostQuery.getOutBatchIds().stream().filter(e -> !existedOutBatchIds.contains(e)).collect(Collectors.toSet());
            //处理无效配送成本 成本批次ID未入库
            invalidDeliveryCostEntities = invalidOutBatchIds.stream().map(outBatchId -> {
                DeliveryCostEntity deliveryCostEntity = new DeliveryCostEntity();
                deliveryCostEntity.setOutBatchId(outBatchId);
                deliveryCostEntity.setAverageCost(BigDecimal.ZERO);
                return deliveryCostEntity;
            }).collect(Collectors.toList());
        }
        List<DeliveryCostEntity> deliveryCostEntities = tmsDeliveryCostMapper.queryCostList(deliveryCostQuery);
        if (deliveryCostEntities == null){
            deliveryCostEntities = new ArrayList<>();
        }
        deliveryCostEntities.addAll(invalidDeliveryCostEntities);
        return deliveryCostEntities;
    }

    @Override
    public void saveCostRelation(List<DeliveryCostRelationEntity> costRelations) {
        if (CollectionUtils.isEmpty(costRelations)){
            return;
        }
        List<TmsDeliveryCostRelation> tmsDeliveryCostRelations = costRelations.stream().map(TmsDeliveryCostRelationConverter::entity2TmsDeliveryCostRelation).collect(Collectors.toList());
        tmsDeliveryCostRelations.forEach(tmsDeliveryCostRelationMapper::insert);
    }

    @Override
    public List<DeliveryCostRelationEntity> queryRelationsByOutBatchIds(Collection<String> outBatchIds) {
        if (CollectionUtils.isEmpty(outBatchIds)){
            return Collections.emptyList();
        }
        List<TmsDeliveryCostRelation> tmsDeliveryCostRelations = tmsDeliveryCostRelationMapper.selectList(new LambdaQueryWrapper<TmsDeliveryCostRelation>().
                in(TmsDeliveryCostRelation::getOutBatchId, outBatchIds));
        if (CollectionUtils.isEmpty(tmsDeliveryCostRelations)){
            return Collections.emptyList();
        }
        return tmsDeliveryCostRelations.stream().map(TmsDeliveryCostRelationConverter::tmsDeliveryCostRelation2Entity).collect(Collectors.toList());
    }
}
