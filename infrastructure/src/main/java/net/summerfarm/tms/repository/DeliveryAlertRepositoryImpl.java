package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.alert.DeliveryAlertRepository;
import net.summerfarm.tms.alert.entity.AreaAdCode;
import net.summerfarm.tms.alert.entity.DeliveryAlertEntity;
import net.summerfarm.tms.converter.CompleteDeliveryConverter;
import net.summerfarm.tms.dao.CompleteDelivery;
import net.summerfarm.tms.dao.CompleteDeliveryAdCodeMapping;
import net.summerfarm.tms.dao.TmsCompleteDeliveryRule;
import net.summerfarm.tms.enums.DeliveryAlertEnums;
import net.summerfarm.tms.facade.wnc.WncQueryFacade;
import net.summerfarm.tms.facade.wnc.dto.AdCodeMsgDTO;
import net.summerfarm.tms.mapper.CompleteDeliveryAdCodeMappingMapper;
import net.summerfarm.tms.mapper.CompleteDeliveryMapper;
import net.summerfarm.tms.mapper.TmsCompleteDeliveryRuleMapper;
import net.summerfarm.tms.query.alert.DeliveryAlertQuery;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:配送提醒仓库实现类
 * date: 2023/3/21 15:13
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class DeliveryAlertRepositoryImpl implements DeliveryAlertRepository {

    private final CompleteDeliveryMapper completeDeliveryMapper;

    private final TmsCompleteDeliveryRuleMapper tmsCompleteDeliveryRuleMapper;
    private final CompleteDeliveryAdCodeMappingMapper completeDeliveryAdCodeMappingMapper;
    private final WncQueryFacade wncQueryFacade;


    @Override
    public PageInfo<DeliveryAlertEntity> queryPage(DeliveryAlertQuery deliveryAlertQuery) {
        PageHelper.startPage(deliveryAlertQuery.getPageIndex(), deliveryAlertQuery.getPageSize());
        List<DeliveryAlertEntity> deliveryAlertEntityList = new ArrayList<>();
        return PageInfoHelper.createPageInfo(deliveryAlertEntityList);
    }

    @Override
    public void saveOrUpdate(DeliveryAlertEntity deliveryAlertEntity) {

    }

    @Override
    public List<DeliveryAlertEntity> queryList(DeliveryAlertQuery deliveryAlertQuery) {
        List<CompleteDelivery> completeDeliveries = completeDeliveryMapper.selectList(new LambdaQueryWrapper<CompleteDelivery>()
                .eq(deliveryAlertQuery.getStoreNo() != null, CompleteDelivery::getStoreNo, deliveryAlertQuery.getStoreNo())
                .eq(CompleteDelivery::getStatus, DeliveryAlertEnums.Status.NORMAL));
        return completeDeliveries.stream().map(CompleteDeliveryConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryAlertEntity> queryListWithAreas(DeliveryAlertQuery deliveryAlertQuery) {
        List<DeliveryAlertEntity> deliveryAlertEntityList = this.queryList(deliveryAlertQuery);
        if (CollectionUtils.isEmpty(deliveryAlertEntityList)){
            return new ArrayList<>();
        }
        deliveryAlertEntityList.forEach(deliveryAlertEntity -> {
            deliveryAlertEntity.setAreaAdCodes(Collections.emptyList());
        });

        // 过滤Id
        List<Integer> completeDeliveryIdList = deliveryAlertEntityList.stream().map(DeliveryAlertEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(completeDeliveryIdList)){
            return Collections.emptyList();
        }

        // 查询映射
        List<CompleteDeliveryAdCodeMapping> completeDeliveryAdCodeMappings = completeDeliveryAdCodeMappingMapper.selectList(new LambdaQueryWrapper<CompleteDeliveryAdCodeMapping>()
                .in(CompleteDeliveryAdCodeMapping::getCompleteDeliveryId, completeDeliveryIdList));
        if(CollectionUtils.isEmpty(completeDeliveryAdCodeMappings)){
            return Collections.emptyList();
        }

        // 根据映射查询有效区域信息
        List<String> adCodeList = completeDeliveryAdCodeMappings.stream().map(CompleteDeliveryAdCodeMapping::getAdCode)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        List<AdCodeMsgDTO> adCodeMsgDTOS = wncQueryFacade.queryEffectiveAdCodeMsgByCodes(adCodeList);

        // 分组处理
        Map<String, List<AdCodeMsgDTO>> adCodeEffective2Map = adCodeMsgDTOS.stream().collect(Collectors.groupingBy(AdCodeMsgDTO::getAdCode));
        Map<Integer, List<CompleteDeliveryAdCodeMapping>> completeDeliveryKey2List = completeDeliveryAdCodeMappings.stream().collect(Collectors.groupingBy(CompleteDeliveryAdCodeMapping::getCompleteDeliveryId));

        for (DeliveryAlertEntity deliveryAlertEntity : deliveryAlertEntityList) {
            List<CompleteDeliveryAdCodeMapping> mappings = completeDeliveryKey2List.get(deliveryAlertEntity.getId());
            if(CollectionUtils.isEmpty(mappings)){
                continue;
            }
            // 获取adCodeList
            List<String> deliveryAlertAdCodeList = mappings.stream().map(CompleteDeliveryAdCodeMapping::getAdCode).collect(Collectors.toList());
            List<AreaAdCode> areaAdCodes = new ArrayList<>();
            for (String deliveryAlertAdCode : deliveryAlertAdCodeList) {
                List<AdCodeMsgDTO> adCodeMsgDTOList = adCodeEffective2Map.get(deliveryAlertAdCode);
                if(CollectionUtils.isEmpty(adCodeMsgDTOList)){
                    continue;
                }
                AdCodeMsgDTO adCodeMsgDTO = adCodeMsgDTOList.get(0);
                AreaAdCode areaAdCode = new AreaAdCode();

                areaAdCode.setCompleteDeliveryId(deliveryAlertEntity.getId());
                areaAdCode.setCity(adCodeMsgDTO.getCity());
                areaAdCode.setArea(adCodeMsgDTO.getArea());
                areaAdCode.setAdCode(adCodeMsgDTO.getAdCode());
                areaAdCodes.add(areaAdCode);
            }
            deliveryAlertEntity.setAreaAdCodes(areaAdCodes);
        }
        return deliveryAlertEntityList;
    }

    @Override
    public List<Integer> queryAlertStoreList() {
        List<Integer> storeNos = new ArrayList<>();
        List<CompleteDelivery> completeDeliveries = completeDeliveryMapper.selectList(new LambdaQueryWrapper<CompleteDelivery>().select(CompleteDelivery::getStoreNo).eq(CompleteDelivery::getStatus, DeliveryAlertEnums.Status.NORMAL).groupBy(CompleteDelivery::getStoreNo));
        List<TmsCompleteDeliveryRule> deliveryRules = tmsCompleteDeliveryRuleMapper.selectList(new LambdaQueryWrapper<TmsCompleteDeliveryRule>().select(TmsCompleteDeliveryRule::getStoreNo).groupBy(TmsCompleteDeliveryRule::getStoreNo));
        storeNos.addAll(completeDeliveries.stream().map(CompleteDelivery::getStoreNo).collect(Collectors.toList()));
        storeNos.addAll(deliveryRules.stream().map(TmsCompleteDeliveryRule::getStoreNo).collect(Collectors.toList()));
        return storeNos;
    }
}
