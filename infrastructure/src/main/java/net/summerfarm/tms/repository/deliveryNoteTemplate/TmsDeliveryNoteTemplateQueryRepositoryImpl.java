package net.summerfarm.tms.repository.deliveryNoteTemplate;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongConverter;
import net.summerfarm.tms.converter.deliveryNoteTemplate.TmsDeliveryNoteTemplateConverter;
import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplate;
import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateQueryParam;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateQueryRepository;
import net.summerfarm.tms.mapper.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongMapper;
import net.summerfarm.tms.mapper.deliveryNoteTemplate.TmsDeliveryNoteTemplateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
@Repository
public class TmsDeliveryNoteTemplateQueryRepositoryImpl implements TmsDeliveryNoteTemplateQueryRepository {

    @Autowired
    private TmsDeliveryNoteTemplateMapper tmsDeliveryNoteTemplateMapper;
    @Autowired
    private TmsDeliveryNoteTemplateBelongMapper tmsDeliveryNoteTemplateBelongMapper;


    @Override
    public PageInfo<TmsDeliveryNoteTemplateEntity> getPage(TmsDeliveryNoteTemplateQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<TmsDeliveryNoteTemplateEntity> entities = tmsDeliveryNoteTemplateMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public TmsDeliveryNoteTemplateEntity selectById(Long id) {
        return TmsDeliveryNoteTemplateConverter.toTmsDeliveryNoteTemplateEntity(tmsDeliveryNoteTemplateMapper.selectById(id));
    }


    @Override
    public List<TmsDeliveryNoteTemplateEntity> selectByCondition(TmsDeliveryNoteTemplateQueryParam param) {
        return TmsDeliveryNoteTemplateConverter.toTmsDeliveryNoteTemplateEntityList(tmsDeliveryNoteTemplateMapper.selectByCondition(param));
    }

    @Override
    public List<TmsDeliveryNoteTemplateEntity> queryWithBelong(TmsDeliveryNoteTemplateQueryParam param) {
        if(param == null){
            return null;
        }
        List<TmsDeliveryNoteTemplate> tmsDeliveryNoteTemplates = tmsDeliveryNoteTemplateMapper.selectList(new LambdaQueryWrapper<TmsDeliveryNoteTemplate>()
                .eq(param.getUseState() != null, TmsDeliveryNoteTemplate::getUseState, param.getUseState()));
        if(CollectionUtils.isEmpty(tmsDeliveryNoteTemplates)){
            return Collections.emptyList();
        }

        List<Long> templateIdList = tmsDeliveryNoteTemplates.stream().map(TmsDeliveryNoteTemplate::getId).collect(Collectors.toList());
        List<TmsDeliveryNoteTemplateBelong> tmsDeliveryNoteTemplateBelongList = tmsDeliveryNoteTemplateBelongMapper.selectList(new LambdaQueryWrapper<TmsDeliveryNoteTemplateBelong>()
                .in(TmsDeliveryNoteTemplateBelong::getDeliveryNoteTemplateId, templateIdList));

        List<TmsDeliveryNoteTemplateEntity> tmsDeliveryNoteTemplateEntities = tmsDeliveryNoteTemplates.stream()
                .map(TmsDeliveryNoteTemplateConverter::toTmsDeliveryNoteTemplateEntity)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(tmsDeliveryNoteTemplateBelongList)){
            return tmsDeliveryNoteTemplateEntities;
        }

        Map<Long, List<TmsDeliveryNoteTemplateBelong>> templateId2ListMap = tmsDeliveryNoteTemplateBelongList
                .stream()
                .collect(Collectors.groupingBy(TmsDeliveryNoteTemplateBelong::getDeliveryNoteTemplateId));

        for (TmsDeliveryNoteTemplateEntity tmsDeliveryNoteTemplateEntity : tmsDeliveryNoteTemplateEntities) {
            List<TmsDeliveryNoteTemplateBelong> belongs = templateId2ListMap.get(tmsDeliveryNoteTemplateEntity.getId());
            if(CollectionUtils.isEmpty(belongs)){
                continue;
            }

            tmsDeliveryNoteTemplateEntity.setTmsDeliveryNoteTemplateBelongEntities(belongs.stream()
                    .map(TmsDeliveryNoteTemplateBelongConverter::toTmsDeliveryNoteTemplateBelongEntity)
                    .collect(Collectors.toList()));
        }

        return tmsDeliveryNoteTemplateEntities;
    }
}