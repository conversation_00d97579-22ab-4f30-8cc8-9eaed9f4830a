package net.summerfarm.tms.repository;

import akka.stream.impl.fusing.Collect;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.tms.base.carrier.CarrierRepository;
import net.summerfarm.tms.base.carrier.entity.CarrierAccountEntity;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.driver.enums.BusinessTypeEnum;
import net.summerfarm.tms.converter.CarrierAccountConverter;
import net.summerfarm.tms.converter.CarrierConverter;
import net.summerfarm.tms.converter.CarrierInvoiceConverter;
import net.summerfarm.tms.dao.Carrier;
import net.summerfarm.tms.dao.CarrierAccount;
import net.summerfarm.tms.dao.CarrierInvoice;
import net.summerfarm.tms.dao.FinanceInvoicePartner;
import net.summerfarm.tms.mapper.CarrierAccountMapper;
import net.summerfarm.tms.mapper.CarrierInvoiceMapper;
import net.summerfarm.tms.mapper.CarrierMapper;
import net.summerfarm.tms.mapper.FinanceInvoicePartnerMapper;
import net.summerfarm.tms.query.base.carrier.CarrierQuery;
import net.summerfarm.tms.utils.PageInfoHelper;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/7/26 13:36<br/>
 *
 * <AUTHOR> />
 */
@Service
public class CarrierRepositoryImpl implements CarrierRepository {

    @Resource
    private CarrierMapper carrierMapper;
    @Resource
    private CarrierInvoiceMapper carrierInvoiceMapper;
    @Resource
    private CarrierAccountMapper carrierAccountMapper;
    @Resource
    private FinanceInvoicePartnerMapper financeInvoicePartnerMapper;

    @Override
    public String getNameById(Long carrierId) {
        if (carrierId == null) {
            return null;
        }
        Carrier carrier = carrierMapper.selectByPrimaryKey(carrierId);
        return carrier.getCarrierName();
    }

    @Override
    public List<CarrierEntity> searchByName(String name) {
        List<Carrier> carriers = carrierMapper.selectList(new LambdaQueryWrapper<Carrier>()
                .in(Carrier::getBusinessType, Arrays.asList(BusinessTypeEnum.trunk.getCode(), BusinessTypeEnum.trunkAndCity.getCode()))
                .like(Carrier::getCarrierName, name));
        return carriers.stream().map(CarrierConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<CarrierEntity> getListByIdList(Collection<Long> idsList) {
        if (CollectionUtils.isEmpty(idsList)) {
            return Lists.newArrayList();
        }
        List<Carrier> carriers = carrierMapper.selectBatchIds(idsList);
        return carriers.stream().map(CarrierConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<CarrierEntity> queryPrimaryIdList(List<Long> carrierIdList) {
        if(CollectionUtils.isEmpty(carrierIdList)){
            return Collections.emptyList();
        }
        List<Carrier> carriers = carrierMapper.selectList(new LambdaQueryWrapper<Carrier>()
                .in(Carrier::getId, carrierIdList)
        );
        return carriers.stream().map(CarrierConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public PageInfo<CarrierEntity> queryPageList(CarrierQuery carrierQuery) {
        PageHelper.startPage(carrierQuery.getPageIndex(), carrierQuery.getPageSize());
        List<Carrier> carriers = carrierMapper.selectList(new LambdaQueryWrapper<Carrier>()
                .eq(StringUtils.isNotBlank(carrierQuery.getCarrierName()), Carrier::getCarrierName, carrierQuery.getCarrierName())
                .eq(carrierQuery.getId() != null,Carrier::getId,carrierQuery.getId())
                .orderByDesc(Carrier::getId)
        );
        PageInfo pageInfo = PageInfoHelper.createPageInfo(carriers);

        pageInfo.setList(carriers.stream().map(CarrierConverter::do2Entity).collect(Collectors.toList()));
        return pageInfo;
    }

    @Override
    public CarrierEntity queryById(Long id) {
        return CarrierConverter.do2Entity(carrierMapper.selectById(id));
    }

    @Override
    public CarrierEntity queryWithAccountInvoiceById(Long id) {
        //查询承运商信息
        Carrier carrier = carrierMapper.selectById(id);
        if(carrier == null){
            return null;
        }
        CarrierEntity carrierEntity = CarrierConverter.do2Entity(carrier);
        //查询承运商账户信息
        List<CarrierAccount> carrierAccounts = carrierAccountMapper.selectList(new LambdaQueryWrapper<CarrierAccount>()
                .eq(CarrierAccount::getCarrierId, carrier.getId()));
        //查询承运商发票信息
        CarrierInvoice carrierInvoice = carrierInvoiceMapper.selectOne(new LambdaQueryWrapper<CarrierInvoice>()
                .eq(CarrierInvoice::getCarrierId, carrier.getId())
                .orderByDesc(CarrierInvoice::getId)
                .last("limit 1"));

        carrierEntity.setCarrierAccountEntityList(carrierAccounts.stream().map(CarrierAccountConverter::do2Entity).collect(Collectors.toList()));
        carrierEntity.setCarrierInvoiceEntity(CarrierInvoiceConverter.do2Entity(carrierInvoice));

        return carrierEntity;
    }

    @Override
    public void update(CarrierEntity entity) {
        Carrier carrier = CarrierConverter.entity2Do(entity);
        if(carrier == null){
            return;
        }
        int updateNum = carrierMapper.updateById(carrier);

        if(updateNum > 0 && entity.getCarrierInvoiceEntity() != null){
            CarrierInvoice carrierInvoice = CarrierInvoiceConverter.entity2Do(entity.getCarrierInvoiceEntity());
            //先删除后新增
            carrierInvoiceMapper.delete(new LambdaQueryWrapper<CarrierInvoice>().eq(CarrierInvoice::getCarrierId,carrier.getId()));
            carrierInvoice.setCarrierId(carrier.getId());
            carrierInvoiceMapper.insert(carrierInvoice);
        }

        if(updateNum > 0 && !CollectionUtils.isEmpty(entity.getCarrierAccountEntityList())){
            List<CarrierAccount> carrierAccounts = entity.getCarrierAccountEntityList().stream().map(CarrierAccountConverter::entity2Do).collect(Collectors.toList());
            carrierAccountMapper.delete(new LambdaQueryWrapper<CarrierAccount>().eq(CarrierAccount::getCarrierId, carrier.getId()));
            for (CarrierAccount carrierAccount : carrierAccounts) {
                carrierAccount.setCarrierId(carrier.getId());
                carrierAccountMapper.insert(carrierAccount);
            }
        }
    }

    @Override
    public long queryCountByName(String carrierName) {
        return carrierMapper.selectCount(new LambdaQueryWrapper<Carrier>().eq(Carrier::getCarrierName,carrierName));
    }

    @Override
    public void save(CarrierEntity carrierEntity) {
        Carrier carrier = CarrierConverter.entity2Do(carrierEntity);
        carrierMapper.insert(carrier);

        if(carrierEntity.getCarrierInvoiceEntity() != null){
            CarrierInvoice carrierInvoice = CarrierInvoiceConverter.entity2Do(carrierEntity.getCarrierInvoiceEntity());
            carrierInvoice.setCarrierId(carrier.getId());
            carrierInvoiceMapper.insert(carrierInvoice);
        }

        if(!CollectionUtils.isEmpty(carrierEntity.getCarrierAccountEntityList())){
            List<CarrierAccount> carrierAccounts = carrierEntity.getCarrierAccountEntityList().stream().map(CarrierAccountConverter::entity2Do).collect(Collectors.toList());
            for (CarrierAccount carrierAccount : carrierAccounts) {
                carrierAccount.setCarrierId(carrier.getId());
                carrierAccountMapper.insert(carrierAccount);
            }
        }
        UserBase user = UserInfoHolder.getUser();

        FinanceInvoicePartner financeInvoicePartner = new FinanceInvoicePartner();
        financeInvoicePartner.setSupplierId(carrier.getId().intValue());
        financeInvoicePartner.setSupplierType(1);
        financeInvoicePartner.setCreator(user.getNickname());
        financeInvoicePartner.setCreatorAdminId(user.getBizUserId());
        financeInvoicePartner.setCreateTime(LocalDateTime.now());
        financeInvoicePartnerMapper.insert(financeInvoicePartner);
    }

    @Override
    public List<CarrierEntity> queryList(CarrierQuery carrierQuery) {
        List<Carrier> carriers = carrierMapper.selectList(new LambdaQueryWrapper<Carrier>()
                .like(StringUtils.isNotBlank(carrierQuery.getCarrierName()), Carrier::getCarrierName, carrierQuery.getCarrierName())
                .in(CollectionUtils.isNotEmpty(carrierQuery.getBusinessTypes()),Carrier::getBusinessType,carrierQuery.getBusinessTypes())
                .like(StringUtils.isNotBlank(carrierQuery.getSubBusinessType()), Carrier::getSubBusinessType, carrierQuery.getSubBusinessType())
                .in(CollectionUtils.isNotEmpty(carrierQuery.getIdList()),Carrier::getId,carrierQuery.getIdList())
                .in(CollectionUtils.isNotEmpty(carrierQuery.getCarrierNameList()),Carrier::getCarrierName,carrierQuery.getCarrierNameList())
        );

        return carriers.stream().map(CarrierConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<CarrierEntity> queryListWithInvoiceAccount(CarrierQuery carrierQuery) {
        List<Carrier> carriers = carrierMapper.selectList(new LambdaQueryWrapper<Carrier>()
                .like(StringUtils.isNotBlank(carrierQuery.getCarrierName()), Carrier::getCarrierName, carrierQuery.getCarrierName())
                .eq(carrierQuery.getId() != null,Carrier::getId,carrierQuery.getId())
                .orderByDesc(Carrier::getId)
        );

        if(CollectionUtils.isEmpty(carriers)){
           return Collections.emptyList();
        }
        List<Long> carrierIdList = carriers.stream().map(Carrier::getId).collect(Collectors.toList());

        //查询承运商账户信息
        List<CarrierAccount> carrierAccounts = carrierAccountMapper.selectList(new LambdaQueryWrapper<CarrierAccount>()
                .in(CarrierAccount::getCarrierId, carrierIdList));
        //查询承运商发票信息
        List<CarrierInvoice> carrierInvoices = carrierInvoiceMapper.selectList(new LambdaQueryWrapper<CarrierInvoice>()
                .in(CarrierInvoice::getCarrierId, carrierIdList));

        Map<Long, List<CarrierAccount>> carrierIdAccountListMap = carrierAccounts.stream().collect(Collectors.groupingBy(CarrierAccount::getCarrierId));
        Map<Long, List<CarrierInvoice>> carrierIdInvoiceListMap = carrierInvoices.stream().collect(Collectors.groupingBy(CarrierInvoice::getCarrierId));

        List<CarrierEntity> carrierEntityList = carriers.stream().map(CarrierConverter::do2Entity).collect(Collectors.toList());
        for (CarrierEntity carrierEntity : carrierEntityList) {
            if(!CollectionUtils.isEmpty(carrierIdAccountListMap.get(carrierEntity.getId()))){
                carrierEntity.setCarrierAccountEntityList(carrierIdAccountListMap.get(carrierEntity.getId()).stream().map(CarrierAccountConverter::do2Entity).collect(Collectors.toList()));
            }
            if(!CollectionUtils.isEmpty(carrierIdInvoiceListMap.get(carrierEntity.getId()))){
                carrierEntity.setCarrierInvoiceEntity(CarrierInvoiceConverter.do2Entity(carrierIdInvoiceListMap.get(carrierEntity.getId()).get(0)));
            }
        }

        return carrierEntityList;
    }
}
