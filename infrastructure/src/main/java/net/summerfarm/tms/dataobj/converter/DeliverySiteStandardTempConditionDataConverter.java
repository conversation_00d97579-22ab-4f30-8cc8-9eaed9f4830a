package net.summerfarm.tms.dataobj.converter;

import net.summerfarm.tms.dataobj.DeliverySiteStandardTempConditionDataObj;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempCondition;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempUnit;
import net.summerfarm.tms.enums.TmsTemperatureEnum;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 站点标品温度条件转换类<br/>
 * date: 2024/9/3 11:36<br/>
 *
 * <AUTHOR> />
 */
public class DeliverySiteStandardTempConditionDataConverter {

    public static List<DeliverySiteStandardTempCondition> objList2DTO(List<DeliverySiteStandardTempConditionDataObj> deliverySiteStandardTempConditions) {
        if(CollectionUtils.isEmpty(deliverySiteStandardTempConditions)){
            return Collections.emptyList();
        }
        Map<Long, List<DeliverySiteStandardTempConditionDataObj>> deliverySiteId2DataObjMap = deliverySiteStandardTempConditions.stream().collect(Collectors.groupingBy(DeliverySiteStandardTempConditionDataObj::getDeliverySiteId));

        return deliverySiteId2DataObjMap.keySet().stream()
                .map(deliverySiteId -> Optional.ofNullable(deliverySiteId2DataObjMap.get(deliverySiteId))
                        .filter(a -> !CollectionUtils.isEmpty(a))
                        .map(standardTempDataObjs -> {
                            int coldNum = standardTempDataObjs.stream()
                                    .filter(e -> Objects.equals(e.getTemperature(), TmsTemperatureEnum.COLD.getCode()))
                                    .mapToInt(DeliverySiteStandardTempConditionDataObj::getCount).sum();

                            int freezeNum = standardTempDataObjs.stream()
                                    .filter(e -> Objects.equals(e.getTemperature(), TmsTemperatureEnum.FREEZE.getCode()))
                                    .mapToInt(DeliverySiteStandardTempConditionDataObj::getCount).sum();

                            StringJoiner sj = new StringJoiner("|");
                            if (coldNum > 0) {
                                sj.add(TmsTemperatureEnum.COLD.getName());
                            }
                            if (freezeNum > 0) {
                                sj.add(TmsTemperatureEnum.FREEZE.getName());
                            }

                            DeliverySiteStandardTempCondition deliverySiteStandardTempCondition = new DeliverySiteStandardTempCondition();
                            deliverySiteStandardTempCondition.setDeliverySiteId(deliverySiteId);
                            deliverySiteStandardTempCondition.setStandardTempCondition(sj.toString());
                            deliverySiteStandardTempCondition.setNonFruitColdNum(coldNum);
                            deliverySiteStandardTempCondition.setNonFruitFreezeNum(freezeNum);

                            return deliverySiteStandardTempCondition;
                        }))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }


    public static List<DeliverySiteStandardTempUnit> objList2DTOForUnit(List<DeliverySiteStandardTempConditionDataObj> deliverySiteStandardTempConditions) {
        if (CollectionUtils.isEmpty(deliverySiteStandardTempConditions)) {
            return Collections.emptyList();
        }

        List<DeliverySiteStandardTempUnit> result = new ArrayList<>();
        for (DeliverySiteStandardTempConditionDataObj obj : deliverySiteStandardTempConditions) {
            DeliverySiteStandardTempUnit unit = new DeliverySiteStandardTempUnit();
            unit.setDeliverySiteId(obj.getDeliverySiteId());
            unit.setTemperature(obj.getTemperature());
            unit.setUnit(obj.getUnit());
            unit.setCount(obj.getCount());
            result.add(unit);
        }

        return result;
    }

}
