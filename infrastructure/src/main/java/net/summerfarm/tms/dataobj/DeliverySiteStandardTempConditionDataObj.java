package net.summerfarm.tms.dataobj;

import lombok.Data;

/**
 * Description: 站点标品存储条件<br/>
 * date: 2024/9/2 17:05<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteStandardTempConditionDataObj {

    /**
     * 配送站点ID
     */
    private Long deliverySiteId;

    /**
     * 标品存储条件
     */
    private Integer temperature;

    /**
     * 标品单位
     */
    private String unit;

    /**
     * 数量
     */
    private Integer count;

}
