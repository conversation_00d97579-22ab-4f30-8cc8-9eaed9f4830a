package net.summerfarm.tms.dao.performance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 履约审核申诉
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-243 14:40:50
 */
@Getter
@Setter
@TableName("tms_delivery_performance_review_detail_appeal")
public class TmsDeliveryPerformanceReviewDetailAppeal implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 履约审核任务ID
     */
    @TableField("performance_review_task_id")
    private Long performanceReviewTaskId;

    /**
     * 履约审核详情ID
     */
    @TableField("del_perf_detail_review_id")
    private Long delPerfDetailReviewId;

    /**
     * 配送批次ID
     */
    @TableField("delivery_batch_id")
    private Long deliveryBatchId;

    /**
     * 状态0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 城配仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 城配仓名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 申诉失败原因
     */
    @TableField("appeal_fail_reason")
    private String appealFailReason;

}
