package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 完成配送提醒规则项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-79 13:34:17
 */
@Getter
@Setter
@TableName("tms_complete_delivery_rule_item")
public class TmsCompleteDeliveryRuleItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private Long id;

    /**
     * 规则ID
     */
    @TableField("rule_id")
    private Long ruleId;

    /**
     * 配送仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 渠道，0：鲜沐，1：SAAS
     */
    @TableField("`channel`")
    private Integer channel;

    /**
     * 类型，0：品牌，1：门店
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 对应类型业务编号
     */
    @TableField("biz_no")
    private String bizNo;

    /**
     * 对应类型业务名称
     */
    @TableField("biz_name")
    private String bizName;

    /**
     * 配送提醒开始时间
     */
    @TableField("begin_time")
    private LocalTime beginTime;

    /**
     * 配送提醒结束时间
     */
    @TableField("end_time")
    private LocalTime endTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
