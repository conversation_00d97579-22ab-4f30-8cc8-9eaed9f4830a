package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * complete_delivery_customer完成配送特殊客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-79 17:00:35
 */
@Getter
@Setter
@TableName("complete_delivery_customer")
public class CompleteDeliveryCustomer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 完成配送提醒表id
     */
    @TableField("complete_delivery_id")
    private Integer completeDeliveryId;

    /**
     * 大客户id
     */
    @TableField("admin_id")
    private Integer adminId;

    /**
     * 大客户名称备注
     */
    @TableField("name_remakes")
    private String nameRemakes;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;


}
