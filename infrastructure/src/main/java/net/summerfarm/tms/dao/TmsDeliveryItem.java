package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 配送单详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-256 17:55:54
 */
@Getter
@Setter
@TableName("tms_delivery_item")
public class TmsDeliveryItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 配送单ID
     */
    @TableField("delivery_order_id")
    private Long deliveryOrderId;

    /**
     * 委托单id
     */
    @TableField("dist_order_id")
    private Long distOrderId;

    /**
     * 外部订单号
     */
    @TableField("out_order_id")
    private String outOrderId;

    /**
     * 外部条目id
     */
    @TableField("out_item_id")
    private String outItemId;

    /**
     * 计划签收数量
     */
    @TableField("plan_receipt_count")
    private Integer planReceiptCount;

    /**
     * 实际签收数量
     */
    @TableField("real_receipt_count")
    private Integer realReceiptCount;

    /**
     * 缺货数量
     */
    @TableField("short_count")
    private Integer shortCount;

    /**
     * 拦截数量
     */
    @TableField("intercept_count")
    private Integer interceptCount;

    /**
     * 拒收数量
     */
    @TableField("reject_count")
    private Integer rejectCount;

    /**
     * 拒收原因
     */
    @TableField("reject_remark")
    private String rejectRemark;

    /**
     * 扫码数量
     */
    @TableField("scan_count")
    private Integer scanCount;

    /**
     * 无码数量
     */
    @TableField("noscan_count")
    private Integer noscanCount;

    /**
     * 无码原因
     */
    @TableField("noscan_reason")
    private String noscanReason;

    /**
     * 无码货物照片
     */
    @TableField("noscan_pics")
    private String noscanPics;

    /**
     * 商品名称
     */
    @TableField("out_item_name")
    private String outItemName;
    /**
     * 包装类型：0单品 1包裹
     */
    @TableField("pack_type")
    private Integer packType;

    /**
     * 温区,1:常温,2:冷藏,3:冷冻
     */
    @TableField("temperature")
    private Integer temperature;

}
