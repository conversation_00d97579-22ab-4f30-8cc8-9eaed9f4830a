package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送点位物品回收表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-353 11:25:14
 */
@Getter
@Setter
@TableName("tms_delivery_site_item_recycle")
public class TmsDeliverySiteItemRecycle implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配送点位物品ID
     */
    @TableField("delivery_site_item_id")
    private Long deliverySiteItemId;

    /**
     * 配送点位ID
     */
    @TableField("delivery_site_id")
    private Long deliverySiteId;

    /**
     * 外部货品ID(sku)
     */
    @TableField("out_item_id")
    private String outItemId;

    /**
     * 回收照片
     */
    @TableField("recycle_pics")
    private String recyclePics;

    /**
     * 规格单位数量
     */
    @TableField("specification_quantity")
    private BigDecimal specificationQuantity;

    /**
     * 规格单位
     */
    @TableField("specification_unit")
    private String specificationUnit;

    /**
     * 基础规格单位数量(小规格单位)
     */
    @TableField("basic_spec_quantity")
    private BigDecimal basicSpecQuantity;

    /**
     * 基础规格单位(小规格单位)
     */
    @TableField("basic_spec_unit")
    private String basicSpecUnit;

    /**
     * 回收原因类型，1：回收数量不符，2：商品已损坏，3：店铺未开门下次回收，4：包装破损，5：客户已用不退了，6：其它
     */
    @TableField("reason_type")
    private Integer reasonType;

    /**
     * 异常说明备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


    /**
     * 温区,1:常温,2:冷藏,3:冷冻
     */
    @TableField("temperature")
    private Integer temperature;
}
