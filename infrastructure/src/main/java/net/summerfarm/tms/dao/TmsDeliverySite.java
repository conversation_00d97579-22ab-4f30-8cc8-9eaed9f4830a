package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 运输单表 点位维度, 路线每个站点的情况
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-301 18:29:56
 */
@Getter
@Setter
@TableName("tms_delivery_site")
public class TmsDeliverySite implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 批次ID
     */
    @TableField(value = "delivery_batch_id")
    private Long deliveryBatchId;

    /**
     * 点位ID
     */
    @TableField("site_id")
    private Long siteId;

    /**
     * 类型 1起点,2途径点,3终点
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 点位在路线上的顺序
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 10未到站,20已到站,30已出发
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 计划到站时间
     */
    @TableField("plan_arrive_time")
    private LocalDateTime planArriveTime;

    /**
     * 实际到达时间
     */
    @TableField("sign_in_time")
    private LocalDateTime signInTime;

    /**
     * 实际到达地点
     */
    @TableField("sign_in_poi")
    private String signInPoi;

    /**
     * 实际打卡和预计到达时间的差值
     */
    @TableField("sign_in_diff_minute")
    private Integer signInDiffMinute;

    /**
     * 实际打卡地和点位的差值
     */
    @TableField("sign_in_diff_km")
    private BigDecimal signInDiffKm;

    /**
     * 签到打卡照片
     */
    @TableField("sign_in_pics")
    private String signInPics;

    /**
     * 签到备注
     */
    @TableField("sign_in_remark")
    private String signInRemark;

    /**
     * 超出距离备注
     */
    @TableField("out_reason")
    private String outReason;

    /**
     * 是否超出距离 0 正常 1超出
     */
    @TableField("out_distance")
    private Integer outDistance;

    /**
     * 出发打卡时间
     */
    @TableField("sign_out_time")
    private LocalDateTime signOutTime;

    /**
     * 实际出发打卡点和点位的差值 km
     */
    @TableField("sign_out_diff_km")
    private BigDecimal signOutDiffKm;

    /**
     * 出发打卡拍照
     */
    @TableField("sign_out_pics")
    private String signOutPics;

    /**
     * 出发打卡备注
     */
    @TableField("sign_out_remark")
    private String signOutRemark;

    /**
     * 出发温度
     */
    @TableField("sign_out_temperature")
    private BigDecimal signOutTemperature;

    /**
     * 状态，0：正常，1：部分拦截，2：全部拦截
     */
    @TableField("intercept_state")
    private Integer interceptState;

    /**
     * 到仓距离
     */
    @TableField("distance")
    private BigDecimal distance;

    /**
     * 外部客户名
     */
    @TableField("outer_client_name")
    private String outerClientName;

    /**
     * 签到打卡距离
     */
    @TableField("sign_in_distance")
    private BigDecimal signInDistance;

    /**
     * 是否正常签收，0：正常，1：不正常
     */
    @TableField("sign_in_status")
    private Integer signInStatus;

    /**
     * 外部客户号
     */
    @TableField("outer_client_id")
    private String outerClientId;

    /**
     * 签发状态
     */
    @TableField("sign_out_status")
    private Integer signOutStatus;

    /**
     * 签到异常类型
     */
    @TableField("sign_in_err_type")
    private String signInErrType;
    /**
     * 出发异常类型
     */
    @TableField("sign_out_err_type")
    private String signOutErrType;
    /**
     * 签到地址
     */
    @TableField("sign_in_address")
    private String signInAddress;
    /**
     * 实际出发点位
     */
    @TableField("sign_out_poi")
    private String signOutPoi;
    /**
     * 实际出发点位地址
     */
    @TableField("sign_out_address")
    private String signOutAddress;
    /**
     * 配送方式 0正常配送 1专车配送
     */
    @TableField("send_way")
    private Integer sendWay;

    /**
     * 智能排线顺序
     */
    @TableField("intelligence_sequence")
    private Integer intelligenceSequence;

    /**
     * 配送照片签收面单
     */
    @TableField("sign_in_sign_pic")
    private String signInSignPic;

    /**
     * 配送照片货物照片
     */
    @TableField("sign_in_product_pic")
    private String signInProductPic;

    /**
     * 超区异常打卡原因类型
     */
    @TableField("out_reason_type")
    private String outReasonType;

    /**
     * 超区异常打卡图片
     */
    @TableField("out_pic")
    private String outPic;

    /**
     * 是否发送过未入仓消息提示
     */
    @TableField("arrive_msg_send")
    private Boolean arriveMsgSend;

    /**
     * 外部品牌号
     */
    @TableField("outer_brand_id")
    private String outerBrandId;

    /**
     * 外部品牌名
     */
    @TableField("outer_brand_name")
    private String outerBrandName;

    /**
     * 计划出站时间
     */
    @TableField("plan_out_time")
    private LocalDateTime planOutTime;

    /**
     * 实际打卡出仓和预计出仓时间的差值
     */
    @TableField("sign_out_diff_minute")
    private Integer signOutDiffMinute;

    /**
     * 是否发送过未出仓消息提示
     */
    @TableField("leave_msg_send")
    private Boolean leaveMsgSend;

    /**
     * 配送备注
     */
    @TableField("send_remark")
    private String sendRemark;


    /**
     * 属性照片字段，json对象格式
     *
     * @see net.summerfarm.tms.jsonobject.DeliverySitePropertyJson
     */
    @TableField("property_json")
    private String propertyJson;

    /**
     * 配送存储条件
     */
    @TableField("temperature_conditions")
    private String temperatureConditions;

    /**
     * 蚁群算法顺序
     */
    @TableField("ant_sequence")
    private Integer antSequence;

    /**
     * 区域id
     */
    @TableField("ad_code_msg_id")
    private Integer adCodeMsgId;
}
