package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 履约审核详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-179 16:03:34
 */
@Getter
@Setter
@TableName("tms_delivery_performance_review_detail")
public class TmsDeliveryPerformanceReviewDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 履约审核任务ID
     */
    @TableField("performance_review_task_id")
    private Long performanceReviewTaskId;

    /**
     * 状态 0待审核 1无法审核 2合规 3不合规
     */
    @TableField("state")
    private Integer state;

    /**
     * 照片信息
     */
    @TableField("pic_json")
    private String picJson;

    /**
     * 配送/履约日期
     */
    @TableField("delivery_time")
    private LocalDateTime deliveryTime;

    /**
     * 司机名称
     */
    @TableField("driver_name")
    private String driverName;

    /**
     * 司机电话
     */
    @TableField("driver_phone")
    private String driverPhone;

    /**
     * 车牌号
     */
    @TableField("car_number")
    private String carNumber;

    /**
     * 车型0:小面包车 1:中面包车 2:依维柯 3:小型货车 4: 4米2 5:6米8  6:7米6  7:7 9米6  8 :13米5 9:17米5
     */
    @TableField("car_type")
    private Integer carType;

    /**
     * 车辆存储条件 0常温、1冷藏
     */
    @TableField("car_storage")
    private Integer carStorage;

    /**
     * 配送批次ID
     */
    @TableField("delivery_batch_id")
    private Long deliveryBatchId;

    /**
     * 配送点id
     */
    @TableField("delivery_site_id")
    private Long deliverySiteId;

    /**
     * 点位id
     */
    @TableField("site_id")
    private Long siteId;

    /**
     * 详情地址
     */
    @TableField("site_address")
    private String siteAddress;

    /**
     * 外部客户名
     */
    @TableField("outer_client_name")
    private String outerClientName;

    /**
     * 外部品牌名
     */
    @TableField("outer_brand_name")
    private String outerBrandName;

    /**
     * 开始点位Id
     */
    @TableField("begin_site_id")
    private Long beginSiteId;

    /**
     * 开始点位名称
     */
    @TableField("begin_site_name")
    private String beginSiteName;

    /**
     * 线路编码
     */
    @TableField("path_code")
    private String pathCode;

    /**
     * 线路名称
     */
    @TableField("path_name")
    private String pathName;

    /**
     * 批次类型 -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    @TableField("delivery_batch_type")
    private Integer deliveryBatchType;

    /**
     * 配送方式 0正常配送 1专车配送
     */
    @TableField("send_way")
    private Integer sendWay;

    /**
     * 是否超出距离   0 正常 1超出
     */
    @TableField("out_distance")
    private Integer outDistance;

    /**
     * 判罚金额
     */
    @TableField("penalty_money")
    private BigDecimal penaltyMoney;

    /**
     * 点位照片原因

     * 城配出仓 装载照片
     * 1000 保温措施不合格
     * 1001 无装车照片
     * 1002 无保温被
     * 1003 无冰板
     * 1004 无保温箱、干冰
     * 1005 冷藏数量不符
     * 1006 冷冻数量不符
     * 1007 干冰数量不符
     * 1008 冰板未完全复冻
     * 1009 蛋糕摆放不规范
     * 1010 冰板材料不合格
     * 1011 装车照片拍照不清晰
     * 1012 保温被未盖住货物
     * 1013 冰板数量不充足

城配出仓 车牌照片
1200 车牌不符
1201 无车牌照

城配-签收 门店抬头
2000 门头不符
2001 无门头照
2002 门头不清晰

城配-签收 门店抬头
2201 签收单不合格
2202 无签收单照片
城配-签收 货物照片

2401 未做保温措施
2402 未测温

干线-签收 到仓-签收照片
3000 无车辆温度照片
3001 其他

干线-签收 出仓-装载照片
3200 无整体货物照片

干线-签收 出仓-封签照片
3400 无封签照片
     */
    @TableField("site_pic_reason")
    private String sitePicReason;

    /**
     * 温区条件
     */
    @TableField("temperature_conditions")
    private String temperatureConditions;

    /**
     * 出发温度
     */
    @TableField("sign_out_temperature")
    private BigDecimal signOutTemperature;

    /**
     * 出发温度
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 点位名称
     */
    @TableField("site_name")
    private String siteName;

    /**
     * 签到备注
     */
    @TableField("sign_in_remark")
    private String signInRemark;

    /**
     * 是否正常签收，0：正常，1：不正常
     */
    @TableField("sign_in_status")
    private Integer signInStatus;

    /**
     * 非水果冷藏数量
     */
    @TableField("non_fruit_cold_num")
    private Integer nonFruitColdNum;

    /**
     * 非水果冷冻数量
     */
    @TableField("non_fruit_freeze_num")
    private Integer nonFruitFreezeNum;

}
