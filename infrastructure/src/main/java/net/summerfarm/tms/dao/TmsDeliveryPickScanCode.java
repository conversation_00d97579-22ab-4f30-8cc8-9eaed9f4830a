package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 拣货扫码表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-226 15:18:20
 */
@Getter
@Setter
@TableName("tms_delivery_pick_scan_code")
public class TmsDeliveryPickScanCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 拣货任务ID
     */
    @TableField("pick_id")
    private Long pickId;

    /**
     * 配送批次id
     */
    @TableField("delivery_batch_id")
    private Long deliveryBatchId;

    /**
     * 点位ID
     */
    @TableField("site_id")
    private Long siteId;

    /**
     * 外部条目id
     */
    @TableField("out_item_id")
    private String outItemId;

    /**
     * 货品描述
     */
    @TableField("item_desc")
    private String itemDesc;

    /**
     * 唯一码
     */
    @TableField("only_code")
    private String onlyCode;


}
