package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 承运商账户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-285 15:09:05
 */
@Getter
@Setter
@TableName("carrier_account")
public class CarrierAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 承运商id
     */
    @TableField("carrier_id")
    private Long carrierId;

    /**
     * 支付方式 1、银行卡 2、现金
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 账户名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 开户银行
     */
    @TableField("account_bank")
    private String accountBank;

    /**
     * 银行卡归属地
     */
    @TableField("account_ascription")
    private String accountAscription;

    /**
     * 账号
     */
    @TableField("`account`")
    private String account;


}
