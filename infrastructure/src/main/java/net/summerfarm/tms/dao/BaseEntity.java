package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023-05-06
 **/
@Data
public class BaseEntity implements Serializable {
    private static final long serialVersionUID = -5494182198030693080L;

    /**
     * 主键 单号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 更新人ID
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;
}
