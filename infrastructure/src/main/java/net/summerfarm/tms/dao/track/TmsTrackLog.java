package net.summerfarm.tms.dao.track;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 轨迹日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-243 14:40:50
 */
@Getter
@Setter
@TableName("tms_track_log")
public class TmsTrackLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 业务类型 0履约申诉
     */
    @TableField("biz_type")
    private Integer bizType;

    /**
     * 业务ID
     */
    @TableField("biz_no")
    private Long bizNo;

    /**
     * 动作名称
     */
    @TableField("action_name")
    private String actionName;

    /**
     * 操作人
     */
    @TableField("operater")
    private String operater;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


}
