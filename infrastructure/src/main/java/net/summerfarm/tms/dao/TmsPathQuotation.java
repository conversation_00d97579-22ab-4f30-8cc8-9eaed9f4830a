package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

;

/**
 * 路由报价信息
 * <AUTHOR>
 * @Date 2023-05-09
 **/
@Data
@TableName("tms_path_quotation")
public class TmsPathQuotation implements Serializable {
    private static final long serialVersionUID = 2224835410032367469L;
    /**
     * 主键 单号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 路由id
     */
    @TableField("path_id")
    private Long pathId;

    /**
     * 承运商id
     */
    @TableField("carrier_id")
    private Long carrierId;

    /**
     * 车型
     * @see net.summerfarm.tms.enums.CarTypeEnum
     */
    @TableField("car_type")
    private Integer carType;

    /**
     * 存储条件
     * @see net.summerfarm.tms.enums.CarStorageEnum
     */
    @TableField("storage")
    private Integer storage;

    /**
     * 报价费用，单位/元
     */
    @TableField("quotation_fee")
    private BigDecimal quotationFee; 
}
