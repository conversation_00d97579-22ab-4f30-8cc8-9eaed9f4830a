package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 路由车次表
 * <AUTHOR>
 * @Date 2023-05-09
 **/
@Data
@TableName("tms_path_car")
public class TmsPathCar implements Serializable {
    private static final long serialVersionUID = -6635940417506315935L;

    /**
     * 主键 单号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 路由id
     */
    @TableField("path_id")
    private Long pathId;

    /**
     * 调度类型,1-干线，2-城配
     */
    @TableField("dispatch_type")
    private Integer dispatchType;

    /**
     * 班次类型 0-正班，1-加班
     */
    @TableField("shift_type")
    private Integer shiftType;


    /**
     * 开始计算时间
     */
    @TableField("start_time")
    private LocalDate startTime;


    /**
     * 周期类型，0-表示每天，1表示每周
     */
    @TableField("period_type")
    private Integer periodType;

    /**
     * 每周选项下的类型，0-每天，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六，7-周日
     */
    @TableField("every_week_type")
    private String everyWeekType;

    /**
     * 每天选项下，间隔天数
     */
    @TableField("interval_days")
    private Integer intervalDays;

    /**
     * 承运类型，-1前一天，0-当日
     */
    @TableField("carry_type")
    private Integer carryType;

    /**
     * 承运时间,HH:mm
     */
    @TableField("carry_time")
    private String carryTime;

    /**
     * 承运商id
     */
    @TableField("carrier_id")
    private Long carrierId;


    /**
     * 司机id
     */
    @TableField("driver_id")
    private Integer driverId;


    /**
     * 车辆id
     */
    @TableField("car_id")
    private Integer carId;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 配送批次类型，-1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车、6提货用车
     * @see DeliveryBatchTypeEnum
     */
    @TableField("delivery_batch_type")
    private Integer deliveryBatchType;

}
