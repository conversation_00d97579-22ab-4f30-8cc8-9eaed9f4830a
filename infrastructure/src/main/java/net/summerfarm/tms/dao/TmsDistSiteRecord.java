package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送点位变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 17:31:54
 */
@Getter
@Setter
@TableName("tms_dist_site_record")
public class TmsDistSiteRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 点位ID
     */
    @TableField("site_id")
    private Long siteId;

    /**
     * 运输点位ID
     */
    @TableField("delivery_site_id")
    private Long deliverySiteId;

    /**
     * 新省
     */
    @TableField("new_province")
    private String newProvince;

    /**
     * 新市
     */
    @TableField("new_city")
    private String newCity;

    /**
     * 新区
     */
    @TableField("new_area")
    private String newArea;

    /**
     * 新详细地址
     */
    @TableField("new_address")
    private String newAddress;

    /**
     * 原poi
     */
    @TableField("old_poi")
    private String oldPoi;

    /**
     * 新poi
     */
    @TableField("new_poi")
    private String newPoi;

    /**
     * 原/新poi距离差值，单位m
     */
    @TableField("distance")
    private Long distance;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private Integer operatorId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

}
