package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 缺货核准申诉
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-250 14:11:13
 */
@Getter
@Setter
@TableName("tms_lack_approved_appeal")
public class TmsLackApprovedAppeal implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 缺货核准id
     */
    @TableField("approved_id")
    private Long approvedId;

    /**
     * 申诉方:1城配、2干线、3仓库
     */
    @TableField("side")
    private Integer side;

    /**
     * 申诉说明
     */
    @TableField("description")
    private String description;

    /**
     * 申诉凭证
     */
    @TableField("certificate")
    private String certificate;

    /**
     * 申诉人名称
     */
    @TableField("appeal_people_name")
    private String appealPeopleName;


}
