package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送批次关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03 10:26:45
 */
@Getter
@Setter
@TableName("tms_delivery_batch_relation")
public class TmsDeliveryBatchRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配送批次ID
     */
    @TableField("batch_id")
    private Long batchId;

    /**
     * 关联配送批次ID
     */
    @TableField("relate_batch_id")
    private Long relateBatchId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;


}
