package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 干线城配委托单关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-103 18:45:05
 */
@Getter
@Setter
@TableName("tms_trunk_city_dist_relationship")
public class TmsTrunkCityDistRelationship implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 干线委托单ID
     */
    @TableField("trunk_dist_id")
    private Long trunkDistId;

    /**
     * 城配委托单ID
     */
    @TableField("city_dist_id")
    private Long cityDistId;

    /**
     * 外部单号
     */
    @TableField("outer_order_id")
    private String outerOrderId;


}
