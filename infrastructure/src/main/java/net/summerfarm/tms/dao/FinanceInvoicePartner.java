package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 发票来源公司中间表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-286 13:57:58
 */
@Getter
@Setter
@TableName("finance_invoice_partner")
public class FinanceInvoicePartner implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合作方id
     */
    @TableField("supplier_id")
    private Integer supplierId;

    /**
     * 合作方类型 0、供应商 1、承运商
     */
    @TableField("supplier_type")
    private Integer supplierType;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 发起人adminId
     */
    @TableField("creator_admin_id")
    private Integer creatorAdminId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
