package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送提醒对应行政区域表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-79 13:43:40
 */
@Getter
@Setter
@TableName("complete_delivery_ad_code_mapping")
public class CompleteDeliveryAdCodeMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 区域编码
     */
    @TableField("ad_code")
    private String adCode;

    /**
     * 完成配送提醒id
     */
    @TableField("complete_delivery_id")
    private Integer completeDeliveryId;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
