package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import net.summerfarm.tms.enums.DriverAccountEnums;

/**
 * 司机账户信息表
 *
 * <AUTHOR>
 * @Date 2023-07-10
 **/
@Data
@TableName("tms_driver_account")
public class TmsDriverAccount extends BaseEntity {
	private static final long serialVersionUID = 4821256108561503187L;

	/**
	 * 车辆id
	 */
	@TableField("driver_id")
	private Long driverId;

	/**
	 * 支付方式 1、银行卡 2、现金
	 *
	 * @see DriverAccountEnums.DriverAccountTypeEnum
	 */
	@TableField("pay_type")
	private Integer payType;

	/**
	 * 账户名称
	 */
	@TableField("account_name")
	private String accountName;

	/**
	 * 开户银行
	 */
	@TableField("account_bank")
	private String accountBank;

	/**
	 * 银行卡归属地
	 */
	@TableField("account_ascription")
	private String accountAscription;

	/**
	 * 账号
	 */
	@TableField("account")
	private String account;


}
