package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 停运时间
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-66 14:40:19
 */
@Getter
@Setter
@TableName("tms_stop_delivery")
public class TmsStopDelivery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 城配仓
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 停运开始时间
     */
    @TableField("shutdown_start_time")
    private LocalDate shutdownStartTime;

    /**
     * 停运结束时间
     */
    @TableField("shutdown_end_time")
    private LocalDate shutdownEndTime;

    /**
     * 是否删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;


}
