package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送到店打卡
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-298 17:09:31
 */
@Getter
@Setter
@TableName("tms_delivery_site_checkin_punch")
public class TmsDeliverySiteCheckinPunch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 配送点位Id
     */
    @TableField("delivery_site_id")
    private Long deliverySiteId;

    /**
     * 打卡范围km
     */
    @TableField("punch_range")
    private BigDecimal punchRange;

    /**
     * 打卡详细地址
     */
    @TableField("punch_address")
    private String punchAddress;

    /**
     * 打卡POI
     */
    @TableField("punch_poi")
    private String punchPoi;

    /**
     * 打卡到点距离km
     */
    @TableField("distance_to_site")
    private BigDecimal distanceToSite;

    /**
     * 超区原因
     */
    @TableField("exceed_reason")
    private String exceedReason;


}
