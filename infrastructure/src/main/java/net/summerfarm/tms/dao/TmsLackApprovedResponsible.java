package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 缺货核准责任方
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-250 14:11:13
 */
@Getter
@Setter
@TableName("tms_lack_approved_responsible")
public class TmsLackApprovedResponsible implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 缺货核准id
     */
    @TableField("approved_id")
    private Long approvedId;

    /**
     * 责任方 1仓库 2城配 3无法判责 4干线
     */
    @TableField("responsible")
    private Integer responsible;

    /**
     * 是否买赔 0是 1否
     */
    @TableField("buy_out")
    private Integer buyOut;

    /**
     * 买赔金额
     */
    @TableField("buy_out_money")
    private BigDecimal buyOutMoney;


}
