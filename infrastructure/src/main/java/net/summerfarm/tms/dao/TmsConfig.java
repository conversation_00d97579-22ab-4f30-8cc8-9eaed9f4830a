package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * tms系统配置表
 *
 * <AUTHOR>
 * @Date 2023-03-23
 **/
@Data
@TableName("tms_config")
public class TmsConfig implements Serializable {
    private static final long serialVersionUID = 3733518084034818959L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置键值
     *
     * @see net.summerfarm.tms.enums.TmsConfigKeyEnum
     */
    @TableField("`name`")
    private String name;

    /**
     * 配置value值
     */
    @TableField("`value`")
    private String value;

    /**
     *备注
     */
    @TableField("remark")
    private String remark;
}
