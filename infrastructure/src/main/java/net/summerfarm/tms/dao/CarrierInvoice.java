package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 承运商发票表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-285 15:09:05
 */
@Getter
@Setter
@TableName("carrier_invoice")
public class CarrierInvoice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 承运商id
     */
    @TableField("carrier_id")
    private Long carrierId;

    /**
     * 发票抬头
     */
    @TableField("invoice_head")
    private String invoiceHead;

    /**
     * 税号
     */
    @TableField("tax_no")
    private String taxNo;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
