package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送超距离设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-195 14:08:58
 */
@Getter
@Setter
@TableName("tms_out_distance_config")
public class TmsOutDistanceConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 城配仓
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 签收距离（公里）
     */
    @TableField("out_distance")
    private BigDecimal outDistance;

    /**
     * 状态0正常 1暂停
     */
    @TableField("state")
    private Integer state;

    /**
     * 操作人id
     */
    @TableField("admin_id")
    private Integer adminId;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
