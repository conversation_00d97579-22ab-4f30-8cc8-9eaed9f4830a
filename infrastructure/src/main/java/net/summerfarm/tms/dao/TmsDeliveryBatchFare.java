package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送批次运费明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-150 11:08:26
 */
@Getter
@Setter
@TableName("tms_delivery_batch_fare")
public class TmsDeliveryBatchFare implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private Long id;

    /**
     * 配送批次ID
     */
    @TableField("delivery_batch_id")
    private Long deliveryBatchId;

    /**
     * 费用类型，10：喜茶费用，20：益禾堂费用，30：美团费用，40：大客户费用，50：调拨费用，60：采购费用
     */
    @TableField("fare_type")
    private Integer fareType;

    /**
     * 费用金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    public String buildUk(){
        return this.deliveryBatchId + "#" + this.fareType;
    }


}
