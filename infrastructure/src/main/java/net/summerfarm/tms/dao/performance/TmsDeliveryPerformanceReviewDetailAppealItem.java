package net.summerfarm.tms.dao.performance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 履约审核申诉项
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-243 14:40:50
 */
@Getter
@Setter
@TableName("tms_delivery_performance_review_detail_appeal_item")
public class TmsDeliveryPerformanceReviewDetailAppealItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 申诉ID
     */
    @TableField("appeal_id")
    private Long appealId;

    /**
     * 配送点id
     */
    @TableField("delivery_site_id")
    private Long deliverySiteId;

    /**
     * 点位id
     */
    @TableField("site_id")
    private Long siteId;

    /**
     * 路线次序
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 详情地址
     */
    @TableField("site_address")
    private String siteAddress;

    /**
     * 不合规图片类型
     */
    @TableField("non_compliance_site_pic_type")
    private String nonComplianceSitePicType;

    /**
     * 不合规图片
     */
    @TableField("non_compliance_pic")
    private String nonCompliancePic;

    /**
     * 不合规原因
     */
    @TableField("non_compliance_reason")
    private String nonComplianceReason;

    /**
     * 申诉图片
     */
    @TableField("appeal_pic")
    private String appealPic;

    /**
     * 申诉原因
     */
    @TableField("appeal_reason")
    private String appealReason;


}
