package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送履约审核任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-179 16:03:34
 */
@Getter
@Setter
@TableName("tms_delivery_performance_review_task")
public class TmsDeliveryPerformanceReviewTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 审核类型 0城配-出仓、1城配-签收、2干线-签收
     */
    @TableField("review_task_type")
    private Integer reviewTaskType;

    /**
     * 名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 判罚标准
     */
    @TableField("penalty_standards")
    private BigDecimal penaltyStandards;

    /**
     * 属性JSON(城配仓/调度类型、配送/履约日期)
     */
    @TableField("property_json")
    private String propertyJson;

    /**
     * 状态0审核中、1审核完成、2已关闭
     */
    @TableField("state")
    private Integer state;

    /**
     * 创建人名称
     */
    @TableField("create_name")
    private String createName;

    /**
     * 审核模式
     * 0-人工审核，1-AI审核
     */
    @TableField("review_mode")
    private Integer reviewMode;
}
