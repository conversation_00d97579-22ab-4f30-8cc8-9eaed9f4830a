package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TmsDistSite {

    private Long id;

    private String provice;

    private String city;

    private String area;

    private String address;

    private String poi;

    private String phone;

    private String name;
    /**
     * 点位类型，0：客户，1：城配仓，2：库存仓，3：监管仓，4：采购地址，5：Saas，6：指定地址
     */
    private Integer type;

    private Integer state;

    private BigDecimal punchDistance;

    private String outBusinessNo;

    private String outTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String contactPerson;

    private Long superviseSiteId;

    private Long creator;

    /**
     * 0智能排线 1手动排线
     */
    private Integer intelligencePath;

    @TableField(exist = false)
    private String region;

    @TableField(value = "site_pics")
    private String sitePics;


    /**
     * 点位用途 0店铺门店
     */
    @TableField(value = "site_use")
    private Integer siteUse;
}