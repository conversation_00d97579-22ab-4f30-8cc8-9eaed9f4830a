package net.summerfarm.tms.dao;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 完成配送提醒规则组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-79 13:34:17
 */
@Getter
@Setter
@TableName("tms_complete_delivery_rule")
public class TmsCompleteDeliveryRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private Long id;

    /**
     * 配送仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;

    /**
     * 品牌规则文件oss标识
     */
    @TableField(value = "brand_rule_object_oss_key", updateStrategy = FieldStrategy.IGNORED)
    private String brandRuleObjectOssKey;

    /**
     * 门店规则文件oss标识
     */
    @TableField(value = "merchant_rule_object_oss_key", updateStrategy = FieldStrategy.IGNORED)
    private String merchantRuleObjectOssKey;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
