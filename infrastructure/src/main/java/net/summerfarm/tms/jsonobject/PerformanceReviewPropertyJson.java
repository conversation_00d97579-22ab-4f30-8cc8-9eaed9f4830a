package net.summerfarm.tms.jsonobject;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/6/30 15:43<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PerformanceReviewPropertyJson {
    /**
     * 城配仓名称
     */
    private List<String> storeNames;

    /**
     * 城配仓编号
     */
    private List<String> storeNos;
    /**
     * 调度类型名称
     */
    private List<String> batchTypeNames;
    /**
     * 调度类型集合
     */
    private List<Integer> batchTypes;

    /**
     * 开始时间
     */
    private LocalDate beginDeliveryTime;

    /**
     * 结束时间
     */
    private LocalDate endDeliveryTime;

}
