package net.summerfarm.tms.jsonobject;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/10/20 14:29<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryBatchLoadRatioCalculateJson {
    /**
     * 车辆体积
     */
    private BigDecimal carLoadVolume;
    /**
     * 车辆重量
     */
    private BigDecimal carLoadWeight;
    /**
     * 车辆件数
     */
    private Integer carLoadQuantity;

    /**
     * 运载体积
     */
    private BigDecimal transportationLoadVolume;
    /**
     * 运载重量,单位:吨
     */
    private BigDecimal transportationLoadWeight;
    /**
     * 运载件数
     */
    private Integer transportationLoadQuantity;
}
