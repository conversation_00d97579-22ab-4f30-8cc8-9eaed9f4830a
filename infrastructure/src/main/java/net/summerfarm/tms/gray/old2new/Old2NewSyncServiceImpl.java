package net.summerfarm.tms.gray.old2new;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.dao.TmsDistOrder;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.wnc.WncQueryFacade;
import net.summerfarm.tms.gray.dto.DataSyncCompareResult;
import net.summerfarm.tms.gray.dto.OldDistOrderDTO;
import net.summerfarm.tms.gray.mapper.OldDistOrderMapper;
import net.summerfarm.tms.mapper.TmsDistOrderMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:委托单同步仓库实现类
 * date: 2022/10/24 11:03
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class Old2NewSyncServiceImpl implements Old2NewSyncService {

    private final TmsDistOrderMapper tmsDistOrderMapper;
    private final OldDistOrderMapper oldDistOrderMapper;
    private final SiteRepository siteRepository;
    private final WncQueryFacade wncQueryFacade;

    @Override
    public OldDistOrderDTO queryOrderDeliveryPlanDetail(Integer id) {
        return oldDistOrderMapper.queryOrderDeliveryPlanDetail(id);
    }

    @Override
    public OldDistOrderDTO queryAfterSaleDeliveryPlanDetail(Integer id) {
        return oldDistOrderMapper.queryAfterSaleDeliveryPlanDetail(id);
    }

    @Override
    public OldDistOrderDTO querySampleApplyDeliveryPlanDetail(Integer id) {
        return oldDistOrderMapper.querySampleApplyDeliveryPlanDetail(id);
    }

    @Override
    public OldDistOrderDTO querySaasOrderDeliveryPlanDetail(Integer id) {
        OldDistOrderDTO distOrderDTO = oldDistOrderMapper.querySaasOrderDeliveryPlanDetail(id);
        if (distOrderDTO == null) {
            return null;
        }
        if (distOrderDTO.getType() == 4) {
            distOrderDTO.setType(0);
        }
        if (distOrderDTO.getDeliveryType() == 4) {
            distOrderDTO.setDeliveryType(1);
        } else {
            distOrderDTO.setDeliveryType(0);
        }
        return distOrderDTO;
    }

    @Override
    public DataSyncCompareResult dataSyncResultHandle(LocalDate deliveryTimeFrom, LocalDate deliveryTimeTo, Integer storeNo) {
        //查询老模型需要同步的配送计划集合
        List<OldDistOrderDTO> needSyncDataList = queryNeedSyncDataList(deliveryTimeFrom, deliveryTimeTo, storeNo);
        Set<String> needSyncDataSet = needSyncDataList.stream()
                .map(oldDistOrderDTO -> buildUk(oldDistOrderDTO.getOuterOrderId(),
                        oldDistOrderDTO.getSource().getCode(),
                        oldDistOrderDTO.getExpectBeginTime(),
                        oldDistOrderDTO.getOuterContactId()))
                .collect(Collectors.toSet());
        //查询新模型已经同步的配送计划集合
        Long siteId = storeNo == null ? null : siteRepository.getCitySiteIdByOutBusinessNo(storeNo);
        LocalDateTime deliveryDateTimeTo = deliveryTimeTo == null ? null : deliveryTimeTo.atStartOfDay();
        List<TmsDistOrder> syncedDistOrderList = tmsDistOrderMapper.querySyncedDistOrderList(deliveryTimeFrom.atStartOfDay(),deliveryDateTimeTo, siteId);
        Set<String> syncedDistOrderSet = syncedDistOrderList.stream()
                .map(tmsDistOrder -> buildUk(tmsDistOrder.getOuterOrderId(),
                        tmsDistOrder.getSource(),
                        tmsDistOrder.getExpectBeginTime(),
                        tmsDistOrder.getOuterContactId()))
                .collect(Collectors.toSet());

        log.info("老模型配送计划条数:{},新模型配送计划条数:{}", needSyncDataList.size(), syncedDistOrderList.size());
        //未同步配送计划集合
        List<OldDistOrderDTO> noSyncDataList = new ArrayList<>();
        //需删除配送计划集合
        List<OldDistOrderDTO> needDelDataList = new ArrayList<>();
        for (OldDistOrderDTO oldDistOrderDTO : needSyncDataList) {
            String outerOrderId = oldDistOrderDTO.getOuterOrderId();
            Integer source = oldDistOrderDTO.getSource().getCode();
            LocalDateTime expectBeginTime = oldDistOrderDTO.getExpectBeginTime();
            String outerContactId = oldDistOrderDTO.getOuterContactId();
            if (!syncedDistOrderSet.contains(buildUk(outerOrderId, source, expectBeginTime, outerContactId))) {
                noSyncDataList.add(oldDistOrderDTO);
            }
        }
        for (TmsDistOrder tmsDistOrder : syncedDistOrderList) {
            String outerOrderId = tmsDistOrder.getOuterOrderId();
            Integer source = tmsDistOrder.getSource();
            LocalDateTime expectBeginTime = tmsDistOrder.getExpectBeginTime();
            String outerContactId = tmsDistOrder.getOuterContactId();
            if (!needSyncDataSet.contains(buildUk(outerOrderId, source, expectBeginTime, outerContactId))) {
                OldDistOrderDTO oldDistOrderDTO = new OldDistOrderDTO(outerOrderId, source, expectBeginTime, outerContactId);
                oldDistOrderDTO.setId(tmsDistOrder.getId().intValue());
                needDelDataList.add(oldDistOrderDTO);
            }
        }
        return new DataSyncCompareResult(noSyncDataList, needDelDataList, needSyncDataList.size() == syncedDistOrderList.size());
    }

    private String buildUk(String outerOrderId, Integer source, LocalDateTime expectBeginTime, String outerContactId) {
        return outerOrderId + "#" + source + "#" + expectBeginTime + "#" + outerContactId;
    }

    private List<OldDistOrderDTO> queryNeedSyncDataList(LocalDate deliveryTimeFrom,LocalDate deliveryTimeTo, Integer storeNo) {
        // 查询POP代下单的门店id
        List<Long> popHelperOrderMerchantIds = wncQueryFacade.queryPopHelperOrderMerchantInfo();

        List<OldDistOrderDTO> needSyncDataList = new ArrayList<>(500);
        List<OldDistOrderDTO> mall = oldDistOrderMapper.queryMallNeedSyncDataList(deliveryTimeFrom, deliveryTimeTo, storeNo,popHelperOrderMerchantIds);
        List<OldDistOrderDTO> afterSale = oldDistOrderMapper.queryAfterSaleNeedSyncDataList(deliveryTimeFrom, deliveryTimeTo, storeNo,popHelperOrderMerchantIds);
        List<OldDistOrderDTO> sampleApply = oldDistOrderMapper.querySampleApplyNeedSyncDataList(deliveryTimeFrom, deliveryTimeTo, storeNo,popHelperOrderMerchantIds);
        List<OldDistOrderDTO> saas = oldDistOrderMapper.querySaasNeedSyncDataList(deliveryTimeFrom, deliveryTimeTo, storeNo);
        needSyncDataList.addAll(mall);
        needSyncDataList.addAll(afterSale);
        needSyncDataList.addAll(sampleApply);
        needSyncDataList.addAll(saas);
        return needSyncDataList;
    }

    @Override
    public List<OldDistOrderDTO> queryMallDeliveryPlanByOrderNo(String orderNo) {
        List<OldDistOrderDTO> oldDistOrderDTOS = oldDistOrderMapper.queryMallDeliveryPlanByOrderNo(orderNo);
        List<OldDistOrderDTO> resultList = new ArrayList<>();
        for (OldDistOrderDTO oldDistOrderDTO : oldDistOrderDTOS) {
            resultList.add(oldDistOrderMapper.queryOrderDeliveryPlanDetail(oldDistOrderDTO.getId()));
        }
        return resultList;
    }

    @Override
    public TmsResult<Void> completePathCheakData(LocalDate deliveryTime, Integer storeNo) {
        DataSyncCompareResult dataSyncCompareResult = this.dataSyncResultHandle(deliveryTime, deliveryTime, storeNo);
        if(dataSyncCompareResult.successFlag()){
            return TmsResult.VOID_SUCCESS;
        }
        //通知告警
        StringJoiner messageSj = new StringJoiner(";");
        //需要删除的数据
        List<OldDistOrderDTO> needDelDataList = dataSyncCompareResult.getNeedDelDataList();
        //需要同步的数据
        List<OldDistOrderDTO> noSyncDataList = dataSyncCompareResult.getNoSyncDataList();


        if(!CollectionUtils.isEmpty(noSyncDataList)){
            String noSyncOrderNos = noSyncDataList.stream().map(OldDistOrderDTO::getOuterOrderId).collect(Collectors.joining(","));
            messageSj.add("storeNo:"+storeNo+"完成排线数据异常-----没有同步的订单号如下------："+noSyncOrderNos+"\n|\r");
        }
        if(!CollectionUtils.isEmpty(needDelDataList)){
            String needDelOrderNos = noSyncDataList.stream().map(OldDistOrderDTO::getOuterOrderId).collect(Collectors.joining(","));
            messageSj.add("storeNo:"+storeNo + "监测截单时间同步数据异常-----需要删除的订单号如下------："+needDelOrderNos);
        }
        //返回异常
        HashMap<String, String> msgMap = new HashMap<>();
        msgMap.put("text",messageSj.toString());
        msgMap.put("title","完成排线告警");
        //配置中发送群机器人的url
        DingTalkRobotUtil.sendMsgAndAtAll("markdown", "https://oapi.dingtalk.com/robot/send?access_token=17d60bc0272195b94ac997cb6d758fb0e98d5879c485d1e387d69c31a94cc29c", () -> msgMap);

        throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_ERROR,"存在数据同步异常情况，已自动联系到管理员，请稍后再试！！");
    }
}
