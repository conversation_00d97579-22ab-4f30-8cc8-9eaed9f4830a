package net.summerfarm.tms.mapper.delivery;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.model.delivery.TmsDeliveryPickShortOrderMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-24 14:21:44
 */
public interface TmsDeliveryPickShortOrderMappingMapper extends BaseMapper<TmsDeliveryPickShortOrderMapping> {

    /**
     * 批量插入
     *
     * @param recordList
     * @return
     */
    int batchInsert(@Param("list") List<TmsDeliveryPickShortOrderMapping> recordList);

    /**
     * 根据配送批次id查询拣货缺货信息
     *
     * @param deliveryBatchId
     * @return
     */
    List<TmsDeliveryPickShortOrderMapping> listByDeliveryBatchId(@Param("deliveryBatchId") Long deliveryBatchId);

}

