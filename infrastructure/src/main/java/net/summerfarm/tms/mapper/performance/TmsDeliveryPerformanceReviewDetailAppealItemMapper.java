package net.summerfarm.tms.mapper.performance;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppealItem;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAppealItemQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 履约审核申诉项 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-243 14:40:50
 */
public interface TmsDeliveryPerformanceReviewDetailAppealItemMapper extends BaseMapper<TmsDeliveryPerformanceReviewDetailAppealItem> {

    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(TmsDeliveryPerformanceReviewDetailAppealItem record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(TmsDeliveryPerformanceReviewDetailAppealItem record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    TmsDeliveryPerformanceReviewDetailAppealItem selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<TmsDeliveryPerformanceReviewDetailAppealItem> selectByCondition(TmsDeliveryPerformanceReviewDetailAppealItemQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<DeliveryPerformanceReviewDetailAppealItemEntity> getPage(TmsDeliveryPerformanceReviewDetailAppealItemQueryParam param);
}
