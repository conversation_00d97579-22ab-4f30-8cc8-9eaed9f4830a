package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.TmsDeliveryPick;
import net.summerfarm.tms.dataobj.DeliverySiteStandardTempConditionDataObj;
import net.summerfarm.tms.delivery.entity.BatchPickDetailEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 签发,装货表,点位+sku Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-256 17:55:54
 */
public interface TmsDeliveryPickMapper extends BaseMapper<TmsDeliveryPick> {

    /**
     * 批量保存
     * @param tmsDeliveryPicks 保存
     */
    void saveBatch(@Param("tmsDeliveryPicks")List<TmsDeliveryPick> tmsDeliveryPicks);

    List<BatchPickDetailEntity> queryByBatch(@Param("batchIds") List<Long> batchIds);

    /**
     * 根据拣货站点信息查询标品温度条件
     * @param deliverySiteIds 站点ID集合
     * @return 配送站点对应拣货标品的数据信息
     */
    List<DeliverySiteStandardTempConditionDataObj> queryPickStandardItemTemperatureConditionsByDelSiteIds(@Param("deliverySiteIds")List<Long> deliverySiteIds);

    /**
     * 根据拣货站点信息查询标品温度单位数量
     * @param deliverySiteIds 站点ID集合
     * @return 配送站点对应拣货标品的数据信息
     */
    List<DeliverySiteStandardTempConditionDataObj> queryPickStandardItemTemperatureUnitByDelSiteIds(@Param("deliverySiteIds")List<Long> deliverySiteIds);
}
