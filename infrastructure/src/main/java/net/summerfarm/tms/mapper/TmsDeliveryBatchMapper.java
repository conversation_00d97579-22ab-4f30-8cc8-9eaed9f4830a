package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.TmsDeliveryBatch;
import net.summerfarm.tms.delivery.entity.BoardEntity;
import net.summerfarm.tms.delivery.vo.BatchCarLoadStaticVO;
import net.summerfarm.tms.delivery.vo.BatchLoadRatioVO;
import net.summerfarm.tms.query.board.BoardQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 配送批次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-256 17:55:54
 */
public interface TmsDeliveryBatchMapper extends BaseMapper<TmsDeliveryBatch> {

    List<BoardEntity> queryBoardData(BoardQuery boardQuery);

    TmsDeliveryBatch queryByIdForceMaster(@Param("batchId") Long batchId);

    /**
     * 查询批次车辆装载信息
     * @param batchIds 批次ID集合
     * @return 结果
     */
    BatchCarLoadStaticVO selectBatchCarLoad(@Param("list") List<Long> batchIds);

    /**
     * 更新批次装载率
     * @param batchLoadRatioVO
     * @return
     */
    int updateBatchLoadRatioById(BatchLoadRatioVO batchLoadRatioVO);
}
