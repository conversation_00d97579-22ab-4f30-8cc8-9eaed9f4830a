package net.summerfarm.tms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.TmsDriver;
import net.summerfarm.tms.query.base.driver.DriverQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TmsDriverMapper extends BaseMapper<TmsDriver> {
    int deleteByPrimaryKey(Long id);

    int insert(TmsDriver record);

    int insertSelective(TmsDriver record);

    TmsDriver selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TmsDriver record);

    int updateByPrimaryKey(TmsDriver record);

    TmsDriver selectByTmsDriver(TmsDriver queryDriver);

    List<TmsDriver> selectListByTmsDriver(DriverQuery driverQuery);

    TmsDriver selectOtherByPhone(@Param("id") Long id, @Param("phone") String phone);

    List<TmsDriver> selectByIdList(@Param("driverIds") List<Long> driverIds);
}