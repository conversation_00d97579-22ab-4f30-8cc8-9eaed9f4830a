package net.summerfarm.tms.mapper;

import net.summerfarm.tms.base.distance.dto.OutDistanceConfigVO;
import net.summerfarm.tms.base.distance.entity.OutDistanceEntity;
import net.summerfarm.tms.dao.TmsOutDistanceConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.query.base.distance.OutDistanceQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 配送超距离设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-195 14:08:58
 */
public interface TmsOutDistanceConfigNewMapper extends BaseMapper<TmsOutDistanceConfig> {

    /**
     * 分页查询
     * @param outDistanceQuery 查询
     * @return 结果
     */
    List<OutDistanceEntity> queryList(OutDistanceQuery outDistanceQuery);
}
