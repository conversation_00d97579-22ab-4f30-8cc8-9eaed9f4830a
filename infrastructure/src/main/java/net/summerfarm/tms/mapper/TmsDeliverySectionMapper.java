package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.TmsDeliverySection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 配送路段表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-342 13:56:46
 */
public interface TmsDeliverySectionMapper extends BaseMapper<TmsDeliverySection> {

    /**
     * 批量保存
     *
     * @param tmsDeliverySections 路段集合
     */
    void saveBatch(@Param("tmsDeliverySections") List<TmsDeliverySection> tmsDeliverySections);
}
