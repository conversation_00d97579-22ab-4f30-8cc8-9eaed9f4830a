package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.TmsPathSection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 路线详情-路段 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-256 17:55:54
 */
public interface TmsPathSectionMapper extends BaseMapper<TmsPathSection> {

    List<TmsPathSection> queryMatchEndSiteTrunkPath(@Param("endSiteId") Long endSiteId);

    TmsPathSection querySectionByBeginEndIdType(@Param("beginSiteId")Long beginSiteId,
                                                @Param("endSiteId") Long endSiteId,
                                                @Param("pathType") int pathType);
}
