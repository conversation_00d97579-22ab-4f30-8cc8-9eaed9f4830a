package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.TmsDistOrder;
import net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderFlatObject;
import net.summerfarm.tms.dist.flatObject.ManyFulfillmentWayDistOrderFlatObject;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 委托单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-256 17:55:54
 */
public interface TmsDistOrderMapper extends BaseMapper<TmsDistOrder> {

    /**
     * 根据条件查询委托单集合
     *
     * @param distOrderQuery 委托单查询
     * @return 委托单集合
     */
    List<TmsDistOrder> selectByQuery(DistOrderQuery distOrderQuery);

    /**
     * 查询新模型已经同步的配送计划集合
     *
     * @param deliveryTimeFrom 配送时间
     * @param deliveryTimeTo 配送时间
     * @param siteId 城配仓点位ID
     * @return 新模型已经同步的配送计划集合
     */
    List<TmsDistOrder> querySyncedDistOrderList(@Param(value = "deliveryTimeFrom") LocalDateTime deliveryTimeFrom,
                                                @Param(value = "deliveryTimeTo") LocalDateTime deliveryTimeTo,
                                                @Param(value = "siteId") Long siteId);


    /**
     * 查询委托单信息
     * @param expectBeginTime 开始时间
     * @param outerContactId 外部联系人Id
     * @param sources 来源
     * @return
     */
    List<TmsDistOrder> queryListForceMaster(@Param(value = "expectBeginTime")LocalDateTime expectBeginTime,
                                            @Param(value = "outerContactId")String outerContactId,
                                            @Param(value = "sources")List<Integer> sources);

    /**
     * 根据配送时间+城配仓查询配送单信息
     * @param deliveryTime 配送时间
     * @param storeNo 城配仓编号
     * @return 配送单信息集合
     */
    List<DeliveryNoteOrderFlatObject> queryDeliveryNoteByDateAndStoreNo(@Param(value = "deliveryTime")LocalDate deliveryTime,
                                                                        @Param(value = "storeNo")Integer storeNo);

    /**
     * 根据订单编号查询配送单信息
     * @param orderNo 订单编号
     * @return 配送单信息集合
     */
    List<DeliveryNoteOrderFlatObject> queryOrderDeliveryNoteByOrderNo(@Param(value = "orderNo")String orderNo);

    /**
     * 批量更新履约方式
     * @param distOrderIds 委托单ID集合
     * @param fulfillmentDeliveryWay 履约方式
     * @return 更新的记录数
     */
    int batchUpdateFulfillmentWay(@Param("distOrderIds") List<Long> distOrderIds,
                                  @Param("fulfillmentDeliveryWay") Integer fulfillmentDeliveryWay);

    /**
     * 查询干线转运委托单的起点站点
     * @param expectBeginTime 期待开始时间
     * @param sources 来源
     * @param state 状态
     * @return 起点站点集合
     */
    List<Long> queryTrunkTransportBeginSites(@Param(value = "expectBeginTime") LocalDateTime expectBeginTime,
                                             @Param(value = "sources") List<Integer> sources,
                                             @Param(value = "state") Integer state);

    /**
     * 查询次日有多个履约配送方式的委托单
     * @param deliveryTime 配送时间
     * @param sourceList 来源
     * @return 结果
     */
    List<ManyFulfillmentWayDistOrderFlatObject> queryHaveNextDeliveryDayManyFulfillmentWay(@Param(value = "deliveryTime") LocalDate deliveryTime,
                                                                                           @Param(value = "sourceList") List<Integer> sourceList);
}
