package net.summerfarm.tms.mapper.performance;

import net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppeal;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.performance.entity.dataobject.ReviewDetailAppealPageObj;
import net.summerfarm.tms.query.performance.AppealQuery;

import java.util.List;

/**
 * <p>
 * 履约审核申诉 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-243 14:40:50
 */
public interface TmsDeliveryPerformanceReviewDetailAppealMapper extends BaseMapper<TmsDeliveryPerformanceReviewDetailAppeal> {

    /**
     * 分页查询申诉列表
     * @param query 查询条件
     * @return 分页信息
     */
    List<ReviewDetailAppealPageObj> queryReviewDetailAppealPage(AppealQuery query);
}
