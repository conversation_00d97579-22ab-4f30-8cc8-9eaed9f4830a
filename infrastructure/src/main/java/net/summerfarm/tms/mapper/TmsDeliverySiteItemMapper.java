package net.summerfarm.tms.mapper;

import net.summerfarm.tms.dao.TmsDeliverySiteItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dataobj.DeliverySiteStandardTempConditionDataObj;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempCondition;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 配送点位详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-298 18:35:13
 */
public interface TmsDeliverySiteItemMapper extends BaseMapper<TmsDeliverySiteItem> {

    /**
     * 批量保存
     * @param tmsDeliverySiteItems
     */
    void saveBatch(@Param("tmsDeliverySiteItems") List<TmsDeliverySiteItem> tmsDeliverySiteItems);

    /**
     * 根据配送点位ID集合查询配送单项
     * @param deliverySiteIdList 配送点位集合
     * @return 结果
     */
    List<TmsDeliverySiteItem> queryListForceMasterByDeliverySiteId(@Param("deliverySiteIdList") List<Long> deliverySiteIdList);

    /**
     * 查询配送缺货需要初始化的ID集合
     * @return 结果
     */
    @Select("SELECT `id` FROM `tms_delivery_site_item` WHERE `short_count` > 0 AND `status` = 0 LIMIT 500")
    List<Integer> selectDeliveryShortInitIds();

    /**
     * 查询回收异常需要初始化的ID集合
     * @return 结果
     */
    @Select("SELECT `id` FROM `tms_delivery_site_item` WHERE `remark` IS NOT NULL  AND `status` = 0 LIMIT 500")
    List<Integer> selectRecycleAbnormalInitIds();

    /**
     * 根据配送点位ID集合查询配送站点标品存储详情
     * @param deliverySiteIds
     * @return
     */
    List<DeliverySiteStandardTempConditionDataObj> queryStandardItemTemperatureConditionsByIds(@Param("deliverySiteIdList")List<Long> deliverySiteIds);
}
