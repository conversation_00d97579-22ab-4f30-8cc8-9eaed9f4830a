package net.summerfarm.tms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.TmsCar;
import net.summerfarm.tms.query.base.car.CarQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TmsCarMapper extends BaseMapper<TmsCar> {
    int deleteByPrimaryKey(Long id);

    int insertSelective(TmsCar record);

    TmsCar selectByPrimaryKey(Long id);
    
    TmsCar selectByCarNumber(@Param("carNumber") String carNumber);

    int updateByPrimaryKey(TmsCar tmsCar);

    List<TmsCar> selectByTmsCar(TmsCar tmsCar);

    List<TmsCar> selectByIdList(@Param("carIds") List<Long> carIds);

    List<TmsCar> selectByQuery(CarQuery carQuery);
}