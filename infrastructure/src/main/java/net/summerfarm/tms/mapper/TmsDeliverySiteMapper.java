package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.SameDeliverySite;
import net.summerfarm.tms.dao.TmsDeliverySite;
import net.summerfarm.tms.delivery.entity.CitySitePerformanceVO;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 运输单表 点位维度, 路线每个站点的情况 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-256 17:55:54
 */
public interface TmsDeliverySiteMapper extends BaseMapper<TmsDeliverySite> {

    /**
     * 顺序加
     * @param deliveryBatchId
     * @param sourceSeq
     * @param newSequence
     */
    void updateSortAdd(@Param("deliveryBatchId") Long deliveryBatchId, @Param("sourceSeq") Integer sourceSeq, @Param("newSequence") Integer newSequence);

    /**
     * 顺序减
     *
     * @param deliveryBatchId
     * @param sourceSeq
     * @param newSequence
     */
    void updateSortSub(@Param("deliveryBatchId") Long deliveryBatchId, @Param("sourceSeq") Integer sourceSeq, @Param("newSequence") Integer newSequence);


    /**
     * 查询待发送钉钉消息提示的配送点位集合
     *
     * <pr>配送点位的消息发送记录标识为空，当前时间在计划签收时间之前</pr>
     *
     * @param batchIds  调度单id集合
     * @param arriveMsg 入仓消息到达
     * @return
     */
    List<DeliverySiteEntity> queryWaitSendMsgList(@Param("batchIds") Collection<Long> batchIds, @Param("arriveMsg") boolean arriveMsg);

    /**
     * 查询城配审核相关信息
     * @param deliverySiteQuery 查询
     * @return 结果
     */
    List<CitySitePerformanceVO> queryCitySitePerformance(DeliverySiteQuery deliverySiteQuery);

    /**
     * 根据批次ID和点位查询主库信息
     * @param deliveryBatchId 批次ID
     * @param siteId 点位ID
     * @return 配送点位信息
     */
    TmsDeliverySite queryForceMasterByBatchIdAndSiteId(@Param("deliveryBatchId")Long deliveryBatchId,@Param("siteId") Long siteId);

    /**
     * 强制查询主库
     * @param deliveryBatchIds 批次集合
     * @return 结果
     */
    List<TmsDeliverySite> queryForceMasterByBatchIds(@Param("deliveryBatchIds")List<Long> deliveryBatchIds);

    /**
     * 查询指定时间相同配送点位信息
     * @param deliveryTime 配送时间
     * @return 结果
     */
    List<SameDeliverySite> querySameSitesByDeliveryTime(LocalDateTime deliveryTime);

    /**
     * 查询配送点位信息
     * @param beginSiteId 起点ID
     * @param endSiteId 终点ID
     * @param deliveryTime 配送时间
     * @return 结果
     */
    List<TmsDeliverySite> queryListByBeginAndEndAndDeliveryTime(@Param("beginSiteId") Long beginSiteId, @Param("endSiteId") Long endSiteId,
                                                                @Param("deliveryTime") LocalDateTime deliveryTime);

    /**
     * 查询指定时间无配送单的配送点位信息
     * @param deliveryTime 配送时间
     * @return 结果
     */
    List<TmsDeliverySite> querySiteWithNoOrderByDeliveryTime(LocalDateTime deliveryTime);

    /**
     * 查询有专车配送的城配批次ID集合
     * @param deliveryTime 配送日期
     * @return 有专车配送的城配批次ID集合
     */
    List<Long> queryCityDeliveryHaveSpecialSendSiteBatchIds(LocalDateTime deliveryTime);

    /**
     * 查询最近日期配送门店POI列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param city      城市
     * @param area      区域
     * @return POI列表
     */
    List<String> queryRecentlyDateDeliveryStorePoi(@Param("startTime") LocalDate startTime,
                                                   @Param("endTime") LocalDate endTime,
                                                   @Param("city") String city,
                                                   @Param("area") String area);
}
