package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.dao.TmsPath;
import net.summerfarm.tms.query.path.QueryPathFilterDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 线路表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-256 17:55:54
 */
public interface TmsPathMapper extends BaseMapper<TmsPath> {

    /**
     * 查询承运商信息
     * @return 承运商信息
     */
    @Select(value = "SELECT concat( `id`,',', `carrier_name` ) FROM carrier")
    List<String> selectCarriers();

    /**
     * 查询仓库点位信息
     * @return 仓库点位信息
     */
    @Select(value = "SELECT concat( `id`,',', `name` ) FROM tms_dist_site WHERE `type` IN (1,2)")
    List<String> selectDistSites();

    /**
     * 查询有效干线路线ID信息
     *
     * @return 有效干线路线ID信息
     */
    @Select(value = "SELECT `id` FROM tms_path WHERE `type` = 1 AND `status` = 1")
    List<Long> selectPathIds();

    /**
     * 查询路由列表
     *
     * @param filterDTO
     * @return
     */
    List<TmsPathEntity> queryList(@Param("filter") QueryPathFilterDTO filterDTO);
}
