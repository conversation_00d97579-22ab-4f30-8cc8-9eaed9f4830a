package net.summerfarm.tms.mapper;

import net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetailAiReview;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailAiReviewEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAiReviewQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-09-18 00:16:55
 * @version 1.0
 *
 */
@Mapper
public interface TmsDeliveryPerformanceReviewDetailAiReviewMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(TmsDeliveryPerformanceReviewDetailAiReview record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(TmsDeliveryPerformanceReviewDetailAiReview record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    TmsDeliveryPerformanceReviewDetailAiReview selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<TmsDeliveryPerformanceReviewDetailAiReview> selectByCondition(TmsDeliveryPerformanceReviewDetailAiReviewQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<TmsDeliveryPerformanceReviewDetailAiReviewEntity> getPage(TmsDeliveryPerformanceReviewDetailAiReviewQueryParam param);
}

