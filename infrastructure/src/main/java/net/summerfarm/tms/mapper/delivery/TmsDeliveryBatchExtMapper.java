package net.summerfarm.tms.mapper.delivery;

import net.summerfarm.tms.delivery.entity.TmsDeliveryBatchExtEntity;
import net.summerfarm.tms.delivery.param.query.TmsDeliveryBatchExtQueryParam;
import net.summerfarm.tms.model.delivery.TmsDeliveryBatchExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-05-14 15:11:42
 * @version 1.0
 *
 */
@Mapper
public interface TmsDeliveryBatchExtMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(TmsDeliveryBatchExt record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(TmsDeliveryBatchExt record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    TmsDeliveryBatchExt selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<TmsDeliveryBatchExt> selectByCondition(TmsDeliveryBatchExtQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<TmsDeliveryBatchExtEntity> getPage(TmsDeliveryBatchExtQueryParam param);
}

