package net.summerfarm.tms.mapper;

import net.summerfarm.tms.dao.TmsDeliveryBatchRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.delivery.entity.DeliveryBatchRelationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 配送批次关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03 10:26:45
 */
public interface TmsDeliveryBatchRelationMapper extends BaseMapper<TmsDeliveryBatchRelation> {

    /**
     * 批量插入
     * @param tmsDeliveryBatchRelations 数据集合
     * @return 影响条数
     */
    int insertBatch(@Param(value = "list") List<TmsDeliveryBatchRelation> tmsDeliveryBatchRelations);

    /**
     * 查询批次关联关系
     * @param batchId 配送批次ID
     * @return 批次关联关系集合
     */
    List<DeliveryBatchRelationEntity> selectBatchRelationListForceMaster(@Param(value = "batchId") Long batchId);
}
