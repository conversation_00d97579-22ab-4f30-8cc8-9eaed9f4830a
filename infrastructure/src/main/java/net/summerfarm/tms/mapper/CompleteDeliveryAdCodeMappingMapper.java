package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.alert.entity.AreaAdCode;
import net.summerfarm.tms.fence.entity.AdCodeMsgEntity;
import net.summerfarm.tms.outland.mapper.CityStoreNoVO;
import net.summerfarm.tms.dao.CompleteDeliveryAdCodeMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CompleteDeliveryAdCodeMappingMapper extends BaseMapper<CompleteDeliveryAdCodeMapping> {
    int deleteByPrimaryKey(Long id);

    int insert(CompleteDeliveryAdCodeMapping record);

    int insertSelective(CompleteDeliveryAdCodeMapping record);

    CompleteDeliveryAdCodeMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CompleteDeliveryAdCodeMapping record);

    int updateByPrimaryKey(CompleteDeliveryAdCodeMapping record);

    List<CityStoreNoVO> selectByAdCodes(@Param("adCodes") List<String> adCodes);

    void insertBatch(@Param("completeDeliveryAdCodeMappings")List<CompleteDeliveryAdCodeMapping> completeDeliveryAdCodeMappings);

    void deleteByCompleteDeliveryId(Integer id);

    List<AdCodeMsgEntity> selectAddressByCDId(@Param("completeDeliveryId") Integer completeDeliveryId);

    List<String> selectAreaByCodeList();

    List<String> selectAreaByCompleteId(@Param("completeDeliveryId") Integer completeDeliveryId);

    void deleteByAdCode(@Param("adCode") String adCode);
}