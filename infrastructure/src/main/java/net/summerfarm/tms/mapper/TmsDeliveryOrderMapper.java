package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.TmsDeliveryOrder;
import net.summerfarm.tms.delivery.entity.query.DeliveryOrderRelatedQuery;
import net.summerfarm.tms.delivery.vo.BatchDetailLoadStaticVO;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 配送单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-256 17:55:54
 */
public interface TmsDeliveryOrderMapper extends BaseMapper<TmsDeliveryOrder> {

    /**
     * 根据配送时间查询相同起点、终点、配送时间配送单不同批次数据
     * @param deliveryTime 配送时间
     * @return 结果
     */
    List<TmsDeliveryOrder> queryListWithDiffBatch(LocalDateTime deliveryTime);

    /**
     * 根据委托单Id强制主库查询
     * @param distId 委托单
     * @return 配送单集合
     */
    List<TmsDeliveryOrder> queryListByDistIdForceMaster(Long distId);

    /**
     * 批量插入
     * @param tmsDeliveryOrderList 配送单集合
     */
    int insertBatch(@Param(value = "list") List<TmsDeliveryOrder> tmsDeliveryOrderList);

    /**
     * 查询批次装载明细数据汇总
     * @param batchIds 批次ID集合
     * @return 结果
     */
    BatchDetailLoadStaticVO selectBatchLoadDetail(@Param(value = "list") List<Long> batchIds);

    /**
     * 查询配送单相关信息
     *
     * <AUTHOR>
     * @date 2025/2/25 15:20
     * @param deliveryOrderRelatedQuery
     * @return java.util.List<net.summerfarm.tms.dao.TmsDeliveryOrder>
     */
    List<TmsDeliveryOrder> selectListByCondition(DeliveryOrderRelatedQuery deliveryOrderRelatedQuery);

    /**
     * 分页查询配送单（MyBatis实现）
     *
     * @param deliveryOrderQuery 查询条件
     * @return 配送单列表
     */
    List<TmsDeliveryOrder> queryPageByCondition(DeliveryOrderQuery deliveryOrderQuery);
}
