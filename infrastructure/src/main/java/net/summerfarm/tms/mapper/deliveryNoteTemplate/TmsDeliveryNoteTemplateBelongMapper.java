package net.summerfarm.tms.mapper.deliveryNoteTemplate;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateBelongQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Mapper
public interface TmsDeliveryNoteTemplateBelongMapper extends BaseMapper<TmsDeliveryNoteTemplateBelong> {
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(TmsDeliveryNoteTemplateBelong record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(TmsDeliveryNoteTemplateBelong record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    TmsDeliveryNoteTemplateBelong selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<TmsDeliveryNoteTemplateBelong> selectByCondition(TmsDeliveryNoteTemplateBelongQueryParam param);

}

