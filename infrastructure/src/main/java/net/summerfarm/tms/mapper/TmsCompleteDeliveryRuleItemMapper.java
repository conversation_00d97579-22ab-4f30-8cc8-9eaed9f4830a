package net.summerfarm.tms.mapper;

import net.summerfarm.tms.dao.TmsCompleteDeliveryRuleItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 完成配送提醒规则项 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-79 13:34:17
 */
public interface TmsCompleteDeliveryRuleItemMapper extends BaseMapper<TmsCompleteDeliveryRuleItem> {

    /**
     * 批量插入
     * @param tmsCompleteDeliveryRuleItems 数据集合
     * @return 影响条数
     */
    void insertBatch(@Param(value = "list") List<TmsCompleteDeliveryRuleItem> tmsCompleteDeliveryRuleItems);

}
