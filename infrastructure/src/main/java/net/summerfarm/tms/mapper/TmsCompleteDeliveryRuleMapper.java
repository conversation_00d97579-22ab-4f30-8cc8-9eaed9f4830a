package net.summerfarm.tms.mapper;

import net.summerfarm.tms.alert.entity.DeliveryAlertRuleGroupEntity;
import net.summerfarm.tms.dao.TmsCompleteDeliveryRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.query.alert.DeliveryAlertRuleQuery;

import java.util.List;

/**
 * <p>
 * 完成配送提醒规则组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-79 13:34:17
 */
public interface TmsCompleteDeliveryRuleMapper extends BaseMapper<TmsCompleteDeliveryRule> {

    /**
     * 分页查询
     * @param deliveryAlertRuleQuery 查询实体
     * @return 分页实体
     */
    List<DeliveryAlertRuleGroupEntity> queryPage(DeliveryAlertRuleQuery deliveryAlertRuleQuery);
}
