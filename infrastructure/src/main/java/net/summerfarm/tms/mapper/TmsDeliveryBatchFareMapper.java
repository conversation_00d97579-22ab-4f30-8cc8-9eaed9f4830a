package net.summerfarm.tms.mapper;

import net.summerfarm.tms.dao.TmsDeliveryBatchFare;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 配送批次运费明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-150 11:08:26
 */
public interface TmsDeliveryBatchFareMapper extends BaseMapper<TmsDeliveryBatchFare> {

    /**
     * 批量插入
     * @param tmsDeliveryBatchFareList 数据集合
     * @return 影响条数
     */
    int insertBatch(@Param(value = "list") List<TmsDeliveryBatchFare> tmsDeliveryBatchFareList);
}
