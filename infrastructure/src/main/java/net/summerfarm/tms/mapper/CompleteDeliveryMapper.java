package net.summerfarm.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.alert.dto.CompleteDeliveryCommand;
import net.summerfarm.tms.alert.dto.CompleteDeliveryQuery;
import net.summerfarm.tms.alert.dto.CompleteDeliveryVO;
import net.summerfarm.tms.dao.CompleteDelivery;
import net.summerfarm.tms.query.alert.CompleteDeliveryTimeQueryParam;
import org.apache.ibatis.annotations.Param;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR> 2021/07/09
 * 完成配送提醒
 */
public interface CompleteDeliveryMapper extends BaseMapper<net.summerfarm.tms.dao.CompleteDelivery > {

    /**
     * 完成配送提醒-新增消息提醒
     */
    void addCompleteDelivery(CompleteDeliveryCommand completeDeliveryCommand);

    /**
     * 完成配送提醒-分页查询接口
     * @param completeDeliveryQuery
     * @return
     */
    List<CompleteDeliveryVO> select(CompleteDeliveryQuery completeDeliveryQuery);

    /**
     * 完成配送提醒-编辑消息提醒接口
     * @return
     */
    int update(@Param("id") Integer id, @Param("completeDeliveryTime") String completeDeliveryTime, @Param("realName") String realName);

    /**
     * 删除完成配送提醒
     */
    int delete(@Param("id") Integer id);

    /**
     * 修改状态 0 正常 1 暂停
     * @param id
     * @return
     */
    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    CompleteDelivery selectById(@Param("id")Integer id);

    /**
     * 查询最晚配送时效
     * @param query 查询
     * @return 结果
     */
    List<String> queryListAreaLastTime(@Param("query") CompleteDeliveryTimeQueryParam query);
}
