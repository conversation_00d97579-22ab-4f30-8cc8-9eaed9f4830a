package net.summerfarm.tms.mapper;

import net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailEntity;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 履约审核详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-179 16:03:34
 */
public interface TmsDeliveryPerformanceReviewDetailMapper extends BaseMapper<TmsDeliveryPerformanceReviewDetail> {

    long queryTrunkCountByTaskIdAndState(@Param("performanceReviewTaskId") Long performanceReviewTaskId,@Param("state") Integer state);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<TmsDeliveryPerformanceReviewDetailEntity> getPage(TmsDeliveryPerformanceReviewDetailQueryParam param);
}
