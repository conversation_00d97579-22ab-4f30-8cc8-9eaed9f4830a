package net.summerfarm.tms.schedulex.delivery;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.gray.old2new.DistOrderSyncService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

/**
 * Description:配送单-配送点位内部对账检测定时任务
 * date: 2023/11/28 14:27
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliverySiteCheckV2Processor extends XianMuJavaProcessorV2 {

    @Resource
    private DistOrderSyncService distOrderSyncService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("start DeliverySiteCheckV2Processor:{}", context.getInstanceParameters());
        try {
            LocalDate beginDate = LocalDate.now();
            if (StringUtils.isEmpty(context.getInstanceParameters())) {
                beginDate = LocalDate.now().plusDays(1);
            } else {
                beginDate = LocalDate.parse(context.getInstanceParameters(), DateTimeFormatter.ISO_LOCAL_DATE);
            }

            TmsResult<Void> result = distOrderSyncService.deliverySiteSyncCompare(beginDate, false);

            if (!result.isSuccess()){
                HashMap<String, String> msgMap = new HashMap<>();
                msgMap.put("text",result.getErrorMessage());
                msgMap.put("title","配送单-配送点位内部对账检测告警通知");
                //配置中发送群机器人的url
                DingTalkRobotUtil.sendMsgAndAtAll("markdown", "https://open.feishu.cn/open-apis/bot/v2/hook/171b6a47-3f4d-4d11-ae91-9136126cf504", () -> msgMap);
            }
            return new ProcessResult(InstanceStatus.SUCCESS, "定时任务比对成功");
        }catch (Exception e){
            log.error("数据同步结果比对 任务异常", e);
            return new ProcessResult(InstanceStatus.FAILED, e.getMessage());
        }

    }
}
