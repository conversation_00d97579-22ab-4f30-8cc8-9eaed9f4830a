package net.summerfarm.tms.schedulex.delivery;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.common.EventBusService;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliverySiteRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/12/9 18:08<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class BatchRealDisProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;
    @Resource
    private DeliverySiteRepository deliverySiteRepository;
    @Resource
    private SiteRepository siteRepository;
    @Resource
    private EventBusService eventBusService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("start DeliveryBatchCreateProcessor:{}", context.getInstanceParameters());
        try {
            LocalDate deliveryTime = LocalDate.now();
            if (!StringUtils.isEmpty(context.getInstanceParameters())) {
                deliveryTime = LocalDate.parse(context.getInstanceParameters(), DateTimeFormatter.ISO_LOCAL_DATE);
            }
            List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                    .deliveryTime(deliveryTime.atStartOfDay())
                    .deliveryBatchStatus(DeliveryBatchStatusEnum.IN_DELIVERY.getCode())
                    .deliveryBatchTypeList(Arrays.asList(DeliveryBatchTypeEnum.city.getCode()))
                    .build());

            for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
                List<DeliverySiteEntity> siteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(deliveryBatchEntity.getId()).build());

                //获取城配仓点位，城配仓点位可能是无需打卡没有打卡时间
                List<DeliverySiteEntity> citySiteList = siteEntities.stream()
                        .filter(deliverySiteEntity -> Objects.equals(deliverySiteEntity.getSiteId(), deliveryBatchEntity.getBeginSiteId())).collect(Collectors.toList());
                //客户配送点位
                List<DeliverySiteEntity> sendSiteList = siteEntities.stream()
                        .filter(deliverySiteEntity -> !Objects.equals(deliverySiteEntity.getSiteId(), deliveryBatchEntity.getBeginSiteId()))
                        .filter(deliverySiteEntity -> !Objects.equals(DeliverySiteInterceptStateEnum.allIntecept.getCode(), deliverySiteEntity.getInterceptState()))
                        .filter(deliverySiteEntity -> DeliverySiteStatusEnum.FINISH_DELIVERY == deliverySiteEntity.getStatus())
                        .filter(deliverySiteEntity -> deliverySiteEntity.getSignInTime() != null)
                        .collect(Collectors.toList());

                if (sendSiteList.stream().anyMatch(a -> Objects.equals(a.getSendWay(), DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()))) {
                    sendSiteList = sendSiteList.stream().filter(deliverySiteEntity ->
                            Objects.equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue(), deliverySiteEntity.getSendWay())).collect(Collectors.toList());
                }
                sendSiteList.sort(Comparator.comparing(DeliverySiteEntity::getSignInTime));

                List<Long> siteIds = Lists.newArrayList();
                siteIds.add(citySiteList.get(0).getSiteId());
                siteIds.addAll(sendSiteList.stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList()));

                List<SiteEntity> siteEntitys = siteRepository.queryPrimaryIdList(siteIds);
                Map<Long, SiteEntity> siteEntityMap = siteEntitys.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));


                //保存路段信息和实际配送距离
                List<WaypointsInput> waypointsInputList = new ArrayList<>();
                SiteEntity beginSite = siteEntityMap.get(citySiteList.get(0).getSiteId());
                waypointsInputList.add(WaypointsInput.builder().siteId(beginSite.getId()).poi(beginSite.getPoi()).build());
                for (DeliverySiteEntity siteEntity : sendSiteList) {
                    SiteEntity sendSite = siteEntityMap.get(siteEntity.getSiteId());
                    if (Objects.nonNull(sendSite)) {
                        waypointsInputList.add(WaypointsInput.builder().siteId(sendSite.getId()).poi(sendSite.getPoi()).build());
                    }
                }
                //消息去消费处理距离问题
                CalcTmsPathDistanceMessage calcIntelligencePath = CalcTmsPathDistanceMessage.builder()
                        .batchId(deliveryBatchEntity.getId())
                        .type(DeliverySectionEnums.Type.FINISH_SEND).waypointsInputList(waypointsInputList).build();
                eventBusService.calcPathDistance(calcIntelligencePath);
            }
            return new ProcessResult(InstanceStatus.SUCCESS, "定时任务计算生成配送距离数据成功");
        } catch (Exception e) {
            log.error("定时任务计算生成配送距离数据 任务异常", e);
            throw e;
        }
    }

}
