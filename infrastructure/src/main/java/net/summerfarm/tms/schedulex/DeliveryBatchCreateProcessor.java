package net.summerfarm.tms.schedulex;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.enums.PathAutoSwitchEnum;
import net.summerfarm.tms.enums.PathPeriodTypeEnum;
import net.summerfarm.tms.enums.TmsPathStatusEnum;
import net.summerfarm.tms.enums.TmsPathTypeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.path.TmsPathService;
import net.summerfarm.tms.path.dto.PathCarDTO;
import net.summerfarm.tms.path.dto.TmsPathDTO;
import net.summerfarm.tms.query.path.PathQuery;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliveryBatchCreateProcessor extends XianMuJavaProcessorV2 {

    @Resource
    TmsPathService tmsPathService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("start DeliveryBatchCreateProcessor:{}", context.getJobParameters());
        try {
            LocalDate bizDate = LocalDate.now();

            if (StringUtils.isEmpty(context.getJobParameters())) {
                bizDate = LocalDate.now().plusDays(1);
            } else {
                bizDate = LocalDate.parse(context.getJobParameters(), DateTimeFormatter.ISO_LOCAL_DATE);
            }
            PathQuery pathQuery = new PathQuery();
            pathQuery.setStatus(TmsPathStatusEnum.ACTIVE.code);
            pathQuery.setType(TmsPathTypeEnum.MAIN_ROAD.code);
            pathQuery.setAutoSwitch(PathAutoSwitchEnum.GENERATE.getValue());
            pathQuery.setNeedPagination(false);
            TmsResult<PageInfo<TmsPathDTO>> pageInfoTmsResult = tmsPathService.queryList(pathQuery);
            if (!pageInfoTmsResult.isSuccess()) {
                log.error("查询path失败:{} {}", pageInfoTmsResult.getErrCode(), pageInfoTmsResult.getErrorMessage());
                return new ProcessResult(InstanceStatus.FAILED);
            }
            List<TmsPathDTO> tmsPathDTOList = pageInfoTmsResult.getData().getList();
            StringBuilder result = new StringBuilder();
            for (TmsPathDTO tmsPathDTO : tmsPathDTOList) {
                List<PathCarDTO> pathCarList = tmsPathDTO.getPathCarList();
                if (CollectionUtils.isEmpty(pathCarList)) {
                    continue;
                }
                for (PathCarDTO pathCarDTO : pathCarList) {
                    if (Objects.equals(pathCarDTO.getPeriodType(), PathPeriodTypeEnum.AUTO_CREATE.getCode())){
                        // 不需要处理
                        log.info("有承运单时自动创建类型,不需要自动创建,pathId:{},车次Id:{}", tmsPathDTO.getPathId(), pathCarDTO.getCarId());
                        continue;
                    }
                    try {
                        if (Objects.equals(pathCarDTO.getPeriodType(), PathPeriodTypeEnum.EVERY_WEEK.getCode())) {
                            List<Integer> everyWeekDays = Stream.of(pathCarDTO.getEveryWeekType().split(","))
                                    .map(Integer::valueOf)
                                    .collect(Collectors.toList());
                            if (!everyWeekDays.contains(0)) {
                                if (!everyWeekDays.contains(bizDate.getDayOfWeek().getValue())) {
                                    log.info("不是周期日{}, 不需要自动创建,pathId:{},车次Id:{}", bizDate, tmsPathDTO.getPathId(),
                                            pathCarDTO.getCarId());
                                    continue;
                                }
                            }
                            TmsResult<DeliveryBatchEntity> tmsResult = tmsPathService.createDeliveryBatchByPath(tmsPathDTO, bizDate, pathCarDTO);
                            if (!tmsResult.isSuccess()) {
                                result.append(" pathId:")
                                        .append(tmsPathDTO.getPathId())
                                        .append(" 失败:")
                                        .append(tmsResult.getErrorMessage());
                            }
                        } else {
                            if (bizDate.isBefore(pathCarDTO.getStartTime())) {
                                log.info("当前时间在开始计算时间之前, 不需要自动创建,bizDate:{},pathId:{},车次Id:{}", bizDate, tmsPathDTO.getPathId(), pathCarDTO.getCarId());
                                continue;
                            }
                            long days = Math.abs(pathCarDTO.getStartTime().toEpochDay() - bizDate.toEpochDay()) - 1;
                            if (pathCarDTO.getIntervalDays() > 0) {
                                if (days == 0 || days % pathCarDTO.getIntervalDays() != 0) {
                                    log.info("每天条件下间隔天数不满足, 不需要自动创建,bizDate:{},pathId:{},车次Id:{}", bizDate, tmsPathDTO.getPathId(), pathCarDTO.getCarId());
                                    continue;
                                }
                            }
                            TmsResult<DeliveryBatchEntity> tmsResult = tmsPathService.createDeliveryBatchByPath(tmsPathDTO, bizDate, pathCarDTO);
                            if (!tmsResult.isSuccess()) {
                                result.append(" pathId:")
                                        .append(tmsPathDTO.getPathId())
                                        .append(" 失败:")
                                        .append(tmsResult.getErrorMessage());
                            }


                        }
                    } catch (TmsRuntimeException e) {
                        log.error("路由生成调度单异常，pathId:{},车次Id:{}", tmsPathDTO.getPathId(), pathCarDTO.getCarId(), e);
                    }
                }

            }
            return new ProcessResult(InstanceStatus.SUCCESS, result.toString());
        } catch (Exception e) {
            log.error("定时生成调度单 任务异常", e);
            throw e;
        }
    }

}
