package net.summerfarm.tms.schedulex;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.gray.old2new.DistOrderSyncService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * Description:数据同步结果比对
 * date: 2022/11/8 15:51
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataSyncResultCompareProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private DistOrderSyncService distOrderSyncService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("start DataSyncResultCompareProcessor:{}", context.getInstanceParameters());
        try {
            LocalDate beginDate = null;
            Boolean flag = null;
            String paramStr = context.getInstanceParameters();
            if (paramStr != null) {
                String[] items = paramStr.split(",");
                if (!StringUtils.isEmpty(items[0])) {
                    beginDate = LocalDate.parse(items[0]);
                }
                if (items.length > 1) {
                    flag = Boolean.valueOf(items[1]);
                }
            }
            TmsResult<Void> result = null;
            for (int i = 0; i < 3; i++) {
                result = distOrderSyncService.dataSyncResultCompare(beginDate, flag, null);
                if (result.isSuccess()){
                    break;
                }
                Thread.sleep(1000);
            }
            return new ProcessResult(result.isSuccess() ?
                    InstanceStatus.SUCCESS : InstanceStatus.FAILED, result.getErrorMessage());
        } catch (Exception e) {
            log.error("数据同步结果比对 任务异常", e);
            return new ProcessResult(InstanceStatus.FAILED, e.getMessage());
        }
    }

}
