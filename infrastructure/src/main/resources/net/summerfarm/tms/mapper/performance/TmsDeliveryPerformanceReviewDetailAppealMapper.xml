<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.performance.TmsDeliveryPerformanceReviewDetailAppealMapper">

    <select id="queryReviewDetailAppealPage"  parameterType="net.summerfarm.tms.query.performance.AppealQuery" resultType="net.summerfarm.tms.performance.entity.dataobject.ReviewDetailAppealPageObj">
        select t.id,
               t.performance_review_task_id,
               ta.review_task_type,
               d.delivery_time,
               d.driver_phone,
               d.car_number,
               d.car_storage,
               d.car_type,
               d.outer_client_name,
               d.path_code,
               d.sequence,
               d.path_name,
               t.create_time,
               d.outer_client_name,
               d.car_storage,
               d.sequence,
               d.driver_name,
               d.penalty_money,
               t.status,
               t.store_name
        from tms_delivery_performance_review_detail_appeal t
                inner join tms_delivery_performance_review_detail d on t.del_perf_detail_review_id = d.id
                inner join tms_delivery_performance_review_task ta on t.performance_review_task_id = ta.id
                inner join tms_delivery_batch b on d.delivery_batch_id = b.id
        <where>
            <if test="id != null">
                and t.id = #{id}
            </if>
            <if test="performanceReviewTaskId != null">
                and t.performance_review_task_id = #{performanceReviewTaskId}
            </if>
            <if test="storeNo != null">
                and t.store_no = #{storeNo}
            </if>
            <if test="driverId != null">
                and b.driver_id = #{driverId}
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
            <if test="beginDeliveryTime != null">
                and d.delivery_time &gt;= #{beginDeliveryTime}
            </if>
            <if test="endDeliveryTime != null">
                and d.delivery_time &lt;= #{endDeliveryTime}
            </if>
            <if test="beginCreateTime != null">
                and t.create_time &gt;= #{beginCreateTime}
            </if>
            <if test="endCreateTime != null">
                and t.create_time &lt;= #{endCreateTime}
            </if>
            <if test="outerClientName != null">
                and d.outer_client_name = #{outerClientName}
            </if>
        </where>
        order by t.id desc
    </select>
</mapper>
