<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.tms.mapper.CompleteDeliveryMapper">

    <resultMap id="ResultMap" type="net.summerfarm.tms.dao.CompleteDelivery">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="store_no" property="storeNo" jdbcType="INTEGER"/>
        <result column="complete_delivery_time" property="completeDeliveryTime" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="addCompleteDelivery" parameterType="net.summerfarm.tms.alert.dto.CompleteDeliveryCommand" useGeneratedKeys="true" keyProperty="id">
        insert into complete_delivery (complete_delivery_time,status,create_time,creator,updater,store_no,update_time,city)
        values
            (#{completeDeliveryTime},0,now(),#{creator},#{updater},#{storeNo},now(),#{city})
    </insert>

    <select id="select" parameterType="net.summerfarm.tms.alert.dto.CompleteDeliveryQuery" resultType="net.summerfarm.tms.alert.dto.CompleteDeliveryVO">
        select wlc.region region,wlc.store_no storeNo,wlc.store_name storeName,cd.complete_delivery_time completeDeliveryTime,cd.status status,cd.updater updater,cd.id,cd.city
        from complete_delivery cd
        left join warehouse_logistics_center wlc on cd.store_no = wlc.store_no
        <where>
            <if test="region != null">
                and wlc.region = #{region}
            </if>
            <if test="storeNo != null">
                and cd.store_no = #{storeNo}
            </if>
            <if test="city != null">
                and cd.city like concat('%',#{city},'%')
            </if>
            <if test="completeDeliveryTime != null">
                and cd.complete_delivery_time = #{completeDeliveryTime}
            </if>
            <if test="regions != null and regions.size > 0">
                and wlc.region in
                <foreach collection="regions" item="region" open="(" close=")" separator=",">
                    #{region}
                </foreach>
            </if>
            <if test="storeNos != null and storeNos.size > 0">
                and cd.store_no in
                <foreach collection="storeNos" item="storeNo" open="(" close=")" separator=",">
                    #{storeNo}
                </foreach>
            </if>
        </where>
    </select>

    <update id="update">
        update complete_delivery
        <set>
            <if test="completeDeliveryTime != null">
                complete_delivery_time = #{completeDeliveryTime},
            </if>
            <if test="realName != null">
                updater = #{realName},
            </if>
            update_time = now(),
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="Integer">
        delete from complete_delivery
        where id = #{id}
    </delete>

    <update id="updateStatus">
        update complete_delivery
        set status = #{status}
        where id = #{id}
    </update>

    <select id="selectById" resultType="net.summerfarm.tms.dao.CompleteDelivery">
        SELECT	t.city  FROM complete_delivery t where t.id = #{id}
    </select>

    <select id="queryListAreaLastTime" resultType="java.lang.String">
        SELECT
            complete_delivery_time
        FROM
            `complete_delivery` t
                LEFT JOIN complete_delivery_ad_code_mapping c ON t.id = c.complete_delivery_id
                LEFT JOIN ad_code_msg a ON c.ad_code = a.ad_code
                AND a.`status` = #{query.adCodeStatus}
        WHERE
            t.`status` = #{query.completeDeliveryStatus}
          AND a.city = #{query.city}
          <if test="query.area != null and query.area != ''">
              AND a.area = #{query.area}
          </if>
    </select>
</mapper>