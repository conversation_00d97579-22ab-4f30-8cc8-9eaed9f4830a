<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDriverMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsDriver">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="cooperation_cycle" jdbcType="TINYINT" property="cooperationCycle"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"/>
        <result column="id_card_front_pic" jdbcType="VARCHAR" property="idCardFrontPic"/>
        <result column="id_card_behind_pic" jdbcType="VARCHAR" property="idCardBehindPic"/>
        <result column="driver_pics" jdbcType="VARCHAR" property="driverPics"/>
        <result column="admin_id" jdbcType="INTEGER" property="adminId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="city_site_id" property="citySiteId"/>
        <result column="city_carrier_id" property="cityCarrierId"/>
        <result column="base_user_id" property="baseUserId"/>
        <result column="keep_temperature_method_audit" property="keepTemperatureMethodAudit"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, name, phone, cooperation_cycle, business_type, status, id_card, id_card_front_pic,
        id_card_behind_pic, driver_pics, admin_id, create_time, update_time,city_site_id,city_carrier_id,base_user_id,
        keep_temperature_method_audit
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tms_driver
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tms_driver
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="net.summerfarm.tms.dao.TmsDriver" keyColumn="id" keyProperty="id"
            useGeneratedKeys="true">
        insert into tms_driver (id, name, phone,
        cooperation_cycle, business_type, status,
        id_card, id_card_front_pic, id_card_behind_pic,
        driver_pics, admin_id, create_time,
        update_time,city_site_id,city_carrier_id,base_user_id,keep_temperature_method_audit)
        values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
        #{cooperationCycle,jdbcType=TINYINT}, #{businessType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT},
        #{idCard,jdbcType=VARCHAR}, #{idCardFrontPic,jdbcType=VARCHAR}, #{idCardBehindPic,jdbcType=VARCHAR},
        #{driverPics,jdbcType=VARCHAR}, #{adminId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},#{citySiteId},#{cityCarrierId},#{baseUserId},#{keepTemperatureMethodAudit})
    </insert>
    <insert id="insertSelective" parameterType="net.summerfarm.tms.dao.TmsDriver">
        insert into tms_driver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="cooperationCycle != null">
                cooperation_cycle,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="idCard != null">
                id_card,
            </if>
            <if test="idCardFrontPic != null">
                id_card_front_pic,
            </if>
            <if test="idCardBehindPic != null">
                id_card_behind_pic,
            </if>
            <if test="driverPics != null">
                driver_pics,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="keepTemperatureMethodAudit != null">
                keep_temperature_method_audit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="cooperationCycle != null">
                #{cooperationCycle,jdbcType=TINYINT},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="idCard != null">
                #{idCard,jdbcType=VARCHAR},
            </if>
            <if test="idCardFrontPic != null">
                #{idCardFrontPic,jdbcType=VARCHAR},
            </if>
            <if test="idCardBehindPic != null">
                #{idCardBehindPic,jdbcType=VARCHAR},
            </if>
            <if test="driverPics != null">
                #{driverPics,jdbcType=VARCHAR},
            </if>
            <if test="adminId != null">
                #{adminId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="keepTemperatureMethodAudit != null">
                #{keepTemperatureMethodAudit,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.tms.dao.TmsDriver">
        update tms_driver
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="cooperationCycle != null">
                cooperation_cycle = #{cooperationCycle,jdbcType=TINYINT},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="idCard != null">
                id_card = #{idCard,jdbcType=VARCHAR},
            </if>
            <if test="idCardFrontPic != null">
                id_card_front_pic = #{idCardFrontPic,jdbcType=VARCHAR},
            </if>
            <if test="idCardBehindPic != null">
                id_card_behind_pic = #{idCardBehindPic,jdbcType=VARCHAR},
            </if>
            <if test="driverPics != null">
                driver_pics = #{driverPics,jdbcType=VARCHAR},
            </if>
            <if test="adminId != null">
                admin_id = #{adminId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="keepTemperatureMethodAudit != null">
                keep_temperature_method_audit = #{keepTemperatureMethod},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.tms.dao.TmsDriver">
        update tms_driver
        set name = #{name,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        cooperation_cycle = #{cooperationCycle,jdbcType=TINYINT},
        business_type = #{businessType,jdbcType=TINYINT},
        status = #{status,jdbcType=TINYINT},
        id_card = #{idCard,jdbcType=VARCHAR},
        id_card_front_pic = #{idCardFrontPic,jdbcType=VARCHAR},
        id_card_behind_pic = #{idCardBehindPic,jdbcType=VARCHAR},
        driver_pics = #{driverPics,jdbcType=VARCHAR},
        admin_id = #{adminId,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        city_site_id = #{citySiteId},
        city_carrier_id = #{cityCarrierId},
        keep_temperature_method_audit = #{keepTemperatureMethodAudit,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByTmsDriver" parameterType="net.summerfarm.tms.dao.TmsDriver" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tms_driver t
        <where>
            <if test="phone != null and phone !=''">
                and phone = #{phone}
            </if>
        </where>
        limit 1
    </select>

    <select id="selectListByTmsDriver" parameterType="net.summerfarm.tms.query.base.driver.DriverQuery" resultMap="BaseResultMap">
        select
        id, name, phone, cooperation_cycle, business_type, status, id_card, id_card_front_pic,
        id_card_behind_pic, driver_pics, admin_id, create_time, IFNULL(update_time,create_time) update_time,
        city_site_id,city_carrier_id,keep_temperature_method_audit
        from
        tms_driver t
        <where>
            <if test="id != null">
                and t.id = #{id}
            </if>
            <if test="phone != null and phone !=''">
                and t.phone = #{phone}
            </if>
            <if test="name != null and name !=''">
                and t.name like CONCAT('%',#{name},'%')
            </if>
            <if test="cooperationCycle != null">
                and t.cooperation_cycle = #{cooperationCycle}
            </if>
            <if test="businessType != null">
                and (t.business_type = #{businessType} or t.business_type = 2)
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
            <if test="citySiteId != null">
                and t.city_site_id = #{citySiteId}
            </if>
            <if test="cityCarrierId != null">
                and t.city_carrier_id = #{cityCarrierId}
            </if>
            <if test="citySiteIds != null and citySiteIds.size > 0">
                and t.city_site_id in
                <foreach collection="citySiteIds" item="citySiteId" open="(" close=")" separator=",">
                    #{citySiteId}
                </foreach>
            </if>
            <if test="keepTemperatureMethodAudit != null">
                and t.keep_temperature_method_audit = #{keepTemperatureMethodAudit}
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectOtherByPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tms_driver t
        <where>
            <if test="phone != null and phone !=''">
                and t.phone = #{phone}
            </if>
            <if test="id != null">
                and t.id <![CDATA[ <>  ]]> #{id}
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tms_driver
        <where>
            status = 0
            <if test="driverIds != null">
                and id in
                <foreach collection="driverIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


</mapper>