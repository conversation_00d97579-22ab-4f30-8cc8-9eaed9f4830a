<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryBatchMapper">
    <select id="queryBoardData" parameterType="net.summerfarm.tms.query.board.BoardQuery" resultType="net.summerfarm.tms.delivery.entity.BoardEntity">
        SELECT
            td.id       as deliveryCarId,
            td.`name`   as driver,
            td.phone    as phone,
            t.path_code as path,
            COUNT( CASE WHEN s.`status` = 22 THEN 1 END ) as deliveredCount,
            tc.`id`     as tmsCarId,
            tc.car_number as carNumber,
            COUNT( s.id ) - 1 - COUNT( CASE WHEN s.`status` = 22 THEN 1 END ) as unDeliveredCount,
            COUNT( s.id ) - 1 as deliveryAmount,
            t.`delivery_time` as deliveryTime,
            SUM( s.out_distance ) as outDistanceCount,
            IFNULL(subtable.isShort,0) as shortCount,
            tds.`out_business_no` as storeNo,
            tds.name              as storeName
        FROM
            tms_delivery_batch t
                LEFT JOIN `tms_dist_site` tds on tds.`id` =t.`begin_site_id`
                LEFT JOIN tms_driver td ON td.id = t.driver_id
                LEFT JOIN tms_car tc ON tc.id = t.car_id
                LEFT JOIN tms_delivery_site s ON t.id = s.delivery_batch_id
                LEFT JOIN (
                    SELECT t.`id`,SUM(isShort) isShort FROM
                          (
                            SELECT
                                t.`id`,
                                IF( COUNT( a.id ) <![CDATA[>]]> 0, 1, 0 ) isShort
                            FROM
                                `tms_delivery_batch` t
                                    LEFT JOIN `tms_delivery_site` s ON t.`id` = s.`delivery_batch_id`
                                    LEFT JOIN `tms_delivery_site_item` a ON a.`delivery_site_id` = s.`id`
                            WHERE
                                t.delivery_time <![CDATA[>=]]> #{deliveryTime}
                                AND t.delivery_time <![CDATA[<=]]> #{deliveryEndTime}
                                AND t.status != 10
                                AND t.`path_id` != - 1
                                AND t.type = 5
                                AND a.`short_count` <![CDATA[>]]> 0
                                <if test="siteIds != null">
                                    AND t.`begin_site_id` in
                                    <foreach collection="siteIds" item="siteId" index="index" open="(" close=")" separator=",">
                                        #{siteId}
                                    </foreach>
                                </if>
                            GROUP BY
                                t.`id`,s.`id`
                    ) t GROUP BY t.`id`
                ) subtable ON subtable.id = t.id
        WHERE
            t.delivery_time <![CDATA[>=]]> #{deliveryTime}
            AND t.delivery_time <![CDATA[<=]]> #{deliveryEndTime}
            AND t.`path_id` != - 1
            AND t.type = 5
            AND t.status != 10
            AND s.intercept_state IN ( 0, 2 )
        <if test="siteIds != null">
            AND t.`begin_site_id` in
            <foreach collection="siteIds" item="siteId" index="index" open="(" close=")" separator=",">
                #{siteId}
            </foreach>
        </if>
        GROUP BY
            t.begin_site_id,
            t.id,
            t.`delivery_time`
        ORDER BY
            t.`begin_site_id` ASC,
            unDeliveredCount DESC,
            deliveryTime desc
    </select>

    <select id="queryByIdForceMaster" parameterType="java.lang.Long" resultType="net.summerfarm.tms.dao.TmsDeliveryBatch">
        /*FORCE_MASTER*/ SELECT t.id,t.be_path_time as bePathTime,status FROM tms_delivery_batch t where t.id = #{batchId}
    </select>

    <select id="selectBatchCarLoad" resultType="net.summerfarm.tms.delivery.vo.BatchCarLoadStaticVO">
        /*FORCE_MASTER*/
        SELECT
        IFNULL(SUM(tc.`volume`),0) AS loadVolume,
        IFNULL(SUM(tc.`weight`),0) AS loadWeight,
        IFNULL(SUM(tc.`quantity`),0) AS loadQuantity
        FROM `tms_delivery_batch` tdb
        LEFT JOIN `tms_car` tc ON  tdb.`car_id` = tc.`id`
        <where>
            <if test="list != null and list.size > 0">
                AND tdb.`id` IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateBatchLoadRatioById" parameterType="net.summerfarm.tms.delivery.vo.BatchLoadRatioVO">
        UPDATE tms_delivery_batch
        SET volume_load_ratio = #{volumeLoadRatio},
            weight_load_ratio = #{weightLoadRatio},
            quantity_load_ratio = #{quantityLoadRatio}
        WHERE id = #{batchId}
    </update>
</mapper>
