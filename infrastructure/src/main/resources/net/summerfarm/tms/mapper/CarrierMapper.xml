<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.CarrierMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.Carrier">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="carrier_name" jdbcType="VARCHAR" property="carrierName"/>
        <result column="director" jdbcType="VARCHAR" property="director"/>
        <result column="director_phone" jdbcType="CHAR" property="directorPhone"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="cooperation_agreement" jdbcType="VARCHAR" property="cooperationAgreement"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, create_time, update_time, carrier_name, director, director_phone, address, cooperation_agreement,
        business_type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from carrier
        where id = #{id,jdbcType=BIGINT}
    </select>
</mapper>