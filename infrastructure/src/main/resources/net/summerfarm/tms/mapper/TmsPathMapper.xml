<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsPathMapper">


    <select id="queryList" resultType="net.summerfarm.tms.base.path.entity.TmsPathEntity">
        select t.id as pathId,
        t.status,
        t.region,
        t.path_name,
        t.distance,
        t.create_time,
        t.updater,
        t.update_time
        from tms_path t
        where t.type = 1
        <if test="filter.regionLike != null and filter.regionLike.trim() !=  ''">
            and t.region like concat('%',#{filter.regionLike},'%')
        </if>
        <if test="filter.beginSiteIdEquals != null">
            and t.begin_site_id = #{filter.beginSiteIdEquals}
        </if>
        <if test="filter.endSiteIdEquals != null">
            and t.end_site_id = #{filter.endSiteIdEquals}
        </if>
        <if test="filter.statusEquals != null">
            and t.status = #{filter.statusEquals}
        </if>
        order by t.id desc
    </select>
</mapper>
