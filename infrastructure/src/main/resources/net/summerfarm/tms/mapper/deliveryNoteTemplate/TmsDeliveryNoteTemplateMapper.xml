<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.deliveryNoteTemplate.TmsDeliveryNoteTemplateMapper">
    <!-- 结果集映射 -->
    <resultMap id="tmsDeliveryNoteTemplateResultMap" type="net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplate">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="delivery_note_name" property="deliveryNoteName" jdbcType="VARCHAR"/>
		<result column="app_source" property="appSource" jdbcType="INTEGER"/>
		<result column="use_state" property="useState" jdbcType="INTEGER"/>
		<result column="last_operator_name" property="lastOperatorName" jdbcType="VARCHAR"/>
		<result column="front_page_str" property="frontPageStr" jdbcType="LONGVARCHAR"/>
		<result column="show_price_flag" property="showPriceFlag" jdbcType="INTEGER"/>
		<result column="scope_type" property="scopeType" jdbcType="INTEGER"/>
        <result column="belong_business_name" property="belongBusinessName" jdbcType="VARCHAR"/>
        <result column="show_price_template_oss_url" property="showPriceTemplateOssUrl" jdbcType="VARCHAR"/>
        <result column="no_show_price_template_oss_url" property="noShowPriceTemplateOssUrl" jdbcType="VARCHAR"/>

    </resultMap>

    <!-- 列定义 -->
    <sql id="tmsDeliveryNoteTemplateColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.delivery_note_name,
          t.app_source,
          t.use_state,
          t.last_operator_name,
          t.front_page_str,
          t.show_price_flag,
          t.scope_type,
          t.belong_business_name,
          t.show_price_template_oss_url,
          t.no_show_price_template_oss_url
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="deliveryNoteName != null and deliveryNoteName !=''">
                AND t.delivery_note_name = #{deliveryNoteName}
            </if>
			<if test="appSource != null">
                AND t.app_source = #{appSource}
            </if>
			<if test="useState != null">
                AND t.use_state = #{useState}
            </if>
			<if test="lastOperatorName != null and lastOperatorName !=''">
                AND t.last_operator_name = #{lastOperatorName}
            </if>
			<if test="frontPageStr != null and frontPageStr !=''">
                AND t.front_page_str = #{frontPageStr}
            </if>
			<if test="showPriceFlag != null">
                AND t.show_price_flag = #{showPriceFlag}
            </if>
			<if test="scopeType != null">
                AND t.scope_type = #{scopeType}
            </if>
            <if test="belongBusinessName != null">
                AND t.belong_business_name like CONCAT('%',#{belongBusinessName},'%')
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="deliveryNoteName != null">
                    t.delivery_note_name = #{deliveryNoteName},
                </if>
                <if test="appSource != null">
                    t.app_source = #{appSource},
                </if>
                <if test="useState != null">
                    t.use_state = #{useState},
                </if>
                <if test="lastOperatorName != null">
                    t.last_operator_name = #{lastOperatorName},
                </if>
                <if test="frontPageStr != null">
                    t.front_page_str = #{frontPageStr},
                </if>
                <if test="showPriceFlag != null">
                    t.show_price_flag = #{showPriceFlag},
                </if>
                <if test="scopeType != null">
                    t.scope_type = #{scopeType},
                </if>
                <if test="belongBusinessName != null">
                    t.belong_business_name = #{belongBusinessName},
                </if>
                <if test="showPriceTemplateOssUrl != null">
                    t.show_price_template_oss_url = #{showPriceTemplateOssUrl},
                </if>
                <if test="noShowPriceTemplateOssUrl != null">
                    t.no_show_price_template_oss_url = #{noShowPriceTemplateOssUrl},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="tmsDeliveryNoteTemplateResultMap" >
        SELECT <include refid="tmsDeliveryNoteTemplateColumns" />
        FROM tms_delivery_note_template t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateQueryParam"  resultType="net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.delivery_note_name deliveryNoteName,
            t.app_source appSource,
            t.use_state useState,
            t.last_operator_name lastOperatorName,
            t.front_page_str frontPageStr,
            t.show_price_flag showPriceFlag,
            t.scope_type scopeType,
            t.belong_business_name belongBusinessName,
            t.show_price_template_oss_url,
            t.no_show_price_template_oss_url
        FROM tms_delivery_note_template t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateQueryParam" resultMap="tmsDeliveryNoteTemplateResultMap" >
        SELECT <include refid="tmsDeliveryNoteTemplateColumns" />
        FROM tms_delivery_note_template t
        <include refid="whereColumnBySelect"></include>
    </select>


  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplate" >
        UPDATE tms_delivery_note_template t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplate" >
        DELETE FROM tms_delivery_note_template
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>