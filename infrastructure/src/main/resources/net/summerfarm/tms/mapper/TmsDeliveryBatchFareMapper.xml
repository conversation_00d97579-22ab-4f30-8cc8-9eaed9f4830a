<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryBatchFareMapper">

    <insert id="insertBatch">
        insert into tms_delivery_batch_fare (delivery_batch_id, fare_type,amount, creator)
        values
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="item" separator=",">
                (#{item.deliveryBatchId}, #{item.fareType},#{item.amount}, #{item.creator})
            </foreach>
        </if>
    </insert>
</mapper>
