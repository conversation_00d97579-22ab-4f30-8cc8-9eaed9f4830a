<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelongMapper">
    <!-- 结果集映射 -->
    <resultMap id="tmsDeliveryNoteTemplateBelongResultMap" type="net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="delivery_note_template_id" property="deliveryNoteTemplateId" jdbcType="NUMERIC"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="scope_type" property="scopeType" jdbcType="INTEGER"/>
		<result column="scope_business_id" property="scopeBusinessId" jdbcType="VARCHAR"/>
		<result column="scope_business_name" property="scopeBusinessName" jdbcType="VARCHAR"/>
        <result column="app_source" property="appSource" jdbcType="INTEGER"/>

    </resultMap>

    <!-- 列定义 -->
    <sql id="tmsDeliveryNoteTemplateBelongColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.delivery_note_template_id,
          t.tenant_id,
          t.scope_type,
          t.scope_business_id,
          t.scope_business_name,
          t.app_source
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="deliveryNoteTemplateId != null">
                AND t.delivery_note_template_id = #{deliveryNoteTemplateId}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="scopeType != null">
                AND t.scope_type = #{scopeType}
            </if>
			<if test="scopeBusinessId != null and scopeBusinessId !=''">
                AND t.scope_business_id = #{scopeBusinessId}
            </if>
			<if test="scopeBusinessName != null and scopeBusinessName !=''">
                AND t.scope_business_name = #{scopeBusinessName}
            </if>
            <if test="appSource != null">
                AND t.app_source = #{appSource}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="deliveryNoteTemplateId != null">
                    t.delivery_note_template_id = #{deliveryNoteTemplateId},
                </if>
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="scopeType != null">
                    t.scope_type = #{scopeType},
                </if>
                <if test="scopeBusinessId != null">
                    t.scope_business_id = #{scopeBusinessId},
                </if>
                <if test="scopeBusinessName != null">
                    t.scope_business_name = #{scopeBusinessName},
                </if>
                <if test="appSource != null">
                    t.app_source = #{appSource},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="tmsDeliveryNoteTemplateBelongResultMap" >
        SELECT <include refid="tmsDeliveryNoteTemplateBelongColumns" />
        FROM tms_delivery_note_template_belong t
		WHERE t.id = #{id}
    </select>



    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateBelongQueryParam" resultMap="tmsDeliveryNoteTemplateBelongResultMap" >
        SELECT <include refid="tmsDeliveryNoteTemplateBelongColumns" />
        FROM tms_delivery_note_template_belong t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tms_delivery_note_template_belong
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="deliveryNoteTemplateId != null">
				  delivery_note_template_id,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="scopeType != null">
				  scope_type,
              </if>
              <if test="scopeBusinessId != null">
				  scope_business_id,
              </if>
              <if test="scopeBusinessName != null">
				  scope_business_name,
              </if>
              <if test="appSource != null">
                  app_source,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="deliveryNoteTemplateId != null">
				#{deliveryNoteTemplateId,jdbcType=NUMERIC},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="scopeType != null">
				#{scopeType,jdbcType=INTEGER},
              </if>
              <if test="scopeBusinessId != null">
				#{scopeBusinessId,jdbcType=VARCHAR},
              </if>
              <if test="scopeBusinessName != null">
				#{scopeBusinessName,jdbcType=VARCHAR},
              </if>
              <if test="appSource != null">
                #{app_source},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong" >
        UPDATE tms_delivery_note_template_belong t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.tms.dao.deliveryNoteTemplate.TmsDeliveryNoteTemplateBelong" >
        DELETE FROM tms_delivery_note_template_belong
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>


</mapper>