<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliverySiteItemMapper">

    <insert id="saveBatch" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.tms.dao.TmsDeliverySiteItem">
        INSERT INTO `tms_delivery_site_item`
            (
              `delivery_site_id`, `out_item_id`, `plan_receipt_count`, `real_receipt_count`,
             `short_count`, `intercept_count`, `reject_count`, `reject_reason`,
             `scan_count`, `noscan_count`, `noscan_reason`, `noscan_pics`,
             `type`, `remark`, `out_item_name`, `out_item_type`, `pack_type`, `temperature`
             )
             VALUES
        <foreach collection="tmsDeliverySiteItems" item="item" separator=",">
                    (
            #{item.deliverySiteId},#{item.outItemId},#{item.planReceiptCount},#{item.realReceiptCount},
            #{item.shortCount},#{item.interceptCount},#{item.rejectCount},#{item.rejectReason},
            #{item.scanCount},#{item.noscanCount},#{item.noscanReason},#{item.noscanPics},
            #{item.type},#{item.remark},#{item.outItemName},#{item.outItemType},#{item.packType},#{item.temperature}
            )
        </foreach>
    </insert>

    <select id="queryListForceMasterByDeliverySiteId" resultType="net.summerfarm.tms.dao.TmsDeliverySiteItem">
        /*FORCE_MASTER*/ SELECT * FROM tms_delivery_site_item t where t.delivery_site_id in
        <foreach collection="deliverySiteIdList" open="(" close=")" separator="," item="deliverySiteId">
            #{deliverySiteId}
        </foreach>
    </select>

    <select id="queryStandardItemTemperatureConditionsByIds" resultType="net.summerfarm.tms.dataobj.DeliverySiteStandardTempConditionDataObj">
        select t.delivery_site_id as deliverySiteId, t.temperature as temperature, count(*) as count
        from tms_delivery_site_item t
        where
        t.delivery_site_id in
        <foreach collection="deliverySiteIdList" open="(" close=")" separator="," item="deliverySiteId">
            #{deliverySiteId}
        </foreach>
          AND t.out_item_type != 1
          AND t.temperature in (1, 2)
        group by t.delivery_site_id, t.temperature
    </select>
</mapper>
