<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.delivery.TmsDeliveryBatchExtMapper">
    <!-- 结果集映射 -->
    <resultMap id="tmsDeliveryBatchExtResultMap" type="net.summerfarm.tms.model.delivery.TmsDeliveryBatchExt">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="delivery_batch_id" property="deliveryBatchId" jdbcType="NUMERIC"/>
		<result column="ext_key" property="extKey" jdbcType="VARCHAR"/>
		<result column="ext_value" property="extValue" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="tmsDeliveryBatchExtColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.delivery_batch_id,
          t.ext_key,
          t.ext_value
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="deliveryBatchId != null">
                AND t.delivery_batch_id = #{deliveryBatchId}
            </if>
			<if test="extKey != null and extKey !=''">
                AND t.ext_key = #{extKey}
            </if>
			<if test="extValue != null and extValue !=''">
                AND t.ext_value = #{extValue}
            </if>
            <if test="batchIds != null and batchIds.size != 0">
                AND t.delivery_batch_id IN
                <foreach item="item" index="index" collection="batchIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="deliveryBatchId != null">
                    t.delivery_batch_id = #{deliveryBatchId},
                </if>
                <if test="extKey != null">
                    t.ext_key = #{extKey},
                </if>
                <if test="extValue != null">
                    t.ext_value = #{extValue},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="tmsDeliveryBatchExtResultMap" >
        SELECT <include refid="tmsDeliveryBatchExtColumns" />
        FROM tms_delivery_batch_ext t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.tms.delivery.param.query.TmsDeliveryBatchExtQueryParam"  resultType="net.summerfarm.tms.delivery.entity.TmsDeliveryBatchExtEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.delivery_batch_id deliveryBatchId,
            t.ext_key extKey,
            t.ext_value extValue
        FROM tms_delivery_batch_ext t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.tms.delivery.param.query.TmsDeliveryBatchExtQueryParam" resultMap="tmsDeliveryBatchExtResultMap" >
        SELECT <include refid="tmsDeliveryBatchExtColumns" />
        FROM tms_delivery_batch_ext t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.tms.model.delivery.TmsDeliveryBatchExt" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tms_delivery_batch_ext
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="deliveryBatchId != null">
				  delivery_batch_id,
              </if>
              <if test="extKey != null">
				  ext_key,
              </if>
              <if test="extValue != null">
				  ext_value,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="deliveryBatchId != null">
				#{deliveryBatchId,jdbcType=NUMERIC},
              </if>
              <if test="extKey != null">
				#{extKey,jdbcType=VARCHAR},
              </if>
              <if test="extValue != null">
				#{extValue,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.tms.model.delivery.TmsDeliveryBatchExt" >
        UPDATE tms_delivery_batch_ext t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.tms.model.delivery.TmsDeliveryBatchExt" >
        DELETE FROM tms_delivery_batch_ext
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>