<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryItemMapper">

    <insert id="saveBatch" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.tms.dao.TmsDeliveryItem">
        INSERT INTO `tms_delivery_item` (
                                        `delivery_order_id`, `dist_order_id`, `out_order_id`, `out_item_id`,
                                         `plan_receipt_count`, `real_receipt_count`, `short_count`, `intercept_count`,
                                         `reject_count`, `reject_remark`, `scan_count`, `noscan_count`,
                                         `noscan_reason`, `noscan_pics`, `out_item_name`, `pack_type`, `temperature`)
                                         VALUES
        <foreach collection="tmsDeliveryItems" item="item" separator=",">
            (
            #{item.deliveryOrderId}, #{item.distOrderId}, #{item.outOrderId}, #{item.outItemId},
            #{item.planReceiptCount}, #{item.realReceiptCount}, #{item.shortCount}, #{item.interceptCount},
            #{item.rejectCount}, #{item.rejectRemark}, #{item.scanCount}, #{item.noscanCount},
            #{item.noscanReason}, #{item.noscanPics}, #{item.outItemName}, #{item.packType}, #{item.temperature}
            )
        </foreach>
    </insert>
</mapper>
