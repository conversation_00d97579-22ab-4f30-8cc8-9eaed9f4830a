<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryPerformanceReviewDetailAiReviewMapper">
    <!-- 结果集映射 -->
    <resultMap id="tmsDeliveryPerformanceReviewDetailAiReviewResultMap" type="net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetailAiReview">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="performance_review_task_id" property="performanceReviewTaskId" jdbcType="NUMERIC"/>
		<result column="performance_review_detail_id" property="performanceReviewDetailId" jdbcType="NUMERIC"/>
		<result column="city_sign_pic_results" property="citySignPicResults" jdbcType="LONGVARCHAR"/>
		<result column="city_delivery_pic_results" property="cityDeliveryPicResults" jdbcType="LONGVARCHAR"/>
		<result column="city_product_pic_results" property="cityProductPicResults" jdbcType="LONGVARCHAR"/>
		<result column="city_sign_pic_pass" property="citySignPicPass" jdbcType="TINYINT"/>
		<result column="city_delivery_pic_pass" property="cityDeliveryPicPass" jdbcType="TINYINT"/>
		<result column="city_product_pic_pass" property="cityProductPicPass" jdbcType="TINYINT"/>
		<result column="all_pass" property="allPass" jdbcType="TINYINT"/>
        <result column="city_vehicle_plate_pic_results" property="cityVehiclePlatePicResults" jdbcType="LONGVARCHAR"/>
        <result column="city_vehicle_plate_pic_pass" property="cityVehiclePlatePicPass" jdbcType="TINYINT"/>
        <result column="city_load_pic_results" property="cityLoadPicResults" jdbcType="LONGVARCHAR"/>
        <result column="city_load_pic_pass" property="cityLoadPicPass" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="tmsDeliveryPerformanceReviewDetailAiReviewColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.performance_review_task_id,
          t.performance_review_detail_id,
          t.city_sign_pic_results,
          t.city_delivery_pic_results,
          t.city_product_pic_results,
          t.city_sign_pic_pass,
          t.city_delivery_pic_pass,
          t.city_product_pic_pass,
          t.all_pass,
          t.city_vehicle_plate_pic_results,
          t.city_vehicle_plate_pic_pass,
          t.city_load_pic_results,
          t.city_load_pic_pass
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="performanceReviewTaskId != null">
                AND t.performance_review_task_id = #{performanceReviewTaskId}
            </if>
			<if test="performanceReviewDetailId != null">
                AND t.performance_review_detail_id = #{performanceReviewDetailId}
            </if>
            <if test="performanceReviewDetailIdList != null and performanceReviewDetailIdList.size > 0">
                AND t.performance_review_detail_id in
                <foreach collection="performanceReviewDetailIdList" item="performanceReviewDetailId1" open="(" close=")" separator=",">
                    #{performanceReviewDetailId1}
                </foreach>
            </if>
			<if test="citySignPicResults != null and citySignPicResults !=''">
                AND t.city_sign_pic_results = #{citySignPicResults}
            </if>
			<if test="cityDeliveryPicResults != null and cityDeliveryPicResults !=''">
                AND t.city_delivery_pic_results = #{cityDeliveryPicResults}
            </if>
			<if test="cityProductPicResults != null and cityProductPicResults !=''">
                AND t.city_product_pic_results = #{cityProductPicResults}
            </if>
			<if test="citySignPicPass != null">
                AND t.city_sign_pic_pass = #{citySignPicPass}
            </if>
			<if test="cityDeliveryPicPass != null">
                AND t.city_delivery_pic_pass = #{cityDeliveryPicPass}
            </if>
			<if test="cityProductPicPass != null">
                AND t.city_product_pic_pass = #{cityProductPicPass}
            </if>
			<if test="allPass != null">
                AND t.all_pass = #{allPass}
            </if>
            <if test="cityVehiclePlatePicResults != null">
                AND t.city_vehicle_plate_pic_results = #{cityVehiclePlatePicResults}
            </if>
            <if test="cityVehiclePlatePicPass != null">
                AND t.city_vehicle_plate_pic_pass = #{cityVehiclePlatePicPass}
            </if>
            <if test="cityLoadPicResults != null">
                AND t.city_load_pic_results = #{cityLoadPicResults}
            </if>
            <if test="cityLoadPicPass != null">
                AND t.city_load_pic_pass = #{cityLoadPicPass}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="performanceReviewTaskId != null">
                    t.performance_review_task_id = #{performanceReviewTaskId},
                </if>
                <if test="performanceReviewDetailId != null">
                    t.performance_review_detail_id = #{performanceReviewDetailId},
                </if>
                <if test="citySignPicResults != null">
                    t.city_sign_pic_results = #{citySignPicResults},
                </if>
                <if test="cityDeliveryPicResults != null">
                    t.city_delivery_pic_results = #{cityDeliveryPicResults},
                </if>
                <if test="cityProductPicResults != null">
                    t.city_product_pic_results = #{cityProductPicResults},
                </if>
                <if test="citySignPicPass != null">
                    t.city_sign_pic_pass = #{citySignPicPass},
                </if>
                <if test="cityDeliveryPicPass != null">
                    t.city_delivery_pic_pass = #{cityDeliveryPicPass},
                </if>
                <if test="cityProductPicPass != null">
                    t.city_product_pic_pass = #{cityProductPicPass},
                </if>
                <if test="allPass != null">
                    t.all_pass = #{allPass},
                </if>
                <if test="cityVehiclePlatePicResults != null">
                    t.city_vehicle_plate_pic_results = #{cityVehiclePlatePicResults},
                </if>
                <if test="cityVehiclePlatePicPass != null">
                    t.city_vehicle_plate_pic_pass = #{cityVehiclePlatePicPass},
                </if>
                <if test="cityLoadPicResults != null">
                    t.city_load_pic_results = #{cityLoadPicResults},
                </if>
                <if test="cityLoadPicPass != null">
                    t.city_load_pic_pass = #{cityLoadPicPass},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="tmsDeliveryPerformanceReviewDetailAiReviewResultMap" >
        SELECT <include refid="tmsDeliveryPerformanceReviewDetailAiReviewColumns" />
        FROM tms_delivery_performance_review_detail_ai_review t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAiReviewQueryParam"  resultType="net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailAiReviewEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.performance_review_task_id performanceReviewTaskId,
            t.performance_review_detail_id performanceReviewDetailId,
            t.city_sign_pic_results citySignPicResults,
            t.city_delivery_pic_results cityDeliveryPicResults,
            t.city_product_pic_results cityProductPicResults,
            t.city_sign_pic_pass citySignPicPass,
            t.city_delivery_pic_pass cityDeliveryPicPass,
            t.city_product_pic_pass cityProductPicPass,
            t.all_pass allPass,
            t.city_vehicle_plate_pic_results cityVehiclePlatePicResults,
            t.city_vehicle_plate_pic_pass cityVehiclePlatePicPass,
            t.city_load_pic_results cityLoadPicResults,
            t.city_load_pic_pass cityLoadPicPass
        FROM tms_delivery_performance_review_detail_ai_review t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAiReviewQueryParam" resultMap="tmsDeliveryPerformanceReviewDetailAiReviewResultMap" >
        SELECT <include refid="tmsDeliveryPerformanceReviewDetailAiReviewColumns" />
        FROM tms_delivery_performance_review_detail_ai_review t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetailAiReview" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tms_delivery_performance_review_detail_ai_review
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="performanceReviewTaskId != null">
				  performance_review_task_id,
              </if>
              <if test="performanceReviewDetailId != null">
				  performance_review_detail_id,
              </if>
              <if test="citySignPicResults != null">
				  city_sign_pic_results,
              </if>
              <if test="cityDeliveryPicResults != null">
				  city_delivery_pic_results,
              </if>
              <if test="cityProductPicResults != null">
				  city_product_pic_results,
              </if>
              <if test="citySignPicPass != null">
				  city_sign_pic_pass,
              </if>
              <if test="cityDeliveryPicPass != null">
				  city_delivery_pic_pass,
              </if>
              <if test="cityProductPicPass != null">
				  city_product_pic_pass,
              </if>
              <if test="allPass != null">
				  all_pass,
              </if>
              <if test="cityVehiclePlatePicResults != null">
                  city_vehicle_plate_pic_results,
              </if>
              <if test="cityVehiclePlatePicPass != null">
                  city_vehicle_plate_pic_pass,
              </if>
              <if test="cityLoadPicResults != null">
                  city_load_pic_results,
              </if>
              <if test="cityLoadPicPass != null">
                  city_load_pic_pass,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="performanceReviewTaskId != null">
				#{performanceReviewTaskId,jdbcType=NUMERIC},
              </if>
              <if test="performanceReviewDetailId != null">
				#{performanceReviewDetailId,jdbcType=NUMERIC},
              </if>
              <if test="citySignPicResults != null">
				#{citySignPicResults,jdbcType=LONGVARCHAR},
              </if>
              <if test="cityDeliveryPicResults != null">
				#{cityDeliveryPicResults,jdbcType=LONGVARCHAR},
              </if>
              <if test="cityProductPicResults != null">
				#{cityProductPicResults,jdbcType=LONGVARCHAR},
              </if>
              <if test="citySignPicPass != null">
				#{citySignPicPass,jdbcType=TINYINT},
              </if>
              <if test="cityDeliveryPicPass != null">
				#{cityDeliveryPicPass,jdbcType=TINYINT},
              </if>
              <if test="cityProductPicPass != null">
				#{cityProductPicPass,jdbcType=TINYINT},
              </if>
              <if test="allPass != null">
				#{allPass,jdbcType=TINYINT},
              </if>
              <if test="cityVehiclePlatePicResults!= null">
                  #{cityVehiclePlatePicResults,jdbcType=LONGVARCHAR},
              </if>
              <if test="cityVehiclePlatePicPass!= null">
                  #{cityVehiclePlatePicPass,jdbcType=TINYINT},
              </if>
              <if test="cityLoadPicResults!= null">
                  #{cityLoadPicResults,jdbcType=LONGVARCHAR},
              </if>
              <if test="cityLoadPicPass!= null">
                  #{cityLoadPicPass,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetailAiReview" >
        UPDATE tms_delivery_performance_review_detail_ai_review t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.tms.dao.TmsDeliveryPerformanceReviewDetailAiReview" >
        DELETE FROM tms_delivery_performance_review_detail_ai_review t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>

	<!-- 根据主键ID进行批量物理删除 -->
	<delete id="batchRemove" parameterType="java.util.List" >
        DELETE FROM tms_delivery_performance_review_detail_ai_review t
		WHERE t.id IN
        <foreach item="item" collection="list" index="index" open="("
                 separator="," close=")">
			#{item}
        </foreach>
    </delete>



</mapper>