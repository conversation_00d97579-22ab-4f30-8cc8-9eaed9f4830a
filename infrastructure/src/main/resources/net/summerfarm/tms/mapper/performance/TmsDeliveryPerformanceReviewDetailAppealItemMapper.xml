<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.performance.TmsDeliveryPerformanceReviewDetailAppealItemMapper">
        <!-- 结果集映射 -->
        <resultMap id="tmsDeliveryPerformanceReviewDetailAppealItemResultMap" type="net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppealItem">
            <id column="id" property="id" jdbcType="NUMERIC"/>
            <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
            <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
            <result column="appeal_id" property="appealId" jdbcType="NUMERIC"/>
            <result column="delivery_site_id" property="deliverySiteId" jdbcType="NUMERIC"/>
            <result column="site_id" property="siteId" jdbcType="NUMERIC"/>
            <result column="sequence" property="sequence" jdbcType="INTEGER"/>
            <result column="site_address" property="siteAddress" jdbcType="VARCHAR"/>
            <result column="non_compliance_site_pic_type" property="nonComplianceSitePicType" jdbcType="VARCHAR"/>
            <result column="non_compliance_pic" property="nonCompliancePic" jdbcType="VARCHAR"/>
            <result column="non_compliance_reason" property="nonComplianceReason" jdbcType="VARCHAR"/>
            <result column="appeal_pic" property="appealPic" jdbcType="VARCHAR"/>
            <result column="appeal_reason" property="appealReason" jdbcType="VARCHAR"/>
        </resultMap>

        <!-- 列定义 -->
        <sql id="tmsDeliveryPerformanceReviewDetailAppealItemColumns">
            t.id,
          t.create_time,
          t.update_time,
          t.appeal_id,
          t.delivery_site_id,
          t.site_id,
          t.sequence,
          t.site_address,
          t.non_compliance_site_pic_type,
          t.non_compliance_pic,
          t.non_compliance_reason,
          t.appeal_pic,
          t.appeal_reason
        </sql>

        <!-- 查询条件SQL -->
        <sql id="whereColumnBySelect">
            <trim prefix="WHERE" prefixOverrides="AND | OR">
                <if test="id != null">
                    AND t.id = #{id}
                </if>
                <if test="createTime != null">
                    AND t.create_time = #{createTime}
                </if>
                <if test="updateTime != null">
                    AND t.update_time = #{updateTime}
                </if>
                <if test="appealId != null">
                    AND t.appeal_id = #{appealId}
                </if>
                <if test="deliverySiteId != null">
                    AND t.delivery_site_id = #{deliverySiteId}
                </if>
                <if test="siteId != null">
                    AND t.site_id = #{siteId}
                </if>
                <if test="sequence != null">
                    AND t.sequence = #{sequence}
                </if>
                <if test="siteAddress != null and siteAddress !=''">
                    AND t.site_address = #{siteAddress}
                </if>
                <if test="nonComplianceSitePicType != null and nonComplianceSitePicType !=''">
                    AND t.non_compliance_site_pic_type = #{nonComplianceSitePicType}
                </if>
                <if test="nonCompliancePic != null and nonCompliancePic !=''">
                    AND t.non_compliance_pic = #{nonCompliancePic}
                </if>
                <if test="nonComplianceReason != null and nonComplianceReason !=''">
                    AND t.non_compliance_reason = #{nonComplianceReason}
                </if>
                <if test="appealPic != null and appealPic !=''">
                    AND t.appeal_pic = #{appealPic}
                </if>
                <if test="appealReason != null and appealReason !=''">
                    AND t.appeal_reason = #{appealReason}
                </if>
            </trim>
        </sql>

        <!-- 修改字段SQL -->
        <sql id="whereColumnByUpdate">
            <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="appealId != null">
                    t.appeal_id = #{appealId},
                </if>
                <if test="deliverySiteId != null">
                    t.delivery_site_id = #{deliverySiteId},
                </if>
                <if test="siteId != null">
                    t.site_id = #{siteId},
                </if>
                <if test="sequence != null">
                    t.sequence = #{sequence},
                </if>
                <if test="siteAddress != null">
                    t.site_address = #{siteAddress},
                </if>
                <if test="nonComplianceSitePicType != null">
                    t.non_compliance_site_pic_type = #{nonComplianceSitePicType},
                </if>
                <if test="nonCompliancePic != null">
                    t.non_compliance_pic = #{nonCompliancePic},
                </if>
                <if test="nonComplianceReason != null">
                    t.non_compliance_reason = #{nonComplianceReason},
                </if>
                <if test="appealPic != null">
                    t.appeal_pic = #{appealPic},
                </if>
                <if test="appealReason != null">
                    t.appeal_reason = #{appealReason},
                </if>
            </trim>
        </sql>

        <!-- 根据主键ID获取数据 -->
        <select id="selectById" parameterType="java.lang.Long" resultMap="tmsDeliveryPerformanceReviewDetailAppealItemResultMap" >
            SELECT <include refid="tmsDeliveryPerformanceReviewDetailAppealItemColumns" />
            FROM tms_delivery_performance_review_detail_appeal_item t
            WHERE t.id = #{id}
        </select>

        <!-- 查询列表可以根据分页进行查询 -->
        <select id="getPage" parameterType="net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAppealItemQueryParam"  resultType="net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity" >
            SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.appeal_id appealId,
            t.delivery_site_id deliverySiteId,
            t.site_id siteId,
            t.sequence sequence,
            t.site_address siteAddress,
            t.non_compliance_site_pic_type nonComplianceSitePicType,
            t.non_compliance_pic nonCompliancePic,
            t.non_compliance_reason nonComplianceReason,
            t.appeal_pic appealPic,
            t.appeal_reason appealReason
            FROM tms_delivery_performance_review_detail_appeal_item t
            <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
        </select>


        <!-- 根据条件查询对象 -->
        <select id="selectByCondition" parameterType="net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAppealItemQueryParam" resultMap="tmsDeliveryPerformanceReviewDetailAppealItemResultMap" >
            SELECT <include refid="tmsDeliveryPerformanceReviewDetailAppealItemColumns" />
            FROM tms_delivery_performance_review_detail_appeal_item t
            <include refid="whereColumnBySelect"></include>
        </select>



        <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
        <insert id="insertSelective" parameterType="net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppealItem" keyProperty="id" useGeneratedKeys="true">
            INSERT INTO tms_delivery_performance_review_detail_appeal_item
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="id != null">
                    id,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="updateTime != null">
                    update_time,
                </if>
                <if test="appealId != null">
                    appeal_id,
                </if>
                <if test="deliverySiteId != null">
                    delivery_site_id,
                </if>
                <if test="siteId != null">
                    site_id,
                </if>
                <if test="sequence != null">
                    sequence,
                </if>
                <if test="siteAddress != null">
                    site_address,
                </if>
                <if test="nonComplianceSitePicType != null">
                    non_compliance_site_pic_type,
                </if>
                <if test="nonCompliancePic != null">
                    non_compliance_pic,
                </if>
                <if test="nonComplianceReason != null">
                    non_compliance_reason,
                </if>
                <if test="appealPic != null">
                    appeal_pic,
                </if>
                <if test="appealReason != null">
                    appeal_reason,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="id != null">
                    #{id,jdbcType=NUMERIC},
                </if>
                <if test="createTime != null">
                    #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="appealId != null">
                    #{appealId,jdbcType=NUMERIC},
                </if>
                <if test="deliverySiteId != null">
                    #{deliverySiteId,jdbcType=NUMERIC},
                </if>
                <if test="siteId != null">
                    #{siteId,jdbcType=NUMERIC},
                </if>
                <if test="sequence != null">
                    #{sequence,jdbcType=INTEGER},
                </if>
                <if test="siteAddress != null">
                    #{siteAddress,jdbcType=VARCHAR},
                </if>
                <if test="nonComplianceSitePicType != null">
                    #{nonComplianceSitePicType,jdbcType=VARCHAR},
                </if>
                <if test="nonCompliancePic != null">
                    #{nonCompliancePic,jdbcType=VARCHAR},
                </if>
                <if test="nonComplianceReason != null">
                    #{nonComplianceReason,jdbcType=VARCHAR},
                </if>
                <if test="appealPic != null">
                    #{appealPic,jdbcType=VARCHAR},
                </if>
                <if test="appealReason != null">
                    #{appealReason,jdbcType=VARCHAR},
                </if>
            </trim>
        </insert>

        <!-- 根据主键ID进行修改 -->
        <update id="updateSelectiveById" parameterType="net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppealItem" >
            UPDATE tms_delivery_performance_review_detail_appeal_item t
            <include refid="whereColumnByUpdate"></include>
            <where>
                t.id = #{id,jdbcType=NUMERIC}
            </where>
        </update>



        <!-- 根据主键ID进行物理删除 -->
        <delete id="remove" parameterType="net.summerfarm.tms.dao.performance.TmsDeliveryPerformanceReviewDetailAppealItem" >
            DELETE FROM tms_delivery_performance_review_detail_appeal_item
            WHERE id = #{id,jdbcType=NUMERIC}
        </delete>


    </mapper>
