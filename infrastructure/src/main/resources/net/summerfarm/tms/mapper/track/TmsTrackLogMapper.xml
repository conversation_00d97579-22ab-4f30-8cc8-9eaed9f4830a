<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.track.TmsTrackLogMapper">
    <!-- 结果集映射 -->
    <resultMap id="tmsTrackLogResultMap" type="net.summerfarm.tms.dao.track.TmsTrackLog">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="biz_type" property="bizType" jdbcType="INTEGER"/>
		<result column="biz_no" property="bizNo" jdbcType="NUMERIC"/>
		<result column="action_name" property="actionName" jdbcType="VARCHAR"/>
		<result column="operater" property="operater" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="tmsTrackLogColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.biz_type,
          t.biz_no,
          t.action_name,
          t.operater,
          t.remark
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="bizType != null">
                AND t.biz_type = #{bizType}
            </if>
			<if test="bizNo != null">
                AND t.biz_no = #{bizNo}
            </if>
			<if test="actionName != null and actionName !=''">
                AND t.action_name = #{actionName}
            </if>
			<if test="operater != null and operater !=''">
                AND t.operater = #{operater}
            </if>
			<if test="remark != null and remark !=''">
                AND t.remark = #{remark}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="bizType != null">
                    t.biz_type = #{bizType},
                </if>
                <if test="bizNo != null">
                    t.biz_no = #{bizNo},
                </if>
                <if test="actionName != null">
                    t.action_name = #{actionName},
                </if>
                <if test="operater != null">
                    t.operater = #{operater},
                </if>
                <if test="remark != null">
                    t.remark = #{remark},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="tmsTrackLogResultMap" >
        SELECT <include refid="tmsTrackLogColumns" />
        FROM tms_track_log t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.tms.track.param.query.TmsTrackLogQueryParam"  resultType="net.summerfarm.tms.track.entity.TmsTrackLogEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.biz_type bizType,
            t.biz_no bizNo,
            t.action_name actionName,
            t.operater operater,
            t.remark remark
        FROM tms_track_log t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.tms.track.param.query.TmsTrackLogQueryParam" resultMap="tmsTrackLogResultMap" >
        SELECT <include refid="tmsTrackLogColumns" />
        FROM tms_track_log t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.tms.dao.track.TmsTrackLog" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tms_track_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="bizType != null">
				  biz_type,
              </if>
              <if test="bizNo != null">
				  biz_no,
              </if>
              <if test="actionName != null">
				  action_name,
              </if>
              <if test="operater != null">
				  operater,
              </if>
              <if test="remark != null">
				  remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="bizType != null">
				#{bizType,jdbcType=INTEGER},
              </if>
              <if test="bizNo != null">
				#{bizNo,jdbcType=NUMERIC},
              </if>
              <if test="actionName != null">
				#{actionName,jdbcType=VARCHAR},
              </if>
              <if test="operater != null">
				#{operater,jdbcType=VARCHAR},
              </if>
              <if test="remark != null">
				#{remark,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.tms.dao.track.TmsTrackLog" >
        UPDATE tms_track_log t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.tms.dao.track.TmsTrackLog" >
        DELETE FROM tms_track_log
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>