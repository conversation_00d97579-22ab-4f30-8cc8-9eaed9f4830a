<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsOutDistanceConfigNewMapper">

    <select id="queryList" parameterType="net.summerfarm.tms.query.base.distance.OutDistanceQuery" resultType="net.summerfarm.tms.base.distance.entity.OutDistanceEntity">
        SELECT
                t.*,
               a.realname   as adminName,
               wlc.store_name as storeName
        FROM
        tms_out_distance_config t
        LEFT JOIN admin a ON t.admin_id=a.admin_id
        LEFT JOIN warehouse_logistics_center wlc ON wlc.store_no = t.store_no
        <where>
            <if test="storeNo != null">
                t.store_no = #{storeNo}
            </if>
            <if test="state != null">
                and t.state = #{state}
            </if>
            <if test="storeNos != null and storeNos.size > 0">
                AND t.store_no in
                <foreach collection="storeNos" item="storeNo" open="(" close=")" separator=",">
                    #{storeNo}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
