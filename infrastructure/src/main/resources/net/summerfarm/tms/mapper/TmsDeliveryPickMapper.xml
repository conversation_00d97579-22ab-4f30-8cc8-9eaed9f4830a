<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryPickMapper">
    <insert id="saveBatch" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.tms.dao.TmsDeliveryPick">
        INSERT INTO `tms_delivery_pick` (`delivery_batch_id`, `site_id`, `driver_id`, `out_item_id`,
        `item_desc`, `particle`, `type`, `quantity`,
        `pick_quantity`,`short_quantity`, `intercept_quantity`, `unit`,
        `status`, `finish_time`, `temperature`, `weight`,
        `delivery_site_id`,`specification`,`category_type`,`pack_type`,`outer_client_name`)
        VALUES
        <foreach collection="tmsDeliveryPicks" item="item" separator=",">
            (
            #{item.deliveryBatchId}, #{item.siteId}, #{item.driverId}, #{item.outItemId},
            #{item.itemDesc}, #{item.particle}, #{item.type}, #{item.quantity},
            #{item.pickQuantity}, #{item.shortQuantity}, #{item.interceptQuantity}, #{item.unit},
            #{item.status}, #{item.finishTime}, #{item.temperature}, #{item.weight},
            #{item.deliverySiteId}, #{item.specification},#{item.categoryType},#{item.packType},#{item.outerClientName}
            )
        </foreach>


    </insert>

    <select id="queryByBatch" resultType="net.summerfarm.tms.delivery.entity.BatchPickDetailEntity">
        SELECT delivery_batch_id AS deliveryBatchId, IFNULL(SUM(short_quantity), 0) AS shortQuantity FROM tms_delivery_pick
        <where>
            delivery_batch_id IN
        <foreach collection="batchIds" open="(" close=")" separator="," item="batchId">
            #{batchId}
        </foreach>
        GROUP BY delivery_batch_id
        </where>
    </select>

    <select id="queryPickStandardItemTemperatureConditionsByDelSiteIds" resultType="net.summerfarm.tms.dataobj.DeliverySiteStandardTempConditionDataObj">
        select t.delivery_site_id as deliverySiteId, t.temperature as temperature, sum(t.quantity) as count
        from tms_delivery_pick t
        where t.delivery_site_id in
        <foreach collection="deliverySiteIds" open="(" close=")" separator="," item="deliverySiteId">
            #{deliverySiteId}
        </foreach>
          and t.category_type != 1
          AND t.temperature in (1, 2)
        group by t.delivery_site_id, t.temperature
    </select>

    <select id="queryPickStandardItemTemperatureUnitByDelSiteIds" resultType="net.summerfarm.tms.dataobj.DeliverySiteStandardTempConditionDataObj">
        select t.delivery_site_id as deliverySiteId, t.temperature as temperature, t.unit, ifnull(sum(t.quantity), 0) as `count`
        from tms_delivery_pick t
        where t.delivery_site_id in
        <foreach collection="deliverySiteIds" open="(" close=")" separator="," item="deliverySiteId">
            #{deliverySiteId}
        </foreach>
        and t.category_type != 1
        AND t.temperature in (1, 2)
        group by t.delivery_site_id, t.temperature, t.unit
    </select>
</mapper>
