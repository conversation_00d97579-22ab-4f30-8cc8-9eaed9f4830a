<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryOrderMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsDeliveryOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_id" jdbcType="BIGINT" property="batchId"/>
        <result column="dist_order_id" jdbcType="BIGINT" property="distOrderId"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="out_order_id" jdbcType="VARCHAR" property="outOrderId"/>
        <result column="out_contact_id" jdbcType="VARCHAR" property="outContactId"/>
        <result column="out_client_id" jdbcType="VARCHAR" property="outClientId"/>
        <result column="out_client_name" jdbcType="VARCHAR" property="outClientName"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="begin_site_id" jdbcType="BIGINT" property="beginSiteId"/>
        <result column="end_site_id" jdbcType="BIGINT" property="endSiteId"/>
        <result column="site_type" jdbcType="INTEGER" property="siteType"/>
        <result column="address_detail" jdbcType="VARCHAR" property="addressDetail"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="send_remark" jdbcType="VARCHAR" property="sendRemark"/>
        <result column="fence_id" jdbcType="INTEGER" property="fenceId"/>
        <result column="fence_name" jdbcType="VARCHAR" property="fenceName"/>
        <result column="ad_code_msg_id" jdbcType="INTEGER" property="adCodeMsgId"/>
        <result column="custom_area_name" jdbcType="VARCHAR" property="customAreaName"/>
    </resultMap>

    <select id="queryListWithDiffBatch" resultMap="BaseResultMap">
        SELECT `begin_site_id`, `end_site_id`, `delivery_time`
        FROM (SELECT `begin_site_id`, `end_site_id`, `delivery_time`, `batch_id`
              FROM `tms_delivery_order`
              WHERE `source` in (200,201,202,203,210,211,220,230,231)
                AND `delivery_time` = #{deliveryTime}
              GROUP BY `begin_site_id`, `end_site_id`, `delivery_time`, `batch_id`) tmp
        GROUP BY `begin_site_id`, `end_site_id`, `delivery_time`
        HAVING COUNT(*) > 1
    </select>

    <select id="queryListByDistIdForceMaster" resultType="net.summerfarm.tms.dao.TmsDeliveryOrder">
        /*FORCE_MASTER*/
        SELECT
        *
        FROM
        tms_delivery_order
        WHERE
        dist_order_id = #{distId}
    </select>

    <insert id="insertBatch">
        insert into tms_delivery_order (`id`, `batch_id`, `dist_order_id`, `out_order_id`,
                                        `out_contact_id`, `out_client_id`, `out_client_name`, `delivery_time`, `sequence`,
                                        `begin_site_id`, `end_site_id`, `address_detail`, `name`, `phone`, `state`, `finish_time`,
                                        `type`, `source`, `site_type`, `send_remark`,`fence_id`,`fence_name`,`ad_code_msg_id`,`custom_area_name`)
        values
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="item" separator=",">
                (#{item.id}, #{item.batchId}, #{item.distOrderId},#{item.outOrderId},
                 #{item.outContactId}, #{item.outClientId},#{item.outClientName}, #{item.deliveryTime},#{item.sequence}
                 ,#{item.beginSiteId}, #{item.endSiteId},#{item.addressDetail}, #{item.name},#{item.phone},#{item.state}, #{item.finishTime},
                 #{item.type}, #{item.source},#{item.siteType},#{item.sendRemark},#{item.fenceId},#{item.fenceName},#{item.adCodeMsgId},#{item.customAreaName})
            </foreach>
        </if>
    </insert>

    <select id="selectBatchLoadDetail" resultType="net.summerfarm.tms.delivery.vo.BatchDetailLoadStaticVO">
        /*FORCE_MASTER*/
        SELECT
        IFNULL(SUM(IFNULL(`volume`,0) * IFNULL(`quantity`,0)),0) AS loadVolume,
        IFNULL(SUM(IFNULL(`weight`,0) * IFNULL(`quantity`,0)),0) AS loadWeight,
        IFNULL(SUM(IFNULL(`quantity`,0)),0) AS loadQuantity
        FROM `tms_dist_item`
        WHERE `dist_order_id` IN (
        SELECT dist_order_id
        FROM `tms_delivery_order`
        <where>
        <if test="list != null and list.size > 0">
            AND `batch_id` IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        AND state != 50
        </where>
        )
    </select>


    <select id="selectListByCondition" resultType="net.summerfarm.tms.dao.TmsDeliveryOrder">
        select DISTINCT
            o.*
        from
            tms_delivery_order o
                inner join tms_delivery_batch b on b.id = o.batch_id
                inner join tms_car c on b.car_id = c.id
                and c.status = 0
        where
            c.car_number = #{carNumber,jdbcType=VARCHAR}
          and b.delivery_time = #{deliveryTime,jdbcType=DATE}
          and o.`source` = #{outOrderSource,jdbcType=INTEGER}
    </select>

    <!-- 分页查询配送单（MyBatis实现） -->
    <select id="queryPageByCondition" resultMap="BaseResultMap" parameterType="net.summerfarm.tms.query.delivery.DeliveryOrderQuery">
        SELECT
            t.id, t.batch_id, t.dist_order_id, t.source, t.out_order_id, t.out_contact_id, t.out_client_id, t.out_client_name,
            t.delivery_time, t.sequence, t.begin_site_id, t.end_site_id, t.site_type, t.address_detail, t.name, t.phone,
            t.state, t.finish_time, t.type, t.create_time, t.update_time, t.send_remark, t.fence_id, t.fence_name, t.ad_code_msg_id, t.custom_area_name
        FROM tms_delivery_order t
        LEFT JOIN tms_dist_order s ON t.dist_order_id = s.id
        <where>
            <!-- 批次ID条件 -->
            <if test="batchId != null">
                AND t.batch_id = #{batchId}
            </if>

            <!-- 委托单ID条件 -->
            <if test="distOrderId != null">
                AND t.dist_order_id = #{distOrderId}
            </if>

            <!-- 起点站点ID条件 -->
            <if test="beginSiteId != null">
                AND t.begin_site_id = #{beginSiteId}
            </if>

            <!-- 终点站点ID条件 -->
            <if test="endSiteId != null">
                AND t.end_site_id = #{endSiteId}
            </if>

            <!-- 来源列表条件 -->
            <if test="sourceList != null and sourceList.size() > 0">
                AND t.source IN
                <foreach collection="sourceList" item="source" open="(" separator="," close=")">
                    #{source}
                </foreach>
            </if>

            <!-- 配送时间条件（当天） -->
            <if test="deliveryTime != null">
                AND t.delivery_time >= #{deliveryTime}
                AND t.delivery_time &lt; DATE_ADD(#{deliveryTime}, INTERVAL 1 DAY)
            </if>

            <!-- 配送时间范围条件 -->
            <if test="beginDeliveryTime != null">
                AND t.delivery_time >= #{beginDeliveryTime}
            </if>
            <if test="endDeliveryTime != null">
                AND t.delivery_time &lt; #{endDeliveryTime}
            </if>

            <!-- 排除关闭状态 -->
            <if test="neState != null">
                AND t.state != #{neState}
            </if>

            <!-- 委托单ID列表条件 -->
            <if test="distIdList != null and distIdList.size() > 0">
                AND t.dist_order_id IN
                <foreach collection="distIdList" item="distId" open="(" separator="," close=")">
                    #{distId}
                </foreach>
            </if>

            <!-- 排除的配送单ID列表 -->
            <if test="notInDeliveryOrderIds != null and notInDeliveryOrderIds.size() > 0">
                AND t.id NOT IN
                <foreach collection="notInDeliveryOrderIds" item="deliveryOrderId" open="(" separator="," close=")">
                    #{deliveryOrderId}
                </foreach>
            </if>

            <!-- 站点对条件（新的更清晰的方式） -->
            <if test="sitePairs != null and sitePairs.size() > 0">
                AND (
                <foreach collection="sitePairs" item="sitePair" separator=" OR ">
                    (t.begin_site_id = #{sitePair.beginSiteId} AND t.end_site_id = #{sitePair.endSiteId})
                </foreach>
                )
            </if>

            <!-- 履约配送方式条件（来自委托单表） -->
            <if test="fulfillmentDeliveryWays != null and fulfillmentDeliveryWays.size() > 0">
                AND s.fulfillment_delivery_way IN
                <foreach collection="fulfillmentDeliveryWays" item="fulfillmentDeliveryWay" open="(" separator="," close=")">
                    #{fulfillmentDeliveryWay}
                </foreach>
            </if>
        </where>

        <!-- 自定义SQL后缀 -->
        <if test="lastSql != null and lastSql != ''">
            ${lastSql}
        </if>
    </select>
</mapper>
