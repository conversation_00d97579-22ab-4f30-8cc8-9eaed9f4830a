<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDistOrderMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsDistOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="outer_order_id" jdbcType="VARCHAR" property="outerOrderId"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="pick_type" jdbcType="INTEGER" property="pickType"/>
        <result column="outer_tenant_id" jdbcType="VARCHAR" property="outerTenantId"/>
        <result column="outer_client_id" jdbcType="VARCHAR" property="outerClientId"/>
        <result column="outer_client_name" jdbcType="VARCHAR" property="outerClientName"/>
        <result column="outer_contact_id" jdbcType="VARCHAR" property="outerContactId"/>
        <result column="outer_remark" jdbcType="VARCHAR" property="outerRemark"/>
        <result column="begin_site_id" jdbcType="BIGINT" property="beginSiteId"/>
        <result column="mid_site_id" jdbcType="BIGINT" property="midSiteId"/>
        <result column="end_site_id" jdbcType="BIGINT" property="endSiteId"/>
        <result column="expect_begin_time" jdbcType="TIMESTAMP" property="expectBeginTime"/>
        <result column="expect_end_time" jdbcType="TIMESTAMP" property="expectEndTime"/>
        <result column="real_arrival_time" jdbcType="TIMESTAMP" property="realArrivalTime"/>
        <result column="time_frame" jdbcType="VARCHAR" property="timeFrame"/>
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId"/>
        <result column="updater_id" jdbcType="VARCHAR" property="updaterId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="DeliveryNoteResultMap" type="net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderFlatObject">
        <id column="distId" jdbcType="BIGINT" property="distId"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="outerBrandName" jdbcType="VARCHAR" property="outerBrandName"/>
        <result column="sendRemark" jdbcType="VARCHAR" property="sendRemark"/>
        <result column="deliverySiteId" jdbcType="BIGINT" property="deliverySiteId"/>
        <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="merchantName" jdbcType="VARCHAR" property="merchantName"/>
        <result column="deliveryTime" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="outContactId" jdbcType="VARCHAR" property="outContactId"/>
        <result column="outerTenantId" jdbcType="VARCHAR" property="outerTenantId"/>
        <result column="bigCustomerId" jdbcType="BIGINT" property="bigCustomerId"/>
        <result column="merchantId" jdbcType="VARCHAR" property="merchantId"/>
        <result column="contactName" jdbcType="VARCHAR" property="contactName"/>
        <result column="contactPhone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="deliveryAddress" jdbcType="VARCHAR" property="deliveryAddress"/>
        <result column="endSiteId" jdbcType="BIGINT" property="endSiteId"/>
        <result column="pathCode" jdbcType="VARCHAR" property="pathCode"/>
        <result column="pathSequence" jdbcType="VARCHAR" property="pathSequence"/>
        <result column="storeName" jdbcType="VARCHAR" property="storeName"/>
        <result column="storeNo" jdbcType="INTEGER" property="storeNo"/>
        <result column="fulfillmentDeliveryWay" jdbcType="INTEGER" property="fulfillmentDeliveryWay"/>
        <collection property="items" ofType="net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderItemFlatObject">
            <id column="distItemId" property="distItemId" jdbcType="BIGINT"/>
            <id column="quantity" property="quantity" jdbcType="INTEGER"/>
            <id column="unit" property="unit" jdbcType="VARCHAR"/>
            <id column="specification" property="specification" jdbcType="VARCHAR"/>
            <id column="weight" property="weight" jdbcType="VARCHAR"/>
            <id column="sku" property="sku" jdbcType="VARCHAR"/>
            <id column="skuName" property="skuName" jdbcType="VARCHAR"/>
            <id column="volume" property="volume" jdbcType="VARCHAR"/>
            <id column="temperature" property="temperature" jdbcType="INTEGER"/>
            <id column="price" property="price" jdbcType="DOUBLE"/>
            <id column="productName" property="productName" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id, outer_order_id, `source`, `type`, pick_type, outer_tenant_id, outer_client_id, outer_client_name,
        outer_contact_id, outer_remark, begin_site_id, mid_site_id,
        end_site_id,expect_begin_time,expect_end_time,real_arrival_time,
        time_frame,`state`,creator_id,creator,create_time,updater_id,updater,update_time
    </sql>

    <select id="selectByQuery" resultMap="BaseResultMap" parameterType="net.summerfarm.tms.query.dist.DistOrderQuery">
        SELECT * FROM tms_dist_order
        <where>
            <if test="distId != null">
                AND id = #{distId}
            </if>
            <if test="outerOrderId != null and outerOrderId != ''">
                AND outer_order_id = #{outerOrderId}
            </if>
            <if test="outerContactId != null and outerContactId != ''">
                AND outer_contact_id = #{outerContactId}
            </if>
            <if test="sources != null and sources.size > 0">
                AND `source` in
                <foreach collection="sources" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status.size > 0">
                AND `state` in
                <foreach collection="status" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="beginSiteId != null">
                AND begin_site_id = #{beginSiteId}
            </if>
            <if test="endSiteId != null">
                AND end_site_id = #{endSiteId}
            </if>
            <if test="exceptBeginTimeFrom != null">
                AND expect_begin_time <![CDATA[>=]]> #{exceptBeginTimeFrom}
            </if>
            <if test="exceptBeginTimeTo != null">
                AND expect_begin_time <![CDATA[<=]]> #{exceptBeginTimeTo}
            </if>
            <if test="outBrandName != null">
                AND outer_brand_name like CONCAT(#{outBrandName},'%')
            </if>
             <if test="isNullFulfillmentDeliveryWay != null">
                 AND fulfillment_delivery_way is null
             </if>
            <if test="fulfillmentDeliveryWays != null and fulfillmentDeliveryWays.size > 0">
                AND fulfillment_delivery_way in
                <foreach collection="fulfillmentDeliveryWays" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            ORDER BY id DESC
        </where>
    </select>

    <select id="querySyncedDistOrderList" resultMap="BaseResultMap" parameterType="java.time.LocalDateTime">
        SELECT id,
        outer_order_id,
        source,
        outer_contact_id,
        expect_begin_time
        FROM tms_dist_order
        <where>
            AND `source` in (200,201,202,203,210,211,220,230,231)
             AND `state` NOT IN (18,19,39)
            <if test="deliveryTimeFrom != null">
                AND expect_begin_time <![CDATA[>=]]> #{deliveryTimeFrom}
            </if>
            <if test="deliveryTimeTo != null">
                AND expect_begin_time <![CDATA[<=]]> #{deliveryTimeTo}
            </if>
            <if test="siteId!=null">
                and begin_site_id=#{siteId}
            </if>
        </where>
    </select>

    <select id="queryListForceMaster" resultType="net.summerfarm.tms.dao.TmsDistOrder">
        /*FORCE_MASTER*/
        SELECT
             *
         FROM
             tms_dist_order
         WHERE
             outer_contact_id = #{outerContactId}
            AND expect_begin_time = #{expectBeginTime}
            AND `source` in
            <foreach collection="sources" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
            order by create_time DESC
    </select>

    <select id="queryDeliveryNoteByDateAndStoreNo" resultMap="DeliveryNoteResultMap">
        SELECT tdo.id                                                               distId,
               tdo.source                                                           source,
               tdsite.outer_brand_name                                              outerBrandName,
               tdsite.send_remark                                                   sendRemark,
               tdsite.id                                                            deliverySiteId,
               tdo.outer_order_id                                                   orderNo,
               tdo.outer_client_name                                                merchantName,
               tdo.expect_begin_time                                                deliveryTime,
               tdo.outer_contact_id                                                 outContactId,
               tdo.outer_tenant_id                                                  bigCustomerId,
               tdo.outer_tenant_id                                                  outerTenantId,
               tdo.outer_client_id                                                  merchantId,
               tdse.`name`                                                          contactName,
               tdse.phone                                                           contactPhone,
               CONCAT(tdse.provice, tdse.city, ifnull(tdse.area, ''), tdse.address) deliveryAddress,
               tdse.id                                                              endSiteId,
               tdb.path_code                                                        pathCode,
               IF(tdb.path_id > 0, tdsite.sequence, null)                           pathSequence,
               tdi.id                                                               distItemId,
               tdi.quantity                                                         quantity,
               tdi.outer_item_price                                                 price,
               tdi.unit                                                             unit,
               tdi.specification                                                    specification,
               tdi.weight                                                           weight,
               tdi.outer_item_id                                                    sku,
               tdi.outer_item_name                                                  skuName,
               tdi.outer_item_name                                                  productName,
               tdi.volume                                                           volume,
               tdi.temperature                                                      temperature,
               tdsb.name                                                            storeName,
               tdsb.out_business_no                                                 storeNo,
               tdo.fulfillment_delivery_way                                         fulfillmentDeliveryWay
        FROM tms_dist_order tdo
                 LEFT JOIN tms_dist_item tdi ON tdo.id = tdi.dist_order_id and tdi.delivery_type = 0
                 LEFT JOIN tms_dist_site tdse ON tdse.id = tdo.end_site_id
                 LEFT JOIN tms_dist_site tdsb ON tdsb.id = tdo.begin_site_id
                 LEFT JOIN tms_delivery_order tdeo ON tdeo.dist_order_id = tdo.id
                 LEFT JOIN tms_delivery_batch tdb ON tdb.id = tdeo.batch_id and tdb.type = 5
                 LEFT JOIN tms_delivery_site tdsite ON tdsite.delivery_batch_id = tdeo.batch_id
            AND tdo.end_site_id = tdsite.site_id
        WHERE tdo.type in (0, 2)
          AND tdsb.out_business_no =  #{storeNo}
          AND tdo.expect_begin_time = #{deliveryTime}
          AND tdo.state NOT IN (18, 19)
          AND tdo.source IN (200, 201, 202, 203, 210, 211, 220,230,231)
    </select>

    <select id="queryOrderDeliveryNoteByOrderNo" resultMap="DeliveryNoteResultMap">
        SELECT tdo.id                                                               distId,
               tdo.source                                                           source,
               tdsite.outer_brand_name                                              outerBrandName,
               tdsite.send_remark                                                   sendRemark,
               tdsite.id                                                            deliverySiteId,
               tdo.outer_order_id                                                   orderNo,
               tdo.outer_client_name                                                merchantName,
               tdo.expect_begin_time                                                deliveryTime,
               tdo.outer_contact_id                                                 outContactId,
               tdo.outer_tenant_id                                                  outerTenantId,
               tdo.outer_client_id                                                  merchantId,
               tdse.`name`                                                          contactName,
               tdse.phone                                                           contactPhone,
               CONCAT(tdse.provice, tdse.city, ifnull(tdse.area, ''), tdse.address) deliveryAddress,
               tdse.id                                                              endSiteId,
               tdb.path_code                                                        pathCode,
               IF(tdb.path_id > 0, tdsite.sequence, null)                           pathSequence,
               tdi.id                                                               distItemId,
               tdi.quantity                                                         quantity,
               tdi.outer_item_price                                                 price,
               tdi.unit                                                             unit,
               tdi.specification                                                    specification,
               tdi.weight                                                           weight,
               tdi.outer_item_id                                                    sku,
               tdi.outer_item_name                                                  skuName,
               tdi.outer_item_name                                                  productName,
               tdi.volume                                                           volume,
               tdi.temperature                                                      temperature,
               tdsb.name                                                            storeName,
               tdsb.out_business_no                                                 storeNo
        FROM tms_dist_order tdo
                 LEFT JOIN tms_dist_item tdi ON tdo.id = tdi.dist_order_id and tdi.delivery_type = 0
                 LEFT JOIN tms_dist_site tdse ON tdse.id = tdo.end_site_id
                 LEFT JOIN tms_dist_site tdsb ON tdsb.id = tdo.begin_site_id
                 LEFT JOIN tms_delivery_order tdeo ON tdeo.dist_order_id = tdo.id
                 LEFT JOIN tms_delivery_batch tdb ON tdb.id = tdeo.batch_id
                 LEFT JOIN tms_delivery_site tdsite ON tdsite.delivery_batch_id = tdeo.batch_id
            AND tdo.end_site_id = tdsite.site_id
        WHERE tdo.type in (0, 2)
          AND tdo.outer_order_id = #{orderNo}
          AND tdo.state NOT IN (18, 19)
          AND tdo.source IN (200, 201, 202, 203, 210, 211, 220,230,231)
    </select>

    <select id="queryTrunkTransportBeginSites" resultType="java.lang.Long">
        SELECT
            begin_site_id
        FROM
            tms_dist_order
        WHERE
            expect_begin_time = #{expectBeginTime}
            AND `source` in
            <foreach collection="sources" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
            AND `state` = #{state}
            AND `fulfillment_delivery_way` = 4
        GROUP BY
            begin_site_id
    </select>

    <select id="queryHaveNextDeliveryDayManyFulfillmentWay" resultType="net.summerfarm.tms.dist.flatObject.ManyFulfillmentWayDistOrderFlatObject">
        SELECT
            expect_begin_time as expectBeginTime,
            outer_client_name as outerClientName,
            GROUP_CONCAT(DISTINCT CONCAT(outer_order_id, ':', fulfillment_delivery_way) SEPARATOR '|') AS orderNoWithFulfillmentWay
        FROM tms_dist_order
        WHERE
        source IN
        <foreach collection="sourceList" item="source" open="(" close=")" separator=",">
            #{source}
        </foreach>
          AND expect_begin_time = #{deliveryTime}
          AND fulfillment_delivery_way IS NOT NULL
        GROUP BY begin_site_id, end_site_id, expect_begin_time, outer_client_name
        HAVING COUNT(DISTINCT fulfillment_delivery_way) > 1
    </select>

    <!-- 批量更新履约方式 -->
    <update id="batchUpdateFulfillmentWay">
        UPDATE tms_dist_order
        SET fulfillment_delivery_way = #{fulfillmentDeliveryWay}
        WHERE id IN
        <foreach collection="distOrderIds" item="distOrderId" open="(" close=")" separator=",">
            #{distOrderId}
        </foreach>
    </update>
</mapper>
