<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryPerformanceReviewDetailMapper">

    <select id="queryTrunkCountByTaskIdAndState" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            ( SELECT COUNT(*) FROM tms_delivery_performance_review_detail WHERE performance_review_task_id = #{performanceReviewTaskId}
                                                                            AND state = #{state} GROUP BY delivery_batch_id ) t
    </select>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
            <if test="performanceReviewTaskId != null">
                AND t.performance_review_task_id = #{performanceReviewTaskId}
            </if>
            <if test="state != null">
                AND t.state = #{state}
            </if>
            <if test="picJson != null and picJson !=''">
                AND t.pic_json = #{picJson}
            </if>
            <if test="deliveryTime != null">
                AND t.delivery_time = #{deliveryTime}
            </if>
            <if test="driverName != null and driverName !=''">
                AND t.driver_name = #{driverName}
            </if>
            <if test="driverPhone != null and driverPhone !=''">
                AND t.driver_phone = #{driverPhone}
            </if>
            <if test="carNumber != null and carNumber !=''">
                AND t.car_number = #{carNumber}
            </if>
            <if test="carType != null">
                AND t.car_type = #{carType}
            </if>
            <if test="carStorage != null">
                AND t.car_storage = #{carStorage}
            </if>
            <if test="deliveryBatchId != null">
                AND t.delivery_batch_id = #{deliveryBatchId}
            </if>
            <if test="deliverySiteId != null">
                AND t.delivery_site_id = #{deliverySiteId}
            </if>
            <if test="siteId != null">
                AND t.site_id = #{siteId}
            </if>
            <if test="siteAddress != null and siteAddress !=''">
                AND t.site_address = #{siteAddress}
            </if>
            <if test="outerClientName != null and outerClientName !=''">
                AND t.outer_client_name = #{outerClientName}
            </if>
            <if test="outerBrandName != null and outerBrandName !=''">
                AND t.outer_brand_name = #{outerBrandName}
            </if>
            <if test="beginSiteId != null">
                AND t.begin_site_id = #{beginSiteId}
            </if>
            <if test="beginSiteName != null and beginSiteName !=''">
                AND t.begin_site_name = #{beginSiteName}
            </if>
            <if test="pathCode != null and pathCode !=''">
                AND t.path_code = #{pathCode}
            </if>
            <if test="pathName != null and pathName !=''">
                AND t.path_name = #{pathName}
            </if>
            <if test="deliveryBatchType != null">
                AND t.delivery_batch_type = #{deliveryBatchType}
            </if>
            <if test="sendWay != null">
                AND t.send_way = #{sendWay}
            </if>
            <if test="outDistance != null">
                AND t.out_distance = #{outDistance}
            </if>
            <if test="penaltyMoney != null">
                AND t.penalty_money = #{penaltyMoney}
            </if>
            <if test="sitePicReason != null and sitePicReason !=''">
                AND t.site_pic_reason = #{sitePicReason}
            </if>
            <if test="temperatureConditions != null and temperatureConditions !=''">
                AND t.temperature_conditions = #{temperatureConditions}
            </if>
            <if test="signOutTemperature != null">
                AND t.sign_out_temperature = #{signOutTemperature}
            </if>
            <if test="sequence != null">
                AND t.sequence = #{sequence}
            </if>
            <if test="siteName != null and siteName !=''">
                AND t.site_name = #{siteName}
            </if>
            <if test="beginSiteIdList != null and beginSiteIdList.size > 0">
                AND t.begin_site_id in
                <foreach collection="beginSiteIdList" index="index" item="beginSiteId1" open="(" separator="," close=")">
                    #{beginSiteId1}
                </foreach>
            </if>
            <if test="driverNameLike != null and driverNameLike !=''">
                AND t.driver_name like concat('%',#{driverNameLike},'%')
            </if>
            <if test="stateList != null and stateList.size > 0">
                AND t.state in
                <foreach collection="stateList" index="index" item="state1" open="(" separator="," close=")">
                    #{state1}
                </foreach>
            </if>
            <if test="deliveryTimeBegin != null">
                AND t.delivery_time &gt;= #{deliveryTimeBegin}
            </if>
            <if test="deliveryTimeEnd != null">
                AND t.delivery_time &lt;= #{deliveryTimeEnd}
            </if>
            <if test="outerClientNameLike != null and outerClientNameLike !=''">
                AND t.outer_client_name like concat('%',#{outerClientNameLike},'%')
            </if>
            <if test="aiAllPass != null">
                <if test="aiAllPass == 2">
                    and aireview.`all_pass` is null
                </if>
            </if>
            <if test="signInStatus != null">
                and t.sign_in_status = #{signInStatus}
            </if>
            <if test="temperatureCondition != null">
                and t.temperature_conditions like concat('%',#{temperatureCondition},'%')
            </if>
        </trim>
    </sql>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailQueryParam"
            resultType="net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailEntity" >
        SELECT
        t.id id,
        t.create_time createTime,
        t.update_time updateTime,
        t.performance_review_task_id performanceReviewTaskId,
        t.state state,
        t.pic_json picJson,
        t.delivery_time deliveryTime,
        t.driver_name driverName,
        t.driver_phone driverPhone,
        t.car_number carNumber,
        t.car_type carType,
        t.car_storage carStorage,
        t.delivery_batch_id deliveryBatchId,
        t.delivery_site_id deliverySiteId,
        t.site_id siteId,
        t.site_address siteAddress,
        t.outer_client_name outerClientName,
        t.outer_brand_name outerBrandName,
        t.begin_site_id beginSiteId,
        t.begin_site_name beginSiteName,
        t.path_code pathCode,
        t.path_name pathName,
        t.delivery_batch_type deliveryBatchType,
        t.send_way sendWay,
        t.out_distance outDistance,
        t.penalty_money penaltyMoney,
        t.site_pic_reason sitePicReason,
        t.temperature_conditions temperatureConditions,
        t.sign_out_temperature signOutTemperature,
        t.sequence sequence,
        t.site_name siteName,
        t.sign_in_status signInStatus,
        t.non_fruit_cold_num nonFruitColdNum,
        t.non_fruit_freeze_num nonFruitFreezeNum,
        t.sign_in_remark signInRemark
        FROM tms_delivery_performance_review_detail t
        <if test="aiAllPass != null">
            <if test="aiAllPass != 2">
            inner join tms_delivery_performance_review_detail_ai_review aireview
                on aireview.`performance_review_detail_id`  = t.id
                and aireview.`all_pass` = #{aiAllPass}
            </if>
            <if test="aiAllPass == 2">
            inner join `tms_delivery_performance_review_task` task on task.id = t.`performance_review_task_id`
                and task.`review_mode` = 1
            left join tms_delivery_performance_review_detail_ai_review aireview
                on aireview.`performance_review_detail_id`  = t.id
            </if>
        </if>
        <include refid="whereColumnBySelect" />
        <include refid="orderByQuery"/>
    </select>

    <sql id="orderByQuery">
        <if test="sortList != null and sortList.size() > 0">
            ORDER BY
            <foreach collection="sortList" item="sort1" index="index" separator=",">
                ${sort1.sortBy} ${sort1.orderBy}
            </foreach>
        </if>
    </sql>
</mapper>
