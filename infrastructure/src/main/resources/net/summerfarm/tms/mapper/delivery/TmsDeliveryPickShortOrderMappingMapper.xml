<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.delivery.TmsDeliveryPickShortOrderMappingMapper">
    <!-- 结果集映射 -->
    <resultMap id="tmsDeliveryPickShortOrderMappingResultMap" type="net.summerfarm.tms.model.delivery.TmsDeliveryPickShortOrderMapping">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="delivery_batch_id" property="deliveryBatchId" jdbcType="NUMERIC"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="out_item_id" property="outItemId" jdbcType="VARCHAR"/>
		<result column="item_desc" property="itemDesc" jdbcType="VARCHAR"/>
		<result column="short_quantity" property="shortQuantity" jdbcType="INTEGER"/>
		<result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
		<result column="driver_phone" property="driverPhone" jdbcType="VARCHAR"/>
		<result column="path_code" property="pathCode" jdbcType="VARCHAR"/>
		<result column="path_sequence" property="pathSequence" jdbcType="INTEGER"/>
        <result column="out_contact_id" property="outContactId" jdbcType="VARCHAR"/>
        <result column="out_client_id" property="outClientId" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="tmsDeliveryPickShortOrderMappingColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.delivery_batch_id,
          t.order_no,
          t.out_item_id,
          t.item_desc,
          t.short_quantity,
          t.driver_name,
          t.driver_phone,
          t.path_code,
          t.path_sequence,
          t.out_contact_id,
          t.out_client_id
    </sql>

    <insert id="batchInsert">
        INSERT INTO tms_delivery_pick_short_order_mapping (
        `delivery_batch_id`, `order_no`, `out_item_id`, `item_desc`, `short_quantity`, `driver_name`, `driver_phone`,
        `path_code`, `path_sequence`, `out_contact_id`, `out_client_id`
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.deliveryBatchId}, #{item.orderNo}, #{item.outItemId}, #{item.itemDesc}, #{item.shortQuantity}, #{item.driverName},
            #{item.driverPhone}, #{item.pathCode}, #{item.pathSequence}, #{item.outContactId}, #{item.outClientId}
            )
        </foreach>
    </insert>

    <select id="listByDeliveryBatchId" resultMap="tmsDeliveryPickShortOrderMappingResultMap" >
        SELECT <include refid="tmsDeliveryPickShortOrderMappingColumns" />
        FROM tms_delivery_pick_short_order_mapping t
        WHERE t.delivery_batch_id = #{deliveryBatchId}
    </select>

</mapper>