<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryBatchRelationMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsDeliveryBatchRelation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_id" jdbcType="BIGINT" property="batchId"/>
        <result column="relate_batch_id" jdbcType="BIGINT" property="relateBatchId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
    </resultMap>

    <resultMap id="EntityResultMap" type="net.summerfarm.tms.delivery.entity.DeliveryBatchRelationEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_id" jdbcType="BIGINT" property="batchId"/>
        <result column="relate_batch_id" jdbcType="BIGINT" property="relateBatchId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, batch_id, relate_batch_id, create_time, update_time, creator
    </sql>

    <insert id="insertBatch">
        insert into tms_delivery_batch_relation (batch_id, relate_batch_id, creator)
        values
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="relation" separator=",">
                (#{relation.batchId}, #{relation.relateBatchId}, #{relation.creator})
            </foreach>
        </if>
    </insert>

    <select id="selectBatchRelationListForceMaster" resultMap="EntityResultMap">
        /*FORCE_MASTER*/
        SELECT
        id,
        batch_id,
        relate_batch_id,
        create_time,
        creator
        FROM tms_delivery_batch_relation
        WHERE batch_id = #{batchId} OR relate_batch_id = #{batchId}
    </select>
</mapper>
