<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsPathSectionMapper">

    <select id="queryMatchEndSiteTrunkPath" resultType="net.summerfarm.tms.dao.TmsPathSection">
        SELECT t.*
        FROM tms_path_section t
                 inner join tms_path s on t.path_id = s.id and s.type = 1
        WHERE t.end_site_id = #{endSiteId}
    </select>

    <select id="querySectionByBeginEndIdType" resultType="net.summerfarm.tms.dao.TmsPathSection">
        SELECT t.*
        FROM tms_path_section t
                 inner join tms_path s on t.path_id = s.id and s.type = #{pathType}
        WHERE t.end_site_id = #{endSiteId}
          and t.begin_site_id = #{beginSiteId}
            limit 1
    </select>
</mapper>
