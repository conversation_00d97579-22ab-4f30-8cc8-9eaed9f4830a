<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.ExpenseMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.tms.expense.dto.Expense">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="delivery_path_id" jdbcType="INTEGER" property="deliveryPathId" />
    <result column="delivery_time" jdbcType="INTEGER" property="deliveryTime" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="is_review" jdbcType="INTEGER" property="isReview" />
    <result column="m_id" jdbcType="INTEGER" property="mId" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, driver_id, delivery_path_id,delivery_time, state, type, store_no, is_review, m_id, updater, update_time,creator, create_time,status,reason
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.tms.expense.dto.Expense" useGeneratedKeys="true">
    insert into expense (driver_id, delivery_path_id, delivery_time,
                         state, `type`, store_no, is_review,
                         m_id, mname, creator, create_time, status)
    values (#{driverId,jdbcType=INTEGER}, #{deliveryPathId,jdbcType=INTEGER}, #{deliveryTime,jdbcType=INTEGER},
            #{state,jdbcType=INTEGER},
            #{type,jdbcType=INTEGER}, #{storeNo,jdbcType=INTEGER}, #{isReview,jdbcType=INTEGER},
            #{mId,jdbcType=INTEGER}, #{mname,jdbcType=INTEGER}, #{creator}, now(), #{status})
  </insert>

  <select id="selectByPathId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from expense
    where delivery_path_id = #{id}
  </select>
  <select id="selectById" resultType="net.summerfarm.tms.expense.dto.ExpenseVO">
    select t.id, t.status, t.reason, t.create_time createTime, t.update_time, t.updater, site.send_way
    from expense t
           left join tms_delivery_site site on site.id = t.tms_delivery_site_id
    where t.id = #{id}
  </select>
  <select id="select" resultType="net.summerfarm.tms.expense.dto.ExpenseVO">
    select
    e.id, e.driver_id driverId, e.delivery_path_id deliveryPathId,e.delivery_time deliveryTime, e.state, e.store_no
    storeNo,w.store_name storeName, e.m_id mId,e.mname, e.updater, e.update_time updateTime,e.creator,e.create_time
    createTime,e.`type`,is_review isReview
    ,e.status,e.reason,site.send_way
    from expense e
    LEFT JOIN warehouse_logistics_center w on w.store_no = e.store_no
    left join tms_delivery_site site on site.id = e.tms_delivery_site_id

    <where>
      <if test="status!=null">
        and e.status = #{status}
      </if>
      <if test="id != null">
        AND e.id = #{id}
      </if>
      <if test="storeNo !=null">
        and e.store_no = #{storeNo}
      </if>
      <if test="createTime != null">
        AND e.create_time = #{createTime}
      </if>
      <if test="mname != null">
        AND e.mname like CONCAT('%', #{mname},'%')
      </if>
      <if test="isReview != null">
        AND e.is_review = #{isReview}
      </if>
      <if test="deliveryTime != null and deliveryEndTime != null">
        and e.delivery_time <![CDATA[>=]]> #{deliveryTime}
        and e.delivery_time <![CDATA[<=]]> #{deliveryEndTime}
      </if>
      <if test="deliveryTime != null and deliveryEndTime == null">
        and e.delivery_time = #{deliveryTime}
      </if>
      <if test="typeList != null and typeList.size != 0">
        and e.type in
        <foreach collection="typeList" open="(" close=")" separator="," item="it">
          #{it}
        </foreach>
      </if>
      <if test="storeNos != null and storeNos.size != 0">
        and e.store_no in
        <foreach collection="storeNos" open="(" close=")" separator="," item="storeNo">
          #{storeNo}
        </foreach>
      </if>
    </where>
    group by e.id
    order by e.create_time desc

  </select>
  <select id="selectExport" parameterType="net.summerfarm.tms.expense.dto.Expense"  resultType="net.summerfarm.tms.expense.dto.ExpenseDetailVO">

    select
    ed.expense_id expenseId,
    w.store_name storeName,
    e.delivery_time deliveryTime,
    e.driver_id driverId,
    ed.type,
    ed.is_review isReview,
    e.mname,
    ed.amount,
    ed.remark,
    e.create_time createTime,
    ed.mileage,
    ed.creator,
    ed.start_address startAddress,
    ed.end_address endAddress,
    e.status,
           e.state,
    site.send_way
    from expense e
    LEFT JOIN expense_detail ed on e.id = ed.expense_id
    LEFT JOIN warehouse_logistics_center w on w.store_no = e.store_no
    left join tms_delivery_site site on site.id = e.tms_delivery_site_id

    <where>
      <if test="status !=null">
        and e.status = #{status}
      </if>
      <if test="storeNo !=null">
        and e.store_no = #{storeNo}
      </if>
      <if test="createTime != null">
        AND e.create_time = #{createTime}
      </if>
      <if test="isReview != null">
        AND e.is_review = #{isReview}
      </if>
      <if test="typeList != null and typeList.size != 0">
        and e.type in
        <foreach collection="typeList" open="(" close=")" separator="," item="it">
          #{it}
        </foreach>
      </if>
      <if test="mname != null">
        AND e.mname like CONCAT('%', #{mname},'%')
      </if>
      <if test="deliveryTime != null and deliveryEndTime != null">
        and e.delivery_time <![CDATA[>=]]> #{deliveryTime}
        and e.delivery_time <![CDATA[<=]]> #{deliveryEndTime}
      </if>
      <if test="deliveryTime != null and deliveryEndTime == null">
        and e.delivery_time = #{deliveryTime}
      </if>
      <if test="storeNos != null and storeNos.size != 0">
        and e.store_no in
        <foreach collection="storeNos" open="(" close=")" separator="," item="storeNo">
          #{storeNo}
        </foreach>
      </if>
    </where>
    group by e.id,ed.id
    order by e.create_time desc
  </select>

  <update id="update" parameterType="net.summerfarm.tms.expense.dto.Expense">
    UPDATE expense
    SET is_review = #{isReview}
    WHERE id = #{id}
  </update>
  <update id="updateByExpense">
    update expense
    set
    <if test="driverId !=null">
      driver_id = #{driverId},
    </if>
    <if test="deliveryPathId !=null">
      delivery_path_id = #{deliveryPathId},
    </if>
    <if test="deliveryTime !=null">
      delivery_time = #{deliveryTime},
    </if>
    <if test="type !=null">
      type = #{type},
    </if>
    <if test="state !=null">
      state = #{state},
    </if>
    <if test="driverId !=null">
      store_no = #{storeNo},
    </if>
    <if test="isReview !=null">
      is_review = #{isReview},
    </if>
    <if test="mname !=null">
      mname = #{mname},
    </if>
    <if test="updater !=null">
      updater = #{updater},
    </if>
    <if test="status != null">
      status =#{status},
      reason = null,
    </if>
    update_time = now()
    where id = #{id}
  </update>

</mapper>