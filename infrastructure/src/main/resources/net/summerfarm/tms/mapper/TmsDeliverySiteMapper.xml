<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliverySiteMapper">

    <update id="updateSortAdd">
        UPDATE tms_delivery_site
        SET sequence = sequence + 1
        WHERE delivery_batch_id = #{deliveryBatchId}
          AND sequence <![CDATA[>=]]> #{newSequence}
          AND sequence <![CDATA[<=]]> #{sourceSeq}
    </update>

    <update id="updateSortSub">
        UPDATE tms_delivery_site
        SET sequence = sequence - 1
        WHERE delivery_batch_id = #{deliveryBatchId}
          AND sequence <![CDATA[>=]]> #{sourceSeq}
          AND sequence <![CDATA[<=]]> #{newSequence}
    </update>
    <select id="queryWaitSendMsgList" resultType="net.summerfarm.tms.delivery.entity.DeliverySiteEntity">
        select
        t.id,t.site_id,t.delivery_batch_id,t.`type`,t.sequence,t.arrive_msg_send,t.plan_arrive_time,t.sign_in_time,
        t.leave_msg_send ,t.plan_out_time,t.sign_out_time from tms_delivery_site t
        where t.delivery_batch_id in
        <foreach collection="batchIds" open="(" close=")" separator="," item="it">
            #{it}
        </foreach>
        <if test="arriveMsg">
            and t.arrive_msg_send is null
            and t.plan_arrive_time &lt; now()
            and t.sign_in_time is null
        </if>
        <if test="!arriveMsg">
            and t.leave_msg_send is null
            and t.plan_out_time &lt; now()
            and t.sign_out_time is null
        </if>
        order by t.sequence
    </select>

    <select id="queryCitySitePerformance" parameterType="net.summerfarm.tms.query.delivery.DeliverySiteQuery" resultType="net.summerfarm.tms.delivery.entity.CitySitePerformanceVO">
        SELECT
            tdb.path_code,
            tdb.path_name,
            t.sign_out_temperature,
            td.`name` as driverName,
            td.phone  as driverPhone,
            tc.car_number as carNumber,
            tc.type as carType,
            t.sign_out_pics as loadPics,
            t.sign_in_pics as deliveryPic,
            t.property_json ,
            t.`status`,
            t.sequence,
            CONCAT( tds.provice, tds.city, ifnull( tds.area, '' ), tds.address ) address,
            t.send_way,
            t.temperature_conditions,
            t.out_distance,
            t.sign_in_sign_pic as signPic,
            t.sign_in_product_pic as productPic,
            tc.storage,
            t.outer_client_name as clientName
        FROM
            tms_delivery_site t
                LEFT JOIN tms_delivery_batch tdb ON t.delivery_batch_id = tdb.id
                LEFT JOIN tms_car tc ON tc.id = tdb.car_id
                LEFT JOIN tms_driver td ON td.id = tdb.driver_id
                LEFT JOIN tms_dist_site tds ON tds.id = t.site_id
        WHERE
            t.delivery_batch_id IN
            <foreach collection="batchIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            AND t.type IN
            <foreach collection="types" item="type" open="(" close=")" separator=",">
                #{type}
            </foreach>
            <if test="driverName != null and driverName != ''">
                and td.`name` like concat('%',#{driverName},'%')
            </if>
            <if test="clientName != null and clientName != ''">
                and t.outer_client_name like concat('%',#{clientName},'%')
            </if>
            and tdb.path_id != -1
        ORDER BY
            tdb.path_code,
            t.sequence ASC
    </select>

    <select id="queryForceMasterByBatchIdAndSiteId" resultType="net.summerfarm.tms.dao.TmsDeliverySite">
        /*FORCE_MASTER*/ SELECT * FROM tms_delivery_site WHERE delivery_batch_id = #{deliveryBatchId} and site_id = #{siteId}
    </select>

    <select id="queryForceMasterByBatchIds" resultType="net.summerfarm.tms.dao.TmsDeliverySite">
        /*FORCE_MASTER*/ SELECT * FROM tms_delivery_site WHERE delivery_batch_id in
        <foreach collection="deliveryBatchIds" item="deliveryBatchId" open="(" close=")" separator=",">
            #{deliveryBatchId}
        </foreach>
    </select>

    <select id="querySameSitesByDeliveryTime" resultType="net.summerfarm.tms.dao.SameDeliverySite">
        SELECT tdb.begin_site_id AS beginSiteId,tds.`site_id` AS endSiteId,tds.`plan_arrive_time` AS deliveryTime
        FROM `tms_delivery_site` tds
        INNER  JOIN `tms_delivery_batch` tdb on tds.`delivery_batch_id` = tdb.`id`
        WHERE tdb.`type` = 5 AND tds.`plan_arrive_time` = #{deliveryTime} AND tds.`type` != 1
        GROUP BY tdb.begin_site_id,tds.`site_id` HAVING COUNT(*) > 1
    </select>

    <select id="queryListByBeginAndEndAndDeliveryTime" resultType="net.summerfarm.tms.dao.TmsDeliverySite">
        SELECT tds.*
        FROM `tms_delivery_site` tds
        INNER  JOIN `tms_delivery_batch` tdb on tds.`delivery_batch_id` = tdb.`id`
        WHERE tdb.`type` = 5 AND tdb.begin_site_id = #{beginSiteId} AND tds.site_id = #{endSiteId}
          AND tds.`plan_arrive_time` = #{deliveryTime}
    </select>

    <select id="querySiteWithNoOrderByDeliveryTime" resultType="net.summerfarm.tms.dao.TmsDeliverySite">
        SELECT * FROM `tms_delivery_site` tds
        LEFT JOIN `tms_delivery_batch` tdb on tds.`delivery_batch_id` = tdb.`id`
        WHERE tdb.`delivery_time` = #{deliveryTime} AND tdb.`type` = 5 AND tds.`type` != 1  AND
                (tdb.`begin_site_id`,tds.`site_id`) NOT IN (SELECT `begin_site_id` , `end_site_id`  FROM `tms_delivery_order` WHERE `delivery_time` = #{deliveryTime}
        AND `source` in(200,201,202,203,210,211,220,230,231))
    </select>

    <select id="queryCityDeliveryHaveSpecialSendSiteBatchIds" resultType="java.lang.Long">
        select t.delivery_batch_id
        from tms_delivery_site t
                 inner join tms_delivery_batch s on t.delivery_batch_id = s.id
        where t.plan_arrive_time = #{deliveryTime}
          and t.send_way = 1
          and t.status = 22
          and s.type = 5
        group by t.delivery_batch_id;
    </select>

    <select id="queryRecentlyDateDeliveryStorePoi" resultType="java.lang.String">
        select distinct tds.poi
        from tms_delivery_site t
                 left join tms_delivery_batch s on s.type = 5 and t.delivery_batch_id = s.id
                 left join tms_dist_site tds on t.site_id = tds.id
        where t.plan_arrive_time &gt;= #{startTime}
          and t.plan_arrive_time &lt;= #{endTime}
          and tds.city=#{city}
          <if test="area != null and area != ''">
              and tds.area=#{area}
          </if>
          and t.status = 22
    </select>
</mapper>
