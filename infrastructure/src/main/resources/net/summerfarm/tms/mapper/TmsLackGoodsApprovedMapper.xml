<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsLackGoodsApprovedMapper">

    <select id="query" parameterType="net.summerfarm.tms.query.delivery.LackApprovedQuery"
            resultType="net.summerfarm.tms.lack.entity.LackApprovedEntity">
        SELECT
        tlga.id,
        tlga.store_no AS storeNo,
        tlga.warehouse_no AS warehouseNo,
        dr.phone as driverPhone,
        dr.name as driverName,
        ds.sign_in_time AS finishTime,
        ds.outer_client_name as mname,
        tlga.sku,
        dsi.out_item_name AS pdName,
        tlga.lack_num AS lackNum,
        tlga.money,
        (tlga.lack_num*tlga.money) AS totalMoney,
        tlga.lack_type AS lackType,
        tlga.remark,
        tlga.responsible,
        tlga.state,
        tlga.pic,
        tlga.amount,
        tlga.stock_lack_num AS stockLackNum,
        tlga.stock_task_id AS stockTaskId,
        concat(batch.path_code,'-',ds.sequence) AS pathName,
        concat(site.provice,site.city,site.area,site.address) address,
        ds.sign_in_pics as deliveryPic,
        ds.sign_in_sign_pic as signPic,
        ds.sign_in_product_pic as productPic,
        tlga.buy_out AS buyOut,
        tlga.buy_out_money AS buyOutMoney,
        tlga.judgment_opinion AS judgmentOpinion,
        tlga.order_no AS orderNo,
        site.phone,
        ds.delivery_batch_id as deliveryBatchId,
        tlga.appeal_flag as appealFlag,
        tlga.m_id as mId
        FROM
        `tms_lack_goods_approved` tlga
        left join tms_delivery_site ds on ds.id = tlga.tms_delivery_site_id
        LEFT JOIN tms_delivery_site_item dsi on dsi.delivery_site_id = tlga.tms_delivery_site_id
        and tlga.sku =dsi.out_item_id
        left join tms_delivery_batch batch on batch.id = ds.delivery_batch_id
        left join tms_driver dr on dr.id = batch.driver_id
        left join tms_dist_site site on site.id = ds.site_id
        WHERE 1=1
        <if test="id != null">
            AND tlga.id = #{id}
        </if>
        <if test="warehouseNo != null">
            AND tlga.warehouse_no = #{warehouseNo}
        </if>
        <if test="storeNo != null">
            AND tlga.store_no = #{storeNo}
        </if>
        <if test="state != null">
            AND tlga.state = #{state}
        </if>
        <if test="startTime != null and endTime != null">
            AND ds.sign_in_time <![CDATA[<=]]> #{endTime} and ds.sign_in_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="mname != null and mname != ''">
            AND ds.outer_client_name like CONCAT('%',#{mname},'%')
        </if>
        <if test="pdName != null and pdName != ''">
            AND dsi.out_item_name like CONCAT('%',#{pdName},'%')
        </if>
        <if test="sku != null and sku != ''">
            AND tlga.sku = #{sku}
        </if>
        <if test="driverName != null and driverName != ''">
            AND dr.name like CONCAT('%',#{driverName},'%')
        </if>
        <if test="lackType != null">
            AND tlga.lack_type = #{lackType}
        </if>
        <if test="lackTypes != null and lackTypes.size > 0">
            AND
            <foreach collection="lackTypes" open="(" close=")" item="lackType" separator="or">
                (tlga.lack_type like CONCAT('%',#{lackType},'%'))
            </foreach>
        </if>
        <if test="areaNos != null and areaNos.size > 0">
            AND tlga.warehouse_no in
            <foreach collection="areaNos" open="(" close=")" item="areaNo" separator=",">
                #{areaNo}
            </foreach>
        </if>
        <if test="storeNos != null and storeNos.size > 0">
            AND tlga.store_no in
            <foreach collection="storeNos" open="(" close=")" item="storeNo" separator=",">
                #{storeNo}
            </foreach>
        </if>
        <if test="ids != null and ids.size > 0">
            AND tlga.id in
            <foreach collection="ids" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="appealFlag != null">
            AND tlga.appeal_flag = #{appealFlag}
        </if>
        group by tlga.id
        order by tlga.id desc
    </select>

    <select id="queryDetailById" resultType="net.summerfarm.tms.lack.entity.LackApprovedEntity">
        SELECT
        tlga.id,
        tlga.store_no AS storeNo,
        tlga.warehouse_no AS warehouseNo,
        dr.phone as driverPhone,
        dr.name as driverName,
        ds.sign_in_time AS finishTime,
        ds.outer_client_name as mname,
        tlga.sku,
        dsi.out_item_name AS pdName,
        tlga.lack_num AS lackNum,
        tlga.money,
        (tlga.lack_num*tlga.money) AS totalMoney,
        tlga.lack_type AS lackType,
        tlga.remark,
        tlga.responsible,
        tlga.state,
        tlga.pic,
        tlga.amount,
        tlga.stock_lack_num AS stockLackNum,
        tlga.stock_task_id AS stockTaskId,
        concat(batch.path_code,'-',ds.sequence) AS pathName,
        concat(site.provice,site.city,site.area,site.address) address,
        ds.sign_in_pics as deliveryPic,
        ds.sign_in_sign_pic as signPic,
        ds.sign_in_product_pic as productPic,
        tlga.buy_out AS buyOut,
        tlga.buy_out_money AS buyOutMoney,
        tlga.judgment_opinion AS judgmentOpinion,
        tlga.order_no AS orderNo,
        site.phone,
        ds.delivery_batch_id as deliveryBatchId,
        tlga.approved_name as approvedName,
        tlga.responsibility_name as responsibilityName,
        tlga.appeal_flag as appealFlag,
        tlga.responsibility_pic as responsibilityPic,
        tlga.responsibility_time as responsibilityTime,
        tlga.approved_time as approvedTime
        FROM
        `tms_lack_goods_approved` tlga
        left join tms_delivery_site ds on ds.id = tlga.tms_delivery_site_id
        LEFT JOIN tms_delivery_site_item dsi on dsi.delivery_site_id = tlga.tms_delivery_site_id and dsi.type=0
        and tlga.sku =dsi.out_item_id
        left join tms_delivery_batch batch on batch.id = ds.delivery_batch_id
        left join tms_driver dr on dr.id = batch.driver_id
        left join tms_dist_site site on site.id = ds.site_id
        WHERE  tlga.id = #{lackApprovedId}
    </select>
</mapper>
