<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDistSiteMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsDistSite">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="provice" jdbcType="VARCHAR" property="provice"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="poi" jdbcType="VARCHAR" property="poi"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="punch_distance" jdbcType="DECIMAL" property="punchDistance"/>
        <result column="out_business_no" jdbcType="VARCHAR" property="outBusinessNo"/>
        <result column="out_time" jdbcType="TIMESTAMP" property="outTime"/>
        <result column="contact_person" jdbcType="VARCHAR" property="contactPerson"/>
        <result column="supervise_site_id" jdbcType="BIGINT" property="superviseSiteId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="intelligence_path" jdbcType="TINYINT" property="intelligencePath"/>
        <result column="site_pics" jdbcType="VARCHAR" property="sitePics"/>
        <result column="site_use" jdbcType="TINYINT" property="siteUse"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, provice, city, area, address, poi, phone, name, type, state, punch_distance,
        out_business_no, out_time, contact_person, supervise_site_id, create_time, update_time,intelligence_path,site_pics,site_use
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tms_dist_site
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tms_dist_site
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true"
            parameterType="net.summerfarm.tms.dao.TmsDistSite">
        insert into tms_dist_site (id, provice, city,
        area, address, poi,
        phone, name, type,
        state, punch_distance, out_business_no,
        out_time, contact_person, supervise_site_id, create_time, update_time, creator, site_pics,
        site_use
        )
        values (#{id,jdbcType=BIGINT}, #{provice,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{poi,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT},
        #{state,jdbcType=TINYINT}, #{punchDistance,jdbcType=DECIMAL}, #{outBusinessNo,jdbcType=VARCHAR},
        #{outTime,jdbcType=TIMESTAMP}, #{contactPerson,jdbcType=VARCHAR},#{superviseSiteId,jdbcType=BIGINT},#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{creator},#{sitePics},
        #{siteUse}
        )
    </insert>
    <insert id="insertSelective" parameterType="net.summerfarm.tms.dao.TmsDistSite">
        insert into tms_dist_site
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="provice != null">
                provice,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="poi != null">
                poi,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="punchDistance != null">
                punch_distance,
            </if>
            <if test="outBusinessNo != null">
                out_business_no,
            </if>
            <if test="outTime != null">
                out_time,
            </if>
            <if test="contactPerson != null">
                contact_person,
            </if>
            <if test="superviseSiteId != null">
                supervise_site_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="sitePics != null">
                site_pics,
            </if>
            <if test="siteUse != null">
                site_use,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="provice != null">
                #{provice,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="poi != null">
                #{poi,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="punchDistance != null">
                #{punchDistance,jdbcType=DECIMAL},
            </if>
            <if test="outBusinessNo != null">
                #{outBusinessNo,jdbcType=VARCHAR},
            </if>
            <if test="outTime != null">
                #{outTime,jdbcType=TIMESTAMP},
            </if>
            <if test="contactPerson != null">
                #{contactPerson,jdbcType=VARCHAR},
            </if>
            <if test="superviseSiteId != null">
                #{superviseSiteId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sitePics != null">
                #{sitePics,jdbcType=VARCHAR},
            </if>
            <if test="siteUse != null">
                #{siteUse},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.tms.dao.TmsDistSite">
        update tms_dist_site
        <set>
            <if test="provice != null">
                provice = #{provice,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="poi != null">
                poi = #{poi,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="punchDistance != null">
                punch_distance = #{punchDistance,jdbcType=DECIMAL},
            </if>
            <if test="outBusinessNo != null">
                out_business_no = #{outBusinessNo,jdbcType=VARCHAR},
            </if>
            <if test="outTime != null">
                out_time = #{outTime,jdbcType=TIMESTAMP},
            </if>
            <if test="contactPerson != null">
                contact_person = #{contactPerson,jdbcType=VARCHAR},
            </if>
            <if test="superviseSiteId != null">
                supervise_site_id = #{superviseSiteId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sitePics != null">
                site_pics = #{sitePics,jdbcType=VARCHAR},
            </if>
            <if test="siteUse != null">
                site_use = #{siteUse}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.tms.dao.TmsDistSite">
        update tms_dist_site
        set provice = #{provice,jdbcType=VARCHAR},
        city = #{city,jdbcType=VARCHAR},
        area = #{area,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        poi = #{poi,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        name = #{name,jdbcType=VARCHAR},
        type = #{type,jdbcType=TINYINT},
        state = #{state,jdbcType=TINYINT},
        punch_distance = #{punchDistance,jdbcType=DECIMAL},
        out_business_no = #{outBusinessNo,jdbcType=VARCHAR},
        out_time = #{outTime,jdbcType=TIMESTAMP},
        contact_person = #{contactPerson,jdbcType=VARCHAR},
        supervise_site_id = #{superviseSiteId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        site_pics = #{sitePics,jdbcType=VARCHAR},
        site_use = #{siteUse,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsert">
        insert into tms_dist_site ( address, poi,
        phone, name, type,
        state, out_business_no,
        create_time,provice,city,area
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.address}, #{item.poi},
            #{item.phone}, #{item.name}, #{item.type},
            #{item.state}, #{item.outBusinessNo},
            #{item.createTime},#{item.provice},#{item.city},#{item.area}
            )
        </foreach>
    </insert>

    <select id="selectByTmsDistSite" parameterType="net.summerfarm.tms.dao.TmsDistSite" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tms_dist_site t
        <where>
            <if test="id != null">
                and t.`id`= #{id}
            </if>
            <if test="type != null">
                and t.`type`= #{type}
            </if>
            <if test="outBusinessNo != null and outBusinessNo != ''">
                and t.`out_business_no`= #{outBusinessNo}
            </if>
        </where>
    </select>

    <select id="selectByOutBusNoAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tms_dist_site t
        <where>
            <if test="outBusinessNoList != null and outBusinessNoList.size > 0">
                t.out_business_no in
                <foreach collection="outBusinessNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="type != null">
                and t.`type`= #{type}
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultType="net.summerfarm.tms.dao.TmsDistSite">
        select * from tms_dist_site t
        <where>
            <if test="siteQuery.types != null and siteQuery.types.size > 0">
                and t.type in
                <foreach collection="siteQuery.types" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
            </foreach>
            </if>
            <if test="siteQuery.type != null">
                and t.type = #{siteQuery.type}
            </if>
            <if test="siteQuery.siteIds != null and siteQuery.siteIds.size != 0">
                and t.id in
                <foreach collection="siteQuery.siteIds" item="item" index="index" open="(" close=")" separator=",">
                     (#{item})
                </foreach>
            </if>
            <if test="siteQuery.name != null">
                and (t.name like concat(#{siteQuery.name}, '%') or concat(t.provice,t.city,t.area,t.address) like concat(#{siteQuery.name}, '%'))
            </if>
            <if test="siteQuery.outBusinessNo != null">
                and t.out_business_no = #{siteQuery.outBusinessNo}
            </if>
        </where>
        order by t.create_time ${siteQuery.orderBy}
    </select>

    <select id="selectByRegion" resultType="net.summerfarm.tms.dao.TmsDistSite">
        SELECT tds.*,wlc.region FROM warehouse_logistics_center wlc
        INNER JOIN tms_dist_site tds ON wlc.store_no = tds.out_business_no AND tds.type = 1
        <where>
            wlc.status = 1 AND wlc.region IN
            <foreach collection="regions" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryForceMasterByOutBusinessNoType" resultType="net.summerfarm.tms.dao.TmsDistSite">
        /*FORCE_MASTER*/
        SELECT
        <include refid="Base_Column_List"/>
        FROM tms_dist_site t
        where
            t.out_business_no = #{outBusinessNo}
            and t.`type`= #{type}
    </select>

    <select id="queryList" resultType="net.summerfarm.tms.dao.TmsDistSite">
        select * from tms_dist_site t
        <where>
            <if test="siteQuery.type != null">
                and t.type = #{siteQuery.type}
            </if>
            <if test="siteQuery.id != null">
                and t.id = #{siteQuery.id}
            </if>
            <if test="siteQuery.name != null and siteQuery.name.trim() != ''">
                and t.name like concat(#{siteQuery.name}, '%')
            </if>
            <if test="siteQuery.fullAddressDetail != null and siteQuery.fullAddressDetail.trim() != ''">
                and concat(t.provice,t.city,t.area,t.address) like concat(#{siteQuery.fullAddressDetail}, '%')
            </if>
            <if test="siteQuery.state != null">
                and t.id = #{siteQuery.state}
            </if>
            <if test="siteQuery.outBusinessNo != null and siteQuery.outBusinessNo.trim() != ''">
                and t.out_business_no = #{siteQuery.outBusinessNo}
            </if>
        </where>
        order by id desc
    </select>

</mapper>