<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.gray.mapper.OldDistOrderMapper">

    <resultMap id="oldDistOrderMap" type="net.summerfarm.tms.gray.dto.OldDistOrderDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="outer_order_id" jdbcType="VARCHAR" property="outerOrderId"/>
        <result column="source" jdbcType="INTEGER" property="sourceDb"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="deliveryType" jdbcType="INTEGER" property="deliveryType"/>
        <result column="status" jdbcType="INTEGER" property="outOrderStatus"/>
        <result column="pick_type" jdbcType="INTEGER" property="pickType"/>
        <result column="outer_brand_name" jdbcType="VARCHAR" property="outerBrandName"/>
        <result column="outer_tenant_id" jdbcType="VARCHAR" property="outerTenantId"/>
        <result column="outer_client_id" jdbcType="VARCHAR" property="outerClientId"/>
        <result column="outer_client_name" jdbcType="VARCHAR" property="outerClientName"/>
        <result column="outer_contact_id" jdbcType="VARCHAR" property="outerContactId"/>
        <result column="outer_remark" jdbcType="VARCHAR" property="outerRemark"/>
        <result column="expect_begin_time" jdbcType="TIMESTAMP" property="expectBeginTime"/>
        <result column="expect_end_time" jdbcType="TIMESTAMP" property="expectEndTime"/>
        <result column="real_arrival_time" jdbcType="TIMESTAMP" property="realArrivalTime"/>
        <result column="cancel_type" jdbcType="INTEGER" property="cancelType"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="time_frame" jdbcType="VARCHAR" property="timeFrame"/>
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <association property="beginSite" javaType="net.summerfarm.tms.gray.dto.OldSiteDTO">
            <result column="outBusinessNo" jdbcType="VARCHAR" property="outBusinessNo"/>
            <result column="beginSitePoi" jdbcType="VARCHAR" property="poi"/>
            <result column="beginSiteType" jdbcType="INTEGER" property="type"/>
        </association>
        <association property="endSite" javaType="net.summerfarm.tms.gray.dto.OldSiteDTO">
            <result column="province" jdbcType="VARCHAR" property="province"/>
            <result column="city" jdbcType="VARCHAR" property="city"/>
            <result column="area" jdbcType="VARCHAR" property="area"/>
            <result column="address" jdbcType="VARCHAR" property="address"/>
            <result column="endSitePoi" jdbcType="VARCHAR" property="poi"/>
            <result column="phone" jdbcType="VARCHAR" property="phone"/>
            <result column="name" jdbcType="VARCHAR" property="name"/>
            <result column="endSiteType" jdbcType="INTEGER" property="type"/>
        </association>
        <collection property="distOrderItemList" ofType="net.summerfarm.tms.gray.dto.OldDistOrderItemDTO">
            <id column="item_id" jdbcType="BIGINT" property="id"/>
            <result column="outer_item_id" jdbcType="VARCHAR" property="outerItemId"/>
            <result column="outer_item_type" jdbcType="VARCHAR" property="outerItemType"/>
            <result column="outer_item_name" jdbcType="VARCHAR" property="outerItemName"/>
            <result column="outer_item_price" jdbcType="VARCHAR" property="outerItemPrice"/>
            <result column="volume" jdbcType="VARCHAR" property="volumeStr"/>
            <result column="weight" jdbcType="VARCHAR" property="weight"/>
            <result column="quantity" jdbcType="INTEGER" property="quantity"/>
            <result column="temperature" jdbcType="INTEGER" property="temperature"/>
            <result column="specification" jdbcType="VARCHAR" property="specification"/>
            <result column="unit" jdbcType="VARCHAR" property="unit"/>
            <result column="skuType" jdbcType="INTEGER" property="type"/>
            <result column="item_delivery_type" jdbcType="INTEGER" property="deliveryType"/>
            <result column="productType" jdbcType="INTEGER" property="productType"/>
        </collection>
    </resultMap>

    <select id="queryOrderDeliveryPlanDetail" resultMap="oldDistOrderMap">
        SELECT
        dp.id,
        dp.order_no AS outer_order_id,
        CASE
        WHEN o.type = 30 THEN 230
        WHEN dp.quantity = 0 THEN 200
        ELSE 203
        END                           AS `source`,
        0 AS type,
        dp.deliverytype AS deliveryType,
        if(ad.sku_sorting = 0, 1, 0) AS pick_type,
        ad.admin_id AS outer_tenant_id,
        ad.name_remakes AS outer_brand_name,
        o.m_id AS outer_client_id,
        m.mname AS outer_client_name,
        dp.contact_id AS outer_contact_id,
        o.remark AS outer_remark,
        dp.delivery_time AS expect_begin_time,
        dp.time_frame,
        dp.order_store_no AS outBusinessNo,
        wlc.poi_note AS beginSitePoi,
        1 AS beginSiteType,
        con.province,
        con.city,
        con.area,
        concat(con.address,ifnull(con.house_number, '')) AS address,
        con.house_number,
        con.poi_note AS endSitePoi,
        con.phone,
        con.contact AS name,
        0 AS endSiteType,
        dp.status,
        ifnull(dp.account_id, dp.admin_id) AS creator_id,
        ifnull(ad2.realname, msa.contact) AS creator,
        if(dp.intercept_flag = 1, 1, 0) AS cancel_type,
        dp.intercept_time AS cancel_time,
        oi.id AS item_id,
        oi.sku AS outer_item_id,
        oi.pd_name AS outer_item_name,
        oi.original_price AS outer_item_price,
        oi.volume,
        oi.weight_num AS weight,
        if(o.type = 1, dp.quantity, oi.amount) AS quantity,
        oi.storage_location AS temperature,
        oi.weight AS specification,
        0 AS item_delivery_type,
        oi.product_type as productType
        FROM delivery_plan dp
        LEFT JOIN orders o on dp.order_no = o.order_no
        LEFT JOIN order_hey_tea oht on oht.order_no = o.order_no AND o.status in (3,6) AND o.type in (0,1,3,12)
        LEFT JOIN order_item oi ON oi.order_no = o.order_no AND oi.sku != 'DF001TF0001' AND oi.category_id != 3 AND
        oi.status in (3,6)
        LEFT JOIN merchant m ON o.m_id = m.m_id
        LEFT JOIN contact con ON dp.contact_id=con.contact_id
        LEFT JOIN `admin` ad ON ad.admin_id = m.admin_id
        LEFT JOIN warehouse_logistics_center wlc ON dp.order_store_no = wlc.store_no
        LEFT JOIN `admin` ad2 ON ad2.admin_id = dp.admin_id
        LEFT JOIN merchant_sub_account msa ON msa.account_id = dp.account_id
        <where>
            dp.status in (3,6) AND dp.intercept_flag = 0 AND dp.id = #{id}
        </where>
    </select>

    <select id="queryAfterSaleDeliveryPlanDetail" resultMap="oldDistOrderMap">
        SELECT
        asdp.id,
        asdp.after_sale_no AS outer_order_id,
        if(o.type=30, 231 ,201) AS `source`,
        asdp.type,
        if(ad.sku_sorting = 0, 1, 0) AS pick_type,
        ad.admin_id AS outer_tenant_id,
        ad.name_remakes AS outer_brand_name,
        asdp.m_id AS outer_client_id,
        m.mname AS outer_client_name,
        asdp.concat_id AS outer_contact_id,
        asdp.delivery_time AS expect_begin_time,
        asdp.out_store_no AS outBusinessNo,
        wlc.poi_note AS beginSitePoi,
        1 AS beginSiteType,
        con.province,
        con.city,
        con.area,
        concat(con.address,ifnull(con.house_number, '')) AS address,
        con.poi_note AS endSitePoi,
        con.phone,
        con.contact AS name,
        0 AS endSiteType,
        asdp.status,
        asp.applyer AS creator_id,
        asp.applyer AS creator,
        if(asdd.intercept_flag = 1, 1, 0) AS cancel_type,
        asdd.intercept_time AS cancel_time,
        asdd.id AS item_id,
        asdd.sku AS outer_item_id,
        0 AS outer_item_price,
        asdd.quantity,
        asdd.type AS item_delivery_type
        FROM after_sale_delivery_path asdp
        LEFT JOIN after_sale_delivery_detail asdd ON asdd.as_delivery_path_id = asdp.id AND asdd.status = 1 AND
        asdd.intercept_flag = 0
        LEFT JOIN after_sale_proof asp ON asdp.after_sale_no = asp.after_sale_order_no
        LEFT JOIN merchant m ON asdp.m_id = m.m_id
        LEFT JOIN contact con ON asdp.concat_id=con.contact_id
        LEFT JOIN `admin` ad ON ad.admin_id = m.admin_id
        LEFT JOIN warehouse_logistics_center wlc ON asdp.out_store_no = wlc.store_no
        LEFT JOIN after_sale_order aso on asdp.after_sale_no = aso.after_sale_order_no
        LEFT JOIN orders o on o.order_no = aso.order_no
        <where>
            asdp.status >= 1 AND asdp.id = #{id}
        </where>
    </select>

    <select id="querySampleApplyDeliveryPlanDetail" resultMap="oldDistOrderMap">
        SELECT
        sa.sample_id AS id,
        sa.sample_id AS outer_order_id,
        202 AS `source`,
        0 AS type,
        if(ad.sku_sorting = 0, 1, 0) AS pick_type,
        ad.admin_id AS outer_tenant_id,
        ad.name_remakes AS outer_brand_name,
        sa.m_id AS outer_client_id,
        m.mname AS outer_client_name,
        sa.contact_id AS outer_contact_id,
        sa.delivery_time AS expect_begin_time,
        sa.store_no AS outBusinessNo,
        wlc.poi_note AS beginSitePoi,
        1 AS beginSiteType,
        con.province,
        con.city,
        con.area,
        concat(con.address,ifnull(con.house_number, '')) AS address,
        con.poi_note AS endSitePoi,
        con.phone,
        con.contact AS name,
        0 AS endSiteType,
        sa.status,
        sa.create_id AS creator_id,
        sa.create_name AS creator,
        if(ss.intercept_flag = 1, 1, 0) AS cancel_type,
        ss.intercept_time AS cancel_time,
        ss.id AS item_id,
        ss.sku AS outer_item_id,
        0 AS outer_item_price,
        ss.amount AS quantity,
        0 AS item_delivery_type
        FROM sample_apply sa
        LEFT JOIN sample_sku ss ON ss.sample_id = sa.sample_id AND ss.intercept_flag = 0
        LEFT JOIN merchant m ON sa.m_id = m.m_id
        LEFT JOIN contact con ON sa.contact_id=con.contact_id
        LEFT JOIN `admin` ad ON ad.admin_id = m.admin_id
        LEFT JOIN warehouse_logistics_center wlc ON sa.store_no = wlc.store_no
        <where>
            sa.status IN (0,1) AND sa.sample_id = #{id}
        </where>
    </select>

    <select id="querySaasOrderDeliveryPlanDetail" resultMap="oldDistOrderMap">
        SELECT
        tdp.id,
        tdp.order_no AS outer_order_id,
        if(tdp.type = 1, 210, 211) AS `source`,
        tdp.delivery_type AS deliveryType,
        tdp.delivery_type AS type,
        0 AS pick_type,
        tdp.tenant_id AS outer_tenant_id,
        tdp.store_id AS outer_client_id,
        con.mname AS outer_client_name,
        tdp.contact_id AS outer_contact_id,
        tdp.delivery_time AS expect_begin_time,
        tdp.store_no AS outBusinessNo,
        wlc.poi_note AS beginSitePoi,
        1 AS beginSiteType,
        con.province,
        con.city,
        con.area,
        con.address,
        con.poi AS endSitePoi,
        con.phone,
        con.name AS name,
        5 AS endSiteType,
        tdp.status,
        'Saas' AS creator_id,
        'Saas' AS creator,
        0 AS cancel_type,
        tdpd.id AS item_id,
        tdpd.sku AS outer_item_id,
        0 AS outer_item_price,
        tdpd.amount AS quantity,
        tdpd.delivery_type AS item_delivery_type
        FROM tms_delivery_plan tdp
        LEFT JOIN tms_delivery_plan_detail tdpd ON tdpd.tms_delivery_plan_id = tdp.id and tdpd.status = 0
        LEFT JOIN outside_contact con ON tdp.contact_id=con.id
        LEFT JOIN warehouse_logistics_center wlc ON tdp.store_no = wlc.store_no
        <where>
            tdp.status = 1 AND tdp.id = #{id}
        </where>
    </select>
    <select id="queryMallDeliveryPlanByOrderNo" resultMap="oldDistOrderMap" parameterType="java.lang.String">
        SELECT dp.id,
               dp.order_no                   AS outer_order_id,
               if(dp.quantity = 0, 200, 203) AS `source`,
               dp.contact_id                 AS outer_contact_id,
               dp.delivery_time              AS expect_begin_time
        FROM delivery_plan dp
        WHERE dp.status IN (3, 6)
          AND dp.intercept_flag = 0
          AND dp.order_no = #{orderNo}
        ORDER BY dp.id DESC
    </select>
    <select id="queryMallNeedSyncDataList" resultMap="oldDistOrderMap" parameterType="java.time.LocalDate">
        SELECT dp.id,
               dp.order_no                   AS outer_order_id,
               CASE
               WHEN o.type = 30 THEN 230
               WHEN dp.quantity = 0 THEN 200
               ELSE 203
               END                           AS `source`,
               dp.contact_id                 AS outer_contact_id,
               dp.delivery_time              AS expect_begin_time
        FROM delivery_plan dp
                 INNER JOIN orders o on dp.order_no = o.order_no AND o.status in (3, 6) AND o.type in (0, 1, 3, 12,30)
                 INNER JOIN order_item oi
                            ON oi.order_no = o.order_no AND oi.sku != 'DF001TF0001' AND oi.category_id != 3 AND
                               oi.status in (3, 6)
        WHERE dp.status IN (3, 6)
          AND dp.intercept_flag = 0
        AND dp.deliverytype = 0
        <if test="deliveryTimeFrom != null">
            AND dp.delivery_time <![CDATA[>=]]> #{deliveryTimeFrom}
        </if>
        <if test="deliveryTimeTo != null">
            AND dp.delivery_time <![CDATA[<=]]> #{deliveryTimeTo}
        </if>
        <if test="storeNo!=null">
            and dp.order_store_no=#{storeNo}
        </if>
        <if test="popHelperOrderMerchantIds != null and popHelperOrderMerchantIds.size() > 0">
            AND o.m_id NOT IN
            <foreach collection="popHelperOrderMerchantIds" item="merchantId" open="(" separator="," close=")">
                #{merchantId}
            </foreach>
        </if>
    </select>
    <select id="queryAfterSaleNeedSyncDataList" resultMap="oldDistOrderMap" parameterType="java.time.LocalDate">
        SELECT asdp.id,
               asdp.after_sale_no AS outer_order_id,
               if(o.type=30, 231 ,201)                AS `source`,
               asdp.concat_id     AS outer_contact_id,
               asdp.delivery_time AS expect_begin_time
        FROM after_sale_delivery_path asdp
                 INNER JOIN after_sale_delivery_detail asdd
                            ON asdd.as_delivery_path_id = asdp.id AND asdd.status = 1 AND asdd.intercept_flag = 0
                INNER JOIN after_sale_order aso on asdp.after_sale_no = aso.after_sale_order_no
                INNER JOIN orders o on o.order_no = aso.order_no
        WHERE asdp.status >= 1
        <if test="deliveryTimeFrom != null">
            AND asdp.delivery_time <![CDATA[>=]]> #{deliveryTimeFrom}
        </if>
        <if test="deliveryTimeTo != null">
            AND asdp.delivery_time <![CDATA[<=]]> #{deliveryTimeTo}
        </if>
        <if test="storeNo!=null">
            and asdp.out_store_no=#{storeNo}
        </if>
        <if test="popHelperOrderMerchantIds != null and popHelperOrderMerchantIds.size() > 0">
            AND asdp.m_id NOT IN
            <foreach collection="popHelperOrderMerchantIds" item="merchantId" open="(" separator="," close=")">
                #{merchantId}
            </foreach>
        </if>
    </select>
    <select id="querySampleApplyNeedSyncDataList" resultMap="oldDistOrderMap" parameterType="java.time.LocalDate">
        SELECT sa.sample_id     AS id,
               sa.sample_id     AS outer_order_id,
               202              AS `source`,
               sa.contact_id    AS outer_contact_id,
               sa.delivery_time AS expect_begin_time
        FROM sample_apply sa
                 INNER JOIN sample_sku ss ON ss.sample_id = sa.sample_id AND ss.intercept_flag = 0
        WHERE sa.status IN (0, 1)
        <if test="deliveryTimeFrom != null">
            AND sa.delivery_time <![CDATA[>=]]> #{deliveryTimeFrom}
        </if>
        <if test="deliveryTimeTo != null">
            AND sa.delivery_time <![CDATA[<=]]> #{deliveryTimeTo}
        </if>
        <if test="storeNo!=null">
            and sa.store_no=#{storeNo}
        </if>
        <if test="popHelperOrderMerchantIds != null and popHelperOrderMerchantIds.size() > 0">
            AND sa.m_id NOT IN
            <foreach collection="popHelperOrderMerchantIds" item="merchantId" open="(" separator="," close=")">
                #{merchantId}
            </foreach>
        </if>
    </select>
    <select id="querySaasNeedSyncDataList" resultMap="oldDistOrderMap" parameterType="java.time.LocalDate">
        SELECT tdp.id,
               tdp.order_no               AS outer_order_id,
               if(tdp.type = 1, 210, 211) AS `source`,
               tdp.contact_id             AS outer_contact_id,
               tdp.delivery_time          AS expect_begin_time
        FROM tms_delivery_plan tdp
                 INNER JOIN tms_delivery_plan_detail tdpd ON tdpd.tms_delivery_plan_id = tdp.id and tdpd.status = 0
        WHERE tdp.status = 1
        <if test="deliveryTimeFrom != null">
            AND tdp.delivery_time <![CDATA[>=]]> #{deliveryTimeFrom}
        </if>
        <if test="deliveryTimeTo != null">
            AND tdp.delivery_time <![CDATA[<=]]> #{deliveryTimeTo}
        </if>
        <if test="storeNo!=null">
            and tdp.store_no=#{storeNo}
        </if>
    </select>

    <select id="queryContactHouseNo" resultType="java.lang.String" parameterType="java.lang.Long">
        SELECT house_number FROM contact WHERE contact_id =#{contactId}
    </select>
</mapper>
