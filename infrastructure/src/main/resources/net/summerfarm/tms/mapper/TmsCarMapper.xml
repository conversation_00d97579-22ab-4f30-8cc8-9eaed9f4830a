<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsCarMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsCar">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="car_number" jdbcType="VARCHAR" property="carNumber"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="volume" jdbcType="DECIMAL" property="volume"/>
        <result column="weight" jdbcType="DECIMAL" property="weight"/>
        <result column="car_photos" jdbcType="VARCHAR" property="carPhotos"/>
        <result column="driver_photos" jdbcType="VARCHAR" property="driverPhotos"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="admin_id" jdbcType="VARCHAR" property="adminId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="commercial_insurance_expire_time" jdbcType="TIMESTAMP"
                property="commercialInsuranceExpireTime"/>
        <result column="commercial_insurance_amount" jdbcType="DECIMAL" property="commercialInsuranceAmount"/>
        <result column="commercial_insurance_policy" jdbcType="VARCHAR" property="commercialInsurancePolicy"/>
        <result column="traffic_insurance_expire_time" jdbcType="TIMESTAMP" property="trafficInsuranceExpireTime"/>
        <result column="traffic_insurance_amount" jdbcType="DECIMAL" property="trafficInsuranceAmount"/>
        <result column="traffic_insurance_policy" jdbcType="VARCHAR" property="trafficInsurancePolicy"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, car_number, type, volume, weight, car_photos, driver_photos, status, admin_id,
        create_time, update_time,storage,commercial_insurance_expire_time,commercial_insurance_amount,commercial_insurance_policy
,traffic_insurance_expire_time,traffic_insurance_amount,traffic_insurance_policy,quantity
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tms_car
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tms_car
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="net.summerfarm.tms.dao.TmsCar" useGeneratedKeys="true" keyProperty="id">
        insert into tms_car (id, car_number, type,
        volume, weight, car_photos,
        driver_photos, status, admin_id,
        create_time, update_time,storage)
        values (#{id,jdbcType=BIGINT}, #{carNumber,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
        #{volume,jdbcType=DECIMAL}, #{weight,jdbcType=DECIMAL}, #{carPhotos,jdbcType=VARCHAR},
        #{driverPhotos,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{adminId,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{storage})
    </insert>
    <insert id="insertSelective" parameterType="net.summerfarm.tms.dao.TmsCar" useGeneratedKeys="true" keyProperty="id">
        insert into tms_car
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="carNumber != null">
                car_number,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="volume != null">
                volume,
            </if>
            <if test="weight != null">
                weight,
            </if>
            <if test="carPhotos != null">
                car_photos,
            </if>
            <if test="driverPhotos != null">
                driver_photos,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="storage != null">
                `storage`,
            </if>
            <if test="commercialInsuranceExpireTime != null">
                commercial_insurance_expire_time,
            </if>
            <if test="commercialInsuranceAmount != null">
                commercial_insurance_amount,
            </if>
            <if test="commercialInsurancePolicy != null">
                commercial_insurance_policy,
            </if>
            <if test="trafficInsuranceExpireTime != null">
                traffic_insurance_expire_time,
            </if>
            <if test="trafficInsuranceAmount != null">
                traffic_insurance_amount,
            </if>
            <if test="trafficInsurancePolicy != null">
                traffic_insurance_policy,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="carNumber != null">
                #{carNumber,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="volume != null">
                #{volume,jdbcType=DECIMAL},
            </if>
            <if test="weight != null">
                #{weight,jdbcType=DECIMAL},
            </if>
            <if test="carPhotos != null">
                #{carPhotos,jdbcType=VARCHAR},
            </if>
            <if test="driverPhotos != null">
                #{driverPhotos,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="adminId != null">
                #{adminId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="storage != null">
                #{storage,jdbcType=INTEGER},
            </if>
            <if test="commercialInsuranceExpireTime != null">
                #{commercialInsuranceExpireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="commercialInsuranceAmount != null">
                #{commercialInsuranceAmount,jdbcType=DECIMAL},
            </if>
            <if test="commercialInsurancePolicy != null">
                #{commercialInsurancePolicy,jdbcType=VARCHAR},
            </if>
            <if test="trafficInsuranceExpireTime != null">
                #{trafficInsuranceExpireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="trafficInsuranceAmount != null">
                #{trafficInsuranceAmount,jdbcType=DECIMAL},
            </if>
            <if test="trafficInsurancePolicy != null">
                #{trafficInsurancePolicy,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="selectByCarNumber" resultMap="BaseResultMap">
        SELECT *
        FROM `tms_car`
        where car_number = #{carNumber} limit 1
    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.tms.dao.TmsCar">
        update tms_car
        set car_number                       = #{carNumber,jdbcType=VARCHAR},
            type                             = #{type,jdbcType=INTEGER},
            volume                           = #{volume,jdbcType=DECIMAL},
            weight                           = #{weight,jdbcType=DECIMAL},
            car_photos                       = #{carPhotos,jdbcType=VARCHAR},
            driver_photos                    = #{driverPhotos,jdbcType=VARCHAR},
            status                           = #{status,jdbcType=TINYINT},
            admin_id                         = #{adminId,jdbcType=VARCHAR},
            create_time                      = #{createTime,jdbcType=TIMESTAMP},
            update_time                      = #{updateTime,jdbcType=TIMESTAMP},
            storage                          = #{storage,jdbcType=INTEGER},
            commercial_insurance_expire_time = #{commercialInsuranceExpireTime,jdbcType=TIMESTAMP},
            commercial_insurance_amount      = #{commercialInsuranceAmount,jdbcType=DECIMAL},
            commercial_insurance_policy      = #{commercialInsurancePolicy,jdbcType=VARCHAR},
            traffic_insurance_expire_time    = #{trafficInsuranceExpireTime,jdbcType=TIMESTAMP},
            traffic_insurance_amount         = #{trafficInsuranceAmount,jdbcType=DECIMAL},
            traffic_insurance_policy         = #{trafficInsurancePolicy,jdbcType=VARCHAR},
            quantity                         = #{quantity,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <!--

      <select id="selectByCar" parameterType="net.summerfarm.tms.car.entity.CarDO" resultType="net.summerfarm.tms.car.entity.CarQueryPageDO">
        SELECT
          t.id,
          t.car_number as carNumber,
          t.type,
          t.volume,
          t.weight,
          t.`status`,
          t.update_time as updateTime,
          a.realname as adminName
        FROM
          `tms_car` t
            LEFT JOIN admin a ON t.admin_id = a.admin_id
        <where>
          <if test="carNumber != null and carNumber != ''">
            and t.car_number like CONCAT('%',#{carNumber},'%')
          </if>
          <if test="type != null">
            and t.type = #{type}
          </if>
          <if test="status != null">
            and t.status = #{status}
          </if>
        </where>
        order by t.id desc
      </select>
    -->

    <select id="selectByTmsCar" parameterType="net.summerfarm.tms.dao.TmsCar" resultMap="BaseResultMap">
        SELECT
        id, car_number, type, volume, weight, car_photos, driver_photos, status, admin_id,
        create_time, IFNULL(update_time,create_time)
        update_time,storage,commercial_insurance_expire_time,commercial_insurance_amount,commercial_insurance_policy
        ,traffic_insurance_expire_time,traffic_insurance_amount,traffic_insurance_policy,quantity
        FROM
        `tms_car` t
        <where>
            <if test="carNumber != null and carNumber != ''">
                and t.car_number like CONCAT('%',#{carNumber},'%')
            </if>
            <if test="type != null">
                and t.type = #{type}
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectByQuery" parameterType="net.summerfarm.tms.query.base.car.CarQuery" resultMap="BaseResultMap">
        SELECT
        id, car_number, type, volume, weight, car_photos, driver_photos, status, admin_id,
        create_time, IFNULL(update_time,create_time)
        update_time,`storage`,commercial_insurance_expire_time,commercial_insurance_amount,commercial_insurance_policy
        ,traffic_insurance_expire_time,traffic_insurance_amount,traffic_insurance_policy,quantity
        FROM
        `tms_car` t
        <where>
            <if test="carNumber != null and carNumber != ''">
                and t.car_number like CONCAT('%',#{carNumber},'%')
            </if>
            <if test="types != null and types.size > 0">
                and t.type in
                <foreach collection="types" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
            <if test="storage != null">
                and t.storage = #{storage}
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="selectByIdList" parameterType="net.summerfarm.tms.dao.TmsCar" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `tms_car`
        <where>
            <if test="carIds != null and carIds.size > 0">
                id in
                <foreach collection="carIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>