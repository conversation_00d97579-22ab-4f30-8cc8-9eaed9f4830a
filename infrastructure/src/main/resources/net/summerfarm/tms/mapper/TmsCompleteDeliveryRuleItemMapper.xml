<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsCompleteDeliveryRuleItemMapper">

    <insert id="insertBatch">
        insert into tms_complete_delivery_rule_item (rule_id, store_no, `channel`,type, biz_no, biz_name, begin_time,end_time)
        values
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="item" separator=",">
                (#{item.ruleId}, #{item.storeNo},#{item.channel}, #{item.type}, #{item.bizNo}, #{item.bizName},#{item.beginTime}, #{item.endTime})
            </foreach>
        </if>
    </insert>
</mapper>
