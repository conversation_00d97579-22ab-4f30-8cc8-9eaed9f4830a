<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsCompleteDeliveryRuleMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsCompleteDeliveryRule">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_no" jdbcType="INTEGER" property="storeNo"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="brand_rule_object_oss_key" jdbcType="VARCHAR" property="brandRuleObjectOssKey"/>
        <result column="merchant_rule_object_oss_key" jdbcType="VARCHAR" property="merchantRuleObjectOssKey"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="AlertResultMap" type="net.summerfarm.tms.alert.entity.DeliveryAlertRuleGroupEntity">
        <id column="store_no" jdbcType="BIGINT" property="storeNo"/>
        <id column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <collection property="deliveryAlertRules" ofType="net.summerfarm.tms.alert.entity.DeliveryAlertRuleEntity">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="store_no" jdbcType="INTEGER" property="storeNo"/>
            <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
            <result column="brand_rule_object_oss_key" jdbcType="VARCHAR" property="brandRuleObjectOssKey"/>
            <result column="merchant_rule_object_oss_key" jdbcType="VARCHAR" property="merchantRuleObjectOssKey"/>
            <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id, store_no, rule_name, brand_rule_object_oss_key, merchant_rule_object_oss_key, create_time, update_time
    </sql>

    <select id="queryPage" resultMap="AlertResultMap" parameterType="net.summerfarm.tms.query.alert.DeliveryAlertRuleQuery">
        SELECT tcdr.store_no,wlc.store_name
        FROM tms_complete_delivery_rule tcdr
        LEFT JOIN warehouse_logistics_center wlc on tcdr.store_no = wlc.store_no
        <where>
            <if test="storeNo != null">
                AND tcdr.store_no = #{storeNo}
            </if>
            <if test="storeNos != null and storeNos.size > 0">
                AND tcdr.store_no in
                <foreach collection="storeNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY tcdr.store_no ORDER BY tcdr.id DESC
    </select>
</mapper>
