package net.summerfarm.tms.common.base;

import com.alibaba.fastjson.JSON;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.dao.TmsDriver;
import net.summerfarm.tms.domain.DeliveryCar;
import net.summerfarm.tms.exceptions.DefaultServiceException;
import net.summerfarm.tms.mapper.TmsDriverMapper;
import net.xianmu.authentication.client.dto.ShiroUser;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description: <br/>
 * date: 2022/7/18 13:54<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class BaseService {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private TmsDriverMapper tmsDriverMapper;

    /**
     * 获取当前Tms登录用户对象
     *
     * @return
     */
    public DeliveryCar getDeliveryCar() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        log.info("获取auth登录用户信息:{}",JSON.toJSONString(user));
        if (user == null) {
            throw new DefaultServiceException("未获取到登录司机信息，请重新登录");
        }
        if (StringUtil.isBlank(user.getPhone())) {
            throw new DefaultServiceException("司机登录账号不存在手机号信息，请联系管理员");
        }

        TmsDriver driver = tmsDriverMapper.selectByPrimaryKey(user.getBizUserId());
        log.info("获取查询司机信息：{}", JSON.toJSONString(driver));
        if(driver == null){
            throw new DefaultServiceException("司机登录账号不存在");
        }
        DeliveryCar deliveryCar = new DeliveryCar();
        deliveryCar.setDriver(driver.getName());
        deliveryCar.setId(Integer.parseInt(String.valueOf(driver.getId())));
        deliveryCar.setPhone(driver.getPhone());

        return deliveryCar;
    }
}
