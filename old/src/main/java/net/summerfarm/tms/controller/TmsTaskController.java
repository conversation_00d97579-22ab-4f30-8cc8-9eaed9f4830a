package net.summerfarm.tms.controller;

import io.swagger.annotations.Api;

import net.summerfarm.tms.common.AjaxResult;
import net.summerfarm.tms.service.TmsTaskService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR> ct
 * create at:  2020/7/15  10:10
 */
@Api(tags = "TMS小程序")
@RestController
@RequestMapping("/tms-task")
public class TmsTaskController {

    @Resource
    TmsTaskService tmsTaskService;

    /**
    * 获取签收距离
    */
    @GetMapping("/getDistance/{storeNo}")
    public AjaxResult getDistance(@PathVariable Integer storeNo){
       return tmsTaskService.getDistance(storeNo);
    }

}
