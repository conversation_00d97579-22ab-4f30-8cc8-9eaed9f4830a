package net.summerfarm.tms.service.impl;


import net.summerfarm.tms.common.AjaxResult;
import net.summerfarm.tms.domain.*;
import net.summerfarm.tms.mapper.*;
import net.summerfarm.tms.service.TmsTaskService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;


/**
 * <AUTHOR> ct
 * create at:  2020/7/15  10:21
 */
@Service
public class TmsTaskServiceImpl implements TmsTaskService {
    @Resource
    private TmsOutDistanceConfigMapper tmsOutDistanceConfigMapper;

    @Override
    public AjaxResult getDistance(Integer storeNo) {
        TmsOutDistanceConfig tmsOutDistanceConfig = tmsOutDistanceConfigMapper.selectByStoreNo(storeNo);
        BigDecimal outDistance = tmsOutDistanceConfig == null ? new BigDecimal(-1) : tmsOutDistanceConfig.getOutDistance();
        return AjaxResult.getOK(outDistance);
    }




}
