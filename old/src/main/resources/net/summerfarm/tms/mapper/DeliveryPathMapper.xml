<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.tms.mapper.DeliveryPathMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.tms.domain.DeliveryPath">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="store_no" property="storeNo" jdbcType="INTEGER"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="contact_id" property="contactId" jdbcType="BIGINT"/>
        <result column="time_frame" property="timeFrame" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="total_volume" property="totalVolume" jdbcType="DECIMAL"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="addtime" property="addtime"/>
        <result column="type" property="type"/>
        <result column="path_status" property="pathStatus"/>
        <result column="finish_poi" property="finishPoi"/>
        <result column="finish_poi_name" property="finishPoiName"/>
        <result column="finish_distance" property="finishDistance"/>
        <result column="delivery_pic" property="deliveryPic"/>
        <result column="brand_type" property="brandType"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="intercept_type" property="interceptType"/>
    </resultMap>

    <sql id="BaseColumn">
        id,store_no,delivery_time,contact_id,time_frame,remark,total_volume,path,sort,addtime,`type`,path_status,finish_poi,finish_poi_name,finish_distance,
        delivery_pic,brand_type,intercept_type,delivery_type
    </sql>


    <select id="selectById" parameterType="java.lang.Integer" resultType="net.summerfarm.tms.domain.vo.DeliveryPathVO">
        SELECT
            dp.id,
            dp.store_no storeNo,
            dp.delivery_time deliveryTime,
            c.contact_id contactId,
            dp.time_frame timeFrame,
            dp.remark ,dp.path,dp.sort,dp.addtime,
            dp.path_status pathStatus,
            dp.finish_poi finishPoi,
            dp.finish_poi_name finishPoiName ,
            dp.finish_distance finishDistance,
            dp.delivery_pic deliveryPic,
            CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,
            a.realname bdName,a.phone bdPhone,c.poi_note poiNote,dp.`type`,m.mname mName,c.phone,a1.name_remakes adminName,
            dp.sign_for_status signForStatus,dp.sign_for_remarks signForRemarks,aae.user_id userId,m.m_id mId,m.mname mName,
            dp.delivery_type deliveryType,
            m.area_no areaNo,
            dp.brand_type brandType
        FROM delivery_path dp
        INNER JOIN contact c ON dp.contact_id= c.contact_id
        left join follow_up_relation fur on c.m_id = fur.m_id and fur.reassign = 0
        left join admin a on fur.admin_id = a.admin_id
        left join admin_auth_extend aae on a.admin_id = aae.admin_id and aae.`type` = 0 and aae.status = 0
        left join merchant m on m.m_id = c.m_id
        left join admin a1 on a1.admin_id = m.admin_id
        WHERE dp.id = #{id,jdbcType=INTEGER}
    </select>

</mapper>