<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.ContactMapper">
    <select id="selectByContactId" parameterType="long" resultType="net.summerfarm.tms.domain.Contact">
        SELECT contact_id contactId, c.phone, m.mname,c.poi_note poiNote,c.store_no storeNo
        FROM contact c
        LEFT JOIN merchant m ON c.m_id = m.m_id
        WHERE c.contact_id = #{contactId}
    </select>

</mapper>
