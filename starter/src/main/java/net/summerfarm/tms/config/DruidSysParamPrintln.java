package net.summerfarm.tms.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * Description: <br/>
 * date: 2023/6/21 11:22<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class DruidSysParamPrintln {

    @Autowired
    private DataSource dataSource;

    @PostConstruct
    public void paramPrintln() throws SQLException {
        log.info("数据源为:{}",dataSource.getClass());
        //获得连接
        Connection connection = dataSource.getConnection();

        DruidDataSource druidDataSource = (DruidDataSource) dataSource;
        log.info("druidDataSource 数据源最大连接池数量:{}",druidDataSource.getMaxActive());
        log.info("druidDataSource 数据源初始化连接数量::{}",druidDataSource.getInitialSize());
        log.info("druidDataSource 数据源最小连接池数量:{}",druidDataSource.getMinIdle());
        log.info("druidDataSource 数据源最大等待时间:{}",druidDataSource.getMaxWait());

        //关闭连接
        connection.close();
    }
}
