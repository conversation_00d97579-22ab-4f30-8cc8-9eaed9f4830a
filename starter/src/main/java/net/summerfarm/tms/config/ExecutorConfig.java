package net.summerfarm.tms.config;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.common.decorator.MdcDecorator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * @Package: net.summerfarm.common.config
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-25
 */
@Configuration
@Slf4j
public class ExecutorConfig {

    @Primary
    @Bean
    public Executor asyncServiceExecutor(){
        log.info("开始配置异步线程池");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(4);
        //配置最大线程数
        executor.setMaxPoolSize(10);
        //配置队列大小
        executor.setQueueCapacity(100);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("async-service-");
        executor.setTaskDecorator(new MdcDecorator());
        executor.initialize();
        return executor;
    }
    
}
