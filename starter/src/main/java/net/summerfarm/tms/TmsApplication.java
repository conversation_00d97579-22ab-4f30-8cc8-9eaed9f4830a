package net.summerfarm.tms;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.google.ortools.Loader;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.util.OrtoolsLoader;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@EnableAsync
@DubboComponentScan(basePackages={"net.summerfarm.tms.provider"})
@MapperScan({"net.summerfarm.tms.mapper","net.summerfarm.tms.*.mapper","net.summerfarm.authorization.mapper,net.summerfarm.warehouse.mapper"})
@SpringBootApplication(scanBasePackages = {"net.summerfarm.tms","net.xianmu.authentication","net.summerfarm.warehouse"})
@Slf4j
public class TmsApplication implements WebMvcConfigurer {

    public static void main(String[] args) {
        String osName = System.getProperty("os.name").toLowerCase();
        try {
            if(osName.contains("win")){
                Loader.loadNativeLibraries();
            }else{
                OrtoolsLoader.loadOrtools();
            }
        } catch (Exception e) {
            log.error("OrtoolsLoader.loadOrtools 加载本地库失败",e);
            e.printStackTrace();
        }
        SpringApplication.run(TmsApplication.class, args);
    }



    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        //1、定义一个convert转换消息的对象
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        //2、添加fastjson的配置信息
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        //3、在convert中添加配置信息
        fastConverter.setFastJsonConfig(fastJsonConfig);
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        fastConverter.setSupportedMediaTypes(supportedMediaTypes);
        fastConverter.setFeatures(SerializerFeature.DisableCircularReferenceDetect);
        StringHttpMessageConverter stringHttpMessageConverter=new StringHttpMessageConverter(StandardCharsets.UTF_8);
        //4、将convert添加到converters中
        converters.add(stringHttpMessageConverter);
        converters.add(fastConverter);
    }
}
