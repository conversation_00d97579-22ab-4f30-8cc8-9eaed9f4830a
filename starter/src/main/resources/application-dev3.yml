spring:
  application:
    name: tms

  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 43622500-784e-4b79-a13e-8954271dee95
    groupId: tms
    appKey: tEBP4GGg6KnvpcxcVbZeEA==

  # 数据库连接
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: test
    password: xianmu619
    initialSize: 48 #初始大小，默认0
    maxActive: 48 #最大活跃连接数，默认8
    maxWait: 1800 #最大等待时间， 默认-1表示无限等待
    minIdle: 48 #最小空闲连接数，默认0
    # tomcat
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 2    #dev1:0  dev2:1  dev3:2 dev4:4  qa:5
    jedis:
      pool:
        max-active: 100 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 1000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  redis:
    port: 6379
    host: test-redis.summerfarm.net
    password: xianmu619
    timeout: 5000
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 20      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
    database: 3
#rocketmq
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_tms
    secret-key: ''
    sendMsgTimeout: 10000

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: c26bc4c2-bd51-4aae-a170-1f04b9c52987
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 20 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认为0，SynchronousQueue，不存储任务 直接丢弃，配置数量>0则是LinkedBlockingQueue，如果设置成负数，默认是 LinkedBlockingQueue<Runnable>(Integer.MAX_VALUE)
    alive: 30000 #默认60秒
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 20 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认为0，SynchronousQueue，不存储任务 直接丢弃，配置数量>0则是LinkedBlockingQueue，如果设置成负数，默认是 LinkedBlockingQueue<Runnable>(Integer.MAX_VALUE)
    alive: 30000 #默认60秒
aspect:
  log:
    open: true


manageAddress: http://manage-svc

## Mybatis 配置
mybatis:
  type-aliases-package: net.summerfarm.tms.model.domain
  mapper-locations: classpath:net.summerfarm.tms.mapper/*.xml,classpath:net.summerfarm.tms.mapper.gray/*.xml,classpath:net/summerfarm/tms/mapper/outland/*.xml
# mybatis sql 打印
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mybatis-plus:
  mapper-locations: classpath:net/summerfarm/tms/mapper/*.xml,classpath:net/summerfarm/tms/mapper/gray/*.xml,classpath:net/summerfarm/tms/mapper/outland/*.xml
  global-config:
    db-config:
      update-strategy: NOT_NULL
      field-strategy: not_empty
      id-type: auto
      db-type: mysql
  configuration:
    # sql 打印
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true
# 日志配置
logging:
  pattern:
    console: "%d - %msg%n"
  level:
    root:  INFO
    org.springframework:  INFO
    org.mybatis:  INFO
    net.summerfarm: INFO
server:
  tomcat:
    accept-count: 500 #队列长度
    max-connections: 200 #最大连接数
    max-threads: 16 #最大线程数
    min-spare-threads: 16 #最小空闲线程数
  port: 80
  servlet:
    session:
      timeout: 3600
    encoding:
      charset: UTF-8

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
log-path:  ${APP_LOG_DIR:../log}

saas:
  domain: http://cosfo-mall-svc
  host: http://cosfo-mall-svc

xm:
  log:
    enable: true
    resp: true
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net

crmAddress: http://crm-svc

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: bd543515-d448-423f-baec-ec069c924c12