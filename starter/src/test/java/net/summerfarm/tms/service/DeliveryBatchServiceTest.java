package net.summerfarm.tms.service;

import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.summerfarm.tms.local.delivery.DeliveryBatchServiceImpl;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2025/1/15 16:42<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DeliveryBatchServiceTest {

    @Resource
    private DeliveryBatchServiceImpl deliveryBatchService;

    @Test
    public void test() {
        CalcTmsPathDistanceMessage message = new CalcTmsPathDistanceMessage();
        message.setBatchId(395125L);
        message.setType(DeliverySectionEnums.Type.INTELLIGENCE_PATH);
        List<WaypointsInput> waypointsInputList = new ArrayList<>();

        waypointsInputList.add(WaypointsInput.builder().siteId(206L).poi("114.36594,27.849444").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(309181L).poi("114.987986,27.127803").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(316340L).poi("114.990530,27.122097").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(316654L).poi("114.987832,27.117485").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(151580L).poi("114.986652,27.118031").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(93434L).poi("114.98617757161459,27.118314887152778").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(72310L).poi("114.983185,27.115397").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(316122L).poi("114.986455,27.11016").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(65919L).poi("114.98711642795139,27.10739556206597").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(316907L).poi("114.987542,27.106137").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(241185L).poi("114.98754,27.105898").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(248155L).poi("114.987185,27.105866").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(78839L).poi("114.987877,27.106230").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(191415L).poi("114.986603,27.100135").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(318402L).poi("114.972361,27.113472").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(307399L).poi("115.021026,27.106046").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(283027L).poi("114.913607,27.034021").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(134019L).poi("114.921731,27.024533").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(321174L).poi("114.917264,26.805545").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(185492L).poi("115.134943,27.222252").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(150373L).poi("115.137846,27.221303").build());
        waypointsInputList.add(WaypointsInput.builder().siteId(190618L).poi("115.438374,27.316266").build());

        message.setWaypointsInputList(waypointsInputList);
        deliveryBatchService.calcPathDistance(message);
    }
}

