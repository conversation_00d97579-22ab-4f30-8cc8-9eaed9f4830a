package net.summerfarm.tms.message.cunsumer.dist;

import com.alibaba.fastjson.JSON;
import net.summerfarm.tms.client.message.in.DistOrderMessage;
import net.summerfarm.tms.cost.DeliveryCostService;
import net.summerfarm.tms.enums.DeliveryCostEnums;
import net.summerfarm.tms.message.consumer.dist.DistOrderHandleConsumer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/4/24 10:16<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DistOrderHandleConsumerTest {

    @Resource
    private DistOrderHandleConsumer distOrderHandleConsumer;

    @Test
    public void processCreateTest() {
        String msg = "{\n" +
                "\t\"distOrderData\": {\n" +
                "\t\t\"addressRemark\": \"\",\n" +
                "\t\t\"beginClientName\": null,\n" +
                "\t\t\"beginSite\": {\n" +
                "\t\t\t\"address\": null,\n" +
                "\t\t\t\"area\": null,\n" +
                "\t\t\t\"city\": null,\n" +
                "\t\t\t\"name\": null,\n" +
                "\t\t\t\"outBusinessNo\": \"2\",\n" +
                "\t\t\t\"phone\": null,\n" +
                "\t\t\t\"poi\": null,\n" +
                "\t\t\t\"province\": null,\n" +
                "\t\t\t\"type\": 1\n" +
                "\t\t},\n" +
                "\t\t\"creator\": \"OFC\",\n" +
                "\t\t\"creatorId\": \"OFC\",\n" +
                "\t\t\"distOrderItemList\": [{\n" +
                "\t\t\t\"deliveryType\": 0,\n" +
                "\t\t\t\"outerItemId\": \"168541244500\",\n" +
                "\t\t\t\"outerItemName\": \"pl测试风景\",\n" +
                "\t\t\t\"outerItemPrice\": 17.50,\n" +
                "\t\t\t\"outerItemType\": \"测试3级\",\n" +
                "\t\t\t\"packType\": null,\n" +
                "\t\t\t\"productType\": 0,\n" +
                "\t\t\t\"quantity\": 1,\n" +
                "\t\t\t\"specification\": \"1包*10KG/一级/好\",\n" +
                "\t\t\t\"temperature\": 3,\n" +
                "\t\t\t\"type\": 0,\n" +
                "\t\t\t\"unit\": \"包\",\n" +
                "\t\t\t\"volume\": 0.027,\n" +
                "\t\t\t\"weight\": 10.00\n" +
                "\t\t}],\n" +
                "\t\t\"endSite\": {\n" +
                "\t\t\t\"address\": \"绍兴路279号现代雅苑杭州市下城区绍兴路279号21号楼2单元1702\",\n" +
                "\t\t\t\"area\": \"拱墅区\",\n" +
                "\t\t\t\"city\": \"杭州市\",\n" +
                "\t\t\t\"name\": \"Cxh测试\",\n" +
                "\t\t\t\"outBusinessNo\": \"350723\",\n" +
                "\t\t\t\"phone\": \"***********\",\n" +
                "\t\t\t\"poi\": \"120.26196895340641,30.280008898191163\",\n" +
                "\t\t\t\"province\": \"浙江\",\n" +
                "\t\t\t\"type\": 0\n" +
                "\t\t},\n" +
                "\t\t\"expectBeginTime\": \"2025-07-17T00:00:00\",\n" +
                "\t\t\"expectEndTime\": null,\n" +
                "\t\t\"outerBrandName\": null,\n" +
                "\t\t\"outerClientId\": \"102680\",\n" +
                "\t\t\"outerClientName\": \"Cxh测试\",\n" +
                "\t\t\"outerContactId\": \"350723\",\n" +
                "\t\t\"outerId\": \"257922\",\n" +
                "\t\t\"outerOrderId\": \"0425CRZIIK0716112262\",\n" +
                "\t\t\"outerRemark\": null,\n" +
                "\t\t\"outerTenantId\": \"874\",\n" +
                "\t\t\"pickType\": 0,\n" +
                "\t\t\"source\": 200,\n" +
                "\t\t\"storeNo\": \"2\",\n" +
                "\t\t\"timeFrame\": null,\n" +
                "\t\t\"type\": 0,\n" +
                "\t\t\"fulfillmentDeliveryWay\": 4\n" +
                "\t},\n" +
                "\t\"event\": \"DIST_ORDER_CREATE\"\n" +
                "}";
        DistOrderMessage distOrderMessage = JSON.parseObject(msg, DistOrderMessage.class);
        distOrderHandleConsumer.process(distOrderMessage);
    }


    @Test
    public void processCancelTest() {
        String cancelMsg = "{\n" +
                "\t\"distOrderData\": {\n" +
                "\t\t\"expectBeginTime\": \"2024-12-28T00:00:00\",\n" +
                "\t\t\"outerContactId\": \"347117\",\n" +
                "\t\t\"outerId\": \"115253\",\n" +
                "\t\t\"outerOrderId\": \"0124TWQO3R1226173053\",\n" +
                "\t\t\"source\": 200,\n" +
                "\t\t\"updater\": \"OFC\",\n" +
                "\t\t\"updaterId\": \"OFC\",\n" +
                "\t\t\"fulfillmentDeliveryWay\": 4\n" +
                "\t},\n" +
                "\t\"event\": \"DIST_ORDER_CANCEL\"\n" +
                "}";

        DistOrderMessage cancelDistOrderMessage = JSON.parseObject(cancelMsg, DistOrderMessage.class);
        distOrderHandleConsumer.process(cancelDistOrderMessage);

    }


    @Test
    public void processChangeTest() {
        String cancelMsg = "{\n" +
                "\t\"distOrderData\": {\n" +
                "\t\t\"newBeginSite\": {\n" +
                "\t\t\t\"address\": null,\n" +
                "\t\t\t\"area\": null,\n" +
                "\t\t\t\"city\": null,\n" +
                "\t\t\t\"name\": null,\n" +
                "\t\t\t\"outBusinessNo\": \"2\",\n" +
                "\t\t\t\"phone\": null,\n" +
                "\t\t\t\"poi\": null,\n" +
                "\t\t\t\"province\": null,\n" +
                "\t\t\t\"type\": 2\n" +
                "\t\t},\n" +
                "\t\t\"updater\": \"OFC\",\n" +
                "\t\t\"updaterId\": \"OFC\",\n" +
                "\t\t\"distOrderItemList\": [{\n" +
                "\t\t\t\"deliveryType\": 0,\n" +
                "\t\t\t\"outerItemId\": \"1029414332831\",\n" +
                "\t\t\t\"outerItemName\": \"外单测试1\",\n" +
                "\t\t\t\"outerItemPrice\": 0.00,\n" +
                "\t\t\t\"outerItemType\": \"\",\n" +
                "\t\t\t\"productType\": null,\n" +
                "\t\t\t\"quantity\": 1,\n" +
                "\t\t\t\"specification\": \"0_1斤*5只\",\n" +
                "\t\t\t\"temperature\": 1,\n" +
                "\t\t\t\"type\": 1,\n" +
                "\t\t\t\"unit\": \"包\",\n" +
                "\t\t\t\"volume\": 0.000001,\n" +
                "\t\t\t\"weight\": 1.00\n" +
                "\t\t}, {\n" +
                "\t\t\t\"deliveryType\": 0,\n" +
                "\t\t\t\"outerItemId\": \"1029417007762\",\n" +
                "\t\t\t\"outerItemName\": \"外单测试2\",\n" +
                "\t\t\t\"outerItemPrice\": 1.98,\n" +
                "\t\t\t\"outerItemType\": \"\",\n" +
                "\t\t\t\"productType\": null,\n" +
                "\t\t\t\"quantity\": 1,\n" +
                "\t\t\t\"temperature\": 1,\n" +
                "\t\t\t\"type\": 1,\n" +
                "\t\t\t\"unit\": \"块\",\n" +
                "\t\t\t\"volume\": 0.000001,\n" +
                "\t\t\t\"weight\": 1.00\n" +
                "\t\t}],\n" +
                "\t\t\"newEndSite\": {\n" +
                "\t\t\t\"address\": \"新杭商务中心啊哈哈哈哈\",\n" +
                "\t\t\t\"area\": \"西湖区\",\n" +
                "\t\t\t\"city\": \"杭州市\",\n" +
                "\t\t\t\"name\": \"外单贝塔托管22\",\n" +
                "\t\t\t\"outBusinessNo\": \"122349\",\n" +
                "\t\t\t\"phone\": \"***********\",\n" +
                "\t\t\t\"poi\": \"120.058595,30.279946\",\n" +
                "\t\t\t\"province\": \"浙江省\",\n" +
                "\t\t\t\"type\": 5\n" +
                "\t\t},\n" +
                "\t\t\"expectBeginTime\": \"2024-04-23T00:00:00\",\n" +
                "\t\t\"expectEndTime\": null,\n" +
                "\t\t\"outerBrandName\": \"外单柠季\",\n" +
                "\t\t\"outerClientId\": \"151876\",\n" +
                "\t\t\"outerClientName\": \"外单贝塔托管22\",\n" +
                "\t\t\"outerContactId\": \"122349\",\n" +
                "\t\t\"outerId\": \"140629\",\n" +
                "\t\t\"outerOrderId\": \"0423外单号哈哈哈哈123123\",\n" +
                "\t\t\"outerRemark\": null,\n" +
                "\t\t\"outerTenantId\": \"3\",\n" +
                "\t\t\"pickType\": 0,\n" +
                "\t\t\"source\": 151,\n" +
                "\t\t\"timeFrame\": null,\n" +
                "\t\t\"type\": 0,\n" +
                "\t\t\"addressRemark\": \"11外单备注啊哈哈哈123\"\n" +
                "\t},\n" +
                "\t\"event\": \"DIST_ORDER_CHANGE\"\n" +
                "}";

        DistOrderMessage cancelDistOrderMessage = JSON.parseObject(cancelMsg, DistOrderMessage.class);
        distOrderHandleConsumer.process(cancelDistOrderMessage);

    }
}
