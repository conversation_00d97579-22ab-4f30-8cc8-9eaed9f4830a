package net.summerfarm.tms.message.cunsumer.performance;

import net.summerfarm.tms.performance.domain.DeliveryPerformanceReviewDetailAppealCommandDomainService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/9/13 17:42<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DeliveryPerformanceReviewAppealCreateConsumerTest {


    @Resource
    private DeliveryPerformanceReviewDetailAppealCommandDomainService deliveryPerformanceReviewDetailAppealCommandDomainService;

    @Test
    public void create(){
        deliveryPerformanceReviewDetailAppealCommandDomainService.create(3410L);
    }
}
