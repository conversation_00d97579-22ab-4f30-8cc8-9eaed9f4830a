package net.summerfarm.tms.message.cunsumer.delivery;

import net.summerfarm.tms.delivery.DeliveryBatchCommandService;
import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.summerfarm.tms.message.consumer.delivery.DeliveryBatchOrToolsAlgorithmConsumer;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2025/4/1 16:13<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DeliveryBatchOrToolsAlgorithmConsumerTest {

    @Resource
    private DeliveryBatchCommandService deliveryBatchCommandService;

    @Test
    public void test(){
        CalcTmsPathDistanceMessage msg = new CalcTmsPathDistanceMessage();
        msg.setBatchId(566468L);
        msg.setType(DeliverySectionEnums.Type.complete_path);
        List<WaypointsInput> waypointsInputList = new ArrayList<>();
        waypointsInputList.add(new WaypointsInput(50L,  "113.132431,28.125376"));

        waypointsInputList.add(new WaypointsInput(83959L,  "112.944088,28.168563"));
        waypointsInputList.add(new WaypointsInput(249246L,  "112.946255,28.167325"));
        waypointsInputList.add(new WaypointsInput(321077L,  "112.946519,28.166971"));
        waypointsInputList.add(new WaypointsInput(293818L,  "112.948705,28.168348"));
        waypointsInputList.add(new WaypointsInput(304521L,  "112.951668,28.169094"));
        waypointsInputList.add(new WaypointsInput(267731L,  "112.952631,28.195242"));


        waypointsInputList.add(new WaypointsInput(329356L,  "112.943626,28.15574"));
        waypointsInputList.add(new WaypointsInput(116141L,  "112.939232,28.152422"));
        waypointsInputList.add(new WaypointsInput(253304L,  "112.938247,28.1523"));
        waypointsInputList.add(new WaypointsInput(77991L,  "112.935726,28.157043"));
        waypointsInputList.add(new WaypointsInput(342319L,  "112.933423,28.163339"));
        waypointsInputList.add(new WaypointsInput(307215L,  "112.937485,28.165692"));
        waypointsInputList.add(new WaypointsInput(342251L,  "112.942890,28.167783"));
        waypointsInputList.add(new WaypointsInput(259562L,  "112.943472,28.168442"));
        waypointsInputList.add(new WaypointsInput(245595L,  "112.943306,28.16994"));
        waypointsInputList.add(new WaypointsInput(274874L,  "112.943511,28.169885"));

        msg.setWaypointsInputList(waypointsInputList);
        deliveryBatchCommandService.calcPathDistanceByOrTools(msg);
    }

}
