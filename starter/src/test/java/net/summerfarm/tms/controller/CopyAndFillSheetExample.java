package net.summerfarm.tms.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import net.summerfarm.tms.excel.strategy.CopyPrevRowStyleAndMergeHandler;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.DeliveryNoteRenderingExcelVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.GoodsItemVO;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CopyAndFillSheetExample {
    public static void main(String[] args) throws Exception {
        // 模板文件路径
        String templatePath = "D:\\work\\ideaWorkSpace\\tms\\starter\\测试环境新版本.xlsx";
        // 输出文件路径
        String outputPath = "output123.xlsx";

        // 使用POI加载模板并复制Sheet页
        try (FileInputStream fis = new FileInputStream(templatePath);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            Workbook workbook = new XSSFWorkbook(fis);
            Sheet sheet1 = workbook.cloneSheet(0);
            Sheet sheet2 = workbook.cloneSheet(0);
            // 设置 Sheet 名称
            workbook.setSheetName(workbook.getSheetIndex(sheet1), "哈哈哈哈哈");
            workbook.setSheetName(workbook.getSheetIndex(sheet2), "我是第三名");

            workbook.write(bos);
            bos.flush();

            // 使用EasyExcel填充数据
            DeliveryNoteRenderingExcelVO data = createData();
            try (ExcelWriter excelWriter = EasyExcel.write(outputPath)
                    .withTemplate(new ByteArrayInputStream(bos.toByteArray()))
                    .registerWriteHandler(new CopyPrevRowStyleAndMergeHandler()).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().sheetName(sheet1.getSheetName()).build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).autoStyle(true).build();
                excelWriter.fill(data.getGoodsItemVOList(), fillConfig, writeSheet);
                excelWriter.fill(data, writeSheet);
            }
        }
    }

    public static GoodsItemVO createGoodsItem(String seq, String itemName, String specification, String temperature, String unit, String quantity, String price, String weightInPounds, String subtotal) {
        GoodsItemVO goodsItemVO = new GoodsItemVO();
        goodsItemVO.setSeq(seq);
        goodsItemVO.setItemName(itemName);
        goodsItemVO.setSpecification(specification);
        goodsItemVO.setTemperature(temperature);
        goodsItemVO.setUnit(unit);
        goodsItemVO.setQuantity(quantity);
        goodsItemVO.setPrice(price);
        goodsItemVO.setWeightInPounds(weightInPounds);
        goodsItemVO.setSubtotal(subtotal);
        return goodsItemVO;
    }

    public static DeliveryNoteRenderingExcelVO createData(){
        List<GoodsItemVO> items = Arrays.asList(
                createGoodsItem("1","商品1","1","1","1","1","1","1","1"),
                createGoodsItem("2","商品2","2","2","2","2","2","2","2"),
                createGoodsItem("3","商品3","2","2","2","2","2","2","2")
        );

        DeliveryNoteRenderingExcelVO vo = new DeliveryNoteRenderingExcelVO();
        vo.setDeliveryNoteName("配送单名称");
        vo.setMerchantName("门店名称");
        vo.setContactName("张三");
        vo.setContactPhone("123123");
        vo.setDetailAddress("详细地址");
        vo.setSendRemark("1231");
        vo.setOrderNo("111");
        vo.setDeliveryTime("2024-12-17");
        vo.setBrandName("刚刚");
        vo.setGoodsItemVOList(items);
        vo.setBdName("李四");
        vo.setBdPhone("1213456464");
        vo.setDeliveryPath("D-15");
        vo.setTotalQuantity(1);
        vo.setTotalWeightInPounds("1.2");
        vo.setTotalSubtotal("500");


        return vo;
    }


}