package net.summerfarm.tms.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2025/1/22 15:06<br/>
 *
 * <AUTHOR> />
 */
public class Test2 {
    public static void main(String[] args) throws Exception {
        // 模板文件路径
        String templatePath = "D:\\work\\ideaWorkSpace\\tms\\starter\\测试环境新版本.xlsx";;

        // 创建模板参数
        TemplateExportParams params = new TemplateExportParams(templatePath);

        // 使用 EasyPoi 导出 Excel
        // 准备数据
        Map<Integer, Map<String, Object>> sheetMap = new HashMap<>();
        Workbook workbook = ExcelExportUtil.exportExcel(sheetMap, params);

        // 动态添加更多 Sheet
        Sheet newSheet = workbook.cloneSheet(0); // 复制第一个 Sheet
        workbook.setSheetName(workbook.getNumberOfSheets() - 1, "Sheet2"); // 设置新 S


        // 第一个 Sheet 的数据
        Map<String, Object> sheet1Data = new HashMap<>();
        sheet1Data.put("merchantName", "门店名称1");
        sheetMap.put(0, sheet1Data);

        // 第二个 Sheet 的数据
        Map<String, Object> sheet2Data = new HashMap<>();
        sheet2Data.put("merchantName", "门店名称2222");
        sheetMap.put(1, sheet2Data);

        workbook = ExcelExportUtil.exportExcel(sheetMap, params);
        // 保存文件
        try (FileOutputStream fos = new FileOutputStream("outputnew.xlsx")) {
            workbook.write(fos);
        }
    }


}
