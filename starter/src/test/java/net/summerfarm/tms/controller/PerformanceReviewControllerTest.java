package net.summerfarm.tms.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.TmsApplication;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.controller.performance.PerformanceReviewController;
import net.summerfarm.tms.controller.performance.vo.PerformanceReviewTaskVO;
import net.summerfarm.tms.controller.performance.vo.ReviewDetailCitySignInVO;
import net.summerfarm.tms.controller.performance.vo.ReviewDetailCitySignOutVO;
import net.summerfarm.tms.provider.dist.TmsDistOrderQueryProviderImpl;
import net.summerfarm.tms.query.delivery.PerformanceReviewDetailQuery;
import net.summerfarm.tms.query.delivery.PerformanceReviewTaskQuery;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
@Slf4j
public class PerformanceReviewControllerTest {

    @Resource
    private PerformanceReviewController performanceReviewController;

    @Test
    public void testqueryPerformanceReviewTask(){
        PerformanceReviewTaskQuery taskQuery = new PerformanceReviewTaskQuery();
        taskQuery.setId(194L);
        taskQuery.setPageIndex(1);
        taskQuery.setPageSize(10);
        TmsResult<PageInfo<PerformanceReviewTaskVO>> result = performanceReviewController.queryPerformanceReviewTask(taskQuery);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void testqueryReviewDetailCitySignIn(){
        PerformanceReviewDetailQuery taskQuery = new PerformanceReviewDetailQuery();
        taskQuery.setPerformanceReviewTaskId(194L);
        taskQuery.setPageIndex(1);
        taskQuery.setPageSize(10);
        TmsResult<PageInfo<ReviewDetailCitySignInVO>>  result = performanceReviewController.queryReviewDetailCitySignIn(taskQuery);
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void testqueryReviewDetailCitySignOut(){
        PerformanceReviewDetailQuery performanceReviewDetailQuery = new PerformanceReviewDetailQuery();
        performanceReviewDetailQuery.setPageIndex(1);
        performanceReviewDetailQuery.setPageSize(10);
        performanceReviewDetailQuery.setPerformanceReviewTaskId(239L);
//        performanceReviewDetailQuery.setAiAllPass(0);
//        performanceReviewDetailQuery.setBeginSiteIds(Arrays.asList(206L, 207L));
//        performanceReviewDetailQuery.setDriverName("im");
//        performanceReviewDetailQuery.setStates(Arrays.asList(3,4));
//        performanceReviewDetailQuery.setBeginDeliveryTime(LocalDate.of(2024, 05, 05));
//        performanceReviewDetailQuery.setEndDeliveryTime(LocalDate.of(2024, 05, 07));
//        performanceReviewDetailQuery.setClientName("贝塔");

//        TmsResult<PageInfo<ReviewDetailCitySignInVO>> result = performanceReviewController
//                .queryReviewDetailCitySignIn(performanceReviewDetailQuery);

        TmsResult<PageInfo<ReviewDetailCitySignOutVO>> result = performanceReviewController
                .queryReviewDetailCitySignOut(performanceReviewDetailQuery);
        log.info("result: {}", JSONObject.toJSONString(result));
    }
}
