package net.summerfarm.tms.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.TmsApplication;
import net.summerfarm.tms.excel.strategy.CopyPrevRowStyleAndMergeHandler;
import net.summerfarm.tms.excel.strategy.CustomCarrierMergeStrategy;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.DeliveryNoteRenderingExcelVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.GoodsItemVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.util.*;

/**
 * Description: <br/>
 * date: 2024/12/16 15:46<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
@Slf4j
public class TmsDeliveryNoteTemplateControllerTest {

    public static GoodsItemVO createGoodsItem(String seq,String itemName,String specification,String temperature,String unit,String quantity,String price,String weightInPounds,String subtotal) {
        GoodsItemVO goodsItemVO = new GoodsItemVO();
        goodsItemVO.setSeq(seq);
        goodsItemVO.setItemName(itemName);
        goodsItemVO.setSpecification(specification);
        goodsItemVO.setTemperature(temperature);
        goodsItemVO.setUnit(unit);
        goodsItemVO.setQuantity(quantity);
        goodsItemVO.setPrice(price);
        goodsItemVO.setWeightInPounds(weightInPounds);
        goodsItemVO.setSubtotal(subtotal);
        return goodsItemVO;
    }

    @Test
    public void testDeliveryNoteTemplate() throws MalformedURLException {
        List<GoodsItemVO> items = Arrays.asList(
                createGoodsItem("1","商品1","1","1","1","1","1","1","1"),
                createGoodsItem("2","商品2","2","2","2","2","2","2","2"),
                createGoodsItem("3","3","2","2","2","2","2","2","2")
        );

        DeliveryNoteRenderingExcelVO vo = new DeliveryNoteRenderingExcelVO();
        vo.setDeliveryNoteName("配送单名称");
        vo.setMerchantName("门店名称");
        vo.setContactName("张三");
        vo.setContactPhone("123123");
        vo.setDetailAddress("详细地址");
        vo.setSendRemark("1231");
        vo.setOrderNo("111");
        vo.setDeliveryTime("2024-12-17");
        vo.setBrandName("刚刚");
        vo.setGoodsItemVOList(items);

        vo.setOrderRemark("订单备注");
        vo.setSendRemark("地址备注");
        vo.setDeliveryPath("D-15");
        vo.setTotalQuantity(1);
        vo.setTotalWeightInPounds("1.2");
        vo.setTotalSubtotal("500");
        vo.setBdName("李四");
        vo.setBdPhone("1213456464");

        String templateFileName = "线上文件.xlsx";
        String fileName = "填充后的文件.xlsx";

        try (ExcelWriter excelWriter = EasyExcel.write(fileName)
                .withTemplate(templateFileName)
                .registerWriteHandler(new CopyPrevRowStyleAndMergeHandler())
                .build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(vo, writeSheet);
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(items, fillConfig, writeSheet);
        }
    }


/*
    public static void main(String[] args) {
        String filePath = "D:\\work\\ideaWorkSpace\\tms\\starter\\excel-file-7913158b2d7b4cdc984beb8893a47551.xlsx"; // 替换为你的文件路径

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            // 遍历所有 Sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                // 遍历所有行和单元格，确保文件结构完整
                for (Row row : sheet) {
                    for (Cell cell : row) {
                        // 读取并重新写入单元格内容
                        cell.setCellValue(cell.getStringCellValue());
                    }
                }
            }

            // 保存优化后的文件
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }

            System.out.println("File optimized successfully.");
        } catch (IOException e) {
            e.printStackTrace();
        }

        List<GoodsItemVO> items = Arrays.asList(
                createGoodsItem("1","商品1","1","1","1","1","1","1","1"),
                createGoodsItem("2","商品2","2","2","2","2","2","2","2"),
                createGoodsItem("3","3","2","2","2","2","2","2","2")
        );

        DeliveryNoteRenderingExcelVO vo = new DeliveryNoteRenderingExcelVO();
        vo.setDeliveryNoteName("配送单名称");
        vo.setMerchantName("门店名称");
        vo.setContactName("张三");
        vo.setContactPhone("123123");
        vo.setDetailAddress("详细地址");
        vo.setSendRemark("1231");
        vo.setOrderNo("111");
        vo.setDeliveryTime("2024-12-17");
        vo.setBrandName("刚刚");
        vo.setGoodsItemVOList(items);

        vo.setOrderRemark("订单备注");
        vo.setSendRemark("地址备注");
        vo.setDeliveryPath("D-15");
        vo.setTotalQuantity(1);
        vo.setTotalWeightInPounds("1.2");
        vo.setTotalSubtotal("500");
        vo.setBdName("李四");
        vo.setBdPhone("1213456464");

        String templateFileName = "D:\\work\\ideaWorkSpace\\tms\\starter\\excel-file-7913158b2d7b4cdc984beb8893a47551.xlsx";
        String fileName = "填充后的文件.xlsx";

        try (ExcelWriter excelWriter = EasyExcel.write(fileName)
                .withTemplate(templateFileName)
                .registerWriteHandler(new CopyPrevRowStyleAndMergeHandler())
                .build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(vo, writeSheet);
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(items, fillConfig, writeSheet);
        }
    }*/

    public static void main(String[] args) {
        List<GoodsItemVO> items = Arrays.asList(
                createGoodsItem("1","商品1","1","1","1","1","1","1","1"),
                createGoodsItem("2","商品2","2","2","2","2","2","2","2"),
                createGoodsItem("3","3","2","2","2","2","2","2","2")
        );

        DeliveryNoteRenderingExcelVO vo = new DeliveryNoteRenderingExcelVO();
        vo.setDeliveryNoteName("配送单名称");
        vo.setMerchantName("门店名称");
        vo.setContactName("张三");
        vo.setContactPhone("123123");
        vo.setDetailAddress("详细地址");
        vo.setSendRemark("1231");
        vo.setOrderNo("111");
        vo.setDeliveryTime("2024-12-17");
        vo.setBrandName("刚刚");
        vo.setGoodsItemVOList(items);

        vo.setOrderRemark("订单备注");
        vo.setSendRemark("地址备注");
        vo.setDeliveryPath("D-15");
        vo.setTotalQuantity(1);
        vo.setTotalWeightInPounds("1.2");
        vo.setTotalSubtotal("500");
        vo.setBdName("李四");
        vo.setBdPhone("1213456464");

        String templateFileName = "D:\\work\\ideaWorkSpace\\tms\\starter\\舒凡提供.xlsx";
        String fileName = "填充后的文件.xlsx";

        try (ExcelWriter excelWriter = EasyExcel.write(fileName)
                .withTemplate(templateFileName)
                .registerWriteHandler(new CopyPrevRowStyleAndMergeHandler())
                .build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(vo, writeSheet);
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(items, fillConfig, writeSheet);
        }
    }

}
