package net.summerfarm.tms.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.google.common.collect.Lists;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.DeliveryNoteRenderingExcelVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.GoodsItemVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.*;

public class DynamicSheetTemplateExample {

    public static GoodsItemVO createGoodsItem(String seq,String itemName,String specification,String temperature,String unit,String quantity,String price,String weightInPounds,String subtotal) {
        GoodsItemVO goodsItemVO = new GoodsItemVO();
        goodsItemVO.setSeq(seq);
        goodsItemVO.setItemName(itemName);
        goodsItemVO.setSpecification(specification);
        goodsItemVO.setTemperature(temperature);
        goodsItemVO.setUnit(unit);
        goodsItemVO.setQuantity(quantity);
        goodsItemVO.setPrice(price);
        goodsItemVO.setWeightInPounds(weightInPounds);
        goodsItemVO.setSubtotal(subtotal);
        return goodsItemVO;
    }

    public static void main(String[] args) {
        String templateFileName = "D:\\work\\ideaWorkSpace\\tms\\starter\\测试环境新版本.xlsx";
        String fileName = "填充后的文件.xlsx";

        // 使用 EasyExcel 填充第一个 Sheet 的数据
        EasyExcel.write(fileName)
                .withTemplate(templateFileName)
                .sheet("Sheet1")
                .doFill(createSheet1Data());

        // 使用 Apache POI 复制模板并动态生成多个 Sheet
        try (FileInputStream fis = new FileInputStream(fileName);
             Workbook workbook = new XSSFWorkbook(fis)) {
            // 获取模板中的第一个 Sheet
            Sheet templateSheet = workbook.getSheetAt(0);

            // 动态生成多个 Sheet
            for (int i = 2; i <= 3; i++) {
                Sheet newSheet = workbook.cloneSheet(0); // 复制模板 Sheet
                workbook.setSheetName(workbook.getNumberOfSheets() - 1, "Sheet" + i); // 设置新 Sheet 名称

                // 填充新 Sheet 的数据
                fillSheetData(newSheet, createSheetData(i));
            }

            // 调整 Sheet 顺序（可选）
            List<String> sheetOrder = Arrays.asList("Sheet3", "Sheet2");
            for (int i = 0; i < sheetOrder.size(); i++) {
                workbook.setSheetOrder(sheetOrder.get(i), i);
            }

            // 保存最终文件
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Map<String, Object> createSheet1Data() {
        Map<String, Object> data = new HashMap<>();
        data.put("merchantName", "门店名称123123132");
        return data;
    }

    private static Map<String, Object> createSheetData(int sheetNumber) {
        Map<String, Object> data = new HashMap<>();
        data.put("merchantName", "门店名称"+ sheetNumber);
        return data;
    }

    private static void fillSheetData(Sheet sheet, Map<String, Object> data) {
        // 填充数据到指定的 Sheet
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 假设模板中占位符为 {name} 和 {age}
            for (Row row : sheet) {
                for (Cell cell : row) {
                    if (cell.getCellType() == CellType.STRING && cell.getStringCellValue().equals("{" + key + "}")) {
                        cell.setCellValue(value.toString());
                    }
                }
            }
        }
    }
}