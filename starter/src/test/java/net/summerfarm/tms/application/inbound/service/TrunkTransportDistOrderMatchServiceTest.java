package net.summerfarm.tms.application.inbound.service;

import net.summerfarm.tms.service.dist.TrunkTransportDistOrderMatchService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2025/7/16 15:57<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TrunkTransportDistOrderMatchServiceTest {

    @Resource
    private TrunkTransportDistOrderMatchService trunkTransportDistOrderMatchService;

    @Test
    public void test(){
        trunkTransportDistOrderMatchService.matchAndCreateBatch();
    }
}
