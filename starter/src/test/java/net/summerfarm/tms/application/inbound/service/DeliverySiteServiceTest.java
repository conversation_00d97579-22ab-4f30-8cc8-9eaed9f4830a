package net.summerfarm.tms.application.inbound.service;

import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.delivery.dto.pick.PickScanCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/8/16 14:33<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DeliverySiteServiceTest {

    @Resource
    private DeliverySiteService deliverySiteService;

    @Test
    public void pickScan(){}{
        PickScanCommand command = new PickScanCommand();
        command.setOnlyCode("000116182S0001");
        command.setBatchId(390889L);
        deliverySiteService.pickScan(command);
    }
}
