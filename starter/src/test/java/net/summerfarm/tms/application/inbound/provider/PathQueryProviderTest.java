package net.summerfarm.tms.application.inbound.provider;

import net.summerfarm.tms.TmsApplication;
import net.summerfarm.tms.client.path.req.PathRouteQueryReq;
import net.summerfarm.tms.client.path.req.PathSiteReq;
import net.summerfarm.tms.client.path.resp.PathRouteResp;
import net.summerfarm.tms.provider.path.PathQueryProviderImpl;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 路线查询测试类
 * date: 2025/7/16 14:14<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
public class PathQueryProviderTest {

    @Resource
    private PathQueryProviderImpl pathQueryProvider;

    @Test
    public void queryIsHaveRoute(){
        //[{"beginSite":{"outBusinessNo":"2","type":1},"endSite":{"address":"三墩路88号","area":"拱墅区","city":"杭州市","name":"张小虎",
        // "outBusinessNo":"353493","phone":"***********","poi":"121.015067,31.165958","province":"浙江","type":0}}]
        PathSiteReq beginReq = new PathSiteReq();
        beginReq.setOutBusinessNo("2");
        beginReq.setType(1);


        PathSiteReq endReq = new PathSiteReq();
        endReq.setOutBusinessNo("353493");
        endReq.setProvince("浙江");
        endReq.setCity("杭州市");
        endReq.setArea("拱墅区");
        endReq.setAddress("三墩路88号");
        endReq.setPoi("121.015067,31.165958");
        endReq.setPhone("***********");
        endReq.setName("张小虎");
        endReq.setType(0);

        PathRouteQueryReq req = new PathRouteQueryReq();
        req.setBeginSite(beginReq);
        req.setEndSite(endReq);

        DubboResponse<PathRouteResp> respDubboResponse = pathQueryProvider.queryIsHaveRoute(req);
        System.out.println(respDubboResponse);

    }
}
