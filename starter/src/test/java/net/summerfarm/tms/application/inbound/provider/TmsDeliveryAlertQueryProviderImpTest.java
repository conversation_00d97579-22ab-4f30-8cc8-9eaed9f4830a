package net.summerfarm.tms.application.inbound.provider;

import net.summerfarm.tms.TmsApplication;
import net.summerfarm.tms.client.alert.req.SummerfarmDeliveryAlertTimeQueryReq;
import net.summerfarm.tms.client.alert.resp.SummerfarmDeliveryAlertTimeResp;
import net.summerfarm.tms.provider.alert.TmsDeliveryAlertQueryProviderImpl;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/3/29 15:40<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
public class TmsDeliveryAlertQueryProviderImpTest {

    @Resource
    private TmsDeliveryAlertQueryProviderImpl tmsDeliveryAlertQueryProvider;

    @Test
    public void querySummerfarmDeliveryAlertTime() {
        SummerfarmDeliveryAlertTimeQueryReq req = new SummerfarmDeliveryAlertTimeQueryReq();
        req.setStoreNo(16);
        req.setAdminId("880");
        req.setMerchantId("30392111");
        req.setCity("上海市");
        req.setArea("徐汇区");

        DubboResponse<SummerfarmDeliveryAlertTimeResp> respDubboResponse = tmsDeliveryAlertQueryProvider.querySummerfarmDeliveryAlertTime(req);
        System.out.println(respDubboResponse);
    }

}
