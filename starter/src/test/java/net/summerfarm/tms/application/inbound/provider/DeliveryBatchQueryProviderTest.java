package net.summerfarm.tms.application.inbound.provider;

import net.summerfarm.tms.TmsApplication;
import net.summerfarm.tms.delivery.dto.DeliverySectionDTO;
import net.summerfarm.tms.provider.delivery.DeliveryBatchQueryProviderImpl;
import net.summerfarm.tms.query.delivery.DeliverySectionQuery;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2025/6/24 16:32<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
public class DeliveryBatchQueryProviderTest {

    @Resource
    private DeliveryBatchQueryProviderImpl deliveryBatchQueryProvider;

    @Test
    public void queryDeliverySectionTest(){
        DubboResponse<List<DeliverySectionDTO>> listDubboResponse = deliveryBatchQueryProvider.queryDeliverySection(DeliverySectionQuery.builder()
                .batchId(570959L)
                .finishDeliveryTime(LocalDateTime.now())
                .type(5)
                .build());
        System.out.println(listDubboResponse);
    }
}
