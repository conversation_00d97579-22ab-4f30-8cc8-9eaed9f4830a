package net.summerfarm.tms.application.inbound.service;

import net.summerfarm.tms.TmsApplication;
import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.summerfarm.tms.local.delivery.DeliveryBatchDistanceHandleService;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2025/5/20 12:02<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
public class DeliveryBatchDistanceHandleServiceTest {

    @Resource
    private DeliveryBatchDistanceHandleService deliveryBatchDistanceHandleService;

    @Test
    public void test(){
        CalcTmsPathDistanceMessage message = new CalcTmsPathDistanceMessage();
        message.setBatchId(569147L);
        message.setType(DeliverySectionEnums.Type.complete_path);
        deliveryBatchDistanceHandleService.calcPathDistance(message);
    }


}
