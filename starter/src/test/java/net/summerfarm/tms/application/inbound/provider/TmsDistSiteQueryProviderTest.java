package net.summerfarm.tms.application.inbound.provider;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.TmsApplication;
import net.summerfarm.tms.client.dist.req.DistSiteQueryReq;
import net.summerfarm.tms.client.dist.resp.DistSiteResp;
import net.summerfarm.tms.provider.dist.TmsDistSiteQueryProviderImpl;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/8/2 17:44<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
public class TmsDistSiteQueryProviderTest {

    @Resource
    private TmsDistSiteQueryProviderImpl tmsDistSiteQueryProvider;

    @Test
    public void testPage(){
        DistSiteQueryReq req = new DistSiteQueryReq();
        DubboResponse<PageInfo<DistSiteResp>> pageInfoDubboResponse = tmsDistSiteQueryProvider.queryPageSite(req);
        System.out.println(pageInfoDubboResponse);

        req.setName("我想你测试");
        DubboResponse<PageInfo<DistSiteResp>> pageInfoDubboResponse2 = tmsDistSiteQueryProvider.queryPageSite(req);
        System.out.println(pageInfoDubboResponse2);

        req = new DistSiteQueryReq();
        req.setType(4);
        DubboResponse<PageInfo<DistSiteResp>> pageInfoDubboResponse3 = tmsDistSiteQueryProvider.queryPageSite(req);
        System.out.println(pageInfoDubboResponse2);

    }
}
