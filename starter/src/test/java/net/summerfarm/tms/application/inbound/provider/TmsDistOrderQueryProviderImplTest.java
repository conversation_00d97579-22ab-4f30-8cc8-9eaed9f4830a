package net.summerfarm.tms.application.inbound.provider;

import com.alibaba.fastjson.JSON;
import net.summerfarm.tms.TmsApplication;
import net.summerfarm.tms.client.delivery.req.CityDeliveryBatchReq;
import net.summerfarm.tms.client.delivery.resp.CityDeliveryBatchResp;
import net.summerfarm.tms.client.dist.req.CreateDistOrderValidateReq;
import net.summerfarm.tms.client.dist.req.DistOrderCancelValidateReq;
import net.summerfarm.tms.client.dist.req.OrderNoDeliveryInfoReq;
import net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq;
import net.summerfarm.tms.client.dist.resp.OuterOrderValidateResp;
import net.summerfarm.tms.client.dist.resp.TrunkChangeTransportResp;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderStandardResp;
import net.summerfarm.tms.client.message.in.DistOrderCancelMessage;
import net.summerfarm.tms.client.message.in.DistOrderCreateMessage;
import net.summerfarm.tms.provider.delivery.TmsCityDeliveryBatchQueryProviderImpl;
import net.summerfarm.tms.provider.dist.TmsDistOrderQueryProviderImpl;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * Description: <br/>
 * date: 2024/4/30 14:20<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
public class TmsDistOrderQueryProviderImplTest {
    @Resource
    private TmsDistOrderQueryProviderImpl tmsDistOrderQueryProvider;

    @Resource
    private TmsCityDeliveryBatchQueryProviderImpl tmsCityDeliveryBatchQueryProvider;
    @Test
    public void test(){
        String msg = "{\n" +
                "\t\"addressRemark\": \"\",\n" +
                "\t\"beginSite\": {\n" +
                "\t\t\"outBusinessNo\": \"1\",\n" +
                "\t\t\"type\": 2\n" +
                "\t},\n" +
                "\t\"creator\": \"子泰上海\",\n" +
                "\t\"creatorId\": \"10134\",\n" +
                "\t\"distOrderItemList\": [{\n" +
                "\t\t\"deliveryType\": 0,\n" +
                "\t\t\"outerItemId\": \"#sku:130ad7701f27baf2413ce199e0a49730#\",\n" +
                "\t\t\"outerItemName\": \"子泰外单2货品-西瓜\",\n" +
                "\t\t\"outerItemPrice\": 0,\n" +
                "\t\t\"productType\": 0,\n" +
                "\t\t\"quantity\": 3,\n" +
                "\t\t\"temperature\": 2,\n" +
                "\t\t\"type\": 0,\n" +
                "\t\t\"unit\": \"件\",\n" +
                "\t\t\"volume\": 0.01,\n" +
                "\t\t\"weight\": 3.000000\n" +
                "\t}],\n" +
                "\t\"endSite\": {\n" +
                "\t\t\"address\": \"汤口镇汤泉路1号\",\n" +
                "\t\t\"area\": \"黄山区\",\n" +
                "\t\t\"city\": \"黄山市\",\n" +
                "\t\t\"name\": \"子泰外单门店2-2\",\n" +
                "\t\t\"outBusinessNo\": \"42246\",\n" +
                "\t\t\"phone\": \"***********\",\n" +
                "\t\t\"poi\": \"118.160149,30.139318\",\n" +
                "\t\t\"province\": \"安徽省\",\n" +
                "\t\t\"type\": 5\n" +
                "\t},\n" +
                "\t\"expectBeginTime\": \"2024-05-08T00:00:00\",\n" +
                "\t\"outerBrandName\": \"子泰外单2\",\n" +
                "\t\"outerClientId\": \"162501\",\n" +
                "\t\"outerClientName\": \"子泰外单门店2-2\",\n" +
                "\t\"outerContactId\": \"42246\",\n" +
                "\t\"outerId\": \"null\",\n" +
                "\t\"outerOrderId\": \"1\",\n" +
                "\t\"outerRemark\": \"第一次导入\",\n" +
                "\t\"outerTenantId\": \"24673\",\n" +
                "\t\"pickType\": 2,\n" +
                "\t\"source\": 151,\n" +
                "\t\"storeNo\": \"1\",\n" +
                "\t\"type\": 0\n" +
                "}";
        DistOrderCreateMessage distOrderCreateMessage = JSON.parseObject(msg, DistOrderCreateMessage.class);
        CreateDistOrderValidateReq req = new CreateDistOrderValidateReq();
        req.setDistOrderCreateMessageList(Arrays.asList(distOrderCreateMessage));
        DubboResponse<List<OuterOrderValidateResp>> listDubboResponse = tmsDistOrderQueryProvider.outerCreateDistOrderValidate(req);
        System.out.println(listDubboResponse);
    }


    @Test
    public void cancel(){
        String cancelMsg = "{\n" +
                "\t\"expectBeginTime\": \"2024-05-10T00:00:00\",\n" +
                "\t\"outerContactId\": \"42166\",\n" +
                "\t\"outerId\": \"143100\",\n" +
                "\t\"outerOrderId\": \"#XMTOON#20240508000013\",\n" +
                "\t\"source\": 151,\n" +
                "\t\"updater\": \"OFC\",\n" +
                "\t\"updaterId\": \"OFC\"\n" +
                "}";
        DistOrderCancelMessage cancelMessage = JSON.parseObject(cancelMsg, DistOrderCancelMessage.class);
        DistOrderCancelValidateReq req = new DistOrderCancelValidateReq();
        req.setDistOrderCancelList(Arrays.asList(cancelMessage));
        DubboResponse<List<OuterOrderValidateResp>> listDubboResponse = tmsDistOrderQueryProvider.outerCancelDistOrderValidate(req);
        System.out.println(listDubboResponse);
    }

    @Test
    public void queryOrderNoDeliveryInfo(){
        OrderNoDeliveryInfoReq req = new OrderNoDeliveryInfoReq();
        req.setDeliveryTime(LocalDate.now().minusDays(5));
        req.setOrderNos(Arrays.asList("PO172292732643331", "PO172302208745665", "PO172293092943921", "PO172292798564058", "PO172293020403123"));
        DubboResponse<List<DistOrderStandardResp>> listDubboResponse = tmsDistOrderQueryProvider.queryOrderNoDeliveryInfo(req);
        System.out.println(listDubboResponse);
    }

    @Test
    public void queryCityDeliveryBatch(){
        CityDeliveryBatchReq req = new CityDeliveryBatchReq();
        req.setDeliveryTime(LocalDate.now().minusDays(3));
        req.setStoreNo(2);
        DubboResponse<List<CityDeliveryBatchResp>> listDubboResponse = tmsCityDeliveryBatchQueryProvider.queryCityDeliveryBatch(req);
        System.out.println(listDubboResponse);
    }

    @Test
    public void queryTrunkChangeTransportDetail(){
        DistOrderQueryStandardReq req = new DistOrderQueryStandardReq();
        req.setOuterOrderId("01250EL7H70723170539");
        req.setOuterContactId("352873");
        req.setSource(200);
        req.setExpectBeginTime(LocalDate.now().atStartOfDay());
        DubboResponse<TrunkChangeTransportResp> trunkChangeTransportRespDubboResponse = tmsDistOrderQueryProvider.queryTrunkChangeTransportDetail(req);
        System.out.println(trunkChangeTransportRespDubboResponse);
    }
}
