package net.summerfarm.tms.provider.dist;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.client.dist.provider.TmsDistSiteQueryProvider;
import net.summerfarm.tms.client.dist.req.DistSiteQueryReq;
import net.summerfarm.tms.client.dist.resp.DistSiteResp;
import net.summerfarm.tms.common.util.BeanCopyUtil;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.provider.dist.converter.DistSiteDubboConverter;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**FenceServiceImpl
 * Description: <br/>
 * date: 2023/1/28 14:53<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsDistSiteQueryProviderImpl implements TmsDistSiteQueryProvider {
    @Resource
    private SiteService siteService;

    @Override
    public DubboResponse<DistSiteResp> queryDistSiteById(Long id, Integer type) {
        TmsResult<SiteDTO> result = siteService.siteDetail(id, type);
        if (result.isSuccess()){
            return DubboResponse.getOK(DistSiteDubboConverter.distSiteDTOToResp(result.getData()));
        }else{
            return DubboResponse.getError(result.getErrCode(), result.getErrorMessage());
        }
    }

    @Override
    public DubboResponse<List<DistSiteResp>> queryDistSite(DistSiteQueryReq distSiteQueryReq) {
        log.info("批量查询点位详情接口参数:{}", JSON.toJSONString(distSiteQueryReq));
        if(distSiteQueryReq.getType() == null){
            throw new TmsRuntimeException("点位类型不能为空");
        }

        if(!Arrays.asList(SiteTypeEnum.warehouse.getCode(),SiteTypeEnum.store.getCode()).contains(distSiteQueryReq.getType())){
            throw new TmsRuntimeException("点位类型只能为仓库或者城配仓点位");
        }
        TmsResult<List<SiteDTO>> result = siteService.queryList(DistSiteDubboConverter.reqToDistSite(distSiteQueryReq));
        if (result.isSuccess()){
            List<SiteDTO> data = result.getData();
            return DubboResponse.getOK(data.stream().map(DistSiteDubboConverter::distSiteDTOToResp).collect(Collectors.toList()));
        }else {
            return DubboResponse.getError(result.getErrCode(), result.getErrorMessage());
        }
    }

    @Override
    public DubboResponse<List<DistSiteResp>> queryPurchaseOwnSaleOutSiteForBms() {
        return DubboResponse.getOK(siteService.queryPurchaseOwnSaleOutSite().stream().map(DistSiteDubboConverter::distSiteDTOToResp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<PageInfo<DistSiteResp>> querySpecifySiteForBms(DistSiteQueryReq distSiteQueryReq) {
        if(Objects.isNull(distSiteQueryReq)){
            throw new TmsRuntimeException("点位类型不能为空");
        }
        PageInfo<SiteDTO> siteDTOPageInfo = siteService.querySpecifySiteForBms(distSiteQueryReq);
        List<DistSiteResp> collect = siteDTOPageInfo.getList().stream().map(DistSiteDubboConverter::distSiteDTOToResp).collect(Collectors.toList());

        PageInfo<DistSiteResp> result = new PageInfo<>();
        BeanCopyUtil.copyProperties(siteDTOPageInfo, result);
        result.setList(collect);

        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<PageInfo<DistSiteResp>> queryPageSite(DistSiteQueryReq req) {
        if(req == null){
            throw new TmsRuntimeException("请求信息不能为空");
        }
        if(req.getType() == null){
            throw new TmsRuntimeException("点位类型不能为空");
        }
        SiteQuery query = SiteQuery.builder()
                .type(req.getType())
                .name(req.getName())
                .fullAddressDetail(req.getFullAddressDetail())
                .id(req.getId())
                .outBusinessNo(req.getOutBusinessNo())
                .state(req.getState())
                .build();
        query.setPageIndex(req.getPageIndex());
        query.setPageSize(req.getPageSize());
        PageInfo<SiteDTO> siteDTOPageInfo = siteService.siteSearchPage(query);

        List<DistSiteResp> distSiteRespList = siteDTOPageInfo.getList().stream().map(DistSiteDubboConverter::distSiteDTOToResp).collect(Collectors.toList());

        PageInfo<DistSiteResp> result = new PageInfo<>();
        BeanCopyUtil.copyProperties(siteDTOPageInfo, result);
        result.setList(distSiteRespList);

        return DubboResponse.getOK(result);
    }


}
