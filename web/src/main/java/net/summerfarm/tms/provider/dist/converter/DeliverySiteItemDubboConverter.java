package net.summerfarm.tms.provider.dist.converter;

import net.summerfarm.tms.client.dist.resp.standard.DistDeliverySiteItemRecycleStandardResp;
import net.summerfarm.tms.client.dist.resp.standard.DistDeliverySiteItemStandardResp;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemRecycleDTO;

/**
 * Description:配送点位物品dubbo转换
 * date: 2023/12/25 14:39
 *
 * <AUTHOR>
 */
public class DeliverySiteItemDubboConverter {

    public static DistDeliverySiteItemStandardResp dto2resp(DeliverySiteItemDTO deliverySiteItemDTO){
        if (deliverySiteItemDTO == null){
            return null;
        }
        DistDeliverySiteItemStandardResp distDeliverySiteItemStandardResp = new DistDeliverySiteItemStandardResp();
        distDeliverySiteItemStandardResp.setId(deliverySiteItemDTO.getId());
        distDeliverySiteItemStandardResp.setOutItemId(deliverySiteItemDTO.getOutItemId());
        distDeliverySiteItemStandardResp.setPlanReceiptCount(deliverySiteItemDTO.getPlanReceiptCount());
        distDeliverySiteItemStandardResp.setRealReceiptCount(deliverySiteItemDTO.getRealReceiptCount());
        distDeliverySiteItemStandardResp.setType(deliverySiteItemDTO.getType());
        distDeliverySiteItemStandardResp.setStatus(deliverySiteItemDTO.getStatus());
        distDeliverySiteItemStandardResp.setRemark(deliverySiteItemDTO.getRemark());
        if (deliverySiteItemDTO.getDeliverySiteRecycleDTO() != null){
            distDeliverySiteItemStandardResp.setItemRecycleResp(recycleDto2resp(deliverySiteItemDTO.getDeliverySiteRecycleDTO()));
        }
        return distDeliverySiteItemStandardResp;
    }

    public static DistDeliverySiteItemRecycleStandardResp recycleDto2resp(DeliverySiteItemRecycleDTO deliverySiteItemRecycleDTO){
        if (deliverySiteItemRecycleDTO == null){
            return null;
        }
        DistDeliverySiteItemRecycleStandardResp distDeliverySiteItemRecycleStandardResp = new DistDeliverySiteItemRecycleStandardResp();
        distDeliverySiteItemRecycleStandardResp.setId(deliverySiteItemRecycleDTO.getId());
        distDeliverySiteItemRecycleStandardResp.setRecyclePics(deliverySiteItemRecycleDTO.getRecyclePics());
        distDeliverySiteItemRecycleStandardResp.setSpecificationQuantity(deliverySiteItemRecycleDTO.getSpecificationQuantity());
        distDeliverySiteItemRecycleStandardResp.setSpecificationUnit(deliverySiteItemRecycleDTO.getSpecificationUnit());
        distDeliverySiteItemRecycleStandardResp.setBasicSpecQuantity(deliverySiteItemRecycleDTO.getBasicSpecQuantity());
        distDeliverySiteItemRecycleStandardResp.setBasicSpecUnit(deliverySiteItemRecycleDTO.getBasicSpecUnit());
        distDeliverySiteItemRecycleStandardResp.setReasonType(deliverySiteItemRecycleDTO.getReasonType());
        distDeliverySiteItemRecycleStandardResp.setReasonTypeDesc(deliverySiteItemRecycleDTO.getReasonTypeDesc());
        distDeliverySiteItemRecycleStandardResp.setRemark(deliverySiteItemRecycleDTO.getRemark());
        return distDeliverySiteItemRecycleStandardResp;

    }


}
