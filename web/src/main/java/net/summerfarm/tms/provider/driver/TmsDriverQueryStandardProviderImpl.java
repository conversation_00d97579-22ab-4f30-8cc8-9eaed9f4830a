package net.summerfarm.tms.provider.driver;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.client.driver.provider.standard.TmsDriverQueryStandardProvider;
import net.summerfarm.tms.client.driver.req.standard.DriverCurrentInfoStandardReq;
import net.summerfarm.tms.client.driver.resp.standard.DriverCurrentInfoStandardResp;
import net.summerfarm.tms.client.enums.DriverCurrentQueryTypeEnum;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.enums.DeliverySiteStatusEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 司机相关查询<br/>
 * date: 2023/5/19 16:37<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsDriverQueryStandardProviderImpl implements TmsDriverQueryStandardProvider {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Override
    public DubboResponse<DriverCurrentInfoStandardResp> queryDriverCurrentInfo(DriverCurrentInfoStandardReq driverCurrentInfoStandardReq) {
        TmsAssert.notNull(driverCurrentInfoStandardReq.getDeliveryBatchId(), ErrorCodeEnum.PARAM_NOT_NULL, "deliveryBatchId");
        TmsAssert.notNull(driverCurrentInfoStandardReq.getDriverId(), ErrorCodeEnum.PARAM_NOT_NULL, "driverId");
        TmsAssert.notNull(driverCurrentInfoStandardReq.getType(), ErrorCodeEnum.PARAM_NOT_NULL, "type");

        DeliveryBatchDTO deliveryBatchDTO = deliveryBatchService.queryDriverCurrentInfo(DeliveryBatchQuery.builder()
                .batchId(driverCurrentInfoStandardReq.getDeliveryBatchId())
                .driverId(driverCurrentInfoStandardReq.getDriverId()).build());

        if(deliveryBatchDTO == null || CollectionUtils.isEmpty(deliveryBatchDTO.getDeliverySiteDTOList())){
            throw new TmsRuntimeException("不存在车辆配送信息或者配送车辆不存在此配送点位");
        }
        //过滤未完成配送点位
        List<DeliverySiteDTO> unFinishDeliverySiteList = deliveryBatchDTO.getDeliverySiteDTOList().stream()
                .filter(site -> !Objects.equals(site.getSiteId(),deliveryBatchDTO.getBeginSiteId()))
                .filter(site -> !Objects.equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode(), site.getStatus()))
                .collect(Collectors.toList());
        //过滤完成配送点位
        List<DeliverySiteDTO> finishDeliverySiteList = deliveryBatchDTO.getDeliverySiteDTOList().stream()
                .filter(site -> !Objects.equals(site.getSiteId(),deliveryBatchDTO.getBeginSiteId()))
                .filter(site -> Objects.equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode(), site.getStatus()))
                .collect(Collectors.toList());
        DriverCurrentInfoStandardResp driverCurrentInfoStandardResp = new DriverCurrentInfoStandardResp();
        //不关心城配仓起点 商城
        if(Objects.equals(driverCurrentInfoStandardReq.getType(), DriverCurrentQueryTypeEnum.NOT_BEGIN_INFO.getCODE())){
            if(CollectionUtils.isEmpty(finishDeliverySiteList)){
                return DubboResponse.getOK(driverCurrentInfoStandardResp);
            }

            DeliverySiteDTO deliverySiteDTO = finishDeliverySiteList.get(0);

            driverCurrentInfoStandardResp.setDeliveryTime(deliveryBatchDTO.getDeliveryTime().toLocalDate());
            driverCurrentInfoStandardResp.setLastAddress(deliverySiteDTO.getSiteDTO().getAddress());
            driverCurrentInfoStandardResp.setLastArea(deliverySiteDTO.getSiteDTO().getArea());
            driverCurrentInfoStandardResp.setLastCity(deliverySiteDTO.getSiteDTO().getCity());
            driverCurrentInfoStandardResp.setLastPoi(deliverySiteDTO.getSignInPoi());
            driverCurrentInfoStandardResp.setLastPositionTime(deliverySiteDTO.getSignInTime());
            driverCurrentInfoStandardResp.setLastProvince(deliverySiteDTO.getSiteDTO().getProvince());
            driverCurrentInfoStandardResp.setPath(deliveryBatchDTO.getPathCode());
            driverCurrentInfoStandardResp.setPoiNote(deliverySiteDTO.getSiteDTO().getPoi());
            driverCurrentInfoStandardResp.setUnFinishCount(unFinishDeliverySiteList.size());
        }else{
            //关心城配仓起点 后台
            //过滤出城配仓点位
            List<DeliverySiteDTO> beginDeliverySiteDTOList = deliveryBatchDTO.getDeliverySiteDTOList().stream()
                    .filter(site -> Objects.equals(site.getSiteId(), deliveryBatchDTO.getBeginSiteId()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(beginDeliverySiteDTOList)){
                return DubboResponse.getOK(driverCurrentInfoStandardResp);
            }
            SiteDTO storeSiteDTO = beginDeliverySiteDTOList.get(0).getSiteDTO();

            driverCurrentInfoStandardResp.setStoreAddress(storeSiteDTO.getAddress());
            driverCurrentInfoStandardResp.setStoreArea(storeSiteDTO.getArea());
            driverCurrentInfoStandardResp.setStoreCity(storeSiteDTO.getCity());
            driverCurrentInfoStandardResp.setStoreProvince(storeSiteDTO.getProvince());
            driverCurrentInfoStandardResp.setStoreNo(Integer.parseInt(storeSiteDTO.getOutBusinessNo()));
            driverCurrentInfoStandardResp.setUnFinishCount(unFinishDeliverySiteList.size());
            //如果是空的去城配仓打卡信息
            if(CollectionUtils.isEmpty(finishDeliverySiteList)){
                List<DeliverySiteDTO> beginSiteDTOList = deliveryBatchDTO.getDeliverySiteDTOList().stream()
                        .filter(site -> Objects.equals(site.getSiteId(), deliveryBatchDTO.getBeginSiteId()))
                        .collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(beginSiteDTOList)){
                    driverCurrentInfoStandardResp.setDeliveryTime(deliveryBatchDTO.getDeliveryTime().toLocalDate());
                    driverCurrentInfoStandardResp.setLastAddress(storeSiteDTO.getAddress());
                    driverCurrentInfoStandardResp.setLastArea(storeSiteDTO.getArea());
                    driverCurrentInfoStandardResp.setLastCity(storeSiteDTO.getCity());
                    driverCurrentInfoStandardResp.setLastPoi(storeSiteDTO.getPoi());
                    driverCurrentInfoStandardResp.setLastPositionTime(beginSiteDTOList.get(0).getSignInTime());
                    driverCurrentInfoStandardResp.setLastProvince(storeSiteDTO.getProvince());
                    driverCurrentInfoStandardResp.setPath(deliveryBatchDTO.getPathCode());
                    driverCurrentInfoStandardResp.setPoiNote(storeSiteDTO.getPoi());
                }
                return DubboResponse.getOK(driverCurrentInfoStandardResp);
            }
            DeliverySiteDTO deliverySiteDTO = finishDeliverySiteList.get(0);
            driverCurrentInfoStandardResp.setDeliveryTime(deliveryBatchDTO.getDeliveryTime().toLocalDate());
            driverCurrentInfoStandardResp.setLastAddress(deliverySiteDTO.getSiteDTO().getAddress());
            driverCurrentInfoStandardResp.setLastArea(deliverySiteDTO.getSiteDTO().getArea());
            driverCurrentInfoStandardResp.setLastCity(deliverySiteDTO.getSiteDTO().getCity());
            driverCurrentInfoStandardResp.setLastPoi(deliverySiteDTO.getSignInPoi());
            driverCurrentInfoStandardResp.setLastPositionTime(deliverySiteDTO.getSignInTime());
            driverCurrentInfoStandardResp.setLastProvince(deliverySiteDTO.getSiteDTO().getProvince());
            driverCurrentInfoStandardResp.setPath(deliveryBatchDTO.getPathCode());
            driverCurrentInfoStandardResp.setPoiNote(deliverySiteDTO.getSiteDTO().getPoi());
        }

        return DubboResponse.getOK(driverCurrentInfoStandardResp);
    }

   /* @PostConstruct
    public void test(){
        DriverCurrentInfoStandardReq driverCurrentInfoStandardReq = new DriverCurrentInfoStandardReq();
        driverCurrentInfoStandardReq.setDriverId(3334L);
        driverCurrentInfoStandardReq.setDeliveryBatchId(6927L);
        driverCurrentInfoStandardReq.setType(1);
        DubboResponse<DriverCurrentInfoStandardResp> driverCurrentInfoStandardRespDubboResponse = queryDriverCurrentInfo(driverCurrentInfoStandardReq);
        System.out.println(JSON.toJSONString(driverCurrentInfoStandardRespDubboResponse));
    }*/
}
