package net.summerfarm.tms.provider.delivery.converter;

import net.summerfarm.tms.client.delivery.resp.TmsLackApprovedResp;
import net.summerfarm.tms.delivery.dto.lackapproved.LackGoodsApprovedDTO;

/**
 * Description: <br/>
 * date: 2023/7/20 15:15<br/>
 *
 * <AUTHOR> />
 */
public class LackApprovedDubboConverter {

    public static TmsLackApprovedResp dto2Resp(LackGoodsApprovedDTO lackGoodsApprovedDTO){
        if(lackGoodsApprovedDTO == null){
            return null;
        }
        TmsLackApprovedResp tmsLackApprovedResp = new TmsLackApprovedResp();
        tmsLackApprovedResp.setId(lackGoodsApprovedDTO.getId());
        tmsLackApprovedResp.setStoreNo(lackGoodsApprovedDTO.getStoreNo());
        tmsLackApprovedResp.setWarehouseNo(lackGoodsApprovedDTO.getWarehouseNo());
        tmsLackApprovedResp.setSku(lackGoodsApprovedDTO.getSku());
        tmsLackApprovedResp.setMId(lackGoodsApprovedDTO.getMId());
        tmsLackApprovedResp.setLackNum(lackGoodsApprovedDTO.getLackNum());
        tmsLackApprovedResp.setMoney(lackGoodsApprovedDTO.getMoney());
        tmsLackApprovedResp.setLackType(lackGoodsApprovedDTO.getLackType());
        tmsLackApprovedResp.setRemark(lackGoodsApprovedDTO.getRemark());
        tmsLackApprovedResp.setState(lackGoodsApprovedDTO.getState());
        tmsLackApprovedResp.setJudgmentOpinion(lackGoodsApprovedDTO.getJudgmentOpinion());
        tmsLackApprovedResp.setStockTaskId(lackGoodsApprovedDTO.getStockTaskId());
        tmsLackApprovedResp.setPic(lackGoodsApprovedDTO.getPic());
        tmsLackApprovedResp.setApprovedAdminId(lackGoodsApprovedDTO.getApprovedAdminId());
        tmsLackApprovedResp.setApprovedTime(lackGoodsApprovedDTO.getApprovedTime());
        tmsLackApprovedResp.setResponsibilityAdminId(lackGoodsApprovedDTO.getResponsibilityAdminId());
        tmsLackApprovedResp.setResponsibilityTime(lackGoodsApprovedDTO.getResponsibilityTime());
        tmsLackApprovedResp.setFinishTime(lackGoodsApprovedDTO.getFinishTime());
        tmsLackApprovedResp.setOrderNo(lackGoodsApprovedDTO.getOrderNo());
        tmsLackApprovedResp.setAmount(lackGoodsApprovedDTO.getAmount());
        tmsLackApprovedResp.setStockLackNum(lackGoodsApprovedDTO.getStockLackNum());
        tmsLackApprovedResp.setTmsDeliverySiteId(lackGoodsApprovedDTO.getTmsDeliverySiteId());
        return tmsLackApprovedResp;

    }
}
