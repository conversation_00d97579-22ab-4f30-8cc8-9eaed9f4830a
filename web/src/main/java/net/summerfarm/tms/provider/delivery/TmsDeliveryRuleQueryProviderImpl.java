package net.summerfarm.tms.provider.delivery;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.delivery.provider.TmsDeliveryRuleQueryProvider;
import net.summerfarm.tms.client.delivery.req.DeliveryRuleQueryReq;
import net.summerfarm.tms.client.delivery.resp.DeliveryRuleResp;
import net.summerfarm.tms.util.RedisCacheUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/3/7 14:11<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsDeliveryRuleQueryProviderImpl implements TmsDeliveryRuleQueryProvider {


    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Override
    public DubboResponse<DeliveryRuleResp> queryDeliveryDateInfo(DeliveryRuleQueryReq deliveryRuleQueryReq) {
        throw new BizException("TmsDeliveryRuleQueryProvider queryDeliveryDateInfo 已废弃");
    }

    @Override
    public DubboResponse<DeliveryRuleResp> queryCacheDeliveryDateInfo(DeliveryRuleQueryReq deliveryRuleQueryReq) {
        throw new BizException("TmsDeliveryRuleQueryProvider queryCacheDeliveryDateInfo 已废弃");
    }


}
