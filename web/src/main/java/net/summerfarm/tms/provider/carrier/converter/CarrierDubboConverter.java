package net.summerfarm.tms.provider.carrier.converter;

import jodd.util.StringUtil;
import net.summerfarm.tms.base.carrier.dto.CarrierDTO;
import net.summerfarm.tms.base.carrier.dto.CarrierInvoiceDTO;
import net.summerfarm.tms.client.carrier.resp.CarrierListResp;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 承运商dubbo转化类<br/>
 * date: 2023/12/26 10:47<br/>
 *
 * <AUTHOR> />
 */
public class CarrierDubboConverter {

    public static CarrierListResp dto2CarrierListResp(CarrierDTO dto) {
        if (dto == null) {
            return null;
        }
        CarrierListResp resp = new CarrierListResp();
        resp.setId(dto.getId());
        resp.setCarrierName(dto.getCarrierName());
        resp.setDirector(dto.getDirector());
        resp.setDirectorPhone(dto.getDirectorPhone());
        resp.setAddress(dto.getAddress());
        resp.setCooperationAgreement(dto.getCooperationAgreement());
        if (StringUtil.isNotBlank(dto.getSubBusinessType())) {
            List<Integer> businessTypeList = Arrays.stream(dto.getSubBusinessType().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            if (dto.getBusinessType() != null) {
                businessTypeList.add(dto.getBusinessType());
            }
            resp.setBusinessType(businessTypeList);
        } else {
            resp.setBusinessType(Collections.singletonList(dto.getBusinessType()));
        }
        CarrierInvoiceDTO carrierInvoiceDTO = dto.getCarrierInvoiceDTO();
        if (carrierInvoiceDTO != null) {
            resp.setTaxNumber(carrierInvoiceDTO.getTaxNo());
        }
        resp.setSocialCreditCode(dto.getSocialCreditCode());
        return resp;
    }
}
