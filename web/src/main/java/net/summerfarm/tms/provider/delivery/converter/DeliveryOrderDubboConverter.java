package net.summerfarm.tms.provider.delivery.converter;

import net.summerfarm.tms.client.delivery.dto.DeliveryOutOrderDTO;
import net.summerfarm.tms.client.delivery.resp.AfterSaleDeliverySiteResp;
import net.summerfarm.tms.client.dist.resp.DeliverySiteItemResp;
import net.summerfarm.tms.client.dist.resp.standard.DistDeliveryBatchStandardResp;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.provider.dist.converter.DeliveryBatchDubboConverter;
import net.summerfarm.tms.provider.dist.converter.DeliverySiteItemDubboConverter;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 配送单转换类
 * <AUTHOR>
 */
public class DeliveryOrderDubboConverter {

    public static AfterSaleDeliverySiteResp dto2Resp(DistOrderDTO distOrderDTO, DeliverySiteDTO deliverySiteDTO, DeliveryBatchDTO deliveryBatchDTO){
        if(distOrderDTO == null){
            return null;
        }
        AfterSaleDeliverySiteResp resp = new AfterSaleDeliverySiteResp();
        resp.setAfterSaleNo(distOrderDTO.getOutOrderId());
        if(deliverySiteDTO != null){
            String signInPic1 = deliverySiteDTO.getSignInPic1();
            String signInPic2 = deliverySiteDTO.getSignInPic2();
            String signInPic3 = deliverySiteDTO.getSignInPic3();
            StringJoiner picSj = new StringJoiner(",");
            if(StringUtils.isNotBlank(signInPic1)){
                picSj.add(signInPic1);
            }
            if(StringUtils.isNotBlank(signInPic2)){
                picSj.add(signInPic2);
            }
            if(StringUtils.isNotBlank(signInPic3)){
                picSj.add(signInPic3);
            }
            resp.setDeliveryPic(picSj.toString());
            resp.setSignInStatus(deliverySiteDTO.getSignInStatus());
            if(!CollectionUtils.isEmpty(deliverySiteDTO.getDeliverySiteItemDTOList())){
                List<DeliverySiteItemResp> itemResps = deliverySiteDTO.getDeliverySiteItemDTOList().stream().map(DeliveryOrderDubboConverter::siteItemDto2Resp).collect(Collectors.toList());
                resp.setDeliverySiteItems(itemResps);
            }
        }
        if (deliveryBatchDTO != null){
            resp.setBatchResp(DeliveryBatchDubboConverter.dto2StandardResp(deliveryBatchDTO));
        }
        resp.setFinishTime(distOrderDTO.getRealArrivalTime());
        resp.setStatus(distOrderDTO.getStatus());


        return resp;
    }

    public static DeliverySiteItemResp siteItemDto2Resp(DeliverySiteItemDTO deliverySiteItemDTO){
        if(deliverySiteItemDTO == null){
            return null;
        }
        DeliverySiteItemResp resp = new DeliverySiteItemResp();
        resp.setId(deliverySiteItemDTO.getId());
        resp.setCreateTime(deliverySiteItemDTO.getCreateTime());
        resp.setUpdateTime(deliverySiteItemDTO.getUpdateTime());
        resp.setDeliverySiteId(deliverySiteItemDTO.getDeliverySiteId());
        resp.setOutItemId(deliverySiteItemDTO.getOutItemId());
        resp.setPlanReceiptCount(deliverySiteItemDTO.getPlanReceiptCount());
        resp.setRealReceiptCount(deliverySiteItemDTO.getRealReceiptCount());
        resp.setShortCount(deliverySiteItemDTO.getShortCount());
        resp.setInterceptCount(deliverySiteItemDTO.getInterceptCount());
        resp.setRejectCount(deliverySiteItemDTO.getRejectCount());
        resp.setRejectReason(deliverySiteItemDTO.getRejectReason());
        resp.setScanCount(deliverySiteItemDTO.getScanCount());
        resp.setNoscanCount(deliverySiteItemDTO.getNoscanCount());
        resp.setNoscanReason(deliverySiteItemDTO.getNoscanReason());
        resp.setNoscanPics(deliverySiteItemDTO.getNoscanPics());
        resp.setRemark(deliverySiteItemDTO.getRemark());
        resp.setType(deliverySiteItemDTO.getType());
        resp.setStatus(deliverySiteItemDTO.getStatus());

        if (deliverySiteItemDTO.getDeliverySiteRecycleDTO() != null){
            resp.setItemRecycleResp(DeliverySiteItemDubboConverter.recycleDto2resp(deliverySiteItemDTO.getDeliverySiteRecycleDTO()));
        }
        return resp;
    }

    public static DeliveryOutOrderDTO convert(DeliveryOrderDTO deliveryOrderDTO) {
        if (Objects.isNull(deliveryOrderDTO)) {
            return null;
        }
        return DeliveryOutOrderDTO.builder()
                .status(deliveryOrderDTO.getStatus())
                .statusDesc(deliveryOrderDTO.getStatusDesc())
                .signInPic(deliveryOrderDTO.getSignInPic())
                .id(deliveryOrderDTO.getId())
                .distOrderId(deliveryOrderDTO.getDistOrderId())
                .deliveryBatchId(deliveryOrderDTO.getDeliveryBatchId())
                .beginSiteId(deliveryOrderDTO.getBeginSiteId())
                .beginSiteName(deliveryOrderDTO.getBeginSiteName())
                .endSiteId(deliveryOrderDTO.getEndSiteId())
                .endSiteName(deliveryOrderDTO.getEndSiteName())
                .outerOrderId(deliveryOrderDTO.getOuterOrderId())
                .build();

    }
}
