package net.summerfarm.tms.provider.fence.converter;

import net.summerfarm.tms.client.fence.req.AdCodeMsgReq;
import net.summerfarm.tms.client.fence.req.AddressFenceQueryReq;
import net.summerfarm.tms.client.fence.resp.AdCodeMsgResp;
import net.summerfarm.tms.client.fence.resp.AddressFenceResp;
import net.summerfarm.tms.fence.dto.AdCodeMsgDTO;
import net.summerfarm.tms.fence.dto.FenceDTO;
import net.summerfarm.tms.query.fence.AdCodeMsgQuery;
import net.summerfarm.tms.query.fence.FenceQuery;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/16 16:00<br/>
 *
 * <AUTHOR> />
 */
public class TmsAddressFenceDubboConverter {

    public static AddressFenceResp fenceDTOToResp(FenceDTO fenceDTO) {
        if(fenceDTO == null){
            return null;
        }
        AddressFenceResp addressFenceResp = new AddressFenceResp();
        addressFenceResp.setFenceId(fenceDTO.getId());
        addressFenceResp.setFenceName(fenceDTO.getFenceName());
        addressFenceResp.setStoreNo(fenceDTO.getStoreNo());
        if(!CollectionUtils.isEmpty(fenceDTO.getAdCodeMsgDTOS())){
            addressFenceResp.setAdCodeMsgList(fenceDTO.getAdCodeMsgDTOS().stream().map(TmsAddressFenceDubboConverter::adCodeMsgDTOToResp).collect(Collectors.toList()));
        }

        return addressFenceResp;
    }


    public static AdCodeMsgResp adCodeMsgDTOToResp(AdCodeMsgDTO adCodeMsgDTO) {
        if(adCodeMsgDTO == null){
            return null;
        }
        AdCodeMsgResp adCodeMsgResp = new AdCodeMsgResp();
        adCodeMsgResp.setProvince(adCodeMsgDTO.getProvince());
        adCodeMsgResp.setCity(adCodeMsgDTO.getCity());
        adCodeMsgResp.setArea(adCodeMsgDTO.getArea());

        return adCodeMsgResp;
    }

    public static FenceQuery addressFenceQueryReqToQuery(AddressFenceQueryReq addressFenceQueryReq) {
        if(addressFenceQueryReq == null){
            return null;
        }
        FenceQuery fenceQuery = new FenceQuery();
        List<AdCodeMsgQuery> adCodeMsgList = new ArrayList<>();
        List<AdCodeMsgReq> adCodeMsgResps = addressFenceQueryReq.getAdCodeMsgResps();
        for (AdCodeMsgReq adCodeMsgResp : adCodeMsgResps) {
            AdCodeMsgQuery adCodeMsgQuery = new AdCodeMsgQuery();
            adCodeMsgQuery.setProvince(adCodeMsgResp.getProvince());
            adCodeMsgQuery.setCity(adCodeMsgResp.getCity());
            adCodeMsgQuery.setArea(adCodeMsgResp.getArea());

            adCodeMsgList.add(adCodeMsgQuery);
        }
        fenceQuery.setAdCodeMsgList(adCodeMsgList);

        return fenceQuery;
    }

    public static FenceQuery fenceQueryReqToQuery(AdCodeMsgReq adCodeMsgReq) {
        if(adCodeMsgReq == null){
            return null;
        }
        FenceQuery fenceQuery = new FenceQuery();
        fenceQuery.setProvince(adCodeMsgReq.getProvince());
        fenceQuery.setCity(adCodeMsgReq.getCity());
        fenceQuery.setArea(adCodeMsgReq.getArea());

        return fenceQuery;
    }
}
