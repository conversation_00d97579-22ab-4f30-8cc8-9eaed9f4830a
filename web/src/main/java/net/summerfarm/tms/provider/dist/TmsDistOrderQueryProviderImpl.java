package net.summerfarm.tms.provider.dist;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider;
import net.summerfarm.tms.client.dist.req.*;
import net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq;
import net.summerfarm.tms.client.dist.resp.*;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderStandardResp;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.DistOrderFulfillmentDeliveryWayEnum;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.provider.dist.converter.DistOrderDubboConverter;
import net.summerfarm.tms.client.dist.resp.TrunkChangeTransportDetailResp;
import net.summerfarm.tms.query.dist.DistOrderOnlyCodeQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/12/23 17:04<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsDistOrderQueryProviderImpl implements TmsDistOrderQueryProvider {
    @Resource
    private DistOrderService distOrderService;
    @Resource
    private DeliveryBatchService deliveryBatchService;
    @Resource
    private WmsQueryFacade wmsQueryFacade;
    @Resource
    private DistOrderRepository distOrderRepository;


    @Override
    public DubboResponse<DistOrderResp> queryDetail(DistOrderQueryReq distOrderQueryReq) {
        log.info("查询委托单详情接口参数:{}", JSON.toJSONString(distOrderQueryReq));
        DistOrderQuery distOrderQuery = DistOrderDubboConverter.reqToDistOrder(distOrderQueryReq);
        TmsResult<DistOrderDTO> result = distOrderService.queryDetail(distOrderQuery);
        if (result.isSuccess()) {
            DistOrderDTO distOrderDTO = result.getData();
            return DubboResponse.getOK(DistOrderDubboConverter.distOrderDTOToResp(distOrderDTO));
        } else {
            return DubboResponse.getError(result.getErrCode(), result.getErrorMessage());
        }
    }

    @Override
    public DubboResponse<Boolean> isAutoSubmitDistOrder(DistBlackConfigQueryReq distBlackConfigQueryReq) {
        return null;
    }

    @Override
    public DubboResponse<List<DistOrderInterceptResp>> queryInterceptSiteDistOrder(DistOrderQueryReq distOrderQueryReq) {
        return null;
    }

    @Override
    public DubboResponse<List<DistOrderResp>> queryDistOrderList(DistOrderQueryReq distOrderQueryReq) {
        List<String> outerOrderIds = distOrderQueryReq.getOuterOrderIds();
        List<String> outerContactIds = distOrderQueryReq.getOuterContactIds();
        List<Integer> sources = distOrderQueryReq.getSources();
        LocalDateTime expectBeginTime = distOrderQueryReq.getExpectBeginTime();
        log.info("请求查询订单状态接口:{}", JSON.toJSONString(distOrderQueryReq));

        TmsAssert.notEmpty(outerOrderIds, ErrorCodeEnum.PARAM_NOT_NULL, "outerOrderIds");
        TmsAssert.notEmpty(outerContactIds, ErrorCodeEnum.PARAM_NOT_NULL, "outerContactIds");
        TmsAssert.notEmpty(sources, ErrorCodeEnum.PARAM_NOT_NULL, "sources");
        TmsAssert.notNull(expectBeginTime, ErrorCodeEnum.PARAM_NOT_NULL, "expectBeginTime");
        //数量大于100取前100
        if(outerOrderIds.size() > 100){
            distOrderQueryReq.setOuterOrderIds(outerOrderIds.subList(0, 100));
        }
        DistOrderQuery distOrderQuery = DistOrderDubboConverter.reqToDistOrder(distOrderQueryReq);

        List<DistOrderDTO> distOrderList = distOrderService.queryDistOrderList(distOrderQuery);
        if (CollectionUtils.isEmpty(distOrderList)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        //查询配送批次信息
        deliveryBatchService.queryDistOrdersBatch(distOrderList);
        List<DistOrderResp> distOrderResps = distOrderList.stream().map(DistOrderDubboConverter::distOrderDTOToResp).collect(Collectors.toList());

        return DubboResponse.getOK(distOrderResps);
    }

    @Override
    public DubboResponse<List<DeliverySiteOnlyCodeResp>> queryDistOrderOnlyCode(@Valid DeliverySiteOnlyCodeQueryReq deliverySiteOnlyCodeQueryReq) {
        DistOrderOnlyCodeQuery distOrderOnlyCodeQuery = DistOrderDubboConverter.distOrderQueryToDTO(deliverySiteOnlyCodeQueryReq);
        return DubboResponse.getOK(DistOrderDubboConverter.onlyCodeRespsToList(distOrderService.queryDistOrderOnlyCode(distOrderOnlyCodeQuery)));
    }

    @Override
    public DubboResponse<List<OuterOrderValidateResp>> outerCreateDistOrderValidate(@Valid CreateDistOrderValidateReq createDistOrderValidateReq) {
        Map<String, String> reslutInfoMap = distOrderService.outerCreateDistOrderValidate(createDistOrderValidateReq.getDistOrderCreateMessageList());
        List<OuterOrderValidateResp> list = this.bulidOuterOrderValidateResps(reslutInfoMap);
        return DubboResponse.getOK(list);
    }

    @Override
    public DubboResponse<List<OuterOrderValidateResp>> outerCancelDistOrderValidate(@Valid DistOrderCancelValidateReq distOrderCancelValidateReq) {
        Map<String, String> reslutInfoMap = distOrderService.outerCancelDistOrderValidate(distOrderCancelValidateReq.getDistOrderCancelList());
        List<OuterOrderValidateResp> list = this.bulidOuterOrderValidateResps(reslutInfoMap);
        return DubboResponse.getOK(list);
    }

    @Override
    public DubboResponse<List<DistOrderStandardResp>> queryOrderNoDeliveryInfo(@Valid OrderNoDeliveryInfoReq req) {
        log.info("queryOrderNoDeliveryInfo param:{}", JSON.toJSONString(req));
        List<DistOrderDTO> distOrderList = distOrderService.queryOrderNoDeliveryInfo(req.getOrderNos(),req.getDeliveryTime());
        return DubboResponse.getOK(distOrderList.stream().map(DistOrderDubboConverter::dtoToStandardResp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<TrunkChangeTransportResp> queryTrunkChangeTransportDetail(@Valid DistOrderQueryStandardReq req) {
        log.info("查询干线转运详情，请求参数：{}", JSON.toJSONString(req));

        // 1. 查询原始委托单，判断是否是干线转运
        DistOrderEntity distOrderEntity = distOrderRepository.queryByUk(req.getOuterOrderId(),
                DistOrderSourceEnum.getDistOrderSourceByCode(req.getSource()),
                req.getExpectBeginTime(), req.getOuterContactId());
        if (distOrderEntity == null) {
            return DubboResponse.getOK(null);
        }
        if (!Objects.equals(distOrderEntity.getFulfillmentDeliveryWay(), DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode())) {
            return DubboResponse.getOK(null);
        }

        // 2. 原始委托单详情
        DistOrderDTO cityTrunkDistOrderInfo = null;
        TmsResult<DistOrderDTO> firstCityResult = distOrderService.queryDetail(DistOrderQuery.builder().distId(distOrderEntity.getDistId()).build());
        if (firstCityResult.isSuccess()) {
            cityTrunkDistOrderInfo = firstCityResult.getData();
        }

        // 3. 转换为TrunkChangeTransportDetailResp列表
        List<TrunkChangeTransportDetailResp> trunkChangeTransportDetailRespList = new ArrayList<>();

        // 添加城配数据
        if (cityTrunkDistOrderInfo != null) {
            List<TrunkChangeTransportDetailResp> cityTrunkDetailResps = DistOrderDubboConverter.dtoToTrunkChangeTransportDetailResp(cityTrunkDistOrderInfo);
            if (!CollectionUtils.isEmpty(cityTrunkDetailResps)) {
                trunkChangeTransportDetailRespList.addAll(cityTrunkDetailResps);
            }
        }

        // 4. 封装返回结果
        TrunkChangeTransportResp response = new TrunkChangeTransportResp();
        for (int i = 0; i < trunkChangeTransportDetailRespList.size(); i++) {
            trunkChangeTransportDetailRespList.get(i).setSequence(i + 1);
        }
        response.setTrunkChangeTransportDetailRespList(trunkChangeTransportDetailRespList);

        return DubboResponse.getOK(response);

    }

    private List<OuterOrderValidateResp> bulidOuterOrderValidateResps(Map<String, String> reslutInfoMap) {
        List<OuterOrderValidateResp> list = new ArrayList<>();
        reslutInfoMap.forEach((k, v) -> {
            OuterOrderValidateResp resp = new OuterOrderValidateResp();
            resp.setOuterId(k);
            resp.setCode(StringUtils.isEmpty(v) ? DubboResponse.SUCCESS_STATUS : DubboResponse.ERROR_STATUS);
            resp.setFailResult(v);
            list.add(resp);
        });
        return list;
    }
}
