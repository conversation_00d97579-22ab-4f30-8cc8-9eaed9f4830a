package net.summerfarm.tms.provider.delivery.converter;

import net.summerfarm.tms.client.delivery.req.DeliveryRuleQueryReq;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.query.fence.DeliveryFenceDateQuery;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/7 15:37<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryRuleDubboConverter {


    public static DeliveryFenceDateQuery reqToQuery(DeliveryRuleQueryReq deliveryRuleQueryReq){
        if(deliveryRuleQueryReq == null){
            return null;
        }
        DeliveryFenceDateQuery deliveryFenceDateQuery = new DeliveryFenceDateQuery();

        deliveryFenceDateQuery.setOrderTime(deliveryRuleQueryReq.getOrderTime());
        deliveryFenceDateQuery.setMerchantId(deliveryRuleQueryReq.getMerchantId());
        deliveryFenceDateQuery.setContactId(deliveryRuleQueryReq.getContactId());

        if(deliveryRuleQueryReq.getQueryBeginDate() != null && deliveryRuleQueryReq.getQueryEndDate() != null){
            List<LocalDate> wantDeliveryTime = new ArrayList<>();
            LocalDate deliveryTime = deliveryRuleQueryReq.getQueryBeginDate();
            while (deliveryTime.compareTo(deliveryRuleQueryReq.getQueryEndDate()) <= 0){
                wantDeliveryTime.add(deliveryTime);
                deliveryTime = deliveryTime.plusDays(1);
            }
            deliveryFenceDateQuery.setWantDeliveryTime(wantDeliveryTime);
        }
        deliveryFenceDateQuery.setDistOrderSource(DistOrderSourceEnum.getDistOrderSourceByCode(deliveryRuleQueryReq.getSource().getValue()));
        deliveryFenceDateQuery.setCity(deliveryRuleQueryReq.getCity());
        deliveryFenceDateQuery.setArea(deliveryRuleQueryReq.getArea());

        return deliveryFenceDateQuery;
    }
}
