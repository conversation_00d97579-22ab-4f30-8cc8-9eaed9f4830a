package net.summerfarm.tms.provider.carrier;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.carrier.CarrierService;
import net.summerfarm.tms.client.carrier.provider.TmsCarrierQueryProvider;
import net.summerfarm.tms.client.carrier.req.CarrierListQueryReq;
import net.summerfarm.tms.client.carrier.resp.CarrierListResp;
import net.summerfarm.tms.enums.CarrierEnums;
import net.summerfarm.tms.provider.carrier.converter.CarrierDubboConverter;
import net.summerfarm.tms.query.base.carrier.CarrierQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 承运商查询服务<br/>
 * date: 2023/12/22 15:54<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsCarrierQueryProviderImpl implements TmsCarrierQueryProvider {

    @Resource
    private CarrierService carrierService;

    @Override
    public DubboResponse<List<CarrierListResp>> queryList(@Valid CarrierListQueryReq req) {
        CarrierQuery query = CarrierQuery.builder()
                .carrierName(req.getCarrierName())
                .idList(req.getCarrierIdList())
                .hasQueryInvoice(true)
                .carrierNameList(req.getCarrierNameList())
                .build();
        if(Arrays.asList(CarrierEnums.BusinessType.Trunk.getValue(),CarrierEnums.BusinessType.City.getValue()).contains(req.getBusinessType())){
            query.setBusinessTypes(Arrays.asList(req.getBusinessType(),CarrierEnums.BusinessType.TrunkAndCity.getValue()));
        }
        if(req.getBusinessType() != null && Objects.equals(req.getBusinessType().toString(),CarrierEnums.SubBusinessType.WAREHOUSE.getValue())){
            query.setSubBusinessType(CarrierEnums.SubBusinessType.WAREHOUSE.getValue());
        }
        return DubboResponse.getOK(carrierService.queryAllCarrier(query).stream().map(CarrierDubboConverter::dto2CarrierListResp).collect(Collectors.toList()));
    }

}
