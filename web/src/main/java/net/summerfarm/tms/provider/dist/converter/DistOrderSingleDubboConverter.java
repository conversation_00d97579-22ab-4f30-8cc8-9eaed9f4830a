package net.summerfarm.tms.provider.dist.converter;

import net.summerfarm.tms.client.dist.resp.standard.DistOrderSingleStandardResp;
import net.summerfarm.tms.dist.dto.DistOrderDTO;

/**
 * Description:
 * date: 2023/6/25 11:10
 *
 * <AUTHOR>
 */
public class DistOrderSingleDubboConverter {

    public static DistOrderSingleStandardResp dtoToStandardResp(DistOrderDTO distOrderDTO) {
        if (distOrderDTO == null) {
            return null;
        }
        DistOrderSingleStandardResp distOrderSingleStandardResp = new DistOrderSingleStandardResp();
        distOrderSingleStandardResp.setDistId(distOrderDTO.getDistId());
        distOrderSingleStandardResp.setExpectBeginTime(distOrderDTO.getExpectBeginTime());
        distOrderSingleStandardResp.setOutOrderId(distOrderDTO.getOutOrderId());
        distOrderSingleStandardResp.setStatus(distOrderDTO.getStatus());
        distOrderSingleStandardResp.setRealArrivalTime(distOrderDTO.getRealArrivalTime());
        distOrderSingleStandardResp.setSource(distOrderDTO.getSource());
        distOrderSingleStandardResp.setOuterContactId(distOrderDTO.getOutContactId());
        return distOrderSingleStandardResp;
    }
}
