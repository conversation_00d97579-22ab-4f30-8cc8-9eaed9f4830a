package net.summerfarm.tms.provider.delivery.converter;

import net.summerfarm.tms.client.delivery.resp.TrunkDeliveryDetailResp;
import net.summerfarm.tms.client.delivery.resp.TrunkDeliverySiteDetail;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/8 18:44<br/>
 *
 * <AUTHOR> />
 */
public class TmsTrunkDeliveryDubboConverter {

    public static TrunkDeliveryDetailResp dto2Resp(DeliveryBatchDTO deliveryBatchDTO){
        if(deliveryBatchDTO == null){
            return null;
        }
        TrunkDeliveryDetailResp trunkDeliveryDetailResp = new TrunkDeliveryDetailResp();

        trunkDeliveryDetailResp.setId(deliveryBatchDTO.getDeliveryBatchId());
        trunkDeliveryDetailResp.setStatus(deliveryBatchDTO.getStatus());
        trunkDeliveryDetailResp.setType(deliveryBatchDTO.getType());
        trunkDeliveryDetailResp.setDriverName(deliveryBatchDTO.getDriver());
        trunkDeliveryDetailResp.setDriverPhone(deliveryBatchDTO.getDriverPhone());
        trunkDeliveryDetailResp.setCarrierId(deliveryBatchDTO.getCarrierId());
        trunkDeliveryDetailResp.setCarrierName(deliveryBatchDTO.getCarrierName());
        trunkDeliveryDetailResp.setCarNumber(deliveryBatchDTO.getCarNumber());
        trunkDeliveryDetailResp.setCarStorage(deliveryBatchDTO.getStorageName());
        trunkDeliveryDetailResp.setStorageCode(deliveryBatchDTO.getStorageCode());
        trunkDeliveryDetailResp.setCarType(deliveryBatchDTO.getCarType());
        trunkDeliveryDetailResp.setCarTypeCode(deliveryBatchDTO.getCarTypeCode());
        trunkDeliveryDetailResp.setEstimateFare(deliveryBatchDTO.getEstimateFare());
        trunkDeliveryDetailResp.setFinishDeliveryTime(deliveryBatchDTO.getFinishDeliveryTime());
        trunkDeliveryDetailResp.setDeliveryTime(deliveryBatchDTO.getDeliveryTime());
        trunkDeliveryDetailResp.setArea(deliveryBatchDTO.getArea());
        trunkDeliveryDetailResp.setClasses(deliveryBatchDTO.getClasses());
        trunkDeliveryDetailResp.setRemark(deliveryBatchDTO.getRemark());
        trunkDeliveryDetailResp.setPlanTotalDistance(deliveryBatchDTO.getPlanTotalDistance());
        trunkDeliveryDetailResp.setTotalWeight(deliveryBatchDTO.getWeightTotal());
        trunkDeliveryDetailResp.setTotalVolume(deliveryBatchDTO.getVolumeTotal());
        trunkDeliveryDetailResp.setCreateId(deliveryBatchDTO.getCreateId());
        trunkDeliveryDetailResp.setWeightLoadRatio(deliveryBatchDTO.getWeightLoadRatio());
        trunkDeliveryDetailResp.setVolumeLoadRatio(deliveryBatchDTO.getVolumeLoadRatio());
        trunkDeliveryDetailResp.setQuantityLoadRatio(deliveryBatchDTO.getQuantityLoadRatio());

        if(deliveryBatchDTO.getQuantityTotal() != null){
            trunkDeliveryDetailResp.setTotalQuantity(deliveryBatchDTO.getQuantityTotal().intValue());
        }

        if(!CollectionUtils.isEmpty(deliveryBatchDTO.getDeliverySiteDTOList())){
            trunkDeliveryDetailResp.setTrunkDeliverySiteDetails(deliveryBatchDTO.getDeliverySiteDTOList().stream()
                    .map(TmsTrunkDeliveryDubboConverter::dSite2DSiteDetail).collect(Collectors.toList()));
        }else{
            trunkDeliveryDetailResp.setTrunkDeliverySiteDetails(Collections.emptyList());
        }

        return trunkDeliveryDetailResp;
    }

    public static TrunkDeliverySiteDetail dSite2DSiteDetail(DeliverySiteDTO deliverySiteDTO){
        if(deliverySiteDTO == null){
            return null;
        }

        TrunkDeliverySiteDetail trunkDeliverySiteDetail = new TrunkDeliverySiteDetail();
        trunkDeliverySiteDetail.setId(deliverySiteDTO.getId());
        trunkDeliverySiteDetail.setDeliveryBatchId(deliverySiteDTO.getDeliveryBatchId());
        trunkDeliverySiteDetail.setSiteId(deliverySiteDTO.getSiteId());
        trunkDeliverySiteDetail.setSiteName(deliverySiteDTO.getSiteName());
        trunkDeliverySiteDetail.setSiteType(deliverySiteDTO.getSiteType());
        trunkDeliverySiteDetail.setSequence(deliverySiteDTO.getSequence());
        trunkDeliverySiteDetail.setStatus(deliverySiteDTO.getStatus());
        if (deliverySiteDTO.getSiteDTO() != null){
            trunkDeliverySiteDetail.setOutBusinessNo(deliverySiteDTO.getSiteDTO().getOutBusinessNo());
        }

        return trunkDeliverySiteDetail;
    }
}


