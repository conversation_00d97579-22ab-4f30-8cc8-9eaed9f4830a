package net.summerfarm.tms.provider.fence;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.fence.provider.TmsAddressFenceProvider;
import net.summerfarm.tms.client.fence.req.AdCodeMsgReq;
import net.summerfarm.tms.client.fence.req.AddressFenceQueryReq;
import net.summerfarm.tms.client.fence.resp.AddressFenceResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.*;

/**
 * Description: <br/>
 * date: 2023/3/16 11:37<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsAddressFenceProviderImpl implements TmsAddressFenceProvider {

    @Override
    public DubboResponse<List<AddressFenceResp>> queryAddress(AddressFenceQueryReq addressFenceQueryReq) {
        throw new BizException("TmsAddressFenceProvider queryAddress 接口已废弃");
    }

    @Override
    public DubboResponse<List<AddressFenceResp>> queryBatchFence(AddressFenceQueryReq addressFenceQueryReq) {
        throw new BizException("TmsAddressFenceProvider queryBatchFence 接口已废弃");
    }

    @Override
    public DubboResponse<AddressFenceResp> queryFence(AdCodeMsgReq adCodeMsgReq) {
        throw new BizException("TmsAddressFenceProvider queryFence 接口已废弃");
    }

}
