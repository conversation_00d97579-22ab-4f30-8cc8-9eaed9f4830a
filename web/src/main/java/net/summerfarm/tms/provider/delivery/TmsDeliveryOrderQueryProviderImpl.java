package net.summerfarm.tms.provider.delivery;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.client.delivery.dto.DeliveryOutOrderDTO;
import net.summerfarm.tms.client.delivery.provider.TmsDeliveryOrderQueryProvider;
import net.summerfarm.tms.client.delivery.req.AfterSaleDeliveryQueryReq;
import net.summerfarm.tms.client.delivery.req.DeliveryOutOrderQueryReq;
import net.summerfarm.tms.client.delivery.resp.AfterSaleDeliverySiteResp;
import net.summerfarm.tms.client.delivery.resp.DeliveryOutOrderResp;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.DeliveryOrderService;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemDTO;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.dist.dto.DistOrderItemDTO;
import net.summerfarm.tms.enums.DeliverySiteEnums;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.provider.delivery.converter.DeliveryOrderDubboConverter;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@DubboService
public class TmsDeliveryOrderQueryProviderImpl implements TmsDeliveryOrderQueryProvider {
    @Resource
    private DistOrderService distOrderService;
    @Resource
    private DeliverySiteService deliverySiteService;
    @Resource
    private DeliveryBatchService deliveryBatchService;
    @Resource
    private DeliveryOrderService deliveryOrderService;

    @Override
    public DubboResponse<AfterSaleDeliverySiteResp> queryAfterSaleDeliveryForOfc(@Valid AfterSaleDeliveryQueryReq req) {
        TmsResult<DistOrderDTO> result = distOrderService.query(req.getAfterSaleNo(), DistOrderSourceEnum.getDistOrderSourceByCode(req.getSource()));
        if (!result.isSuccess()) {
            throw new TmsRuntimeException(result.getErrorMessage());
        }
        if(result.getData() == null){
            return DubboResponse.getOK(null);
        }
        //查询配送单信息
        List<DeliveryOrderDTO> deliveryOrderList = result.getData().getDeliveryOrderList();
        if(CollectionUtils.isEmpty(deliveryOrderList)){
            return DubboResponse.getOK(null);
        }
        if(deliveryOrderList.get(0).getDeliveryBatchId() == null || deliveryOrderList.get(0).getEndSiteId() == null){
            return DubboResponse.getOK(DeliveryOrderDubboConverter.dto2Resp(result.getData(),null, null));
        }
        //查询批次信息
        DeliveryBatchDTO deliveryBatchDTO = deliveryBatchService.queryDetail(deliveryOrderList.get(0).getDeliveryBatchId());

        //根据委托单信息，查询点位配送信息
        DeliverySiteDTO deliverySiteDTO = deliverySiteService.querySiteWithSiteItem(DeliverySiteQuery.builder()
                .batchId(deliveryOrderList.get(0).getDeliveryBatchId())
                .siteId(deliveryOrderList.get(0).getEndSiteId()).build());
        if(deliverySiteDTO == null || deliverySiteDTO.getId() == null){
            return DubboResponse.getOK(DeliveryOrderDubboConverter.dto2Resp(result.getData(),null, deliveryBatchDTO));
        }
        List<DistOrderItemDTO> skuList = result.getData().getSkuList();

        List<DeliverySiteItemDTO> siteItemDTOList = deliverySiteDTO.getDeliverySiteItemDTOList();
        Optional<DeliverySiteItemDTO> remarkOp = siteItemDTOList.stream().filter(item -> StringUtils.isNotBlank(item.getRemark())).findFirst();
        deliverySiteDTO.setSignInStatus(remarkOp.isPresent() ? DeliverySiteEnums.SignInStatus.NO.getValue() : DeliverySiteEnums.SignInStatus.OK.getValue());

        List<DeliverySiteItemDTO> afterSiteItemList = new ArrayList<>();
        for (DeliverySiteItemDTO siteItemDTO : siteItemDTOList) {
            for (DistOrderItemDTO distItemDTO : skuList) {
                if(Objects.equals(distItemDTO.getOuterItemId(),siteItemDTO.getOutItemId()) && Objects.equals(distItemDTO.getDeliveryType(),siteItemDTO.getType())){
                    afterSiteItemList.add(siteItemDTO);
                }
            }
        }

        deliverySiteDTO.setDeliverySiteItemDTOList(afterSiteItemList);
        return DubboResponse.getOK(DeliveryOrderDubboConverter.dto2Resp(result.getData(),deliverySiteDTO,deliveryBatchDTO));
    }

    @Override
    public DubboResponse<DeliveryOutOrderResp> queryDeliveryOutOrderInfo(@Valid DeliveryOutOrderQueryReq queryReq) {
        List<DeliveryOrderDTO> deliveryOrderDTOList = deliveryOrderService.selectListByCondition(queryReq);
        List<DeliveryOutOrderDTO> deliveryOutOrderDTOList = deliveryOrderDTOList.stream()
                .map(DeliveryOrderDubboConverter::convert).collect(Collectors.toList());
        DeliveryOutOrderResp resp = DeliveryOutOrderResp.builder()
                .deliveryOutOrderDTOList(deliveryOutOrderDTOList)
                .build();
        return DubboResponse.getOK(resp);
    }

}
