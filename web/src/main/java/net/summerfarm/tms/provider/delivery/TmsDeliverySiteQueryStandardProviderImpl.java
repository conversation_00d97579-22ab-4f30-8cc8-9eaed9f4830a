package net.summerfarm.tms.provider.delivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.client.delivery.provider.standard.TmsDeliverySiteQueryStandardProvider;
import net.summerfarm.tms.client.delivery.req.standard.DeliverySiteStandardResp;
import net.summerfarm.tms.client.delivery.req.standard.OrderCodeQueryReq;
import net.summerfarm.tms.client.delivery.resp.OrderCodeResp;
import net.summerfarm.tms.client.dist.req.standard.DeliverySiteQueryStandardReq;
import net.summerfarm.tms.client.enums.SourceEnum;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.DeliveryOrderService;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.provider.delivery.converter.DeliverySiteDubboConverter;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/5/15 16:24<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsDeliverySiteQueryStandardProviderImpl  implements TmsDeliverySiteQueryStandardProvider{

    @Resource
    private DeliverySiteService deliverySiteService;
    @Resource
    private SiteService siteService;
    @Resource
    private DeliveryOrderService deliveryOrderService;

    @Override
    public DubboResponse<DeliverySiteStandardResp> queryDeliverySiteDetail(DeliverySiteQueryStandardReq deliverySiteQueryStandardReq) {
        log.info("查询标准化点位配送详情接口参数:{}", JSON.toJSONString(deliverySiteQueryStandardReq));
        TmsAssert.notNull(deliverySiteQueryStandardReq.getDeliveryTime(), ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        TmsAssert.notEmpty(deliverySiteQueryStandardReq.getOuterContactId(), ErrorCodeEnum.PARAM_NOT_NULL, "outerContactId");
        TmsAssert.notEmpty(deliverySiteQueryStandardReq.getStoreNo(), ErrorCodeEnum.PARAM_NOT_NULL, "storeNo");
        TmsAssert.notNull(deliverySiteQueryStandardReq.getType(), ErrorCodeEnum.PARAM_NOT_NULL, "type");

        //查询城配仓点位信息
        TmsResult<SiteDTO> citySiteRes = siteService.query(SiteQuery.builder()
                .type(SiteTypeEnum.store.getCode())
                .outBusinessNo(deliverySiteQueryStandardReq.getStoreNo()).build());
        if(!citySiteRes.isSuccess() || citySiteRes.getData() == null || citySiteRes.getData().getId() == null){
            return DubboResponse.getOK();
        }
        //查询客户点位信息
        TmsResult<SiteDTO> custSiteRes = siteService.query(SiteQuery.builder()
                .type(deliverySiteQueryStandardReq.getType())
                .outBusinessNo(deliverySiteQueryStandardReq.getOuterContactId()).build());
        if(!custSiteRes.isSuccess() || custSiteRes.getData() == null ||  custSiteRes.getData().getId() == null){
            return DubboResponse.getOK();
        }
        //根据起点+日期+终点查询
        List<DeliveryOrderDTO> deliveryOrders = deliveryOrderService.queryList(DeliveryOrderQuery.builder()
                .beginSiteId(citySiteRes.getData().getId())
                .endSiteId(custSiteRes.getData().getId())
                .sourceList(DistOrderSourceEnum.getCityCode())
                .deliveryTime(deliverySiteQueryStandardReq.getDeliveryTime())
                .build());

        deliveryOrders = deliveryOrders.stream().filter(deliveryOrderDTO -> deliveryOrderDTO.getId() != null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(deliveryOrders)){
            return DubboResponse.getOK();
        }
        DeliverySiteDTO deliverySiteDTO = deliverySiteService.queryDeliverySiteSendDetail(deliveryOrders.get(0).getDeliveryBatchId(),custSiteRes.getData().getId());

        if(deliverySiteDTO != null){
            deliverySiteDTO.setDeliveryOrders(deliveryOrders);
        }
        return DubboResponse.getOK(DeliverySiteDubboConverter.dtoToStandardResp(deliverySiteDTO));
    }

    @Override
    public DubboResponse<OrderCodeResp> queryOrderNoByCode(@Valid OrderCodeQueryReq orderCodeQueryReq) {
        String orderNo = deliverySiteService.queryOrderNoByCode(orderCodeQueryReq.getCode());
        OrderCodeResp orderCodeResp = new OrderCodeResp();
        orderCodeResp.setOrderNo(orderNo);
        return DubboResponse.getOK(orderCodeResp);
    }


    /*@PostConstruct
    public void test(){
        DeliverySiteQueryStandardReq deliverySiteQueryStandardReq = new DeliverySiteQueryStandardReq();
        deliverySiteQueryStandardReq.setDeliveryTime(LocalDate.now());
        deliverySiteQueryStandardReq.setOuterContactId("2293");
        deliverySiteQueryStandardReq.setStoreNo("1");
        deliverySiteQueryStandardReq.setType(0);
        DubboResponse<DeliverySiteStandardResp> deliverySiteStandardRespDubboResponse = queryDeliverySiteDetail(deliverySiteQueryStandardReq);
        System.out.println(JSON.toJSON(deliverySiteStandardRespDubboResponse));
        OrderCodeQueryReq req = new OrderCodeQueryReq();
        req.setCode("000077171S00015");
        DubboResponse<OrderCodeResp> orderCodeRespDubboResponse = queryOrderNoByCode(req);
        System.out.println(orderCodeRespDubboResponse);
    }*/
}
