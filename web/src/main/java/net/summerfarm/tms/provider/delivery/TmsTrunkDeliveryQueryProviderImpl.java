package net.summerfarm.tms.provider.delivery;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.delivery.provider.TmsTrunkDeliveryQueryProvider;
import net.summerfarm.tms.client.delivery.req.TrunkDeliveryBatchQueryReq;
import net.summerfarm.tms.client.delivery.req.TrunkDeliveryDetailQueryReq;
import net.summerfarm.tms.client.delivery.resp.TrunkDeliveryBatchResp;
import net.summerfarm.tms.client.delivery.resp.TrunkDeliveryDetailResp;
import net.summerfarm.tms.inbound.converter.delivery.trunk.TrunkDeliveryBatchVOConverter;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.provider.delivery.converter.TmsTrunkDeliveryDubboConverter;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:干线查询接口
 */
@Slf4j
@DubboService
public class TmsTrunkDeliveryQueryProviderImpl implements TmsTrunkDeliveryQueryProvider {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Override
    public DubboResponse<List<TrunkDeliveryDetailResp>> queryTrunkDeliveryDetail(@Valid TrunkDeliveryDetailQueryReq trunkDeliveryDetailQueryReq) {
        long day = ChronoUnit.DAYS.between(trunkDeliveryDetailQueryReq.getBeginQueryDateTime(), trunkDeliveryDetailQueryReq.getEndQueryDateTime());
        if(day > 3){
            throw new TmsRuntimeException("最大时间范围为三天");
        }
        DeliveryBatchQuery deliveryBatchQuery = new DeliveryBatchQuery();
        deliveryBatchQuery.setBeginFinishTime(trunkDeliveryDetailQueryReq.getBeginQueryDateTime());
        deliveryBatchQuery.setEndFinishTime(trunkDeliveryDetailQueryReq.getEndQueryDateTime());
        deliveryBatchQuery.setDeliveryBatchStatus(trunkDeliveryDetailQueryReq.getStatus());

        List<DeliveryBatchDTO> deliveryBatchDTOS = deliveryBatchService.queryTrunkDeliveryDetail(deliveryBatchQuery);
        return DubboResponse.getOK(deliveryBatchDTOS.stream().map(TmsTrunkDeliveryDubboConverter::dto2Resp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<TrunkDeliveryBatchResp> queryTrunkDeliveryBatch(@Valid TrunkDeliveryBatchQueryReq trunkDeliveryBatchQueryReq) {
        DeliveryBatchDTO data = deliveryBatchService.deliveryBatchDetail(trunkDeliveryBatchQueryReq.getDeliveryBatchId()).getData();
        return DubboResponse.getOK(TrunkDeliveryBatchVOConverter.dto2BatchResp(data));
   }

    /*@PostConstruct
    public void test(){
        TrunkDeliveryDetailQueryReq trunkDeliveryDetailQueryReq = new TrunkDeliveryDetailQueryReq();
        trunkDeliveryDetailQueryReq.setStatus(40);

        trunkDeliveryDetailQueryReq.setBeginQueryDateTime(LocalDateTime.now().plusDays(-8));
        trunkDeliveryDetailQueryReq.setEndQueryDateTime(LocalDateTime.now().plusDays(-6));
        queryTrunkDeliveryDetail(trunkDeliveryDetailQueryReq);
    }*/
}
