package net.summerfarm.tms.provider.dist;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.client.dist.provider.standard.TmsDistOrderQueryStandardProvider;
import net.summerfarm.tms.client.dist.req.standard.DistOrderBatchQueryStandardReq;
import net.summerfarm.tms.client.dist.req.standard.DistOrderDriverPathCurrentInfoReq;
import net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderDriverPathCurrentInfoResp;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderSingleStandardResp;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderStandardResp;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.DeliverySiteStatusEnum;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.provider.dist.converter.DistOrderDubboConverter;
import net.summerfarm.tms.provider.dist.converter.DistOrderSingleDubboConverter;
import net.summerfarm.tms.provider.dist.converter.DistSiteDubboConverter;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.query.site.SiteIdQuery;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 委托单标准化查询接口<br/>
 * date: 2023/5/8 14:24<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsDistOrderQueryStandardProviderImpl implements TmsDistOrderQueryStandardProvider {
    @Resource
    private DistOrderService distOrderService;
    @Resource
    private DeliveryBatchService deliveryBatchService;
    @Resource
    private SiteService siteService;
    @Override
    public DubboResponse<DistOrderStandardResp> queryDistOrderDetail(DistOrderQueryStandardReq distOrderQueryStandardReq) {
        log.info("查询标准化委托单详情接口参数:{}", JSON.toJSONString(distOrderQueryStandardReq));
        TmsAssert.notNull(distOrderQueryStandardReq.getSource(), ErrorCodeEnum.PARAM_NOT_NULL, "source");
        TmsAssert.notNull(distOrderQueryStandardReq.getExpectBeginTime(), ErrorCodeEnum.PARAM_NOT_NULL, "expectBeginTime");
        TmsAssert.notEmpty(distOrderQueryStandardReq.getOuterContactId(), ErrorCodeEnum.PARAM_NOT_NULL, "outerContactId");
        TmsAssert.notEmpty(distOrderQueryStandardReq.getOuterOrderId(), ErrorCodeEnum.PARAM_NOT_NULL, "outerOrderId");

        DistOrderQuery distOrderQuery = DistOrderDubboConverter.standardReqToDistOrder(distOrderQueryStandardReq);
        DistOrderDTO distOrderDTO = distOrderService.queryDistOrderWithBatchDeliverySite(distOrderQuery);

        return DubboResponse.getOK(DistOrderDubboConverter.dtoToStandardResp(distOrderDTO));
    }

    @Override
    public DubboResponse<List<DistOrderStandardResp>> queryDistOrderDetailList(@Valid DistOrderBatchQueryStandardReq distOrderBatchQueryStandardReq) {
        log.info("查询标准化委托单详情集合接口参数:{}", JSON.toJSONString(distOrderBatchQueryStandardReq));
        List<DistOrderQueryStandardReq> distOrderQueryStandardReqs = distOrderBatchQueryStandardReq.getDistOrderQueryStandardReqs();
        if (distOrderQueryStandardReqs.size() > 50){
            throw new ProviderException("distOrderQueryStandardReqs超出查询限制");
        }
        DistOrderQuery query = DistOrderQuery.builder()
                .outerOrderIds(distOrderQueryStandardReqs.stream().map(DistOrderQueryStandardReq::getOuterOrderId).collect(Collectors.toList()))
                .sources(distOrderQueryStandardReqs.stream().map(DistOrderQueryStandardReq::getSource).distinct().collect(Collectors.toList()))
                .build();
        //考虑目前喜茶无省心送订单 先不处理省心送的单子
        List<DistOrderDTO> distOrderDTOS = distOrderService.batchQueryDistOrderWithBatchDeliverySite(query);
        return DubboResponse.getOK(distOrderDTOS.stream().map(DistOrderDubboConverter::dtoToStandardResp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<List<DistOrderSingleStandardResp>> queryDistOrderSingleList(@Valid DistOrderBatchQueryStandardReq distOrderBatchQueryStandardReq) {
        log.info("查询标准化委托单(单表)集合接口参数:{}", JSON.toJSONString(distOrderBatchQueryStandardReq));

        List<DistOrderQueryStandardReq> distOrderQueryStandardReqs = distOrderBatchQueryStandardReq.getDistOrderQueryStandardReqs();
        //过滤出省心送类型
        List<DistOrderQueryStandardReq> distOrderQueryStandardReqWithTiming = distOrderQueryStandardReqs.stream().filter(e -> Objects.equals(e.getSource(), DistOrderSourceEnum.XM_MALL_TIMING.getCode())).collect(Collectors.toList());
        //过滤出非省心送类型
        List<DistOrderQueryStandardReq> distOrderQueryStandardReqWithOther = distOrderQueryStandardReqs.stream().filter(e -> !Objects.equals(e.getSource(), DistOrderSourceEnum.XM_MALL_TIMING.getCode())).collect(Collectors.toList());
        List<DistOrderDTO> distOrderDTOS = new ArrayList<>();
        //查询非省心送类型的委托单
        if (!CollectionUtils.isEmpty(distOrderQueryStandardReqWithOther)){
            List<Integer> sources = distOrderQueryStandardReqWithOther.stream().map(DistOrderQueryStandardReq::getSource).distinct().collect(Collectors.toList());
            // todo 兼容POP线上查询 后续可以删除
            sources.add(DistOrderSourceEnum.POP_MALL.getCode());
            sources.add(DistOrderSourceEnum.POP_AFTER_SALE.getCode());
            List<DistOrderDTO> distOrderDTOSWithOther = distOrderService.batchQueryDistOrderList(DistOrderQuery.builder()
                    .outerOrderIds(distOrderQueryStandardReqWithOther.stream().map(DistOrderQueryStandardReq::getOuterOrderId).collect(Collectors.toList()))
                    .sources(sources)
                    .build());
            distOrderDTOS.addAll(distOrderDTOSWithOther);
        }
        //查询省心送类型的委托单
        if (!CollectionUtils.isEmpty(distOrderQueryStandardReqWithTiming)){
            List<DistOrderDTO> distOrderDTOSWithTiming = distOrderService.batchQueryDistOrderList(DistOrderQuery.builder()
                    .outerOrderIds(distOrderQueryStandardReqWithTiming.stream().map(DistOrderQueryStandardReq::getOuterOrderId).collect(Collectors.toList()))
                    .sources(Collections.singletonList(DistOrderSourceEnum.XM_MALL_TIMING.getCode()))
                    .outerContactIds(distOrderQueryStandardReqWithTiming.stream().map(DistOrderQueryStandardReq::getOuterContactId).distinct().collect(Collectors.toList()))
                    .build());
            Set<String> timingOrderUkSet = distOrderQueryStandardReqWithTiming.stream().map(e -> String.format("%s#%s#%s", e.getOuterOrderId(), e.getExpectBeginTime(), e.getOuterContactId())).collect(Collectors.toSet());
            distOrderDTOSWithTiming = distOrderDTOSWithTiming.stream().filter(e -> timingOrderUkSet.contains(String.format("%s#%s#%s", e.getOutOrderId(), e.getExpectBeginTime(), e.getOutContactId()))).collect(Collectors.toList());
            distOrderDTOS.addAll(distOrderDTOSWithTiming);
        }
        List<DistOrderSingleStandardResp> distOrderSingleStandardRespList = distOrderDTOS.stream().map(DistOrderSingleDubboConverter::dtoToStandardResp).collect(Collectors.toList());
        return DubboResponse.getOK(distOrderSingleStandardRespList);
    }

    @Override
    public DubboResponse<List<DistOrderSingleStandardResp>> querySiteDistOrderSingleList(DistOrderQueryStandardReq distOrderQueryReq) {
        List<DistOrderDTO> distOrderDTOS = distOrderService.querySiteDistOrderList(DistOrderQuery.builder()
               .outerOrderId(distOrderQueryReq.getOuterOrderId())
               .source(distOrderQueryReq.getSource()).outerContactId(distOrderQueryReq.getOuterContactId()).expectBeginTime(distOrderQueryReq.getExpectBeginTime())
               .build());
        List<DistOrderSingleStandardResp> distOrderSingleStandardRespList = distOrderDTOS.stream().map(DistOrderSingleDubboConverter::dtoToStandardResp).collect(Collectors.toList());
        return DubboResponse.getOK(distOrderSingleStandardRespList);
    }

    @Override
    public DubboResponse<DistOrderDriverPathCurrentInfoResp> queryDistOrderDriverPathCurrentInfo(@Valid DistOrderDriverPathCurrentInfoReq req) {
        DistOrderQuery query = DistOrderQuery.builder()
                .outerOrderId(req.getOuterOrderId())
                .source(req.getSource())
                .outerContactId(req.getOuterContactId())
                .expectBeginTime(req.getExpectBeginTime())
                .build();
        DistOrderDTO distOrderDTO = distOrderService.queryDistOrderWithBatchDeliverySite(query);
        if(distOrderDTO == null){
            return DubboResponse.getDefaultError("无此订单信息");
        }
        Integer pathStatus = distOrderDTO.getStatus();
        if(!Objects.equals(DistOrderStatusEnum.IN_DELIVERY.getCode(),pathStatus)){
            return DubboResponse.getDefaultError("配送状态不是配送中，无法提供查询");
        }
        //客户点位信息
        SiteDTO customerSiteDTO = siteService.queryDetail(SiteIdQuery.builder().siteId(distOrderDTO.getEndSiteId()).build());

        DeliveryBatchDTO deliveryBatchDTO = deliveryBatchService.queryDriverCurrentInfo(DeliveryBatchQuery.builder()
                .batchId(distOrderDTO.getDeliveryBatchList().get(0).getDeliveryBatchId())
                .driverId(distOrderDTO.getDeliveryBatchList().get(0).getDriverId()).build());

        if(deliveryBatchDTO == null || CollectionUtils.isEmpty(deliveryBatchDTO.getDeliverySiteDTOList())){
            throw new TmsRuntimeException("不存在车辆配送信息或者配送车辆不存在此配送点位");
        }
        //过滤未完成配送点位
        List<DeliverySiteDTO> unFinishDeliverySiteList = deliveryBatchDTO.getDeliverySiteDTOList().stream()
                .filter(site -> !Objects.equals(site.getSiteId(),deliveryBatchDTO.getBeginSiteId()))
                .filter(site -> !Objects.equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode(), site.getStatus()))
                .collect(Collectors.toList());
        //过滤完成配送点位
        List<DeliverySiteDTO> finishDeliverySiteList = deliveryBatchDTO.getDeliverySiteDTOList().stream()
                .filter(site -> !Objects.equals(site.getSiteId(),deliveryBatchDTO.getBeginSiteId()))
                .filter(site -> Objects.equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode(), site.getStatus()))
                .collect(Collectors.toList());
        //获取城配仓点位
        List<DeliverySiteDTO> beginSiteDTOList = deliveryBatchDTO.getDeliverySiteDTOList().stream()
                .filter(site -> Objects.equals(site.getSiteId(), deliveryBatchDTO.getBeginSiteId()))
                .collect(Collectors.toList());

        DistOrderDriverPathCurrentInfoResp resp = new DistOrderDriverPathCurrentInfoResp();
        //过滤出城配仓点位
        List<DeliverySiteDTO> beginDeliverySiteDTOList = deliveryBatchDTO.getDeliverySiteDTOList().stream()
                .filter(site -> Objects.equals(site.getSiteId(), deliveryBatchDTO.getBeginSiteId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(beginDeliverySiteDTOList)){
            return DubboResponse.getOK(resp);
        }

        resp.setLastSite(DistSiteDubboConverter.distSiteDTOToResp(beginDeliverySiteDTOList.get(0).getSiteDTO()));
        resp.setCurrentSite(DistSiteDubboConverter.distSiteDTOToResp(customerSiteDTO));

        resp.setUnFinishCount(unFinishDeliverySiteList.size());
        resp.setDeliveryTime(distOrderDTO.getExpectBeginTime() != null ? distOrderDTO.getExpectBeginTime().toLocalDate() : null);
        resp.setPath(deliveryBatchDTO.getPathCode());
        resp.setLastSitePositionTime(CollectionUtils.isEmpty(finishDeliverySiteList) ? null : finishDeliverySiteList.get(0).getSignInTime());

        //如果是空的去城配仓打卡信息
        if(CollectionUtils.isEmpty(finishDeliverySiteList) && !CollectionUtils.isEmpty(beginSiteDTOList)){
                resp.setLastSitePositionTime(beginSiteDTOList.get(0).getSignInTime());
        }
        return DubboResponse.getOK(resp);
    }

}
