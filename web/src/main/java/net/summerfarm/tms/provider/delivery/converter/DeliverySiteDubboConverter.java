package net.summerfarm.tms.provider.delivery.converter;

import net.summerfarm.tms.client.delivery.req.standard.DeliveryBatchStandardMessage;
import net.summerfarm.tms.client.delivery.req.standard.DeliverySiteItemStandardMessage;
import net.summerfarm.tms.client.delivery.req.standard.DeliverySiteStandardResp;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/5/16 18:36<br/>
 *
 * <AUTHOR> />
 */
public class DeliverySiteDubboConverter {

    public static DeliverySiteStandardResp dtoToStandardResp(DeliverySiteDTO deliverySiteDTO) {
        DeliverySiteStandardResp deliverySiteStandardResp = new DeliverySiteStandardResp();
        if(deliverySiteDTO == null){
            return deliverySiteStandardResp;
        }

        deliverySiteStandardResp.setDeliveryBatchId(deliverySiteDTO.getDeliveryBatchId());
        deliverySiteStandardResp.setId(deliverySiteDTO.getId());
        deliverySiteStandardResp.setPlanArriveTime(deliverySiteDTO.getPlanArriveTime());
        deliverySiteStandardResp.setSequence(deliverySiteDTO.getSequence());
        deliverySiteStandardResp.setSignInPics(deliverySiteDTO.getSignInPic1());
        deliverySiteStandardResp.setSignInSignPic(deliverySiteDTO.getSignInPic2());
        deliverySiteStandardResp.setSignInProductPic(deliverySiteDTO.getSignInPic3());
        deliverySiteStandardResp.setSignInRemark(deliverySiteDTO.getSignInRemark());
        deliverySiteStandardResp.setSignInStatus(deliverySiteDTO.getSignInStatus());
        deliverySiteStandardResp.setSignInTime(deliverySiteDTO.getSignInTime());
        deliverySiteStandardResp.setStatus(deliverySiteDTO.getStatus());
        if(deliverySiteDTO.getDeliveryBatchDTO() != null){
            DeliveryBatchDTO deliveryBatchDTO = deliverySiteDTO.getDeliveryBatchDTO();
            DeliveryBatchStandardMessage deliveryBatchStandardMessage = new DeliveryBatchStandardMessage();
            deliveryBatchStandardMessage.setDeliveryBatchId(deliveryBatchDTO.getDeliveryBatchId());
            deliveryBatchStandardMessage.setDriver(deliveryBatchDTO.getDriver());
            deliveryBatchStandardMessage.setDriverId(deliveryBatchDTO.getDriverId());
            deliveryBatchStandardMessage.setDriverPhone(deliveryBatchDTO.getDriverPhone());
            deliveryBatchStandardMessage.setPathCode(deliveryBatchDTO.getPathCode());
            deliveryBatchStandardMessage.setPathName(deliveryBatchDTO.getPathName());
            deliveryBatchStandardMessage.setCarNumber(deliveryBatchDTO.getCarNumber());
            deliverySiteStandardResp.setBatchMessage(deliveryBatchStandardMessage);
        }
        if(!CollectionUtils.isEmpty(deliverySiteDTO.getDeliveryOrders())){
            deliverySiteStandardResp.setOutOrderIdList(deliverySiteDTO.getDeliveryOrders().stream().map(DeliveryOrderDTO::getOuterOrderId).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(deliverySiteDTO.getDeliverySiteItemDTOList())){
            deliverySiteStandardResp.setDeliverySiteItemMessages(deliverySiteDTO.getDeliverySiteItemDTOList().stream().map(DeliverySiteDubboConverter::deliverySiteItem2Message).collect(Collectors.toList()));
        }

        return deliverySiteStandardResp;
    }

    public static DeliverySiteItemStandardMessage deliverySiteItem2Message(DeliverySiteItemDTO deliverySiteItemDTO){
        if(deliverySiteItemDTO == null){
            return null;
        }
        DeliverySiteItemStandardMessage deliverySiteItemStandardMessage = new DeliverySiteItemStandardMessage();

        deliverySiteItemStandardMessage.setNoscanCount(deliverySiteItemDTO.getNoscanCount());
        deliverySiteItemStandardMessage.setOutItemId(deliverySiteItemDTO.getOutItemId());
        deliverySiteItemStandardMessage.setOutItemName(deliverySiteItemDTO.getOutItemName());
        deliverySiteItemStandardMessage.setPlanReceiptCount(deliverySiteItemDTO.getPlanReceiptCount());
        deliverySiteItemStandardMessage.setRealReceiptCount(deliverySiteItemDTO.getRealReceiptCount());
        deliverySiteItemStandardMessage.setScanCount(deliverySiteItemDTO.getScanCount());
        deliverySiteItemStandardMessage.setShortCount(deliverySiteItemDTO.getShortCount());
        deliverySiteItemStandardMessage.setType(deliverySiteItemDTO.getType());
        deliverySiteItemStandardMessage.setRemark(deliverySiteItemDTO.getRemark());

        return deliverySiteItemStandardMessage;
    }
}
