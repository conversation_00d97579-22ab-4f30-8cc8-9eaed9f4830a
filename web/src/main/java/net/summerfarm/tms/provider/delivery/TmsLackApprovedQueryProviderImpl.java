package net.summerfarm.tms.provider.delivery;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.delivery.provider.TmsLackApprovedQueryProvider;
import net.summerfarm.tms.client.delivery.resp.TmsLackApprovedResp;
import net.summerfarm.tms.delivery.LackApprovedService;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.provider.delivery.converter.LackApprovedDubboConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * Description:缺货核准
 */
@Slf4j
@DubboService
public class TmsLackApprovedQueryProviderImpl implements TmsLackApprovedQueryProvider {

    @Resource
    private LackApprovedService lackApprovedService;

    @Override
    public DubboResponse<TmsLackApprovedResp> queryLackGoodDetailById(Long id) {
        TmsAssert.notNull(id, ErrorCodeEnum.PARAM_NOT_NULL, "id");
        return DubboResponse.getOK(LackApprovedDubboConverter.dto2Resp(lackApprovedService.queryById(id)));
    }
}
