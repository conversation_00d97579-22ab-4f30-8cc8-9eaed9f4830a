package net.summerfarm.tms.message.consumer.delivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.message.out.CompletePathMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 完成排线点位商品的存储条件处理<br/>
 * date: 2023/7/10 16:47<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TMS_PATH, tag = MqConstants.Tag.TAG_DELIVERY_COMPLETE_PATH_BUS_MSG,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_TEMPERATURE_CONDITIONS,
        consumeThreadMin = 1,
        consumeThreadMax = 2)
public class DeliverySiteTemConditionsConsumer extends AbstractMqListener<CompletePathMessage> {

    @Resource
    private DeliverySiteService deliverySiteService;

    @Override
    public void process(CompletePathMessage completePathMessage) {
        log.info("生成点位存储:tms完成排线消息:{}", JSON.toJSONString(completePathMessage));
        List<Long> deliveryBatchIds = completePathMessage.getDeliveryBatchIds();
        deliveryBatchIds = deliveryBatchIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        deliverySiteService.siteTemConditionsCreate(deliveryBatchIds);
    }
}
