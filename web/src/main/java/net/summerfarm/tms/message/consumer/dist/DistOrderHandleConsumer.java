package net.summerfarm.tms.message.consumer.dist;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.message.in.*;
import net.summerfarm.tms.client.message.out.DistResultMessage;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.enums.DistSubjectEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.gray.old2new.DistOrderSyncService;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 委托单统一处理
 * date: 2023/3/7 10:49
 *
 * <AUTHOR>
 */
@Slf4j
@MqOrderlyListener(topic = MqConstants.Topic.TMS_DIST_REQUIREMENT,
        tag = MqConstants.Tag.DIST_ORDER_HANDLE,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DIST_ORDER_HANDLE,
        maxReconsumeTimes = 5)
public class DistOrderHandleConsumer extends AbstractMqListener<DistOrderMessage> {

    @Resource
    private DistOrderSyncService distOrderSyncService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public void process(DistOrderMessage distOrderMessage) {
        log.info("TMS接收委托单需处理消息，{}", JSON.toJSONString(distOrderMessage));

        String msg = getMsg(distOrderMessage);
        String subjectTag = null;
        String outerId = null;
        try {
            DistOrderUkMessage distOrderUk = JSON.parseObject(msg, DistOrderUkMessage.class);
            RLock redissonLock = redissonClient.getLock(RedisConstants.DistRequirement.TMS_DIST_REQUIREMENT_CONTACT_ID + ":" +distOrderUk.getOuterContactId());
            try {
                if (!redissonLock.tryLock(3L, 2L, TimeUnit.SECONDS)) {
                    throw new BizException("TMS接收委托单正在处理，请稍后");
                }
                if (Objects.equals(MqConstants.Event.DIST_ORDER_CREATE.name(), distOrderMessage.getEvent())) {
                    DistOrderCreateMessage distOrderCreateMessage = JSON.parseObject(msg, DistOrderCreateMessage.class);
                    subjectTag = DistSubjectEnum.getSubjectTag(distOrderCreateMessage.getSource());
                    outerId = distOrderCreateMessage.getOuterId();
                    //逻辑处理
                    distOrderSyncService.createDistOrder(distOrderCreateMessage);
                } else if (Objects.equals(MqConstants.Event.DIST_ORDER_CHANGE.name(), distOrderMessage.getEvent())) {
                    DistOrderChangeMessage distOrderChangeMessage = JSON.parseObject(msg, DistOrderChangeMessage.class);
                    subjectTag = DistSubjectEnum.getSubjectTag(distOrderChangeMessage.getSource());
                    outerId = distOrderChangeMessage.getOuterId();
                    //逻辑处理
                    distOrderSyncService.changeDistOrder(distOrderChangeMessage);
                } else if (Objects.equals(MqConstants.Event.DIST_ORDER_CANCEL.name(), distOrderMessage.getEvent())) {
                    DistOrderCancelMessage distOrderCancelMessage = JSON.parseObject(msg, DistOrderCancelMessage.class);
                    subjectTag = DistSubjectEnum.getSubjectTag(distOrderCancelMessage.getSource());
                    outerId = distOrderCancelMessage.getOuterId();
                    //逻辑处理
                    distOrderSyncService.cancelDistOrder(distOrderCancelMessage);
                } else if (Objects.equals(MqConstants.Event.DIST_ORDER_DETAIL_CANCEL.name(), distOrderMessage.getEvent())) {
                    DistOrderDetailCancelMessage distOrderDetailCancelMessage = JSON.parseObject(msg, DistOrderDetailCancelMessage.class);
                    subjectTag = DistSubjectEnum.getSubjectTag(distOrderDetailCancelMessage.getSource());
                    outerId = distOrderDetailCancelMessage.getOuterId();
                    //逻辑处理
                    distOrderSyncService.cancelDistOrderDetail(distOrderDetailCancelMessage);
                }else {
                    log.warn("无法解析,丢弃消息");
                    return;
                }
                mqProducer.send(MqConstants.Topic.TMS_DIST_RESULT, subjectTag, DistResultMessage.success(outerId, distOrderMessage.getEvent()));
            } catch (InterruptedException e) {
                log.error("委托单并发告警,订单信息:{}",JSON.toJSONString(distOrderUk),e);
                throw new BizException("委托单并发告警", e);
            } finally {
                if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                    redissonLock.unlock();
                }
            }
        }catch (Exception e){
            if (!(e instanceof TmsRuntimeException)){
                log.error("委托单处理失败:{}",e.getMessage(), e);
                throw e;
            }
            mqProducer.send(MqConstants.Topic.TMS_DIST_RESULT, subjectTag, exceptionHandle(outerId, distOrderMessage.getEvent(), e));
        }
        //生成备注信息
        try {
            DistOrderUkMessage distOrderUkMessage = JSON.parseObject(msg, DistOrderUkMessage.class);
            if (Objects.equals(MqConstants.Event.DIST_ORDER_CHANGE.name(), distOrderMessage.getEvent())) {
                DistOrderChangeMessage distOrderChangeMessage = JSON.parseObject(msg, DistOrderChangeMessage.class);
                distOrderUkMessage.setExpectBeginTime(distOrderChangeMessage.getNewExpectBeginTime());
                distOrderUkMessage.setOuterContactId(distOrderChangeMessage.getNewEndSite().getOutBusinessNo());
                distOrderUkMessage.setSource(distOrderChangeMessage.getSource());
                distOrderUkMessage.setOuterOrderId(distOrderChangeMessage.getOuterOrderId());
            }
            mqProducer.send(MqConstants.Topic.TMS_PATH,MqConstants.Tag.TAG_TMS_DELIVERY_SITE_SEND_REMARK,JSON.toJSONString(distOrderUkMessage));
        } catch (Exception e) {
            log.error("生成备注信息异常",e);
        }
    }

    private String getMsg(DistOrderMessage distOrderMessage) {
        String msg;
        if (distOrderMessage.getDistOrderData() instanceof String) {
            msg = String.valueOf(distOrderMessage.getDistOrderData());
        } else {
            msg = JSONObject.toJSONString(distOrderMessage.getDistOrderData());
        }
        return msg;
    }

    private DistResultMessage exceptionHandle(String outerId,String event, Exception e) {
        if (e instanceof TmsRuntimeException){
            log.info("委托单处理失败:{}",e.getMessage(), e);
            TmsRuntimeException tmsRuntimeException = (TmsRuntimeException)e;
            return DistResultMessage.fail(outerId, event, tmsRuntimeException.getErrorCode().code, tmsRuntimeException.getMessage());
        }
        log.error("委托单处理失败:{}",e.getMessage(), e);
        return DistResultMessage.fail(outerId, event, ErrorCodeEnum.UNKNOWN);
    }
}
