package net.summerfarm.tms.message.consumer.delivery;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/2/17 10:23<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TMS_PATH, tag = MqConstants.Tag.TAG_DELIVERY_COMPLETE_PATH,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DELIVERY_COMPLETE_PATH,
        consumeThreadMin = 1,
        consumeThreadMax = 2)
public class DeliveryBatchConsumer extends AbstractMqListener<Long> {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Override
    public void process(Long batchId) {
        log.info("生成大客户拣货任务,完成排线收到消息,批次id为:{}",batchId);
        deliveryBatchService.createBigCustomerPick(batchId);
    }

/*    @PostConstruct
    public void test(){
        this.process(3352L);
    }*/
}
