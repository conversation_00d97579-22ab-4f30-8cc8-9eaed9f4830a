package net.summerfarm.tms.message.consumer.delivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.command.DeliveryBatchCommandDomainService;
import net.summerfarm.tms.message.out.CompletePathMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/5/25 11:48<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TMS_PATH, tag = MqConstants.Tag.TAG_DELIVERY_COMPLETE_PATH_BUS_MSG,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DELIVERY_COMPLETE_PATH_BUS_MSG,
        consumeThreadMin = 1,
        consumeThreadMax = 2)
public class DeliveryBatchCompletePathBusMsgConsumer extends AbstractMqListener<CompletePathMessage> {

    @Resource
    private DeliveryBatchService deliveryBatchService;
    @Resource
    private DeliveryBatchCommandDomainService deliveryBatchCommandDomainService;

    @Override
    public void process(CompletePathMessage completePathMessage) {
        log.info("接受到tms完成排线消息总线通知:{}", JSON.toJSONString(completePathMessage));
        List<Long> deliveryBatchIds = completePathMessage.getDeliveryBatchIds();
        deliveryBatchIds = deliveryBatchIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(deliveryBatchIds)){
            //通知tms自己 大客户拣货
            deliveryBatchIds.forEach(id -> deliveryBatchService.finishCompletePathNotifyTms(id));
            //防止主从延迟同步
            try {
                Thread.sleep(1500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            //通知OFC侧
            deliveryBatchIds.forEach(batchId -> deliveryBatchService.notifyCompletePathDeliveryEvents(batchId));
            //计算批次装载率处理
            for (Long deliveryBatchId : deliveryBatchIds) {
                try {
                    deliveryBatchService.executeBatchLoadRatioCalc(deliveryBatchId);
                } catch (Exception e) {
                    log.error("城配完成排线消息计算批次装载率批次id:{},异常:{}",deliveryBatchId, e.getMessage(),e);
                }
            }

            // 贪心算法处理
            try {
                deliveryBatchCommandDomainService.greedyCalcDistance(deliveryBatchIds);
            } catch (Exception e) {
                log.error("城配完成排线消息计算智能排线距离id:{},异常:{}",deliveryBatchIds, e.getMessage(),e);
            }
        }
        log.info("tms完成排线消息总线处理完成:{}", JSON.toJSONString(completePathMessage));
    }
}
