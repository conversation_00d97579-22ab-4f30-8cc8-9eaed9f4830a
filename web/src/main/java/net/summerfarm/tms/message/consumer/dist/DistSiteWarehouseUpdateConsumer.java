package net.summerfarm.tms.message.consumer.dist;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteWarehouseCreateUpdateCommand;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.enums.TenantWarehouseEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.message.in.WarehouseStorageCenterDetailMessage;
import net.summerfarm.tms.message.in.WarehouseStorageCenterUpdateMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2023/8/30 13:43<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TOPIC_WNC_WAREHOUSE,
        tag = MqConstants.Tag.TAG_WNC_WAREHOUSE_UPDATE,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DIST_SITE_WAREHOUSE_UPDATE)
public class DistSiteWarehouseUpdateConsumer extends AbstractMqListener<WarehouseStorageCenterUpdateMessage> {

    @Resource
    private SiteService siteService;

    @Override
    public void process(WarehouseStorageCenterUpdateMessage msg) {
        log.info("TMS接收仓库点位更新处理消息，{}", JSON.toJSONString(msg));
        WarehouseStorageCenterDetailMessage newWahouse = msg.getNewWarehouseInfo();
        if(newWahouse == null || StringUtils.isBlank(newWahouse.getAddress()) || StringUtils.isBlank(newWahouse.getPoiNote()) || newWahouse.getTenantId() == null || StringUtils.isBlank(newWahouse.getWarehouseName()) || newWahouse.getWarehouseNo() == null){
            log.info("仓库点位参数校验不通过");
            return;
        }
        if(!Objects.equals(TenantWarehouseEnum.SUMMERFARM_WAREHOUSE.getCode(),msg.getTenantId())){
            log.info("非鲜沐仓无需生成仓库点位");
            return;
        }
        Map<String, Object> addressMap = GaoDeUtil.getProCityAreaByAddress(newWahouse.getAddress());
        if(addressMap.size() == 0){
            throw new TmsRuntimeException("调用高德api获取仓库点位异常");
        }
        SiteWarehouseCreateUpdateCommand siteWarehouseCreateCommand = new SiteWarehouseCreateUpdateCommand();
        siteWarehouseCreateCommand.setProvince(addressMap.get("province") != null ? String.valueOf(addressMap.get("province")) : null);
        siteWarehouseCreateCommand.setCity(addressMap.get("city") != null ? String.valueOf(addressMap.get("city")) : null);
        siteWarehouseCreateCommand.setArea(addressMap.get("district") != null ? String.valueOf(addressMap.get("district")) : null);
        siteWarehouseCreateCommand.setAddress(newWahouse.getAddress());
        siteWarehouseCreateCommand.setPoi(newWahouse.getPoiNote());
        siteWarehouseCreateCommand.setType(SiteTypeEnum.warehouse.getCode());
        siteWarehouseCreateCommand.setOutBusinessNo(String.valueOf(newWahouse.getWarehouseNo()));
        siteWarehouseCreateCommand.setName(newWahouse.getWarehouseName());
        siteWarehouseCreateCommand.setContactPerson(newWahouse.getPersonContact());
        siteWarehouseCreateCommand.setPhone(newWahouse.getPhone());
        siteWarehouseCreateCommand.setSitePics(newWahouse.getWarehousePic());

        siteService.updateWarehouseSite(siteWarehouseCreateCommand);
    }
}
