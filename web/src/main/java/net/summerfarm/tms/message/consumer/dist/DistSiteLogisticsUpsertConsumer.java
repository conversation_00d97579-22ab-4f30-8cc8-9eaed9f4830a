package net.summerfarm.tms.message.consumer.dist;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteLogisticsUpsertCommand;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.message.in.WarehouseLogisticsCenterUpsertMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Description: 城配仓新增变更信息<br/>
 * date: 2023/8/30 13:43<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TOPIC_WNC_WAREHOUSE,
        tag = MqConstants.Tag.TAG_WNC_WAREHOUSE_LOGISTICS_UPSERT,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DIST_SITE_LOGISTICS_UPSERT)
public class DistSiteLogisticsUpsertConsumer extends AbstractMqListener<WarehouseLogisticsCenterUpsertMessage> {

    @Resource
    private SiteService siteService;

    @Override
    public void process(WarehouseLogisticsCenterUpsertMessage logisticsCenter) {
        log.info("TMS接收城配仓点位新增处理消息，{}", JSON.toJSONString(logisticsCenter));
        if(logisticsCenter == null || StringUtils.isBlank(logisticsCenter.getAddress()) || StringUtils.isBlank(logisticsCenter.getPoiNote())  || StringUtils.isBlank(logisticsCenter.getStoreName()) || logisticsCenter.getStoreNo() == null){
            log.info("城配仓点位参数校验不通过");
            return;
        }
        Map<String, Object> addressMap = GaoDeUtil.getProCityAreaByAddress(logisticsCenter.getAddress());
        if(addressMap.size() == 0){
            throw new TmsRuntimeException("调用高德api获取仓库点位异常");
        }
        SiteLogisticsUpsertCommand siteLogisticsUpsertCommand = new SiteLogisticsUpsertCommand();
        siteLogisticsUpsertCommand.setProvince(addressMap.get("province") != null ? String.valueOf(addressMap.get("province")) : null);
        siteLogisticsUpsertCommand.setCity(addressMap.get("city") != null ? String.valueOf(addressMap.get("city")) : null);
        siteLogisticsUpsertCommand.setArea(addressMap.get("district") != null ? String.valueOf(addressMap.get("district")) : null);

        siteLogisticsUpsertCommand.setPunchDistance(logisticsCenter.getPunchDistance());
        siteLogisticsUpsertCommand.setOutTime(logisticsCenter.getOutTime());
        siteLogisticsUpsertCommand.setState(logisticsCenter.getPunchState());
        siteLogisticsUpsertCommand.setPoi(logisticsCenter.getPoiNote());
        siteLogisticsUpsertCommand.setName(logisticsCenter.getStoreName());
        siteLogisticsUpsertCommand.setAddress(logisticsCenter.getAddress());
        siteLogisticsUpsertCommand.setIntelligencePath(logisticsCenter.getIntelligencePath());
        siteLogisticsUpsertCommand.setPhone(logisticsCenter.getPhone());
        siteLogisticsUpsertCommand.setContactPerson(logisticsCenter.getPersonContact());
        siteLogisticsUpsertCommand.setOutBusinessNo(String.valueOf(logisticsCenter.getStoreNo()));
        siteLogisticsUpsertCommand.setType(SiteTypeEnum.store.getCode());
        siteLogisticsUpsertCommand.setSitePics(logisticsCenter.getStorePic());

        siteService.saveOrUpdateStoreSite(siteLogisticsUpsertCommand);
    }
}
