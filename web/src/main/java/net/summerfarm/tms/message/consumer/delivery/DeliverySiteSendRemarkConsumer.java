package net.summerfarm.tms.message.consumer.delivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.message.in.DistOrderUkMessage;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@MqListener(topic = MqConstants.Topic.TMS_PATH, tag =MqConstants.Tag.TAG_TMS_DELIVERY_SITE_SEND_REMARK, consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DELIVERY_SITE_SEND_REMARK)
public class DeliverySiteSendRemarkConsumer extends AbstractMqListener<DistOrderUkMessage> {

    @Resource
    private DeliverySiteService deliverySiteService;
    @Resource
    private DistOrderService distOrderService;

    @Override
    public void process(DistOrderUkMessage distOrderUkMessage) {
        log.info("接收到备注信息推送委托单唯一键:{}", JSON.toJSONString(distOrderUkMessage));
        if(!cheakParam(distOrderUkMessage)){
            log.error("注信息推送参数校验不通过");
            return;
        };
        //只有鲜沐/外单有备注信息
        DistOrderQuery distOrderQuery = DistOrderQuery.builder()
                .expectBeginTime(distOrderUkMessage.getExpectBeginTime())
                .outerContactId(distOrderUkMessage.getOuterContactId())
                .build();
        if(DistOrderSourceEnum.getXmOrderTypeCode().contains(distOrderUkMessage.getSource())){
            distOrderQuery.setSources(DistOrderSourceEnum.getXmOrderTypeCode());
        }else if(DistOrderSourceEnum.getTrunkOuterCode().contains(distOrderUkMessage.getSource())){
            distOrderQuery.setSources(Collections.singletonList(DistOrderSourceEnum.OUTER_CITY.getCode()));
        }else{
            distOrderQuery.setSources(DistOrderSourceEnum.getSaasOrderTypeCode());
        }
        //查询最近委托单信息
        DistOrderDTO distOrderDTO = distOrderService.queryLastDistOrder(distOrderQuery);
        //根据开始点位查询开始位置
        if(distOrderDTO == null || distOrderDTO.getDistId() == null){
            log.info("没有查询到此点位的委托单信息:{}",JSON.toJSONString(distOrderDTO));
            return;
        }
        if(StringUtils.isBlank(distOrderDTO.getSendRemark())){
            log.info("此点位没有备注信息无需处理:{}",JSON.toJSONString(distOrderDTO));
            return;
        }

        List<DeliveryOrderDTO> deliveryOrderList = distOrderDTO.getDeliveryOrderList();
        if(CollectionUtils.isEmpty(deliveryOrderList) || deliveryOrderList.get(0).getId() == null){
            log.info("此点位没有对应的配送单信息:{}",JSON.toJSONString(deliveryOrderList));
            return;
        }

        log.info("需要更新的点位信息batchId:{},endSiteId:{},sendRemark:{}",deliveryOrderList.get(0).getDeliveryBatchId(),
                distOrderDTO.getEndSiteId(),distOrderDTO.getSendRemark());
        //处理备注信息
        deliverySiteService.sendRemarkHandle(deliveryOrderList.get(0).getDeliveryBatchId(),
                distOrderDTO.getEndSiteId(),
                distOrderDTO.getSendRemark());
    }

    /**
     * 参数校验
     * @param distOrderUkMessage 参数
     * @return 结果
     */
    private Boolean cheakParam(DistOrderUkMessage distOrderUkMessage) {
        if(distOrderUkMessage.getSource() == null || distOrderUkMessage.getExpectBeginTime() == null || StringUtils.isBlank(distOrderUkMessage.getOuterContactId())){
            return false;
        }
        return true;
    }
}
