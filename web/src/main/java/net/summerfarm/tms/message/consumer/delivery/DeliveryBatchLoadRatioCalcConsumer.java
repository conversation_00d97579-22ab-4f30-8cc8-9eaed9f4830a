package net.summerfarm.tms.message.consumer.delivery;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.message.out.CalcBatchLoadRatioMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Set;

/**
 * Description: 计算批次装载率处理
 * date: 2023/8/4 15:46
 *
 * <AUTHOR>
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TMS_PATH, tag = MqConstants.Tag.TAG_CALC_BATCH_LOAD_RATIO,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_CALC_BATCH_LOAD_RATIO,
        consumeThreadMin = 1,
        consumeThreadMax = 2)
public class DeliveryBatchLoadRatioCalcConsumer extends AbstractMqListener<CalcBatchLoadRatioMessage> {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Override
    public void process(CalcBatchLoadRatioMessage calcBatchLoadRatioMessage) {
        log.info("接收到批次装载率计算处理消息:{}", JSON.toJSONString(calcBatchLoadRatioMessage));
        Collection<Long> deliveryBatchIds = calcBatchLoadRatioMessage.getDeliveryBatchIds();
        if (CollectionUtils.isEmpty(deliveryBatchIds)) {
            return;
        }
        Set<Long> deliveryBatchIdSet = Sets.newHashSet(deliveryBatchIds);
        log.info("批次装载率计算去重:{}", JSON.toJSONString(deliveryBatchIdSet));
        for (Long deliveryBatchId : deliveryBatchIdSet) {
            deliveryBatchService.executeBatchLoadRatioCalc(deliveryBatchId);
        }
    }
}
