package net.summerfarm.tms.message.consumer.dist;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteWarehouseCreateUpdateCommand;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.enums.TenantWarehouseEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.message.in.WarehouseStorageCreateMsg;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: <br/>
 * date: 2023/8/30 13:43<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TOPIC_WNC_WAREHOUSE,
        tag = MqConstants.Tag.TAG_WNC_WAREHOUSE_CREATE,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DIST_SITE_WAREHOUSE_CREATE)
public class DistSiteWarehouseCreateConsumer extends AbstractMqListener<WarehouseStorageCreateMsg> {

    @Resource
    private SiteService siteService;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public void process(WarehouseStorageCreateMsg msg) {
        log.info("TMS接收仓库点位处理消息，{}", JSON.toJSONString(msg));
        if(StringUtils.isBlank(msg.getAddress()) || StringUtils.isBlank(msg.getPoiNote()) || msg.getTenantId() == null || StringUtils.isBlank(msg.getWarehouseName()) || msg.getWarehouseNo() ==null){
            log.info("仓库点位参数校验不通过");
            return;
        }
        if(!Objects.equals(TenantWarehouseEnum.SUMMERFARM_WAREHOUSE.getCode(),msg.getTenantId())){
            log.info("非鲜沐仓无需生成仓库点位");
            return;
        }
        RLock redissonLock = redissonClient.getLock(RedisConstants.Site.SITE_CREATE+"-"+msg.getWarehouseNo());

        //锁
        try {
            if (!redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS)) {
                throw new TmsRuntimeException("正在处理，请稍后");
            }

            Map<String, Object> addressMap = GaoDeUtil.getProCityAreaByAddress(msg.getAddress());
            if(addressMap.size() == 0){
                throw new TmsRuntimeException("调用高德api获取仓库点位异常");
            }
            SiteWarehouseCreateUpdateCommand siteWarehouseCreateCommand = new SiteWarehouseCreateUpdateCommand();
            siteWarehouseCreateCommand.setProvince(addressMap.get("province") != null ? String.valueOf(addressMap.get("province")) : null);
            siteWarehouseCreateCommand.setCity(addressMap.get("city") != null ? String.valueOf(addressMap.get("city")) : null);
            siteWarehouseCreateCommand.setArea(addressMap.get("district") != null ? String.valueOf(addressMap.get("district")) : null);
            siteWarehouseCreateCommand.setAddress(msg.getAddress());
            siteWarehouseCreateCommand.setPoi(msg.getPoiNote());
            siteWarehouseCreateCommand.setType(SiteTypeEnum.warehouse.getCode());
            siteWarehouseCreateCommand.setOutBusinessNo(String.valueOf(msg.getWarehouseNo()));
            siteWarehouseCreateCommand.setName(msg.getWarehouseName());
            siteWarehouseCreateCommand.setContactPerson(msg.getPersonContact());
            siteWarehouseCreateCommand.setPhone(msg.getPhone());
            siteWarehouseCreateCommand.setSitePics(msg.getWarehousePic());

            siteService.createWarehouseSite(siteWarehouseCreateCommand);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
    }
}
