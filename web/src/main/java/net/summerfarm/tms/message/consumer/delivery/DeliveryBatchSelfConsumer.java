package net.summerfarm.tms.message.consumer.delivery;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/2/17 10:23<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TMS_PATH, tag = MqConstants.Tag.TAG_DELIVERY_COMPLETE_PATH_SELF, consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DELIVERY_COMPLETE_PATH_SELF,
        consumeThreadMin = 1,
        consumeThreadMax = 1)
public class DeliveryBatchSelfConsumer extends AbstractMqListener<Long> {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Override
    public void process(Long batchId) {
        log.info("手动生成拣货任务批次id为:{}",batchId);
        deliveryBatchService.completePathBySelf(batchId);
    }

    /*@PostConstruct
    public void test(){
        this.process(3352L);
    }*/
}
