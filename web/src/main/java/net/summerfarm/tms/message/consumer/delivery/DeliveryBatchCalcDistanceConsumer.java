package net.summerfarm.tms.message.consumer.delivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.local.delivery.DeliveryBatchDistanceHandleService;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-06-02
 **/
@Slf4j
@MqListener(topic = MqConstants.Topic.TMS_PATH, tag = MqConstants.Tag.TAG_CALC_PATH_DISTANCE,
		consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DELIVERY_CALC_DISTANCE,
		consumeThreadMin = 1,
		consumeThreadMax = 2)
public class DeliveryBatchCalcDistanceConsumer extends AbstractMqListener<CalcTmsPathDistanceMessage> {
	@Autowired
	private DeliveryBatchDistanceHandleService deliveryBatchDistanceHandleService;

	@Override
	public void process(CalcTmsPathDistanceMessage calcTmsPathDistanceMessage) {
		log.info("接受到TMS排线计算公里数逻辑处理:{}", JSON.toJSONString(calcTmsPathDistanceMessage));
		if (Objects.isNull(calcTmsPathDistanceMessage.getBatchId())) {
			log.error("批次id信息为空请检查");
			return;
		}
		if (Objects.isNull(calcTmsPathDistanceMessage.getType())) {
			log.error("计算距离公里数类型为空，请检查参数");
			return;
		}
		deliveryBatchDistanceHandleService.calcPathDistance(calcTmsPathDistanceMessage);
	}
}
