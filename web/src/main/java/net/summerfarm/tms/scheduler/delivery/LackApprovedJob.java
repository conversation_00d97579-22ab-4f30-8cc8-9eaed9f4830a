package net.summerfarm.tms.scheduler.delivery;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.delivery.LackApprovedService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 缺货核准飞书消息
 */
@Slf4j
@Component
public class LackApprovedJob extends XianMuJavaProcessorV2 {

    @Resource
    private LackApprovedService lackApprovedService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        lackApprovedService.sendLackMessageJob();
        return new ProcessResult(true);
    }
}
