package net.summerfarm.tms.scheduler.delivery;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:城配仓出仓晚点监控
 * date: 2023/6/14 14:01
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StoreOutTimeMonitorJob extends XianMuJavaProcessorV2 {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("城配仓出仓监控任务开始");
        deliveryBatchService.batchOutTimeMonitor(null);
        return new ProcessResult(true);
    }
}
