package net.summerfarm.tms.scheduler.alert;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.alert.DeliveryAlertService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:配送提醒监控播报
 * date: 2023/3/22 11:07
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliveryAlertMonitorJob extends XianMuJavaProcessorV2 {

    @Resource
    private DeliveryAlertService deliveryAlertService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("配送提醒监控播报任务开始");
        deliveryAlertService.sendAlert();
        return new ProcessResult(true);
    }
}
