package net.summerfarm.tms.scheduler.delivery;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:存在配送点位无对应配送单处理
 * date: 2023/11/28 17:24
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliverySiteWithNoOrderHandleJob extends XianMuJavaProcessorV2 {

    @Resource
    private DeliverySiteService deliverySiteService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("存在配送点位无对应配送单补偿处理任务开始");
//        deliverySiteService.deliverySiteWithNoOrderHandle();
        return new ProcessResult(true);
    }
}
