package net.summerfarm.tms.scheduler.delivery;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: 相同起点、终点、配送时间配送单不同批次补偿处理
 * date: 2023/4/23 17:01
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliveryOrderWithDiffBatchJob extends XianMuJavaProcessorV2 {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("相同起点、终点、配送时间配送单不同批次补偿处理任务开始");
        deliveryBatchService.diffBatchHandle();
        return new ProcessResult(true);
    }
}
