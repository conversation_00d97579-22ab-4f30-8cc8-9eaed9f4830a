package net.summerfarm.tms.scheduler.delivery;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 调度单入仓时间异常钉钉消息发送job
 *
 * <AUTHOR>
 * @Date 2023-03-24
 **/
@Component
@Slf4j
public class DeliverySiteSignErrorMsgSendJob extends XianMuJavaProcessorV2 {
    @Resource
    private DeliveryBatchService deliveryBatchService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("调度单入场入仓时间异常钉钉消息发送job");
        // 发送满足条件的调度单钉钉消息提示
        deliveryBatchService.sendUnSignBatchDeliverySiteMsg();
        return new ProcessResult(true);
    }
}
