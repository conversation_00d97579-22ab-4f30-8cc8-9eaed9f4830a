package net.summerfarm.tms.controller.delivery.vo.city;

import cn.hutool.core.date.DateUtil;
import jodd.util.StringUtil;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemDTO;
import net.summerfarm.tms.enums.DeliverySiteStatusEnum;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2022/10/27 16:12<br/>
 *
 * <AUTHOR> />
 */
public class CityDeliveryPathVoConverter {

    public static WarehouseLogisticsCenterVO siteDto2Vo(SiteDTO siteDto) {
        WarehouseLogisticsCenterVO warehouseLogisticsCenterVO = new WarehouseLogisticsCenterVO();
        warehouseLogisticsCenterVO.setPoiNote(siteDto.getPoi());
        warehouseLogisticsCenterVO.setStoreNo(Integer.parseInt(siteDto.getOutBusinessNo()));
        warehouseLogisticsCenterVO.setStoreName(siteDto.getName());
        warehouseLogisticsCenterVO.setIntelligencePath(siteDto.getIntelligencePath());

        return warehouseLogisticsCenterVO;
    }

    public static DeliveryCarPathVO deBatchDTO2DePathVo(DeliveryBatchDTO batchDTO, SiteDTO siteDTO) {
        DeliveryCarPathVO deliveryCarPathVO = new DeliveryCarPathVO();

        if(batchDTO == null){
            return deliveryCarPathVO;
        }
        deliveryCarPathVO.setAddtime(batchDTO.getCreatTime());
        deliveryCarPathVO.setCarrierId(batchDTO.getCarrierId());
        deliveryCarPathVO.setDeliveryCarId(batchDTO.getDriverId());
        deliveryCarPathVO.setDeliveryCarVolume(batchDTO.getCarVolume());
        deliveryCarPathVO.setDeliveryTime(batchDTO.getDeliveryTime().toLocalDate());
        deliveryCarPathVO.setId(batchDTO.getDeliveryBatchId());
        deliveryCarPathVO.setNo(Integer.parseInt(batchDTO.getBeginSiteDto().getOutBusinessNo()));
        deliveryCarPathVO.setStoreName(batchDTO.getBeginSiteDto().getName());
        deliveryCarPathVO.setPath(batchDTO.getPathCode());
        deliveryCarPathVO.setPathName(batchDTO.getPathName());
        deliveryCarPathVO.setPathFullLoadRatio(batchDTO.getPathFullLoadRatio());
        deliveryCarPathVO.setPhone(batchDTO.getDriverPhone());
        deliveryCarPathVO.setPlateNumber(batchDTO.getCarNumber());
        deliveryCarPathVO.setRealTotalDistance(batchDTO.getRealTotalDistance());
        deliveryCarPathVO.setStoreNo(Integer.parseInt(batchDTO.getBeginSiteDto().getOutBusinessNo()));
        deliveryCarPathVO.setTmsCarId(batchDTO.getCarId());
        deliveryCarPathVO.setTotalDistance(batchDTO.getPlanTotalDistance());
        deliveryCarPathVO.setDriver(batchDTO.getDriver());
        deliveryCarPathVO.setIntelligenceTotalDistance(batchDTO.getIntelligenceTotalDistance());
        deliveryCarPathVO.setPriceTotal(batchDTO.getPriceTotal());
        deliveryCarPathVO.setPickUpTime(batchDTO.getPickUpTime());

        deliveryCarPathVO.setOutWarehouseTemperature(batchDTO.getSignOutTemperature());
        deliveryCarPathVO.setLoadingPhotos(batchDTO.getSignOutPic());
        deliveryCarPathVO.setVehiclePlatePics(batchDTO.getVehiclePlatePics());
        deliveryCarPathVO.setRefrigeratePics(batchDTO.getRefrigeratePics());
        deliveryCarPathVO.setFreezePics(batchDTO.getFreezePics());
        deliveryCarPathVO.setKeepTemperatureMethodPics(batchDTO.getKeepTemperatureMethodPics());
        deliveryCarPathVO.setOverOutTimeReason(batchDTO.getSignOutRemark());
        deliveryCarPathVO.setWeightTotal(batchDTO.getWeightTotal());
        LocalDateTime signOutTime = batchDTO.getSignOutTime();
        String outTime = siteDTO.getOutTime();

        if(StringUtil.isNotBlank(outTime)){
            LocalDateTime configOutTime = DateUtil.parseLocalDateTime(batchDTO.getDeliveryTime().toLocalDate() + " " + outTime);
            if(signOutTime != null){
                if(signOutTime.isAfter(configOutTime)){
                    deliveryCarPathVO.setIsLate("超时");
                }else{
                    deliveryCarPathVO.setIsLate("未超时");
                }
            }else{
                if(LocalDateTime.now().isAfter(configOutTime)){
                    deliveryCarPathVO.setIsLate("超时");
                }else{
                    deliveryCarPathVO.setIsLate("未超时");
                }
            }
        }else{
            deliveryCarPathVO.setIsLate("未超时");
        }

        return deliveryCarPathVO;
    }

    public static DeliveryPathVO deSiteDTO2DpVo(DeliverySiteDTO deliverySiteDTO) {
        DeliveryPathVO deliveryPathVO = new DeliveryPathVO();

        if(deliverySiteDTO == null){
            return deliveryPathVO;
        }
        deliveryPathVO.setAddress(deliverySiteDTO.getSiteDTO().getCompleteAddress());
        if(Objects.equals(TmsSiteTypeEnum.SAAS.getCode(),deliverySiteDTO.getSiteDTO().getType())){
            deliveryPathVO.setBrandType(1);
        }else{
            deliveryPathVO.setBrandType(0);
        }

        deliveryPathVO.setContact(deliverySiteDTO.getSiteDTO().getName());
        deliveryPathVO.setContactId(deliverySiteDTO.getSiteDTO().getId());
        deliveryPathVO.setDeliveryTime(deliverySiteDTO.getPlanArriveTime() != null ? deliverySiteDTO.getPlanArriveTime().toLocalDate() : null);
        deliveryPathVO.setDeliveryType(deliverySiteDTO.getDeliveryType() != null ? deliverySiteDTO.getDeliveryType().getCode() : null);
        deliveryPathVO.setDistance(deliverySiteDTO.getDistance());
        deliveryPathVO.setId(deliverySiteDTO.getId());
        deliveryPathVO.setInterceptType(deliverySiteDTO.getInterceptState());
        deliveryPathVO.setLatitude(new BigDecimal(deliverySiteDTO.getSiteDTO().getPoi().split(",")[1]));
        deliveryPathVO.setLongitude(new BigDecimal(deliverySiteDTO.getSiteDTO().getPoi().split(",")[0]));
        deliveryPathVO.setMname(deliverySiteDTO.getOuterClientName());
        deliveryPathVO.setPhone(deliverySiteDTO.getSiteDTO().getPhone());
        deliveryPathVO.setPoiNote(deliverySiteDTO.getSiteDTO().getPoi());
        deliveryPathVO.setSort(deliverySiteDTO.getSequence() < 0 ? null : deliverySiteDTO.getSequence());
        deliveryPathVO.setTimeFrame(deliverySiteDTO.getTimeFrame());
        deliveryPathVO.setTotalPrice(deliverySiteDTO.getTotalPrice());
        deliveryPathVO.setTotalVolume(deliverySiteDTO.getTotalVolume());
        deliveryPathVO.setTotalWeight(deliverySiteDTO.getTotalWeight());
        deliveryPathVO.setSendWay(deliverySiteDTO.getSendWay());
        //deliveryPathVO.setPathStatus(deliverySiteDTO.getStatus());
        if(Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.NO.getCode())){
            deliveryPathVO.setPathStatus(0);
        }
        if(Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_PICK.getCode())){
            deliveryPathVO.setPathStatus(1);
        }
        if(Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())){
            deliveryPathVO.setPathStatus(2);
        }
        deliveryPathVO.setFinishDeliveryTime(deliverySiteDTO.getSignInTime());
        List<DeliverySiteItemDTO> deliverySiteItemDTOList = deliverySiteDTO.getDeliverySiteItemDTOList();
        if(!CollectionUtils.isEmpty(deliverySiteItemDTOList)){
            int sum = deliverySiteItemDTOList.stream().mapToInt(DeliverySiteItemDTO::getShortCount).sum();
            deliveryPathVO.setLackFlag(sum > 0);
        }
        deliveryPathVO.setIntelligenceSequence(deliverySiteDTO.getIntelligenceSequence());
        deliveryPathVO.setOuterBrandName(deliverySiteDTO.getOuterBrandName());
        deliveryPathVO.setOrderSourceInfo(deliverySiteDTO.getOrderSourceInfo());

        return deliveryPathVO;
    }

}