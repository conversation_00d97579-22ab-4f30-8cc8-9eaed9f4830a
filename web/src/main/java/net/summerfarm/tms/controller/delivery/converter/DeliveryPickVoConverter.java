package net.summerfarm.tms.controller.delivery.converter;

import net.summerfarm.tms.controller.delivery.vo.trunk.PickUpDetailVO;
import net.summerfarm.tms.delivery.dto.DeliveryPickDTO;
import org.springframework.data.jpa.domain.Specification;

/**
 * Description: 件货装换类<br/>
 * date: 2024/7/25 17:03<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryPickVoConverter {

    public static PickUpDetailVO dto2PickUpVo(DeliveryPickDTO dto) {
        if(dto == null){
            return null;
        }

        PickUpDetailVO vo = new PickUpDetailVO();
        vo.setSku(dto.getOutItemId());
        vo.setPdName(dto.getItemDesc());
        vo.setSkuPic(dto.getSkuPic());
        vo.setSpecification(dto.getSpecification());
        vo.setPickQuantity(dto.getPickQuantity());
        vo.setShortQuantity(dto.getShortQuantity());
        vo.setDeliveryPickId(dto.getId());
        vo.setDeliverySiteId(dto.getDeliverySiteId());
        vo.setBarcodes(dto.getBarcodes());
        vo.setStorageArea(dto.getStorageArea());
        vo.setExtType(dto.getExtType());
        vo.setQuantity(dto.getQuantity());
        vo.setUnit(dto.getUnit());
        vo.setPickStatus(dto.getStatus());
        vo.setDeliverySiteStatus(dto.getDeliverySiteStatus());

        return vo;
    }
}
