package net.summerfarm.tms.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/12/26 16:43<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPickDetailVO implements Serializable {

    /**
     * 大客户名称
     */
    private String adminName;

    /**
     * 大客户id
     */
    private Integer adminId;

    /**
     * 温区,0:未分类,1:冷冻,2:冷藏,3:常温,4:顶汇大流通
     */
    private Integer storageLocation;
    /**
     * 捡获单详情
     */
    private List<DeliveryPickDetail> pickDetails;
    /**
     * 拣货时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickUpTime;
    /**
     * 配送点位ID
     */
    private Long deliverySiteId;
}

@Data
class DeliveryPickDetail implements Serializable{
    /**
     * 拣货单id
     */
    private Long id ;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * sku
     */
    private String sku;

    /**
     * 货品描述
     */
    private String pdName;

    /**
     * 数量
     */
    private Integer skuCnt;

    /**
     * 单位
     */
    private String unit;

    /**
     *1正常/0缺损
     */
    private Integer detailStatus;

    /**
     * 缺损数量
     */
    private Integer shortCnt;

    /**
     * 扫码数量
     */
    private Integer scanCount;

    /**
     * 温区
     */
    private  Integer temperature;

    /**
     * 规格
     */
    private String specification;

    /**
     * 大客户id
     */
    private Integer adminId;

    /**
     * 大客户名称
     */
    private String adminName;

    /**
     * sku类型 0 自营 1 代仓
     */
    private Integer skuType;

    /**
     * sku图
     */
    private String skuPic;

    /**
     * 是否独立捡货 类型 0 独立分拣 1 非
     */
    private Integer skuSorting;

    /**
     * 拦截数量
     */
    private Integer interceptNum;

    /**
     * 所属代仓
     */
    private String nameRemakes;

    /**
     * 加工标识 0非加工商品 1加工商品
     */
    private Integer processFlag;

    /**
     * 加工后商品数量
     */
    private Integer processQuantity;
    /**
     * 加工后重量
     */
    private BigDecimal processWeight;
    /**
     * 加工后单位
     */
    private String processUnit;
    /**
     * 加工转化比(加工前数量/加工后数量)
     */
    private BigDecimal processConversionRatio;


    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 存储条件
     * 0, "未分类"
     * 1, "冷冻"
     * 2, "冷藏"
     * 3, "常温"
     * 4, "顶汇大流通"
     */
    private Integer storageArea;

    /**
     * 条码信息
     */
    private List<String> barcodes;

    /**
     * 外单标识 true外单 false非外单
     */
    private boolean outOrderFlag;

    /**
     * 门店名称
     */
    private String merchantName;

    /**
     * 商品分类，0-普通，1-水果
     */
    private Integer skuCategoryType;
}
