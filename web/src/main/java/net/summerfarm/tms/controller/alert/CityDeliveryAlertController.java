package net.summerfarm.tms.controller.alert;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.tms.alert.CompleteDeliveryService;
import net.summerfarm.tms.alert.DeliveryAlertService;
import net.summerfarm.tms.alert.dto.*;
import net.summerfarm.tms.alert.group.DeliveryAlertRuleValidationGroups;
import net.summerfarm.tms.alert.dto.CompleteDeliveryVO;
import net.summerfarm.tms.anno.CityStoreNo;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.aspect.TmsDataPermission;
import net.summerfarm.tms.query.alert.DeliveryAlertQuery;
import net.summerfarm.tms.query.alert.DeliveryAlertRuleQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Description:
 * date: 2023/3/20 14:40
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tms-new/city-delivery-alert")
public class CityDeliveryAlertController {

    @Resource
    private CompleteDeliveryService completeDeliveryService;
    @Resource
    private DeliveryAlertService deliveryAlertService;

    /**
     * 分页查询配送提醒特殊规则组-城配仓维度
     */
    @PostMapping("/query/rule-group/page")
    @TmsDataPermission
    public TmsResult<PageInfo<DeliveryAlertRuleGroupDTO>> queryAlertRuleGroupPage(@RequestBody DeliveryAlertRuleQuery deliveryAlertRuleQuery) {
        return TmsResult.success(deliveryAlertService.queryAlertRuleGroupPage(deliveryAlertRuleQuery));
    }

    /**
     * 查询配送提醒特殊规则组详情-城配仓维度
     */
    @PostMapping("/query/rule-group/detail")
    @TmsDataPermission
    public TmsResult<List<DeliveryAlertRuleDTO>> queryAlertRuleGroupDetail(@CityStoreNo Integer storeNo) {
        return TmsResult.success(deliveryAlertService.queryAlertRuleGroupDetail(storeNo));
    }

    /**
     * 删除配送提醒特殊规则组-城配仓维度
     */
    @PostMapping("/upsert/rule-group/remove")
    public TmsResult<Void> removeAlertRuleGroup(Integer storeNo) {
        deliveryAlertService.removeAlertRuleGroup(storeNo);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 编辑配送提醒特殊规则组-城配仓维度
     */
    @PostMapping("/upsert/rule-group/edit")
    public TmsResult<Void> editAlertRuleGroup(@RequestBody @Validated DeliveryAlertRuleGroupCommand deliveryAlertRuleGroupCommand) {
        deliveryAlertService.editAlertRuleGroup(deliveryAlertRuleGroupCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 分页查询配送提醒特殊规则-规则维度
     */
    @Deprecated
    @PostMapping("/query/rule/page")
    public TmsResult<PageInfo<DeliveryAlertDTO>> queryAlertRulePage(@RequestBody DeliveryAlertQuery deliveryAlertQuery) {
        return TmsResult.success(deliveryAlertService.queryAlertRulePage(deliveryAlertQuery));
    }

    /**
     * 新增配送提醒特殊规则-规则维度
     */
    @PostMapping("/upsert/rule/add")
    @TmsDataPermission
    public TmsResult<Void> addAlertRule(@RequestBody @Validated(DeliveryAlertRuleValidationGroups.AddGroup.class) DeliveryAlertRuleCommand deliveryAlertRuleCommand) {
        deliveryAlertService.addAlertRule(deliveryAlertRuleCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 编辑配送提醒特殊规则-规则维度
     */
    @PostMapping("/upsert/rule/edit")
    public TmsResult<Void> editAlertRule(@RequestBody @Validated(DeliveryAlertRuleValidationGroups.EditGroup.class) DeliveryAlertRuleCommand deliveryAlertRuleCommand) {
        deliveryAlertService.editAlertRule(deliveryAlertRuleCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 删除配送提醒特殊规则-规则维度
     */
    @PostMapping("/upsert/rule/del")
    public TmsResult<Void> delAlertRule(Long ruleId) {
        deliveryAlertService.delAlertRule(ruleId);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 发送配送提醒
     */
    @PostMapping(value = "/query/send")
    public TmsResult<Void> sendAlert(){
        deliveryAlertService.sendAlert();
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * Excel导出未完成配送数据
     */
    @GetMapping("/export/excel")
    public void excel(String postDate,String postHour,String areaComboStr, HttpServletResponse response) {
        deliveryAlertService.excel(postDate, postHour, areaComboStr, response);
    }

    /**
     * 完成配送提醒-新增消息提醒接口
     * @param completeDeliveryCommand
     * @return
     */
    @ApiOperation(value = "完成配送提醒-新增消息提醒接口", httpMethod = "POST", tags = "完成配送提醒")
//    @RequiresPermissions(value = {"messageReminder:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/add", method = RequestMethod.POST)
    @TmsDataPermission
    public TmsResult<Void> allAlert(@RequestBody CompleteDeliveryCommand completeDeliveryCommand){
        completeDeliveryService.allAlert(completeDeliveryCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 完成配送提醒-分页查询接口
     * @param pageIndex
     * @param pageSize
     * @param completeDeliveryQuery
     * @return
     */
    @ApiOperation(value = "完成配送提醒-分页查询接口", httpMethod = "GET", tags = "完成配送提醒")
//    @RequiresPermissions(value = {"messageReminder:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/page/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    @TmsDataPermission
    public TmsResult<PageInfo<CompleteDeliveryVO>> queryAlertPage(@PathVariable Integer pageIndex, @PathVariable Integer pageSize, CompleteDeliveryQuery completeDeliveryQuery){
        return TmsResult.success(completeDeliveryService.queryAlertPage(pageIndex,pageSize,completeDeliveryQuery));
    }

    /**
     * 完成配送提醒-编辑消息提醒接口
     * @param completeDeliveryCommand
     * @return
     */
    @ApiOperation(value = "完成配送提醒-编辑消息提醒接口", httpMethod = "POST", tags = "完成配送提醒")
//    @RequiresPermissions(value = {"messageReminder:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/edit", method = RequestMethod.POST)
    public TmsResult<Void> editAlert(@RequestBody CompleteDeliveryCommand completeDeliveryCommand){
        completeDeliveryService.editAlert(completeDeliveryCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 完成配送提醒-删除消息提醒接口
     * @param id
     * @return
     */
    @ApiOperation(value = "完成配送提醒-删除消息提醒接口", httpMethod = "GET", tags = "完成配送提醒")
    @RequestMapping(value = "/upsert/del", method = RequestMethod.GET)
    public TmsResult<Void> delAlert(Integer id){
        completeDeliveryService.delAlert(id);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 完成配送提醒-改变消息提醒状态接口
     * @param id
     * @param status
     * @return
     */
    @ApiOperation(value = "完成配送提醒-改变消息提醒状态接口", httpMethod = "GET", tags = "完成配送提醒")
//    @RequiresPermissions(value = {"messageReminder:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/change-status", method = RequestMethod.GET)
    public TmsResult<Void> changeStatus(Integer id, Integer status){
        completeDeliveryService.changeStatus(id,status);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 完成配送提醒-根据城配仓查询行政区域
     * @param id
     * @param storeNo
     * @param address
     * @param city
     * @return
     */
    @ApiOperation(value = "完成配送提醒-根据城配仓查询行政区域", httpMethod = "GET", tags = "完成配送提醒")
//    @RequiresPermissions(value = {"messageReminder:getCityByStoreNo", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/areas", method = RequestMethod.GET)
    public TmsResult getCityByStoreNo(Integer id,Integer storeNo,String address,String city){
        return completeDeliveryService.getCityByStoreNo(id,storeNo,address,city);
    }

    /**
     * 完成配送提醒-查询省市区
     * @param name
     * @param storeNo
     * @return
     */
    @ApiOperation(value = "完成配送提醒-查询省市区", httpMethod = "GET", tags = "完成配送提醒")
//    @RequiresPermissions(value = {"messageReminder:getCity", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/province-city-area", method = RequestMethod.GET)
    public TmsResult getCity(String name,Integer storeNo){
        return completeDeliveryService.getCity(name,storeNo);
    }
}
