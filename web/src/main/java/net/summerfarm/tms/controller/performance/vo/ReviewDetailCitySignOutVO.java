package net.summerfarm.tms.controller.performance.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/6/27 14:29<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ReviewDetailCitySignOutVO {

    /**
     * 履约审核详情id
     */
    private Long performanceReviewDetailId;
    /**
     * 履约审核任务ID
     */
    private Long performanceReviewTaskId;
    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 开始点位名称（城配仓名称）
     */
    private String beginName;

    /**
     * 配送路线
     */
    private String pathCode;

    /**
     * 路线名称
     */
    private String pathName;

    /**
     * 出仓温度
     */
    private BigDecimal signOutTemperature;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机号码
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车型
     */
    private String carType;

    /**
     * 存储条件
     */
    private String storageDesc;

    /**
     * 装载照片
     */
    private String loadPics;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;


    /**
     * 状态 0待审核 1无法审核 2合规 3不合规
     */
    private Integer state;

    /**
     * 判罚金额
     */
    private BigDecimal penaltyMoney;

    /**
     *点位照片原因
     *
     * 城配出仓 装载照片
     * 1000 保温措施不合格
     * 1001 无装车照片
     * 1002 无保温被
     * 1003 无冰板
     * 1004 无保温箱、干冰
     * 1005 冷藏数量不符
     * 1006 冷冻数量不符
     * 1007 干冰数量不符
     * 1008 冰板未完全复冻
     * 1009 蛋糕摆放不规范
     * 1010 冰板材料不合格
     * 1011 装车照片拍照不清晰
     * 1012 保温被未盖住货物
     * 1013 冰板数量不充足
     *
     * 城配出仓 车牌照片
     * 1200 车牌不符
     * 1201 无车牌照
     *
     * 城配出仓 冷冻照片
     * 1300 冷冻货物未按要求贴合干冰
     * 1301 蛋糕摆放不规范
     * 1302 冷冻数量不符
     * 1303 干冰数量不符
     * 1304 无保温箱、干冰
     * 1305 未上传冷冻货物保温照片
     *
     *
     * 城配出仓 冷藏照片
     * 1400 冷藏货物贴合冰板面不足
     * 1401 冰板未完全复冻
     * 1402 冰板材料不合格
     * 1403 装车照片拍照不清晰
     * 1404 冰板数量不充足
     * 1405 冷藏数量不符
     * 1406 无冰板
     * 1407 未上传冷藏货物保温照片
     *
     * 城配-签收 门店抬头
     * 2000 门头不符
     * 2001 无门头照
     * 2002 门头不清晰
     *
     * 城配-签收 门店抬头
     * 2201 签收单不合格
     * 2202 无签收单照片
     * 城配-签收 货物照片
     *
     * 2401 未做保温措施
     * 2402 未测温
     * 2403 无货物图片
     * 2404 货物图片不符
     *
     * 干线-签收 到仓-签收照片
     * 3000 无车辆温度照片
     * 3001 其他
     *
     * 干线-签收 出仓-装载照片
     * 3200 无整体货物照片
     *
     * 干线-签收 出仓-封签照片
     * 3400 无封签照片
     */
    private List<String> sitePicReasons;

    /**
     * 申诉状态 :0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
     */
    private Integer appealStatus;

    /**
     * 非水果冷藏数量
     */
    private Integer nonFruitColdNum;

    /**
     * 非水果冷藏整箱数量
     */
    private Integer nonFruitColdBoxNum;
    /**
     * 非水果冷藏散件数量
     */
    private Integer nonFruitColdPartNum;

    /**
     * 非水果冷冻数量
     */
    private Integer nonFruitFreezeNum;

    /**
     * 非水果冷冻整箱数量
     */
    private Integer nonFruitFreezeBoxNum;

    /**
     * 非水果冷冻散件数量
     */
    private Integer nonFruitFreezePartNum;


    /**
     * ai货检结果
     */
    private ReviewDetailAIResultVO reviewDetailAIResultVO;

    /**
     * 配送点id
     */
    private Long deliverySiteId;

    /**
     * 冷藏照片
     */
    private String refrigeratePics;

    /**
     * 冷冻照片
     */
    private String freezePics;

}
