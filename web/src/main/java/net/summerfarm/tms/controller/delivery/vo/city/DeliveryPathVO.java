package net.summerfarm.tms.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/18 11:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPathVO {
    private String mname;

    private String address;

    private String contact;

    private String phone;

    private BigDecimal distance;

    private String poiNote;

    private BigDecimal longitude;

    private BigDecimal latitude;

    private String driver;
    /**
     * 对应的截单是否有缺货情况
     */
    private Integer isStock;

    /**
     * 返回数量
     */
    private Integer returnCnt;
    /**
     * 返回原因
     */
    private String returnRemark;

    /**
     * 行政区/县
     */
    private List<String> districts;

    private String orderNo;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;

    /**
     *  承运商id
     * @return
     */
    private Long carrierId;


    private Long mId;

    /**
     * 完成序号
     */
    private Integer finishNo;

    private Long id;

    private Integer storeNo;

    private LocalDate deliveryTime;

    private Long contactId;

    private String timeFrame;

    private String remark;

    private BigDecimal totalVolume;
    /**
     * 总重量
     */
    private BigDecimal totalWeight;

    private String path;

    private Integer sort;

    private LocalDateTime addtime;

    private BigDecimal totalPrice;

    /**
     *  出样标签
     */
    private Integer type;

    /**
     * 任务状态 0 待拣货 1.配送中 2完成配送
     */
    private Integer pathStatus;

    private String finishPoi;

    private String finishPoiName;

    private String deliveryPic;

    private Integer finishDistance;

    @ApiModelProperty("是否是正常签收 0 正常 1 不正常")
    private Integer signForStatus;

    @ApiModelProperty("签收备注")
    private String signForRemarks;

    @ApiModelProperty("完成配送时间")
    private LocalDateTime finishTime;

    @ApiModelProperty("配送类型 配送 回收 配送/回收")
    private  Integer deliveryType;

    @ApiModelProperty("拦截类型 0正常 1部分拦截 2全部拦截")
    private  Integer interceptType;

    @ApiModelProperty("品牌类型 0内部，1外部")
    private  Integer brandType;

    @ApiModelProperty("是否超出距离 0 正常 1超出")
    private Integer outDistance;

    @ApiModelProperty("超出距离备注")
    private String outRemark;

    /**
     * 配送方式 0正常配送 1专车配送
     */
    private Integer sendWay;

    /**
     * 完成配送时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishDeliveryTime;

    /**
     * 是否缺货 true缺货 false不缺
     */
    private boolean lackFlag;
    /**
     * 智能排线顺序
     */
    private Integer intelligenceSequence;

    /**
     * 品牌名
     */
    private String outerBrandName;

    /**
     * 订单来源
     */
    private String orderSourceInfo;
}
