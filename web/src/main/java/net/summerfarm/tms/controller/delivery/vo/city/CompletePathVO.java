package net.summerfarm.tms.controller.delivery.vo.city;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/21 10:14<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CompletePathVO {

    @NotEmpty(message = "deliveryCarPath不能为空")
    private List<DeliveryCarPathVO> deliveryCarPath;

    @NotNull(message = "storeNo不能为空")
    private Integer storeNo;

    @NotNull(message = "startDate不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @NotNull(message = "endDate不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;


}
