package net.summerfarm.tms.controller.delivery.vo.trunk;

import lombok.Data;

import java.util.List;

/**
 * Description: 提货详情<br/>
 * date: 2024/7/23 17:06<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PickUpDetailVO {

    /**
     * sku
     */
    private String sku;

    /**
     * 货品描述
     */
    private String pdName;

    /**
     * sku图片
     */
    private String skuPic;

    /**
     * 规格
     */
    private String specification;

    /**
     * 应提货数量
     */
    private Integer quantity;

    /**
     * 实际提货数量
     */
    private Integer pickQuantity;

    /**
     * 缺货数量
     */
    private Integer shortQuantity;

    /**
     * 拣货ID
     */
    private Long deliveryPickId;

    /**
     * 配送点位信息
     */
    private Long deliverySiteId;

    /**
     * 条码信息
     */
    private List<String> barcodes;
    /**
     * 存储条件
     * 0, "未分类"
     * 1, "冷冻"
     * 2, "冷藏"
     * 3, "常温"
     * 4, "顶汇大流通"
     */
    private Integer storageArea;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 单位
     */
    private String unit;

    /**
     * 状态 20 待捡货 25 捡货完成 26 捡货异常
     */
    private Integer pickStatus;

    /**
     * 10, "未到站"
     * 20, "已到站"
     * 22, "已配送"
     * 25, "已拣货"
     * 30, "已出发"
     */
    private Integer deliverySiteStatus;

}
