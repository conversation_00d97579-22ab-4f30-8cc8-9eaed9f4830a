package net.summerfarm.tms.controller.delivery.vo.city;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: <br/>
 * date: 2022/12/26 17:22<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteVO implements Serializable {

    /**
     * 地址
     */
    private String address;

    /**
     * 配送点位id
     */
    private Long siteId;

    /**
     * 配送类型
     */
    private Integer deliveryType;

    /**
     * 配送id
     */
    private Long deliverySiteId;

    /**
     * 拦截类型 0正常 1部分拦截 2全部拦截
     */
    private Integer interceptType;

    /**
     * 店铺id
     */
    private String mId;

    /**
     * 店铺名称
     */
    private String mName;

    /**
     * 路线名称
     */
    private String path;

    /**
     * 任务状态 0 待捡货、1 捡货完成(配送中)、2完成配送
     */
    private Integer pathStatus;

    /**
     * 手机号
     */
    private String phone;

    /**
     * poi
     */
    private String poiNote;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 精准送
     */
    private String timeFrame;
    /**
     * 配送方式 0正常配送 1专车配送
     */
    private Integer sendWay;
    /**
     * 配送备注
     */
    private String sendRemark;

    /**
     * 到店打卡标识 true 需要打卡 false不需要打卡
     */
    private Boolean checkinPunchFlag;

    /**
     * 品牌名
     */
    private String outerBrandName;

    /**
     * 拣货缺货标识 true 缺货 false 不缺货
     */
    private Boolean pickLackFlag;

    /**
     * 订单来源
     */
    private String orderSourceInfo;
}
