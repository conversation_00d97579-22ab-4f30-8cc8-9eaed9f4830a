package net.summerfarm.tms.controller.delivery.vo.city;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/13 14:55<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
public class DeliveryFenceVO implements Serializable {

    /**
     * 可支持配送的日期
     */
    private List<LocalDate> cloudSendDateList;
}
