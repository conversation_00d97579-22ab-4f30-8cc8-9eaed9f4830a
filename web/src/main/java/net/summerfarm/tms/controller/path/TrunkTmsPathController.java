package net.summerfarm.tms.controller.path;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.car.CarService;
import net.summerfarm.tms.base.car.dto.CarDTO;
import net.summerfarm.tms.base.driver.DriverService;
import net.summerfarm.tms.base.driver.dto.DriverDTO;
import net.summerfarm.tms.enums.TmsPathStatusEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.HandleException;
import net.summerfarm.tms.mapper.TmsPathMapper;
import net.summerfarm.tms.path.TmsPathService;
import net.summerfarm.tms.path.dto.*;
import net.summerfarm.tms.query.base.driver.DriverQuery;
import net.summerfarm.tms.query.path.PathQuery;
import net.summerfarm.tms.query.path.QueryPathFilterDTO;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 干线 线路 接口
 */
@RestController
@RequestMapping("/tms-new/trunk-path")
@HandleException
public class TrunkTmsPathController {
    @Resource
    TmsPathService tmsPathService;
    @Resource
    TmsPathMapper tmsPathMapper;
    @Resource
    DriverService driverService;
    @Resource
    CarService carService;

    /**
     * 线路分页查询
     */
    @PostMapping("/query/list")
    public TmsResult<PageInfo<TmsPathDTO>> list(@RequestBody PathQuery pathQuery) {
        return tmsPathService.queryList(pathQuery);
    }

    /**
     * 线路详情
     */
    @PostMapping("/query/path-detail")
    public TmsResult<TmsPathDTO> pathDetail(@RequestBody Long pathId) {
        return tmsPathService.queryDetail(pathId);
    }

    /**
     * 关闭线路
     */
    @PostMapping("/upsert/close")
    public TmsResult<Void> pathClose(@RequestBody Long pathId) {
        return tmsPathService.closePath(pathId);
    }

    /**
     * 删除线路
     */
    @PostMapping("/upsert/delete")
    public TmsResult<Void> pathDelete(@RequestBody Long pathId) {
        return tmsPathService.deletePath(pathId);
    }

    /**
     * 删除线路
     */
    @PostMapping("/upsert/batch-delete")
    public TmsResult<Void> pathBatchDelete(@RequestBody List<Long> pathIdList) {
        for (Long pathId : pathIdList) {
            tmsPathService.deletePath(pathId);
        }
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 保存/更新线路
     */
    @PostMapping("/upsert/save")
    public TmsResult<Void> pathSave(@RequestBody TmsPathDTO tmsPathDTO) {
        return tmsPathService.temporarySave(tmsPathDTO);
    }

    /**
     * 点位map刷新
     */
    @PostMapping("/import/site-map")
    public TmsResult<Void> importSites(@RequestBody String data) throws IOException {
        Map<String, Long> siteMap = new HashMap<>();
        BufferedReader br = new BufferedReader(new StringReader(data));
        String line;
        while ((line = br.readLine()) != null) {
            String[] items = line.split(",");
            siteMap.put(items[1], Long.valueOf(items[0]));
        }
        PathConvertor.siteMap = siteMap;
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 承运商Map刷新
     */
    @PostMapping("/import/carrier-map")
    public TmsResult<Void> importCarriers(@RequestBody String data) throws IOException {
        Map<String, Long> carrierMap = new HashMap<>();
        BufferedReader br = new BufferedReader(new StringReader(data));
        String line;
        while ((line = br.readLine()) != null) {
            String[] items = line.split(",");
            carrierMap.put(items[1], Long.valueOf(items[0]));
        }
        PathConvertor.carrierMap = carrierMap;
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 导入线路
     */
    @PostMapping("/import/path")
    public TmsResult<String> importPaths(@RequestBody String data) {
        try {
            List<TmsPathDTO> tmsPathDTOS = new ArrayList<>();
            BufferedReader br = new BufferedReader(new StringReader(data));

            String line;
            while ((line = br.readLine()) != null) {
                String[] items = line.split("\t");
                TmsPathDTO tmsPathDTO = PathConvertor.buildTmsPath(items);
                //获取driverId
                if (StringUtils.isNotBlank(items[5])) {
                    DriverQuery driverQuery = new DriverQuery();
                    driverQuery.setPhone(items[5]);
                    TmsResult<PageInfo<DriverDTO>> tmsResult = driverService.queryPage(driverQuery);
                    if (tmsResult.getData().getList().size() != 1) {
                        return TmsResult.fail(ErrorCodeEnum.FAILED.code, items[5] + "存在多个或0个");
                    }
                    tmsPathDTO.setDriverId(tmsResult.getData().getList().get(0).getId());
                }
                //获取carId
                if (StringUtils.isNotBlank(items[6])) {
                    TmsResult<List<CarDTO>> carResult = carService.getCarByCarNumber(items[6]);
                    if (carResult.getData().size() != 1) {
                        return TmsResult.fail(ErrorCodeEnum.FAILED.code, items[6] + "存在多个或0个");
                    }
                    tmsPathDTO.setCarId(carResult.getData().get(0).getId());
                }
                tmsPathDTOS.add(tmsPathDTO);
            }

            StringBuilder infoBuilder = new StringBuilder();
            for (int i = 0; i < tmsPathDTOS.size(); i++) {
                TmsResult<Void> result = tmsPathService.temporarySave(tmsPathDTOS.get(i));
                infoBuilder.append(i)
                        .append("执行结果:")
                        .append(result.isSuccess() ? "成功" : result.getErrorMessage())
                        .append("\n");
            }
            return TmsResult.success(infoBuilder.toString());
        } catch (IOException | URISyntaxException e) {
            return TmsResult.fail(ErrorCodeEnum.FAILED.code, e.getMessage());
        }
    }

    /**
     * 导入线路
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/new/import/path")
    public TmsResult<String> newImportPaths(@RequestBody String data) {
        List<String> carriers = tmsPathMapper.selectCarriers();
        List<String> distSites = tmsPathMapper.selectDistSites();
        PathConvertor.carrierMap = carriers.stream().filter(StrUtil::isNotBlank).collect(Collectors.toMap(e -> e.split(",")[1], e -> Long.valueOf(e.split(",")[0]), (oldData, newData) -> newData));
        PathConvertor.siteMap = distSites.stream().filter(StrUtil::isNotBlank).collect(Collectors.toMap(e -> e.split(",")[1], e -> Long.valueOf(e.split(",")[0]), (oldData, newData) -> newData));
        List<Long> pathIdList = tmsPathMapper.selectPathIds();
        this.pathBatchDelete(pathIdList);
        return this.importPaths(data);
    }


    /**
     * 创建路由
     *
     * @param createPathDTO 创建路由请求参数
     * @return
     */
    @PostMapping("/upsert/new/save")
    public TmsResult<Void> createPath(@RequestBody @Valid CreatePathCommand createPathDTO) {
        return tmsPathService.save(createPathDTO);
    }

    /**
     * 编辑路由
     *
     * @param updatePathDTO
     * @return
     */
    @PostMapping("/upsert/new/update")
    @RequiresPermissions(value = {"trunk:trunk-path-update"})
    public TmsResult<Void> updatePath(@RequestBody @Valid UpdatePathCommand updatePathDTO) {
        return tmsPathService.update(updatePathDTO);
    }


    /**
     * 查看路由详情
     *
     * @param pathId
     * @return
     */
    @PostMapping("/query/new/detail")
    public TmsResult<PathDTO> queryDetail(@RequestParam Long pathId) {
        return TmsResult.success(tmsPathService.queryNewDetail(pathId));
    }


    /**
     * 查询路由列表
     *
     * @param filterDTO
     * @return
     */
    @PostMapping("/query/path/list")
    public TmsResult<PageInfo<PathListDTO>> queryPathList(@RequestBody QueryPathFilterDTO filterDTO) {
        return TmsResult.success(tmsPathService.queryNewList(filterDTO));
    }
//    
//    /**
//     * 删除路由
//     *
//     * @param pathId
//     * @return
//     */
//    @PostMapping("/upsert/new/delete")
//    public TmsResult<Void> newDelete(@RequestParam Long pathId) {
//        return TmsResult.success(null);
//    }

    /**
     * 启用路由
     *
     * @param pathId
     * @return
     */
    @PostMapping("/upsert/new/enable")
    public TmsResult<Void> enable(@RequestParam Long pathId) {
        tmsPathService.updateStatus(pathId, TmsPathStatusEnum.ACTIVE);
        return TmsResult.VOID_SUCCESS;
    }


    /**
     * 禁用路由
     *
     * @param pathId
     * @return
     */
    @PostMapping("/upsert/new/disable")
    public TmsResult<Void> disable(@RequestParam Long pathId) {
        tmsPathService.updateStatus(pathId, TmsPathStatusEnum.DISABLE);
        return TmsResult.VOID_SUCCESS;
    }


}
