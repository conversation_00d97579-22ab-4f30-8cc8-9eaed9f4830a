package net.summerfarm.tms.controller.performance.vo;

import lombok.Data;

/**
 * Description: <br/>
 * date: 2023/6/26 18:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PerformanceReviewTaskDetailVO extends PerformanceReviewTaskVO {

    /**
     * 审核详情总数
     */
    private long auditDetailTotalNum;

    /**
     * 审核合规总数
     */
    private long auditComplianceTotalNum;

    /**
     * 审核不合规总数
     */
    private long auditNoComplianceTotalNum;

    /**
     * 待审核总数
     */
    private long waitAuditTotalNum;

    /**
     * 无法审核总数
     */
    private long cannotAuditTotalNum;
}
