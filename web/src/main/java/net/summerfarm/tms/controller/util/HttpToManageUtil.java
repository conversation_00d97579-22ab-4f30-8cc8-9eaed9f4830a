package net.summerfarm.tms.controller.util;

import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.util.HttpUtil;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.controller.AjaxResultConverter;
import net.summerfarm.tms.util.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2022/10/26 10:24<br/>
 *
 * <AUTHOR> />
 */
@Component
public class HttpToManageUtil {

    @Value("${manageAddress}")
    private String manageAddress;

    public <T> TmsResult<T> postJsonToManage(String url, Object param) {
        String result = cn.hutool.http.HttpUtil.createPost(manageAddress + url)
                .timeout(30000)
                .header("token", String.valueOf(ThreadLocalUtil.getThreadLocal().get("token")))
                .body(JSON.toJSONString(param))
                .execute()
                .body();
        return AjaxResultConverter.ajax2TmsResult(result);
    }

    public <T> TmsResult<T> postFormToManage(String url, Object param) {
        String result = cn.hutool.http.HttpUtil.createPost(manageAddress + url)
                .timeout(30000)
                .header("token", String.valueOf(ThreadLocalUtil.getThreadLocal().get("token")))
                .form(JSON.parseObject(JSON.toJSONString(param)))
                .execute()
                .body();
        return AjaxResultConverter.ajax2TmsResult(result);
    }

    public <T> TmsResult<T> getToManage(String url) {
        String result = cn.hutool.http.HttpUtil.createGet(manageAddress + url)
                .timeout(30000)
                .header("token", String.valueOf(ThreadLocalUtil.getThreadLocal().get("token")))
                .execute()
                .body();

        return AjaxResultConverter.ajax2TmsResult(result);
    }

    public void getVoidToManage(String url, HttpServletResponse response) throws IOException {
        HttpResponse httpResponse = cn.hutool.http.HttpUtil.createGet(manageAddress + url)
                .timeout(30000)
                .header("token", String.valueOf(ThreadLocalUtil.getThreadLocal().get("token")))
                .execute();

        // 设置状态码
        response.setStatus(httpResponse.getStatus());

        // 设置响应头
        Map<String, List<String>> headers = httpResponse.headers();
        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
            String name = entry.getKey();
            List<String> values = entry.getValue();
            for (String value : values) {
                response.addHeader(name, value);
            }
        }

        // 设置响应体内容
        byte[] bodyBytes = httpResponse.bodyBytes();
        response.getOutputStream().write(bodyBytes);

    }

    public JSONObject getJsonToManage(String url) {
        String result = cn.hutool.http.HttpUtil.createGet(manageAddress + url)
                .timeout(1500)
                .header("token", String.valueOf(ThreadLocalUtil.getThreadLocal().get("token")))
                .execute()
                .body();
        return JSONObject.parseObject(result);
    }

    public <T> TmsResult<T> deleteToManage(String url) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("token", String.valueOf(ThreadLocalUtil.getThreadLocal().get("token")));
        String result = HttpUtil.
                sendHttpWithHeader(manageAddress + url, RequestMethod.DELETE, null, headerMap);
        return AjaxResultConverter.ajax2TmsResult(result);
    }

    public TmsResult<Void> putJsonToManage(String url, Object param) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("token", String.valueOf(ThreadLocalUtil.getThreadLocal().get("token")));

        String result = HttpUtil.sendHttp(manageAddress + url, RequestMethod.PUT, JSON.toJSONString(param), "application/json", headerMap);
        return AjaxResultConverter.ajax2TmsResult(result);
    }
}
