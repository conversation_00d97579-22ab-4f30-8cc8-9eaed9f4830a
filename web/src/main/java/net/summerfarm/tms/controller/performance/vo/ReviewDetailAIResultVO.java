package net.summerfarm.tms.controller.performance.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReviewDetailAIResultVO implements Serializable {

    /**
     * AI是否全部通过, 1通过，0不通过,2-AI审核中
     */
    private Integer aiAllPass;

    /**
     * AI门店图片是否通过, 1通过，0不通过
     */
    private Integer aiDeliveryPicPass;

    /**
     * AI门店图片是否通过结果
     */
    private String aiDeliveryPicPassResults;

    /**
     * AI签收图片是否通过, 1通过，0不通过
     */
    private Integer aiSignPicPass;

    /**
     * AI签收图片是否通过结果
     */
    private String aiSignPicPassResults;

    /**
     * AI货品图片通过, 1通过，0不通过
     */
    private Integer aiProductPicPass;

    /**
     * AI货品图片通过结果
     */
    private String aiProductPicPassResults;

    /**
     * AI车辆车牌照图片通过, 1通过，0不通过
     */
    private Integer aiVehiclePlatePicPass;

    /**
     * AI车辆车牌照图片通过结果
     */
    private String aiVehiclePlatePicPassResults;

    /**
     * AI车辆装载图片通过, 1通过，0不通过
     */
    private Integer aiVehicleLoadPicPass;

    /**
     * AI车辆装载图片通过结果
     */
    private String aiVehicleLoadPicPassResults;
}
