package net.summerfarm.tms.controller.dist.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.base.BaseObject;
import net.summerfarm.tms.inbound.controller.delivery.vo.trunk.TrunkDeliveryBatchVO;
import net.summerfarm.tms.dist.dto.DistOrderItemDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * 承运单
 */
@Data
public class TrunkDistOrderVO extends BaseObject {

    /**
     * 承运单号
     */
    private Long distId;
    /**
     * 关联单据号
     */
    private String outerOrderId;
    /**
     * 业务类型,100:调拨,101:采购,102:销售出库,103:出样出库,104:补货出库,105:自提销售
     */
    private Integer source;
    /**
     * 业务类型描述
     */
    private String sourceDesc;
    /**
     * 履约时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectBeginTime;
    /**
     * 起点Id
     */
    private Long beginSiteId;
    /**
     * 起点名称
     */
    private String beginSiteName;
    /**
     * 发货人姓名(起点联系人)
     */
    private String beginSiteContactName;
    /**
     * 起点联系方式
     */
    private String beginSitePhone;
    /**
     * 起点详细地址
     */
    private String beginSiteFullAddress;
    /**
     * 中转站Id
     */
    private Long midSiteId;
    /**
     * 中转站名称
     */
    private String midSiteName;
    /**
     * 终点Id
     */
    private Long endSiteId;
    /**
     * 终点名称
     */
    private String endSiteName;
    /**
     * 收货人姓名(终点联系人)
     */
    private String endSiteContactName;
    /**
     * 终点联系方式
     */
    private String endSitePhone;
    /**
     * 终点详细地址
     */
    private String endSiteFullAddress;
    /**
     * 状态,10:待承运,18:已关闭,19:已取消,30:承运中,40:承运完成
     */
    private Integer status;
    /**
     * 状态描述
     */
    private String statusDesc;
    /**
     * 关闭原因
     */
    private String closeReason;
    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String creator;

    /**
     * 冷冻重量
     */
    private BigDecimal freezeWeight;

    /**
     * 冷冻体积
     */
    private BigDecimal freezeVolume;

    /**
     * 冷冻件数
     */
    private Integer freezeQuantity;

    /**
     * 冷藏重量
     */
    private BigDecimal coldWeight;

    /**
     * 冷藏体积
     */
    private BigDecimal coldVolume;

    /**
     * 冷藏件数
     */
    private Integer coldQuantity;

    /**
     * 常温重量
     */
    private BigDecimal normalWeight;

    /**
     * 常温体积
     */
    private BigDecimal normalVolume;

    /**
     * 常温件数
     */
    private Integer normalQuantity;

    /**
     * 合计重量
     */
    private BigDecimal totalWeight;

    /**
     * 合计体积
     */
    private BigDecimal totalVolume;

    /**
     * 合计件数
     */
    private Integer totalQuantity;

    /**
     * 商品列表
     */
    private List<DistOrderItemDTO> skuList;

    /**
     * 调度单列表
     */
    private List<TrunkDeliveryBatchVO> deliveryBatchList;

    /**
     * 备注
     */
    private String sendRemark;

    /**
     * 外部品牌名-客户简称
     */
    private String outBrandName;

    /**
     * 门店名称
     */
    private String storeName;

}
