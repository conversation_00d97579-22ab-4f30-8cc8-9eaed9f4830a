package net.summerfarm.tms.controller.delivery.vo.city;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliveryPickDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.enums.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/1/4 11:23<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryBatchVoConverter {

    public static PageInfo<DeliveryPickListVO> dtoPage2Vo(TmsResult<PageInfo<DeliveryBatchDTO>> deliveryDTOPage) {
        PageInfo<DeliveryBatchDTO> deliveryDTOPageInfo = deliveryDTOPage.getData();
        List<DeliveryBatchDTO> list = deliveryDTOPageInfo.getList();

        List<DeliveryPickListVO> deliveryPickListVOS = new ArrayList<>();

        for (DeliveryBatchDTO deliveryBatchDTO : list) {
            DeliveryPickListVO deliveryPickListVO = new DeliveryPickListVO();

            deliveryPickListVO.setConcatCnt(deliveryBatchDTO.getDeliverySiteDTOList() == null ? 0 : deliveryBatchDTO.getDeliverySiteDTOList().size());
            deliveryPickListVO.setDeliveryTime(deliveryBatchDTO.getDeliveryTime().toLocalDate());
            deliveryPickListVO.setBatchId(deliveryBatchDTO.getDeliveryBatchId());
            deliveryPickListVO.setPath(deliveryBatchDTO.getPathCode());
            deliveryPickListVO.setSkuCnt(deliveryBatchDTO.getSkuCnt());
            deliveryPickListVO.setTaskStatus(deliveryBatchDTO.getStatus());
            deliveryPickListVO.setNeedScanCodeFlag(deliveryBatchDTO.getNeedScanCodeFlag());

            List<DeliverySiteDTO> deliverySiteDTOList = deliveryBatchDTO.getDeliverySiteDTOList();
            if(!CollectionUtils.isEmpty(deliverySiteDTOList)){
                List<DeliverySiteDTO> deliverySiteDTOS = deliverySiteDTOList.stream().filter(site -> Objects.equals(site.getInterceptState(), 2)).collect(Collectors.toList());
                if(deliverySiteDTOList.size() == deliverySiteDTOS.size()){
                    Set<DistTypeEnum> typeSet = deliverySiteDTOList.stream().map(DeliverySiteDTO::getDeliveryType).filter(Objects::nonNull).collect(Collectors.toSet());
                    if(typeSet.size() > 1){
                        deliveryPickListVO.setInterceptFlag(1);
                    }else{
                        for (DistTypeEnum distTypeEnum : typeSet) {
                            if(distTypeEnum.getCode() == DistTypeEnum.DELIVERY.getCode()){
                                deliveryPickListVO.setInterceptFlag(0);
                                break;
                            }
                            if(distTypeEnum.getCode() == DistTypeEnum.RECYCLE.getCode() || distTypeEnum.getCode() == DistTypeEnum.DELIVERY_AND_RECYCLE.getCode()){
                                deliveryPickListVO.setInterceptFlag(1);
                                break;
                            }
                        }
                    }
                }
            }

            deliveryPickListVOS.add(deliveryPickListVO);
        }

        PageInfo<DeliveryPickListVO> listPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(deliveryDTOPageInfo, listPageInfo);
        listPageInfo.setList(deliveryPickListVOS);

        return listPageInfo;
    }


    public static List<DeliveryPickDetailVO> dtoPage2PickVo(TmsResult<DeliveryBatchDTO> batchDTOTmsResult) {
        DeliveryBatchDTO batchDTO = batchDTOTmsResult.getData();
        List<DeliveryPickDTO> deliveryPickDTOS = batchDTO.getDeliveryPickDTOS();
        List<DeliveryPickDetailVO> deliveryPickDetailVOS = new ArrayList<>();
        List<DeliverySiteDTO> deliverySiteDTOList = batchDTO.getDeliverySiteDTOList();

        //只有回收
        if(CollectionUtils.isEmpty(deliveryPickDTOS)){
            DeliverySiteDTO deliverySiteDTO = deliverySiteDTOList.get(0);
            DeliveryPickDetailVO deliveryPickDetailVO = new DeliveryPickDetailVO();
            deliveryPickDetailVO.setPickUpTime(batchDTO.getPickUpTime());
            deliveryPickDetailVO.setDeliverySiteId(deliverySiteDTO.getId());

            deliveryPickDetailVOS.add(deliveryPickDetailVO);
            return deliveryPickDetailVOS;
        }
        //过滤出独立拣货的
        Map<String, List<DeliveryPickDTO>> particlePickMap = deliveryPickDTOS.stream()
                .filter(pick -> Objects.equals(pick.getType(), DeliveryPickTypeEnum.BRAND_SINGLE.getCode()))
                .collect(Collectors.groupingBy(DeliveryPickDTO::getParticle));

        for (String particle : particlePickMap.keySet()) {
            List<DeliveryPickDTO> deliveryPickDTOList = particlePickMap.get(particle);
            Map<Integer, List<DeliveryPickDTO>> tempPickMap = deliveryPickDTOList.stream().collect(Collectors.groupingBy(DeliveryPickDTO::getTemperature));

            installPick(batchDTO,tempPickMap,deliveryPickDetailVOS,particle);
        }

        //普通拣货、门店拣货
        Map<Integer, List<DeliveryPickDTO>> defaultAndMerchantPickMap = deliveryPickDTOS.stream()
                .filter(pick -> Objects.equals(pick.getType(), DeliveryPickTypeEnum.DEFAULT.getCode())
                        || Objects.equals(pick.getType(), DeliveryPickTypeEnum.MERCHANT_PICK.getCode()))
                .collect(Collectors.groupingBy(DeliveryPickDTO::getTemperature));

        installPick(batchDTO, defaultAndMerchantPickMap, deliveryPickDetailVOS,null);

        return deliveryPickDetailVOS;
    }

    private static void installPick(DeliveryBatchDTO batchDTO, Map<Integer, List<DeliveryPickDTO>> defaultPickMap,
                                    List<DeliveryPickDetailVO> deliveryPickDetailVOS,String particle) {
        for (Integer temp : defaultPickMap.keySet()) {
            DeliveryPickDetailVO deliveryPickDetailVO = new DeliveryPickDetailVO();
            deliveryPickDetailVO.setStorageLocation(temp);
            deliveryPickDetailVO.setDeliverySiteId(CollectionUtils.isEmpty(defaultPickMap.get(temp)) ? null : defaultPickMap.get(temp).get(0).getDeliverySiteId());
            deliveryPickDetailVO.setPickUpTime(batchDTO.getPickUpTime());
            if (StringUtils.isNotBlank(particle)) {
                deliveryPickDetailVO.setAdminId(Integer.parseInt(particle.split("#")[0]));
                deliveryPickDetailVO.setAdminName(particle.split("#")[1]);
            }

            List<DeliveryPickDetail> pickDetails = new ArrayList<>();

            List<DeliveryPickDTO> deliveryPickDTOList = defaultPickMap.get(temp);
            for (DeliveryPickDTO deliveryPickDTO : deliveryPickDTOList) {
                DeliveryPickDetail deliveryPickDetail = new DeliveryPickDetail();
                deliveryPickDetail.setId(deliveryPickDTO.getId());
                deliveryPickDetail.setBatchId(deliveryPickDTO.getDeliveryBatchId());
                deliveryPickDetail.setSku(deliveryPickDTO.getOutItemId());
                deliveryPickDetail.setSkuCategoryType(deliveryPickDTO.getCategoryType());
                deliveryPickDetail.setPdName(deliveryPickDTO.getItemDesc());
                deliveryPickDetail.setSkuCnt(deliveryPickDTO.getQuantity());
                deliveryPickDetail.setUnit(deliveryPickDTO.getUnit());
                deliveryPickDetail.setDetailStatus(deliveryPickDTO.getShortQuantity() > 0 ? 0 : 1);
                deliveryPickDetail.setShortCnt(deliveryPickDTO.getShortQuantity());
                deliveryPickDetail.setTemperature(temp);
                deliveryPickDetail.setSpecification(deliveryPickDTO.getSpecification());
                if(Objects.equals(deliveryPickDTO.getType(),DeliveryPickTypeEnum.BRAND_SINGLE.getCode())){
                    deliveryPickDetail.setAdminId(Integer.parseInt(deliveryPickDTO.getParticle().split("#")[0]));
                    deliveryPickDetail.setAdminName(deliveryPickDTO.getParticle().split("#")[1]);
                    deliveryPickDetail.setSkuSorting(0);
                }else{
                    deliveryPickDetail.setSkuSorting(1);
                }
                deliveryPickDetail.setSkuType(deliveryPickDTO.getSkuType());
                deliveryPickDetail.setSkuPic(deliveryPickDTO.getSkuPic());
                deliveryPickDetail.setNameRemakes(deliveryPickDTO.getNameRemakes());

                deliveryPickDetail.setInterceptNum(deliveryPickDTO.getInterceptQuantity());
                deliveryPickDetail.setProcessFlag(deliveryPickDTO.getProcessFlag());
                deliveryPickDetail.setProcessQuantity(deliveryPickDTO.getProcessQuantity());
                deliveryPickDetail.setProcessUnit(deliveryPickDTO.getProcessUnit());
                deliveryPickDetail.setProcessWeight(deliveryPickDTO.getProcessWeight());
                deliveryPickDetail.setProcessConversionRatio(deliveryPickDTO.getProcessConversionRatio());

                deliveryPickDetail.setExtType(deliveryPickDTO.getExtType());
                deliveryPickDetail.setStorageArea(deliveryPickDTO.getStorageArea());
                deliveryPickDetail.setBarcodes(deliveryPickDTO.getBarcodes());
                deliveryPickDetail.setOutOrderFlag(Objects.equals(deliveryPickDTO.getPackType(), DistItemEnums.PackType.PACKAGE.getValue()));
                deliveryPickDetail.setMerchantName(deliveryPickDTO.getOuterClientName());
                deliveryPickDetail.setScanCount(deliveryPickDTO.getScanCount());
                pickDetails.add(deliveryPickDetail);
            }

            deliveryPickDetailVO.setPickDetails(pickDetails);

            deliveryPickDetailVOS.add(deliveryPickDetailVO);
        }
    }


    public static List<DeliverySiteVO> dtoPage2DeliverySiteVo(TmsResult<DeliveryBatchDTO> batchDTOTmsResult) {
        DeliveryBatchDTO batchDTO = batchDTOTmsResult.getData();
        Integer status = batchDTO.getStatus();

        List<DeliverySiteDTO> deliverySiteDTOList = batchDTO.getDeliverySiteDTOList();

        deliverySiteDTOList.sort(Comparator.comparing(DeliverySiteDTO::getSequence));
        List<DeliverySiteVO> deliverySiteVOList = new ArrayList<>();

        for (DeliverySiteDTO deliverySiteDTO : deliverySiteDTOList) {
            DeliverySiteVO deliverySiteVO = new DeliverySiteVO();

            deliverySiteVO.setAddress(deliverySiteDTO.getSiteDTO().getFullAddress());
            deliverySiteVO.setSiteId(deliverySiteDTO.getSiteId());
            if (deliverySiteDTO.getDeliveryType() != null){
                deliverySiteVO.setDeliveryType(deliverySiteDTO.getDeliveryType().getCode());
            }
            deliverySiteVO.setDeliverySiteId(deliverySiteDTO.getId());
            deliverySiteVO.setInterceptType(deliverySiteDTO.getInterceptState());
            deliverySiteVO.setMId(deliverySiteDTO.getOuterClientId());
            deliverySiteVO.setMName(deliverySiteDTO.getOuterClientName());
            deliverySiteVO.setPath(batchDTO.getPathCode());
            if(Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.NO.getCode())){
                deliverySiteVO.setPathStatus(0);
            }
            if(Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_PICK.getCode())){
                deliverySiteVO.setPathStatus(1);
            }
            if(status >= DeliveryBatchStatusEnum.IN_DELIVERY.getCode() &&
                    !Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode()) &&
                    !Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_PICK.getCode())){
                deliverySiteVO.setPathStatus(1);
            }
            if(Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())){
                deliverySiteVO.setPathStatus(2);
            }
            deliverySiteVO.setPhone(deliverySiteDTO.getSiteDTO().getPhone());
            deliverySiteVO.setPoiNote(deliverySiteDTO.getSiteDTO().getPoi());
            deliverySiteVO.setSort(deliverySiteDTO.getSequence());
            deliverySiteVO.setTimeFrame(deliverySiteDTO.getTimeFrame());
            deliverySiteVO.setSendWay(deliverySiteDTO.getSendWay());
            deliverySiteVO.setSendRemark(deliverySiteDTO.getSendRemark());
            deliverySiteVO.setCheckinPunchFlag(deliverySiteDTO.getCheckinPunchFlag());
            deliverySiteVO.setOuterBrandName(deliverySiteDTO.getOuterBrandName());
            deliverySiteVO.setPickLackFlag(deliverySiteDTO.getPickLackFlag());
            deliverySiteVO.setOrderSourceInfo(deliverySiteDTO.getOrderSourceInfo());
            deliverySiteVOList.add(deliverySiteVO);
        }
        return deliverySiteVOList;
    }
}
