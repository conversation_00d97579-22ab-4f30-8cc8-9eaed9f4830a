package net.summerfarm.tms.controller.delivery.vo;

import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 运输点位转换器
 * date: 2022/12/6 14:20
 *
 * <AUTHOR>
 */
public class DeliverySiteVOConverter {


    public static DeliverySiteVO dto2Vo(DeliverySiteDTO deliverySiteDTO) {
        DeliverySiteVO deliverySiteVO = new DeliverySiteVO();
        deliverySiteVO.setId(deliverySiteDTO.getId());
        deliverySiteVO.setType(deliverySiteDTO.getType());
        deliverySiteVO.setSequence(deliverySiteDTO.getSequence());
        deliverySiteVO.setStatus(deliverySiteDTO.getStatus());
        deliverySiteVO.setStatusDesc(deliverySiteDTO.getStatusDesc());
        deliverySiteVO.setSiteName(deliverySiteDTO.getSiteName());
        deliverySiteVO.setSiteId(deliverySiteDTO.getSiteId());
        deliverySiteVO.setSiteType(deliverySiteDTO.getSiteType());
        deliverySiteVO.setSiteTypeDesc(deliverySiteDTO.getSiteTypeDesc());
        deliverySiteVO.setSiteDTO(deliverySiteDTO.getSiteDTO());
        deliverySiteVO.setPlanArriveTime(deliverySiteDTO.getPlanArriveTime());
        deliverySiteVO.setSignInPoi(deliverySiteDTO.getSignInPoi());
        deliverySiteVO.setSignInDiffKm(deliverySiteDTO.getSignInDiffKm());
        deliverySiteVO.setSignInTime(deliverySiteDTO.getSignInTime());
        deliverySiteVO.setSignInDiffMinute(deliverySiteDTO.getSignInDiffMinute());
        deliverySiteVO.setSignInErrType(deliverySiteDTO.getSignInErrType());
        deliverySiteVO.setSignInRemark(deliverySiteDTO.getSignInRemark());
        deliverySiteVO.setSignInPics(deliverySiteDTO.getSignInPic1());
        deliverySiteVO.setSignInStatus(deliverySiteDTO.getSignInStatus());
        deliverySiteVO.setSignOutDiffKm(deliverySiteDTO.getSignOutDiffKm());
        deliverySiteVO.setSignOutTime(deliverySiteDTO.getSignOutTime());
        deliverySiteVO.setSignOutDiffMinute(deliverySiteDTO.getSignOutDiffMinute());
        deliverySiteVO.setSignOutErrType(deliverySiteDTO.getSignOutErrType());
        deliverySiteVO.setSignOutRemark(deliverySiteDTO.getSignOutRemark());
        deliverySiteVO.setSignOutPics(deliverySiteDTO.getSignOutPic1());
        deliverySiteVO.setSignOutStatus(deliverySiteDTO.getSignOutStatus());
        deliverySiteVO.setNextSiteDistanceKm(deliverySiteDTO.getNextSiteDistanceKm());
        deliverySiteVO.setSignInAddress(deliverySiteDTO.getSignInAddress());
        deliverySiteVO.setSignOutPoi(deliverySiteDTO.getSignOutPoi());
        deliverySiteVO.setSignOutAddress(deliverySiteDTO.getSignOutAddress());
        deliverySiteVO.setPlanOutTime(deliverySiteDTO.getPlanOutTime());
        deliverySiteVO.setVehiclePlatePics(deliverySiteDTO.getVehiclePlatePics());
        deliverySiteVO.setSealPics(deliverySiteDTO.getSealPics());
        return deliverySiteVO;
    }

    public static List<DeliverySiteVO> dto2Vo(List<DeliverySiteDTO> deliverySiteDTOs) {
        return deliverySiteDTOs.stream().map(DeliverySiteVOConverter::dto2Vo).collect(Collectors.toList());
    }
}
