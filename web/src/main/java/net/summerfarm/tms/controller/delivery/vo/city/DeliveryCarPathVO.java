package net.summerfarm.tms.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.commons.math3.analysis.function.Add;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2022/10/18 11:18<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryCarPathVO  extends DeliveryCarPath {
    private String number;

    private String plateNumber;

    private String driver;

    private String phone;

    private Integer no;

    private String noName;


    @ApiModelProperty("路线满载率")
    private BigDecimal pathFullLoadRatio;

    @ApiModelProperty("昨日完成排线时间")
    private LocalDateTime yesterdayFinishTime;

    @ApiModelProperty("昨日点位平均时长")
    private BigDecimal avgFinishTime;

    @ApiModelProperty("车辆体积")
    private BigDecimal deliveryCarVolume;

    /**
     * 配送仓名称
     */
    private String storeName;

    /**
     * 完成捡货时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickUpTime;

    /**
     * 装车照片
     */
    private String loadingPhotos;

    /**
     * 冷藏照片
     */
    private String refrigeratePics;

    /**
     * 冷冻照片
     */
    private String freezePics;

    /**
     * 保温措施图片
     */
    private String keepTemperatureMethodPics;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;

    /**
     * 出仓温度
     */
    private BigDecimal outWarehouseTemperature;

    /**
     * 出仓晚点原因
     */
    private String overOutTimeReason;

    /**
     * 是否晚点
     */
    private String isLate;

    @NotNull(groups = {Update.class},message = "id.not.null")
    private Long id;

    private Long deliveryCarId;

    @NotNull(groups = {Add.class},message = "areaNo.not.null")
    @ApiModelProperty("仓库编号")
    private Integer storeNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("配送时间")
    private LocalDate deliveryTime;

    @ApiModelProperty("路线")
    private String path;

    private BigDecimal totalDistance;

    //路线名称
    private String pathName;

    private Integer type ;

    //配送点位数量
    private Integer totalNum ;

    private Long tmsCarId;

    private Long carrierId;

    private BigDecimal realTotalDistance;

    /**
     * 智能排线总距离 km
     */
    private BigDecimal intelligenceTotalDistance;

    /**
     * 批次排线获取价格
     */
    private BigDecimal priceTotal;

    /**
     * 总重量
     */
    private BigDecimal weightTotal;
    
    
}
