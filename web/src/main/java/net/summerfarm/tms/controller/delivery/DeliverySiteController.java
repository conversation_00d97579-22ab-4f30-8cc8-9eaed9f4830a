package net.summerfarm.tms.controller.delivery;

import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.controller.delivery.converter.DeliveryPickVoConverter;
import net.summerfarm.tms.controller.delivery.vo.DeliverySiteVO;
import net.summerfarm.tms.controller.delivery.vo.DeliverySiteVOConverter;
import net.summerfarm.tms.controller.delivery.vo.trunk.PickUpDetailVO;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.delivery.dto.DeliverySitePunchCommand;
import net.summerfarm.tms.delivery.input.PickUpFinishUpdateCommand;
import net.summerfarm.tms.delivery.input.PickUpQueryInput;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 公共-运输点位相关接口
 */
@RestController
@RequestMapping("/tms-new/delivery-site")
public class DeliverySiteController {

    @Resource
    private DeliverySiteService deliverySiteService;

    /**
     * 到仓打卡
     */
    @PostMapping("/upsert/to-punch")
    @XmLock(prefixKey = RedisConstants.Delivery.TRUNK_IN_PUNCH, key = "{deliverySitePunchCommand.deliverySiteIds}",waitTime = 1000, message = "操作频繁，请稍后重试")
    public TmsResult<Void> toPunch(@Validated @RequestBody DeliverySitePunchCommand deliverySitePunchCommand) {
        deliverySiteService.toPunch(deliverySitePunchCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 出仓打卡
     */
    @PostMapping("/upsert/out-punch")
    public TmsResult<Void> outPunch(@Validated @RequestBody DeliverySitePunchCommand deliverySitePunchCommand) {
        deliverySiteService.outPunch(deliverySitePunchCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 查看打卡记录
     */
    @PostMapping("/query/punch-detail")
    public TmsResult<DeliverySiteVO> punchDetail(Long deliverySiteId) {
        return TmsResult.success(DeliverySiteVOConverter.dto2Vo(deliverySiteService.punchDetail(deliverySiteId).getData()));
    }

    /**
     * 查询poi对应地址
     */
    @PostMapping("/query/poi2address")
    public TmsResult<String> poi2address(String poi) {
        return deliverySiteService.poi2address(poi);
    }

    /**
     * 站点提货详情
     */
    @PostMapping("/query/pick-up-detail")
    public CommonResult<List<PickUpDetailVO>> pickUpDetail(@RequestBody @Validated PickUpQueryInput input) {
        return CommonResult.ok(deliverySiteService.queryPickUpDetail(input.getDeliverySiteId()).stream().map(DeliveryPickVoConverter::dto2PickUpVo).collect(Collectors.toList()));
    }

    /**
     * 完成拣货
     */
    @PostMapping("/upsert/trunk-pick-up-finish")
    public CommonResult<Void> trunkPickUpFinish(@RequestBody @Validated PickUpFinishUpdateCommand command) {
        deliverySiteService.trunkPickUpFinish(command);
        return CommonResult.ok();
    }
}
