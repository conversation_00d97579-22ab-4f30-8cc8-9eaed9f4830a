package net.summerfarm.tms.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemRecycleDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/12/26 18:10<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteDetailPcVO implements Serializable {
    private static final long serialVersionUID = 6533141185431944816L;
    /**
     * 地址
     */
    private String address;

    /**
     * 完成配送图片
     */
    private String deliveryPic;

    /**
     * 签收面单照片
     */
    private String signPic;

    /**
     * 货物照片
     */
    private String productPic;


    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 完成配送时间
     */
    private LocalDateTime finishDeliveryTime;

    /**
     * 完成配送地点poi与地址poi的距离
     */
    private Integer finishDistance;

    /**
     * 完成配送poi
     */
    private String finishPoi;

    /**
     * 配送点位id
     */
    private Long deliverySiteId;

    /**
     * 店铺名称
     */
    private String mName;

    /**
     * 店铺id
     */
    private String mId;

    private List<OrderItemPc> orderItemList;

    /**
     * 委托单编号
     */
    private String orderNo;

    /**
     * 路线名称
     */
    private String path;

    /**
     * 任务状态 10 待拣货 22.已配送 25配送中
     */
    private Integer pathStatus;

    /**
     * 配送地址手机号
     */
    private String phone;

    /**
     * 地址poi
     */
    private String poiNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 签收备注
     */
    private String signForRemarks;

    /**
     * 是否是正常签收 0 正常 1 不正常
     */
    private Integer signForStatus;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 精准送
     */
    private String timeFrame;
    /**
     * 配送员名称
     */
    private String driverName;
    /**
     * 配送员电话
     */
    private String driverPhone;

    /**
     * 签到点位地址
     */
    private String signInAddress;
    /**
     * 签到距离
     */
    private BigDecimal signInDistance;

    /**
     * 是否超出距离 0 正常 1超出
     */
    private Integer outDistance;

    /**
     * 来源描述
     */
    private String sourceDescs;

    /**
     * 操作记录
     */
    private List<OperateRecord> operateRecords;

    /**
     * 超出距离备注
     */
    private String outRemark;

    /**
     *超出距离原因类型
     */
    private String outReasonType;

    /**
     * 超出距离照片凭证
     */
    private String outPic;
    

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;

    /**
     * 封签照片
     */
    private String sealPics;

    /**
     * 配送备注
     */
    private String sendRemark;

    /**
     * 到店打卡记录
     */
    private CheakInPunchRecordVO cheakInPunchRecordVO;
}

@Data
class OrderItemPc{
    /**
     * 数量
     */
    private Integer amount;

    /**
     * 扫码数量
     */
    private Integer codeAmount;

    /**
     * 拦截数量
     */
    private Integer interceptNum;

    /**
     * sku配送类型  0 配送  1 回收
     */
    private Integer deliveryType;

    /**
     * 商品名称
     */
    public String pdName;

    /**
     * 缺货数量
     */
    private Integer shortCnt;

    /**
     * sku编号
     */
    public String sku;

    /**
     * sku头图
     */
    private String skuPic;

    /**
     * 有货无码数量
     */
    private Integer withOutQuantity;

    /**
     * 有货无码原因备注
     */
    private String withOutRemake;

    /**
     * 有货无码图片链接
     */
    private String withOutPictures;
    /**
     * 备注
     */
    private String remark;
    /**
     * 单位
     */
    private String unit;

    /**
     * 规格
     */
    private String specification;
    /**
     * 扫码code
     */
    private String codes;
    /**
     * 批次号
     */
    private String batchs;

    /**
     * 存储条件
     * 0, "未分类"
     * 1, "冷冻"
     * 2, "冷藏"
     * 3, "常温"
     * 4, "顶汇大流通"
     */
    private Integer storageArea;

    /**
     * 配送/回收物品状态，0：正常，1：异常
     */
    private Integer status;

    /**
     * 物品配送/回收收状态描述
     */
    private String statusDesc;

    /**
     * 回收信息
     */
    private DeliverySiteItemRecycleDTO deliverySiteItemRecycleDTO;

    /**
     * 条码信息
     */
    private List<String> barcodes;
}

@Data
class OperateRecord{
    /**
     * 日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;
    /**
     * 操作名称
     */
    private String typeName;
    /**
     * 操作人名称
     */
    private String operator;
    /**
     * 备注
     */
    private String remark;
}