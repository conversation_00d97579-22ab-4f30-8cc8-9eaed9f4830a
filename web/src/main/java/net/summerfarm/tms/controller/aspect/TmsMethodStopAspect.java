package net.summerfarm.tms.controller.aspect;

import com.alibaba.fastjson.JSON;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.anno.CityStoreNo;
import net.summerfarm.tms.config.TmsNacosConfig;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.auth.AuthQueryFacade;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.*;

/**
 * Description: <br/>
 * date: 2023/7/13 11:45<br/>
 *
 * <AUTHOR> />
 */
@Aspect
@Slf4j
@Component
public class TmsMethodStopAspect {

    @Resource
    private TmsNacosConfig tmsNacosConfig;

    @Before("@annotation(TmsMethodStop)")
    public void beforeXpi(JoinPoint joinPoint) throws Exception {
        //如果开启了则不能完成排线
        if(tmsNacosConfig.isMethodStop()){
            throw new TmsRuntimeException("管理员已设置不能完成此操作，请联系管理员");
        }
    }
}
