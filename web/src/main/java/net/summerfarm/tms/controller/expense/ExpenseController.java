package net.summerfarm.tms.controller.expense;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiImplicitParam;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.aspect.TmsDataPermission;
import net.summerfarm.tms.expense.ExpenseService;
import net.summerfarm.tms.expense.dto.DeliveryExpenseRecordVO;
import net.summerfarm.tms.expense.dto.ExpenseQueryDTO;
import net.summerfarm.tms.expense.dto.ExpenseVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 费用接口
 * <AUTHOR> xiang
 * create at:  2021-11-11
 */
@RestController
@Slf4j
@RequestMapping("/tms-new/tms-expense")
public class ExpenseController {
    @Resource
    private ExpenseService expenseService;

    /**
     * 提交报销明细记录
     */
    @PostMapping(value = "/insert/detail")
    public AjaxResult insertExpenseDetail(@RequestBody @Validated ExpenseVO expense) {
        return expenseService.insertExpenseDetail(expense);
    }

    /**
     * 查询报销明细
     */
    @PostMapping(value = "/list/detail/{id}")
    @ApiImplicitParam(name = "id", value = "报销单id", paramType = "path", required = true)
    public AjaxResult selectExpenseDetail(@PathVariable int id) {
        return expenseService.selectExpenseDetail(id);
    }

    /**
     * 修改报销明细
     */
    @PostMapping(value = "/update/detail")
    public AjaxResult updateExpenseDetail(@RequestBody ExpenseVO expense) {
        return expenseService.updateExpenseDetail(expense);
    }

    /**
     * 计算里程
     */
    @GetMapping(value = "/mileage")
    public AjaxResult getMileage(String startAddress, String endAddress) {
        return expenseService.getMileage(startAddress, endAddress);
    }

    //查看报销审核通知
    @GetMapping(value = "/select/audit")
    public AjaxResult selectAuditExpense() {
        return expenseService.selectAuditExpense();
    }

    /**
     * 费用列表查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = "/expense/list")
    @TmsDataPermission
    public TmsResult<PageInfo<ExpenseVO>> selectExpense(@RequestBody ExpenseQueryDTO query) {
        return TmsResult.success(expenseService.query(query));
    }

    /**
     * 查看费用报销审核记录
     *
     * @param expenseId
     * @return
     */
    @GetMapping(value = "/select/expense/auditDetail")
    public TmsResult<DeliveryExpenseRecordVO> getAuditRecord(Integer expenseId) {
        return TmsResult.success(expenseService.getAuditDetail(expenseId));

    }

    /**
     * 报销导出
     *
     * @param selectKeys
     * @return
     */
    @GetMapping(value = "/export")
    @TmsDataPermission
    public void selectExpenseExport(ExpenseVO selectKeys) {
        expenseService.selectExpenseExport(selectKeys);
    }

}
