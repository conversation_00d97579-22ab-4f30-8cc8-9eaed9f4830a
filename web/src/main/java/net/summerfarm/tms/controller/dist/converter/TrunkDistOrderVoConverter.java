package net.summerfarm.tms.controller.dist.converter;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.inbound.controller.delivery.vo.trunk.TrunkDeliveryBatchVO;
import net.summerfarm.tms.inbound.converter.delivery.trunk.TrunkDeliveryBatchVOConverter;
import net.summerfarm.tms.controller.dist.vo.TrunkDistOrderVO;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.TrunkDistOrderStatusEnum;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description:干线委托单试图对象转换器
 * date: 2022/9/20 17:25
 *
 * <AUTHOR>
 */
public class TrunkDistOrderVoConverter {

    public static TrunkDistOrderVO dto2Vo(DistOrderDTO distOrderDTO) {
        TrunkDistOrderVO trunkDistOrderVO = new TrunkDistOrderVO();
        trunkDistOrderVO.setDistId(distOrderDTO.getDistId());
        trunkDistOrderVO.setOuterOrderId(distOrderDTO.getOutOrderId());
        //获取干线委托单来源枚举
        trunkDistOrderVO.setSource(distOrderDTO.getSource());
        if(DistOrderSourceEnum.getXmOrderTypeCode().contains(distOrderDTO.getSource())){
            trunkDistOrderVO.setSourceDesc("销售单-干线");
        }else{
            DistOrderSourceEnum distOrderSource = DistOrderSourceEnum.getDistOrderSourceByCode(distOrderDTO.getSource());
            trunkDistOrderVO.setSourceDesc(distOrderSource.getName());
        }
        trunkDistOrderVO.setExpectBeginTime(distOrderDTO.getExpectBeginTime());
        trunkDistOrderVO.setBeginSiteId(distOrderDTO.getBeginSiteId());
        if(Objects.equals(TmsSiteTypeEnum.SAAS.getCode(),distOrderDTO.getBeginType()) &&
                DistOrderSourceEnum.getTrunkOuterCode().contains(distOrderDTO.getSource())){
            trunkDistOrderVO.setBeginSiteContactName(distOrderDTO.getBeginSiteCustomContactName());
            trunkDistOrderVO.setBeginSiteName(distOrderDTO.getBeginClientName());
        }else{
            trunkDistOrderVO.setBeginSiteName(distOrderDTO.getBeginSiteName());
            trunkDistOrderVO.setBeginSiteContactName(distOrderDTO.getBeginSiteContactName());
        }
        trunkDistOrderVO.setBeginSitePhone(distOrderDTO.getBeginSitePhone());
        trunkDistOrderVO.setBeginSiteFullAddress(distOrderDTO.getBeginSiteFullAddress());
        trunkDistOrderVO.setMidSiteId(distOrderDTO.getMidSiteId());
        trunkDistOrderVO.setMidSiteName(distOrderDTO.getMidSiteName());
        trunkDistOrderVO.setEndSiteId(distOrderDTO.getEndSiteId());
        if(Objects.equals(distOrderDTO.getSource(),DistOrderSourceEnum.OUTER_TRUNK.getCode())){
            trunkDistOrderVO.setEndSiteName(distOrderDTO.getOutClientName());
            trunkDistOrderVO.setEndSiteContactName(distOrderDTO.getEndSiteName());
        }else{
            trunkDistOrderVO.setEndSiteName(distOrderDTO.getEndSiteName());
            trunkDistOrderVO.setEndSiteContactName(distOrderDTO.getEndSiteContactName());
        }
        trunkDistOrderVO.setEndSitePhone(distOrderDTO.getEndSitePhone());
        trunkDistOrderVO.setEndSiteFullAddress(distOrderDTO.getEndSiteFullAddress());
        //获取干线委托单状态枚举
        TrunkDistOrderStatusEnum trunkStatus = TrunkDistOrderStatusEnum.getTrunkDistOrderStatusByDistOrderCode(distOrderDTO.getStatus());
        trunkDistOrderVO.setStatus(trunkStatus.getCode());
        trunkDistOrderVO.setStatusDesc(trunkStatus.getName());
        trunkDistOrderVO.setCloseReason(distOrderDTO.getCloseReason());
        trunkDistOrderVO.setCreateTime(distOrderDTO.getCreateTime());
        trunkDistOrderVO.setCreator(distOrderDTO.getCreator());
        trunkDistOrderVO.setFreezeWeight(distOrderDTO.getFreezeWeight());
        trunkDistOrderVO.setFreezeVolume(distOrderDTO.getFreezeVolume());
        trunkDistOrderVO.setFreezeQuantity(distOrderDTO.getFreezeQuantity());
        trunkDistOrderVO.setColdWeight(distOrderDTO.getColdWeight());
        trunkDistOrderVO.setColdVolume(distOrderDTO.getColdVolume());
        trunkDistOrderVO.setColdQuantity(distOrderDTO.getColdQuantity());
        trunkDistOrderVO.setNormalWeight(distOrderDTO.getNormalWeight());
        trunkDistOrderVO.setNormalVolume(distOrderDTO.getNormalVolume());
        trunkDistOrderVO.setNormalQuantity(distOrderDTO.getNormalQuantity());
        trunkDistOrderVO.setTotalWeight(distOrderDTO.getTotalWeight());
        trunkDistOrderVO.setTotalVolume(distOrderDTO.getTotalVolume());
        trunkDistOrderVO.setTotalQuantity(distOrderDTO.getTotalQuantity());
        trunkDistOrderVO.setSkuList(distOrderDTO.getSkuList());
        trunkDistOrderVO.setOutBrandName(distOrderDTO.getOutBrandName());
        trunkDistOrderVO.setSendRemark(distOrderDTO.getSendRemark());
        trunkDistOrderVO.setStoreName(distOrderDTO.getOutClientName());
        List<DeliveryBatchDTO> deliveryBatchList = distOrderDTO.getDeliveryBatchList();
        if (deliveryBatchList == null) {
            deliveryBatchList = new ArrayList<>();
        }
        List<TrunkDeliveryBatchVO> trunkDeliveryBatchVOList = deliveryBatchList.stream().map(TrunkDeliveryBatchVOConverter::dto2Vo).collect(Collectors.toList());
        trunkDistOrderVO.setDeliveryBatchList(trunkDeliveryBatchVOList);
        return trunkDistOrderVO;
    }

    public static PageInfo<TrunkDistOrderVO> dtoPage2VoPage(PageInfo<DistOrderDTO> distOrderPageInfo) {
        List<DistOrderDTO> distOrderDTOs = distOrderPageInfo.getList();
        List<TrunkDistOrderVO> trunkDistOrderVOs = distOrderDTOs.stream().map(TrunkDistOrderVoConverter::dto2Vo).collect(Collectors.toList());

        PageInfo<TrunkDistOrderVO> trunkDistOrderPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(distOrderPageInfo, trunkDistOrderPageInfo);
        trunkDistOrderPageInfo.setList(trunkDistOrderVOs);
        return trunkDistOrderPageInfo;
    }
}
