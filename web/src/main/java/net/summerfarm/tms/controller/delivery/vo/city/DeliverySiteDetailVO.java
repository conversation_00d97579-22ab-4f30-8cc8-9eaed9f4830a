package net.summerfarm.tms.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemRecycleDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/12/26 18:10<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteDetailVO implements Serializable {
    /**
     * 地址
     */
    private String address;

    /**
     * 门店抬头照片
     */
    private String deliveryPic;

    /**
     * 签收面单照片
     */
    private String signPic;

    /**
     * 货物照片
     */
    private String productPic;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 完成配送时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishDeliveryTime;

    /**
     * 完成配送地点poi与地址poi的距离
     */
    private Integer finishDistance;

    /**
     * 完成配送poi
     */
    private String finishPoi;

    /**
     * 配送点位id
     */
    private Long deliverySiteId;

    /**
     * 店铺名称
     */
    private String mName;

    /**
     * 店铺id
     */
    private String mId;

    private List<OrderItem> orderItemList;

    /**
     * 委托单编号
     */
    private String orderNo;

    /**
     * 路线名称
     */
    private String path;

    /**
     * 配送任务状态 0 待捡货、1 捡货完成(配送中)、2完成配送
     */
    private Integer pathStatus;

    /**
     * 配送地址手机号
     */
    private String phone;

    /**
     * 地址poi
     */
    private String poiNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 签收备注
     */
    private String signForRemarks;

    /**
     * 是否是正常签收 0 正常 1 不正常
     */
    private Integer signForStatus;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 精准送
     */
    private String timeFrame;

    /**
     * bd名称
     */
    private String bdName;

    /**
     * bd手机号
     */
    private String bdPhone;

    /**
     * 外部联系人id
     */
    private String contactId;

    /**
     * 地址状态
     */
    private Integer contactStatus;

    /**
     * 报销单id
     */
    private Integer expenseId;

    /**
     * 报销单状态
     * 0,"待审核",1,"审核成功",2,"审核失败";
     */
    private Integer expenseStatus;

    /**
     * 城配仓编号
     */
    private String storeNo;
    /**
     * 完成拣货时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickUpTime;

    /**
     * 计划出仓时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planOutTime;

    /**
     * 配送备注
     */
    private String sendRemark;


    /**
     * 车牌照片
     */
    private String vehiclePlatePics;

    /**
     * 封签照片
     */
    private String sealPics;

    /**
     * 超区照片
     */
    private String outPic;

    /**
     * 超区原因
     */
    private String outReasonType;

    /**
     * 备注
     */
    private String outReason;

    /**
     * 是否是Saas点位，true 是 false否
     */
    private boolean saasFlag;
    /**
     *是否需要扫码 false 不需要 true 需要
     */
    private Boolean scanCodeFlag;

    /**
     * 到店打卡标识 true 需要打卡 false不需要打卡
     */
    private boolean checkinPunchFlag;

    /**
     * 拣货缺货标识 true 缺货 false 不缺货
     */
    private Boolean pickLackFlag;

    /**
     * 打卡范围 km
     */
    private BigDecimal punchRange;

    /**
     * 到店打卡记录
     */
    private CheakInPunchRecordVO cheakInPunchRecordVO;

    /**
     * 点位最近完成配送门店抬头照片
     */
    private String recentHeadPic;

    /**
     * 外部品牌名
     */
    private String outerBrandName;
}

@Data
class OrderItem{
    /**
     * 配送点位详情id
     */
    private Long deliverySiteItemId;
    /**
     * 数量
     */
    private Integer amount;

    /**
     * 扫码数量
     */
    private Integer codeAmount;

    /**
     * 拦截数量
     */
    private Integer interceptNum;

    /**
     * sku配送类型  0 配送  1 回收
     */
    private Integer deliveryType;

    /**
     * 商品名称
     */
    public String pdName;

    /**
     * 缺货数量
     */
    private Integer shortCnt;

    /**
     * 拣货缺货数量
     */
    private Integer pickShortCnt;
    /**
     * 备注
     */
    private String remark;
    /**
     * sku编号
     */
    public String sku;

    /**
     * sku头图
     */
    private String skuPic;

    /**
     * 有货无码数量
     */
    private Integer withOutQuantity;

    /**
     * 有货无码原因备注
     */
    private String withOutRemake;

    /**
     * 有货无码图片链接
     */
    private String withOutPictures;

    /**
     * 单位
     */
    private String unit;

    /**
     * 规格
     */
    private String specification;

    /**
     * 类型 0普通 1水果
     */
    private Integer fruitsType;
    /**
     * 类型 0 自营 1 代仓 2POP
     */
    private Integer skuType;

    /**
     * 所属代仓
     */
    private String nameRemakes;

    /**
     * 加工信息
     */
    private List<ProcessItemVO> processItems;


    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;


    /**
     * 存储条件
     * 0, "未分类"
     * 1, "冷冻"
     * 2, "冷藏"
     * 3, "常温"
     * 4, "顶汇大流通"
     */
    private Integer storageArea;

    /**
     * 配送/回收物品状态，0：正常，1：异常
     */
    private Integer status;

    /**
     * 物品配送/回收收状态描述
     */
    private String statusDesc;

    /**
     * 回收信息
     */
    private DeliverySiteItemRecycleDTO deliverySiteItemRecycleDTO;

    /**
     * 条码信息
     */
    private List<String> barcodes;

    /**
     * sku 外单标识 true外单 false非外单
     */
    private boolean skuOutOrderFlag;

}