package net.summerfarm.tms.controller.delivery;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.driver.DriverService;
import net.summerfarm.tms.base.driver.dto.DriverDTO;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.constants.DataSychConstants;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.contexts.Global;
import net.summerfarm.tms.aspect.Token;
import net.summerfarm.tms.controller.delivery.vo.city.*;
import net.summerfarm.tms.controller.util.HttpToManageUtil;
import net.summerfarm.tms.delivery.dto.cheakinpunch.CheakInPunchSaveCommand;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.delivery.dto.pick.PickScanCommand;
import net.summerfarm.tms.delivery.dto.pick.PickShortCommand;
import net.summerfarm.tms.domain.vo.PunchVO;
import net.summerfarm.tms.domain.vo.SkuBatchCodeVO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.HandleException;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.expense.TmsExpenseService;
import net.summerfarm.tms.expense.dto.ExpenseDetailAddCommand;
import net.summerfarm.tms.ext.AuthExtService;
import net.summerfarm.tms.facade.wms.WmsQueryFacade;
import net.summerfarm.tms.facade.wms.dto.SkuBatchCodeDTO;
import net.summerfarm.tms.facade.wms.dto.SkuBatchCodeTraceDTO;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.delivery.SkuUnitQuery;
import net.summerfarm.tms.util.ThreadLocalUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 城配
 */
@RestController
@RequestMapping("/tms-new/city-delivery-site")
@HandleException
@Slf4j
public class CityDeliverySiteController {
    @Resource
    DeliverySiteService deliverySiteService;
    @Resource
    TmsExpenseService tmsExpenseService;
    @Resource
    private HttpToManageUtil httpToManageUtil;
    @Resource
    private SiteService siteService;
    @Resource
    private DriverService driverService;
    @Resource
    private DeliveryBatchService deliveryBatchService;
    @Resource
    RedisTemplate<String, String> redisTemplate;
 
    @Resource
    AuthExtService authExtService;
    @Resource
    private WmsQueryFacade wmsQueryFacade;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 城配打卡
     */
    @PostMapping("/upsert/sign-in")
    public TmsResult<Void> punch(@RequestBody @Validated DeliveryPunchCommand deliveryPunchCommand) {
        DeliverySiteDTO deliverySiteDTO = new DeliverySiteDTO();
        deliverySiteDTO.setSignInTime(deliveryPunchCommand.getPunchTime());
        if (deliverySiteDTO.getSignInTime() == null) {
            deliverySiteDTO.setSignInTime(LocalDateTime.now());
        }
        deliverySiteDTO.setSignInDistance(deliveryPunchCommand.getPunchDistance());
        deliverySiteDTO.setSignInPoi(deliveryPunchCommand.getPunchAddressPoi());
        deliverySiteDTO.setId(deliveryPunchCommand.getDeliverySiteId());
        return deliverySiteService.signIn(deliverySiteDTO);
    }

    /**
     * 完成拣货前的校验
     *
     * @param validateFinishPickInput
     * @return 校验失败时返回失败原因
     */
    @PostMapping("/query/validate-finish-pick")
    public TmsResult<String> validateFinishPick(@RequestBody @Validated ValidateFinishPickInput validateFinishPickInput) {
        return deliverySiteService.validateFinishPick(validateFinishPickInput.getDeliverySiteId());
    }

    /**
     * 完成拣货
     * [{"id":284136,
     * "pickDetails":[{"detailStatus":1,"id":8442110,"sku":"780484374288"},{"detailStatus":1,"id":8442133,"sku":"N001S01R005"},
     * {"detailStatus":1,"id":8442108,"sku":"402107822"},
     * {"detailStatus":1,"id":8442121,"sku":"827584118207"}]}]
     */
    @PostMapping("/upsert/finish-pick")
    public TmsResult<Void> finishPick(@RequestBody @Validated DeliveryFinishPickCommand deliveryFinishPickCommand) {
        List<DeliveryPickDetailCommand> deliveryPickDetails = deliveryFinishPickCommand.getDeliveryPickDetails();
        DeliverySiteDTO deliverySiteDTO = new DeliverySiteDTO();
        deliverySiteDTO.setId(deliveryFinishPickCommand.getDeliverySiteId());

        if(!CollectionUtils.isEmpty(deliveryPickDetails)){
            List<DeliveryPickDTO> deliveryPickDTOS = deliveryPickDetails.stream().map(pickDetail -> {
                DeliveryPickDTO deliveryPickDTO = new DeliveryPickDTO();
                deliveryPickDTO.setId(pickDetail.getId());
                deliveryPickDTO.setOutItemId(pickDetail.getSku());
                deliveryPickDTO.setShortQuantity(pickDetail.getShortCnt());
                deliveryPickDTO.setDeliveryBatchId(pickDetail.getBatchId());
                if(pickDetail.getDetailStatus() == null){
                    throw new TmsRuntimeException(pickDetail.getSku() + "拣货状态不能为空");
                }
                deliveryPickDTO.setStatus(pickDetail.getDetailStatus() == 0 ?
                        DeliveryPickStatusEnum.EXCEPTION_PICK.getCode() : DeliveryPickStatusEnum.COMPLETE_PICK.getCode());
                deliveryPickDTO.setProcessPickQuantity(pickDetail.getProcessShortQuantity());
                return deliveryPickDTO;
            }).collect(Collectors.toList());

            deliverySiteDTO.setDeliveryPickDTOS(deliveryPickDTOS);
        }else{
            deliverySiteDTO.setDeliveryPickDTOS(Collections.emptyList());
        }
        TmsResult<Void> voidTmsResult = deliverySiteService.finishPick(deliverySiteDTO);
        if (voidTmsResult.isSuccess()) {
            Long deliverySiteId = (Long) ThreadLocalUtil.getThreadLocal().get(DataSychConstants.App.FINISH_PICK);
            //通知商城侧
            deliverySiteService.finishPickNotifyEvents(deliverySiteId);
        }
        ThreadLocalUtil.removeValue(DataSychConstants.App.FINISH_PICK);
        return voidTmsResult;
    }

    /**
     * 点位配送 新增无码商品
     */
    @PostMapping("/upsert/add-no-code-sku")
    public TmsResult<Void> addNoCodeSku(@RequestBody @Validated DeliveryWithOutCodeCommand deliveryWithOutCodeCommand) {
        DeliverySiteItemDTO deliverySiteItemDTO = new DeliverySiteItemDTO();
        deliverySiteItemDTO.setId(deliveryWithOutCodeCommand.getDeliverySiteItemId());
        deliverySiteItemDTO.setDeliverySiteId(deliveryWithOutCodeCommand.getDeliverySiteId());
        deliverySiteItemDTO.setOutItemId(deliveryWithOutCodeCommand.getSku());
        deliverySiteItemDTO.setNoscanCount(deliveryWithOutCodeCommand.getQuantity());
        deliverySiteItemDTO.setNoscanReason(deliveryWithOutCodeCommand.getRemake());
        deliverySiteItemDTO.setNoscanPics(deliveryWithOutCodeCommand.getPictures());
        return deliverySiteService.noCodeSave(deliverySiteItemDTO);
    }

    /**
     * 点位配送 更新无码商品
     */
    @PostMapping("/upsert/update-no-code-sku")
    public TmsResult<Void> updateNoCodeSku(@RequestBody @Validated DeliveryWithOutCodeCommand deliveryWithOutCodeCommand) {
        DeliverySiteItemDTO deliverySiteItemDTO = new DeliverySiteItemDTO();
        deliverySiteItemDTO.setId(deliveryWithOutCodeCommand.getDeliverySiteItemId());
        deliverySiteItemDTO.setDeliverySiteId(deliveryWithOutCodeCommand.getDeliverySiteId());
        deliverySiteItemDTO.setOutItemId(deliveryWithOutCodeCommand.getSku());
        deliverySiteItemDTO.setNoscanCount(deliveryWithOutCodeCommand.getQuantity());
        deliverySiteItemDTO.setNoscanReason(deliveryWithOutCodeCommand.getRemake());
        deliverySiteItemDTO.setNoscanPics(deliveryWithOutCodeCommand.getPictures());
        return deliverySiteService.noCodeSave(deliverySiteItemDTO);
    }

    /**
     * 提交报销明细记录
     */
    @PostMapping(value = "/upsert/save-expense-detail")
    public TmsResult<Void> addExpenseDetail(@RequestBody @Validated ExpenseDetailAddCommand expense) {
        TmsAssert.notEmpty(expense.getExpenseDetails(), ErrorCodeEnum.PARAM_NOT_NULL, "ExpenseDetails");

        TmsResult<DeliverySiteDTO> deliverySiteDTOTmsResult = deliverySiteService.query(DeliverySiteQuery.builder().deliverySiteId(expense.getDeliverySiteId()).build());
        if(!deliverySiteDTOTmsResult.isSuccess()){
            return TmsResult.fail(deliverySiteDTOTmsResult.getErrCode(), deliverySiteDTOTmsResult.getErrorMessage());
        }
        DeliverySiteDTO deliverySiteDTO = deliverySiteDTOTmsResult.getData();
        TmsResult<DeliveryBatchDTO> deliveryBatchResult = deliveryBatchService.query(DeliveryBatchQuery.builder().batchId(deliverySiteDTO.getDeliveryBatchId()).build());
        if(!deliveryBatchResult.isSuccess()){
            return TmsResult.fail(deliveryBatchResult.getErrCode(), deliveryBatchResult.getErrorMessage());
        }
        DeliveryBatchDTO deliveryBatchDTO = deliveryBatchResult.getData();
        SiteDTO siteDTO = siteService.siteDetail(deliveryBatchDTO.getBeginSiteId(), null).getData();
        DriverDTO driverDTO = driverService.driverDetail(deliveryBatchDTO.getDriverId()).getData();

        ExpenseDTO expenseDTO = new ExpenseDTO();
        expenseDTO.setId(expense.getId());
        expenseDTO.setDeliveryTime(deliverySiteDTO.getSignInTime());
        expenseDTO.setSiteId(deliverySiteDTO.getSiteId());
        expenseDTO.setTmsDeliverySiteId(deliverySiteDTO.getId());
        expenseDTO.setDriverId(deliveryBatchDTO.getDriverId().intValue());
        expenseDTO.setStoreNo(Integer.valueOf(siteDTO.getOutBusinessNo()));
        expenseDTO.setMname(deliverySiteDTO.getOuterClientName());
        expenseDTO.setMId(Long.valueOf(deliverySiteDTO.getOuterClientId()));
        expenseDTO.setDeliveryTime(deliveryBatchDTO.getDeliveryTime());
        expenseDTO.setState(NumberUtils.INTEGER_ONE);
        expenseDTO.setStatus(ExpenseStatusEnum.AUDIT.ordinal());
        expenseDTO.setCreator(driverDTO.getName());

        List<ExpenseDetailDTO> expenseDetailDTOS = expense.getExpenseDetails().stream()
                .map(expenseDetail -> {
                    ExpenseDetailDTO expenseDetailDTO = new ExpenseDetailDTO();
                    expenseDetailDTO.setType(expenseDetail.getType());
                    expenseDetailDTO.setState(NumberUtils.INTEGER_ONE);
                    expenseDetailDTO.setStartAddress(expenseDetail.getStartAddress());
                    expenseDetailDTO.setEndAddress(expenseDetail.getEndAddress());
                    expenseDetailDTO.setMileage(expenseDetail.getMileage());
                    expenseDetailDTO.setAmount(expenseDetail.getAmount());
                    expenseDetailDTO.setRemark(expenseDetail.getRemark());
                    expenseDetailDTO.setUpdater(expenseDetail.getUpdater());
                    expenseDetailDTO.setUpdateTime(expenseDetail.getUpdateTime());
                    expenseDetailDTO.setCreator(expenseDetail.getCreator());
                    expenseDetailDTO.setCreateTime(expenseDetail.getCreateTime());
                    expenseDetailDTO.setPhotos(expenseDetail.getPhotos());
                    if (!CollectionUtils.isEmpty(expenseDetail.getPictures())) {
                        expenseDetailDTO.setPhotos(String.join(",", expenseDetail.getPictures()));
                    }
                    expenseDetailDTO.setIsReview(expenseDetail.getModify() != null ?
                            expenseDetail.getModify() : NumberUtils.INTEGER_ZERO);
                    return expenseDetailDTO;
                })
                .collect(Collectors.toList());
        expenseDTO.setExpenseDetailDTOList(expenseDetailDTOS);
        return tmsExpenseService.saveExpense(expenseDTO);
    }

    /**
     * 审核接口
     * {"id":7770,"reason":"停车费不在报销范围","status":2}
     */
    @PostMapping("/upsert/expenseAudit")
    @RequiresPermissions(value = {"expense:expense-audit"})
    public TmsResult<Void> expenseAudit(@RequestBody @Validated DeliveryExpenseVO expenseVO) {
        ExpenseDTO expenseDTO = new ExpenseDTO();
        expenseDTO.setId(expenseVO.getId());
        expenseDTO.setUsername(authExtService.getCurrentUserName());
        expenseDTO.setReason(expenseVO.getReason());
        expenseDTO.setStatus(expenseVO.getStatus());
        expenseDTO.setStoreNo(expenseVO.getStoreNo());
        return tmsExpenseService.expenseAudit(expenseDTO);
    }

    /**
     * 点位配送 扫描商品二维码
     */
    @Token
    @PostMapping(value = "/upsert/scan-code")
    public TmsResult<SkuBatchCodeVO> scanCode(@RequestBody @Validated DeliveryScanCodeCommand deliveryScanCodeCommand) {
        SkuBatchCodeDTO skuBatchCodeDTO = null;
        if(deliveryScanCodeCommand.getBatchCodeTraceFlag() == null || !deliveryScanCodeCommand.getBatchCodeTraceFlag()){
            //获取对应唯一码对应的sku
            String batchCode = deliveryScanCodeCommand.getOnlyCode().split(Global.CODE_SEPARATING_SYMBOL)[0] + Global.CODE_SEPARATING_SYMBOL;
            skuBatchCodeDTO = wmsQueryFacade.querySkuByBatchCode(batchCode);
        }else{
            // 溯源码
            SkuBatchCodeTraceDTO skuBatchCodeTraceDTO = wmsQueryFacade.queryBatchTraceCode(deliveryScanCodeCommand.getOnlyCode());
            skuBatchCodeDTO = new SkuBatchCodeDTO();
            skuBatchCodeDTO.setSku(skuBatchCodeTraceDTO.getSku());
        }

        if (skuBatchCodeDTO == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, "没有找到对应的货品");
        }
        if (StringUtils.isBlank(skuBatchCodeDTO.getSku())) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, "没有找到对应的货品");
        }
        DeliverySiteItemCodeDTO deliverySiteItemCodeDTO = new DeliverySiteItemCodeDTO();
        deliverySiteItemCodeDTO.setDeliverySiteId(deliveryScanCodeCommand.getDeliverySiteId());
        deliverySiteItemCodeDTO.setOutItemId(skuBatchCodeDTO.getSku());
        deliverySiteItemCodeDTO.setOnlyCode(deliveryScanCodeCommand.getOnlyCode());
        SkuBatchCodeVO skuBatchCode = new SkuBatchCodeVO();
        // 获取锁对象
        RLock lock = redissonClient.getLock(RedisConstants.Delivery.SCAN_CODE + ":"+deliveryScanCodeCommand.getDeliverySiteId()+":" +deliveryScanCodeCommand.getOnlyCode());
        try {
            if (!lock.tryLock(0L, 10L, TimeUnit.SECONDS)) {
                throw new TmsRuntimeException("正在处理，请稍后");
            }
            TmsResult<DeliverySiteItemCodeDTO> result = deliverySiteService.scanCodeSave(deliverySiteItemCodeDTO);
            if (!result.isSuccess() || result.getData() == null) {
                return TmsResult.fail(result.getErrCode(), result.getErrorMessage());
            }
            skuBatchCode.setSku(result.getData().getOutItemId());
            skuBatchCode.setOnlyCode(result.getData().getOnlyCode());
        } catch (InterruptedException e) {
            throw new TmsRuntimeException("正在处理，请稍后");
        } finally {
            // 释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return TmsResult.success(skuBatchCode);
    }

    /**
     * 点位配送 完成配送
     * {"deliveryPic":"tms/qaxx970imtgrq2r4,tms/vwcp5y7ozqkgrq2r6,tms/emccf2xbcpigrq2r8",
     * "deliveryType":0,
     * "finishDistance":10,
     * "finishPoi":"103.74490424262153,29.596382921006946",
     * "id":10181145,
     * "outDistance":0,
     * "signForRemarks":"",
     * "signForStatus":0}
     */
    @PostMapping("/upsert/finish-delivery-site")
    public TmsResult<Void> finishDeliverySite(@RequestBody @Validated DeliveryFinishSiteCommand deliveryFinishSiteCommand) {
        //添加锁
        String key = RedisConstants.Delivery.TMS_FINISH_DELIVERY_SITE + "_" + deliveryFinishSiteCommand.getDeliverySiteId();
        Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(key, String.valueOf(deliveryFinishSiteCommand.getDeliverySiteId()), 1, TimeUnit.MINUTES);
        if(!tryLock){
            throw new TmsRuntimeException(ErrorCodeEnum.LOCK_ERROR);
        }
        TmsResult<Void> voidTmsResult;
        try {
            //查询点位配送详情
            DeliverySiteDTO deliverySiteDTO = deliverySiteService.finishDeliverySiteQuery(DeliverySiteQuery.builder()
                    .deliverySiteId(deliveryFinishSiteCommand.getDeliverySiteId()).build());

            deliverySiteDTO.setId(deliveryFinishSiteCommand.getDeliverySiteId());
            deliverySiteDTO.setSignInPic1(deliveryFinishSiteCommand.getDeliveryPic());
            deliverySiteDTO.setSignInPoi(deliveryFinishSiteCommand.getFinishPoi());
            deliverySiteDTO.setSignInRemark(deliveryFinishSiteCommand.getSignForRemarks());
            deliverySiteDTO.setSignInStatus(deliveryFinishSiteCommand.getSignForStatus() == null ? 1 : deliveryFinishSiteCommand.getSignForStatus());
            deliverySiteDTO.setSignInDistance(deliveryFinishSiteCommand.getFinishDistance());
            deliverySiteDTO.setOutReasonType(deliveryFinishSiteCommand.getOutReasonType());
            deliverySiteDTO.setOutReason(deliveryFinishSiteCommand.getOutRemark());
            deliverySiteDTO.setSignInPic2(deliveryFinishSiteCommand.getSignPic());
            deliverySiteDTO.setSignInPic3(deliveryFinishSiteCommand.getProductPic());
            deliverySiteDTO.setOutPic(deliveryFinishSiteCommand.getOutPic());
            deliverySiteDTO.setOutDistance(deliveryFinishSiteCommand.getOutDistance());
            deliverySiteDTO.setStatus(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());

            this.deliveryShortParamAssign(deliveryFinishSiteCommand, deliverySiteDTO);
            this.deliveryRecycleAssign(deliveryFinishSiteCommand, deliverySiteDTO);
            voidTmsResult = deliverySiteService.finishDelivery(deliverySiteDTO,deliveryFinishSiteCommand.getCheckScanNum());
        }catch (Exception e) {
            if (e instanceof TmsRuntimeException) {
                throw (TmsRuntimeException) e;
            } else {
                throw e;
            }
        } finally {
            //移除锁
            redisTemplate.delete(key);
        }
        return voidTmsResult;
    }

    private void deliveryRecycleAssign(DeliveryFinishSiteCommand deliveryFinishSiteCommand, DeliverySiteDTO deliverySiteDTO) {
        if (deliveryFinishSiteCommand.getDeliveryFinishSiteRecycleList() == null) {
            return;
        }
        Map<Long, DeliveryFinishSiteRecycleCommand> sku2RecycleMap = deliveryFinishSiteCommand.getDeliveryFinishSiteRecycleList().stream()
                .collect(Collectors.toMap(DeliveryFinishSiteRecycleCommand::getDeliverySiteItemId, Function.identity()));

        for (DeliverySiteItemDTO deliverySiteItemDTO : deliverySiteDTO.getDeliverySiteItemDTOList()) {
            DeliveryFinishSiteRecycleCommand deliverySiteRecycle = sku2RecycleMap.get(deliverySiteItemDTO.getId());
            if (deliverySiteRecycle != null) {
                DeliverySiteItemEnums.Status recycleStatus = DeliverySiteItemEnums.Status.getStatusByValue(deliverySiteRecycle.getStatus());
                String reasonTypeDesc = "";
                if (Objects.equals(recycleStatus, DeliverySiteItemEnums.Status.ABNORMAL)){
                    if (deliverySiteRecycle.getReasonType() == null){
                        throw new BizException("回收异常需填写异常原因");
                    }
                    DeliverySiteItemRecycleEnums.ReasonType recycleReasonType = DeliverySiteItemRecycleEnums.ReasonType.getTypeByValue(deliverySiteRecycle.getReasonType());
                    reasonTypeDesc = recycleReasonType.getContent();
                    if (Objects.equals(recycleReasonType, DeliverySiteItemRecycleEnums.ReasonType.OTHER) && StrUtil.isBlank(deliverySiteRecycle.getRemark())){
                        throw new BizException("回收异常原因为其他类型需填写异常说明");
                    }
                }

                if (deliverySiteRecycle.getSpecificationQuantity() == null && deliverySiteRecycle.getBasicSpecQuantity() == null){
                    throw new BizException("大包装数量和小规格数量至少填写一项");
                }
                //大包装数量
                BigDecimal specificationQuantity = deliverySiteRecycle.getSpecificationQuantity() == null ? BigDecimal.ZERO : deliverySiteRecycle.getSpecificationQuantity();
                //小规格数量
                BigDecimal basicSpecQuantity = deliverySiteRecycle.getBasicSpecQuantity() == null ? BigDecimal.ZERO : deliverySiteRecycle.getBasicSpecQuantity();

                DeliverySiteItemRecycleDTO deliverySiteItemRecycleDTO = new DeliverySiteItemRecycleDTO();

                deliverySiteItemRecycleDTO.setReasonType(deliverySiteRecycle.getReasonType());
                deliverySiteItemRecycleDTO.setReasonTypeDesc(reasonTypeDesc);
                deliverySiteItemRecycleDTO.setDeliverySiteItemId(deliverySiteRecycle.getDeliverySiteItemId());
                deliverySiteItemRecycleDTO.setDeliverySiteId(deliverySiteItemDTO.getDeliverySiteId());
                deliverySiteItemRecycleDTO.setOutItemId(deliverySiteRecycle.getSku());
                deliverySiteItemRecycleDTO.setRecyclePics(deliverySiteRecycle.getRecyclePics());
                deliverySiteItemRecycleDTO.setSpecificationQuantity(specificationQuantity);
                deliverySiteItemRecycleDTO.setSpecificationUnit(deliverySiteRecycle.getSpecificationUnit());
                deliverySiteItemRecycleDTO.setBasicSpecQuantity(basicSpecQuantity);
                deliverySiteItemRecycleDTO.setBasicSpecUnit(deliverySiteRecycle.getBasicSpecUnit());
                deliverySiteItemRecycleDTO.setRemark(deliverySiteRecycle.getRemark());
                //兼容处理一下原点位物品的备注信息
                deliverySiteItemDTO.setStatus(recycleStatus.getValue());
                deliverySiteItemDTO.setRemark(deliverySiteItemRecycleDTO.buildNewRemark(recycleStatus.getValue()));
                deliverySiteItemDTO.setDeliverySiteRecycleDTO(deliverySiteItemRecycleDTO);
            }
        }
    }

    private void deliveryShortParamAssign(DeliveryFinishSiteCommand deliveryFinishSiteCommand, DeliverySiteDTO deliverySiteDTO) {
        if (deliveryFinishSiteCommand.getDeliveryPathShortSkuList() == null){
            return;
        }
        Map<Long, DeliveryFinishSiteShortCommand> sku2ShortCnt = deliveryFinishSiteCommand.getDeliveryPathShortSkuList().stream()
                .collect(Collectors.toMap(DeliveryFinishSiteShortCommand::getDeliverySiteItemId, Function.identity()));

        for (DeliverySiteItemDTO deliverySiteItemDTO : deliverySiteDTO.getDeliverySiteItemDTOList()) {
            DeliveryFinishSiteShortCommand deliverySiteShort = sku2ShortCnt.get(deliverySiteItemDTO.getId());
            if (deliverySiteShort != null) {
                deliverySiteItemDTO.setShortCount(deliverySiteShort.getShortCount());
                deliverySiteItemDTO.setStatus(DeliverySiteItemEnums.Status.ABNORMAL.getValue());
                deliverySiteItemDTO.setRemark(deliverySiteShort.getRemark());
            }
        }
    }

    /**
     * 退单接口
     */
    @PostMapping("/upsert/charge-back")
    @Token
    public TmsResult<Void> chargeBack(@RequestBody @Validated ChargeBackVO chargeBackVO) {
        return deliverySiteService.chargeBack(chargeBackVO.getId());
    }

    /**
     * 配送费调整提醒
     * {"contactId":1632,"deliveryFee":10}
     */
    @PostMapping("/upsert/delivery-fee-notify")
    @Token
    public TmsResult<Void> deliveryFeeNotify(@RequestBody NotifyDeliveryFeeDTO notifyDeliveryFeeDTO) {
        //根据新模型的contactId查询旧模型的数值
        TmsResult<SiteDTO> result = siteService.siteDetail(notifyDeliveryFeeDTO.getContactId(), SiteTypeEnum.cust.getCode());
        if (!result.isSuccess()) {
            return TmsResult.fail(result.getErrCode(), result.getErrorMessage());
        }
        notifyDeliveryFeeDTO.setContactId(Long.parseLong(result.getData().getOutBusinessNo()));
        return httpToManageUtil.postJsonToManage("/order/delivery-fee/notify", notifyDeliveryFeeDTO);
    }

    /**
     * 点位移除路线
     * /order/delivery-path/remove/9681219
     */
    @PostMapping("/upsert/site-remove")
    public TmsResult<Void> siteRemove(@RequestParam Long deliverySiteId) {
        TmsAssert.notNull(deliverySiteId, ErrorCodeEnum.PARAM_NOT_NULL, "deliverySiteId");
        DeliverySiteDTO deliverySiteDTO = deliverySiteService.queryById(deliverySiteId);
        if (deliverySiteDTO == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.NOT_FIND, "点位"));
        }
        //禁止对已拦截点位进行修改路线操作
        //cancel(1, "取消"),本应该判断 == 1,deliverySiteDTO.setInterceptState(deliverySiteEntity.getDeliverySiteInterceptState().getCode()-1);
        if (deliverySiteDTO.getInterceptState() != null && deliverySiteDTO.getInterceptState() == 0){
            return TmsResult.fail(ErrorCodeEnum.DELIVERY_SITE_INTERCEPTED.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DELIVERY_SITE_INTERCEPTED, deliverySiteDTO.getId()));
        }
        if(deliveryBatchService.deliveryBatchValidator(deliverySiteDTO.getDeliveryBatchId())){
            return TmsResult.fail(ErrorCodeEnum.DB_DATA_ERROR.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DB_DATA_ERROR, "已经完成排线"));
        }
        return deliverySiteService.siteRemove(deliverySiteId);
    }

    /**
     * 是否存在换货
     * /order/delivery-path/isHaveExchange/9681666
     */
    @PostMapping("/query/is-have-exchange/{deliverySiteId}")
    public TmsResult<Boolean> isHaveExchange(@PathVariable Long deliverySiteId) {
        return deliverySiteService.isHaveExchange(deliverySiteId);
    }

    /**
     * 查询打卡信息
     */
    @PostMapping("/query/punch")
    public TmsResult<PunchVO> queryPunch(){
        PunchVO punchVO = new PunchVO();
        //查询当前司机当天的配送批次信息
        TmsResult<DeliveryBatchDTO> batchResult = deliveryBatchService.queryCurrentDriverBatch(DeliveryBatchQuery.builder()
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .deliveryTime(LocalDate.now().atStartOfDay())
                .deliveryBatchStatusList(Arrays.asList(DeliveryBatchStatusEnum.TO_BE_PICKED.getCode(),
                        DeliveryBatchStatusEnum.IN_DELIVERY.getCode(),
                        DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode()))
                .build());

        if(!batchResult.isSuccess()){
            return TmsResult.fail(batchResult.getErrCode(), batchResult.getErrorMessage());
        }
        DeliveryBatchDTO deliveryBatchDTO = batchResult.getData();
        if(deliveryBatchDTO == null){
            punchVO.setState(0);
        }else{
            TmsResult<DeliverySiteDTO> deliverySiteDTOTmsResult = deliverySiteService.queryPunch(deliveryBatchDTO);
            if(!deliverySiteDTOTmsResult.isSuccess()){
                return TmsResult.fail(deliverySiteDTOTmsResult.getErrCode(), deliverySiteDTOTmsResult.getErrorMessage());
            }
            DeliverySiteDTO deliverySiteDTO = deliverySiteDTOTmsResult.getData();
            if(deliverySiteDTO.getSignInTime() != null){
                //是否已打卡
                punchVO.setPunchTime(deliverySiteDTO.getSignInTime());
                punchVO.setState(2);
            }else{
                punchVO.setPoi(deliverySiteDTO.getShouldPunchPoi());
                punchVO.setState(deliverySiteDTO.isNeedPunch() ? 1 : 0);
            }
            punchVO.setPunchDistance(deliverySiteDTO.getShouldPunchDistance());
            punchVO.setRealPunchDistance(deliverySiteDTO.getSignInDistance());
            punchVO.setDeliverySiteId(deliverySiteDTO.getId());
        }
        return TmsResult.success(punchVO);
    }

    /**
     * 获取是否已经点击开始配送
     */
    @PostMapping("/query/isBeginDelivery")
    public TmsResult<Boolean> isBeginDelivery(@RequestParam Long batchId){
        return deliverySiteService.isBeginDelivery(batchId);
    }
    /**
     * 获取规定出仓时间
     */
    @PostMapping("/query/out-time")
    public TmsResult<String> queryOutTime(){
        TmsResult<DriverDTO>  driverDTOTmsResult = driverService.getCurrentDriver();
        if(!driverDTOTmsResult.isSuccess()){
            return TmsResult.fail(driverDTOTmsResult.getErrCode(), driverDTOTmsResult.getErrorMessage());
        }
        DriverDTO driverDTO = driverDTOTmsResult.getData();
        if(driverDTO == null || driverDTO.getCityStoreNo() == null){
            return TmsResult.success("");
        }
        TmsResult<SiteDTO> result = siteService.siteDetail(Long.parseLong(String.valueOf(driverDTO.getCityStoreNo())), TmsSiteTypeEnum.STORE.getCode());
        if(!result.isSuccess()){
            return TmsResult.fail(result.getErrCode(), result.getErrorMessage());
        }
        return TmsResult.success(result.getData().getOutTime());
    }

    /**
     * 获取点位配送详情小程序端
     */
    @PostMapping("/query/delivery-site-detail")
    public TmsResult<DeliverySiteDetailVO> queryDeliverySiteDetail(@RequestParam Long deliverySiteId){
        TmsResult<DeliverySiteDTO> deliverySiteDTOTmsResult = deliverySiteService.queryDeliverySiteDetailApp(deliverySiteId);
        if(!deliverySiteDTOTmsResult.isSuccess()){
            return TmsResult.fail(deliverySiteDTOTmsResult.getErrCode(), deliverySiteDTOTmsResult.getErrorMessage());
        }
        return TmsResult.success(DeliverySiteVoConverter.dto2DeliverySiteDetailVo(deliverySiteDTOTmsResult));
    }

    /**
     * 获取点位配送详情PC端
     */
    @PostMapping("/query/delivery-site-detail-pc")
    public TmsResult<DeliverySiteDetailPcVO> queryDeliverySiteDetailPc(@RequestParam Long deliverySiteId){
        TmsResult<DeliverySiteDTO> deliverySiteDTOTmsResult = deliverySiteService.queryDeliverySiteDetail(deliverySiteId);
        DeliverySiteDTO deliverySiteDTO = deliverySiteDTOTmsResult.getData();
        //点位配送详情信息
        List<DeliverySiteItemDTO> deliverySiteItemDTOList = deliverySiteDTO.getDeliverySiteItemDTOList();
        for (DeliverySiteItemDTO deliverySiteItemDTO : deliverySiteItemDTOList) {
            List<DeliverySiteItemCodeDTO> deliverySiteItemCodeDTOS = deliverySiteItemDTO.getDeliverySiteItemCodeDTOS();
            if(CollectionUtils.isEmpty(deliverySiteItemCodeDTOS)){
                continue;
            }
            List<String> batchCodeList = deliverySiteItemCodeDTOS.stream().map(DeliverySiteItemCodeDTO::getOnlyCode).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(batchCodeList)){
                StringJoiner batches = new StringJoiner(",");
                Set<String> batchCodeSet = batchCodeList.stream().map(batchCode -> batchCode.split(Global.CODE_SEPARATING_SYMBOL)[0] + Global.CODE_SEPARATING_SYMBOL).collect(Collectors.toSet());
                batchCodeSet.forEach(batchCode -> {
                    try {
                        SkuBatchCodeDTO skuBatchCodeDTO = wmsQueryFacade.querySkuByBatchCode(batchCode);
                        batches.add(skuBatchCodeDTO.getPurchaseNo());
                    } catch (Exception e) {
                        log.error("请求WMS获取批次异常:{}",batchCode,e);
                    }
                });
                deliverySiteItemDTO.setBatchs(batches.toString());
            }
        }
        DeliveryBatchDTO deliveryBatchDTO = deliverySiteDTO.getDeliveryBatchDTO();
        TmsResult<DeliverySiteDTO> storeSiteDTORes = deliverySiteService.query(DeliverySiteQuery.builder()
                .siteId(deliveryBatchDTO.getBeginSiteId())
                .batchId(deliveryBatchDTO.getDeliveryBatchId()).build());
        if(!storeSiteDTORes.isSuccess()){
            return TmsResult.fail(storeSiteDTORes.getErrCode(), storeSiteDTORes.getErrorMessage());
        }
        deliverySiteDTO.setStoreSiteDTO(storeSiteDTORes.getData());
        return TmsResult.success(DeliverySiteVoConverter.dto2DeliverySiteDetailPcVo(deliverySiteDTOTmsResult));
    }

    /**
     * 点位缺货数量
     */
    @PostMapping("/upsert/site-short")
    public TmsResult<Void> siteShort(@RequestBody @Validated SiteShortCommand siteShortCommand){
        return deliverySiteService.siteShort(siteShortCommand);
    }

    /**
     * 司机评价详情
     */
    @PostMapping("/query/driver-appraise")
    public TmsResult<List<DriverAppraiseVO>> queryDriverAppraise(@RequestParam Long siteId){
        TmsAssert.notNull(siteId, ErrorCodeEnum.PARAM_NOT_NULL, "siteId");
        List<TmsDriverAppraiseDTO> tmsDriverAppraiseDTOS = deliverySiteService.queryDriverAppraise(siteId);
        return TmsResult.success(tmsDriverAppraiseDTOS.stream().map(DeliverySiteVoConverter::driverAppraiseDTO2VO).collect(Collectors.toList()));
    }

    /**
     * 到店打卡
     */
    @PostMapping("/upsert/cheakin-punch")
    public TmsResult<Void> cheakinPunch(@RequestBody @Validated CheakInPunchSaveCommand cheakInPunchSaveCommand){
        deliverySiteService.cheakinPunch(cheakInPunchSaveCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 查询城配回收类型
     */
    @PostMapping("/query/city-recycle-type")
    public TmsResult<List<CityRecycleTypeVO>> queryCityRecycleType() {
        DeliverySiteItemRecycleEnums.ReasonType[] values = DeliverySiteItemRecycleEnums.ReasonType.values();
        List<CityRecycleTypeVO> cityRecycleTypes = new ArrayList<>();

        for (DeliverySiteItemRecycleEnums.ReasonType value : values) {
            CityRecycleTypeVO cityRecycleType = new CityRecycleTypeVO();
            cityRecycleType.setType(value.getValue());
            cityRecycleType.setTypeName(value.getContent());
            cityRecycleTypes.add(cityRecycleType);
        }
        return TmsResult.success(cityRecycleTypes);
    }

    /**
     * 查询SKU货品单位信息
     */
    @PostMapping("/query/sku-unit")
    public TmsResult<SkuUnitDTO> querySkuUnit(@RequestBody @Validated SkuUnitQuery skuUnitQuery) {
        SkuUnitDTO skuUnitDTO = deliverySiteService.querySkuUnit(skuUnitQuery);
        return TmsResult.success(skuUnitDTO);
    }

    /**
     * 配送点位物品状态数据初始化
     */
    @PostMapping("/site-item/status-data-init")
    public TmsResult<Void> siteItemStatusDataInit() {
        deliverySiteService.siteItemStatusDataInit();
        return TmsResult.VOID_SUCCESS;
    }


    /**
     * 城配-拣货单扫码
     */
    @PostMapping("/upsert/pick-scan")
    @XmLock(prefixKey = RedisConstants.Delivery.CITY_PICK_SCAN_CODE, key = "{command.batchId}",waitTime = 1000, message = "操作频繁，请稍后重试")
    public CommonResult<PickScanVO> pickScan(@RequestBody @Validated PickScanCommand command) {
        return CommonResult.ok(PickScanVO.builder().sku(deliverySiteService.pickScan(command)).build());
    }

    /**
     * 城配-拣货单缺货
     */
    @PostMapping("/upsert/pick-short")
    @XmLock(prefixKey = RedisConstants.Delivery.CITY_PICK_SHORT, key = "{command.deliveryPickId}",waitTime = 1000, message = "操作频繁，请稍后重试")
    public CommonResult<Void> pickShort(@RequestBody @Validated PickShortCommand command) {
        deliverySiteService.pickShort(command);
        return CommonResult.ok();
    }
}
