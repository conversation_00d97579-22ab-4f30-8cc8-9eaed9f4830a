package net.summerfarm.tms.controller.base.site;

import com.aliyun.odps.simpleframework.xml.core.Validate;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteBatchUpdateCommand;
import net.summerfarm.tms.base.site.dto.SiteCommand;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.exceptions.HandleException;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.summerfarm.tms.query.site.SiteIdQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 点位 接口
 */
@RestController
@RequestMapping("/tms-new/site")
@HandleException
public class SiteController {

    @Autowired
    private SiteService siteService;


    /**
     * 批量更新城配仓打卡规则
     *
     * @return
     */
    @PostMapping("/batchUpdateCitySiteRule")
    public TmsResult<Void> batchUpdateCitySiteRule(@RequestBody SiteBatchUpdateCommand siteBatchUpdateCommand) {
        return siteService.batchUpdateCitySiteRule(siteBatchUpdateCommand);
    }

    /**
     * 点位搜索
     */
    @PostMapping("/query/site-search")
    public TmsResult<List<SiteDTO>> siteSearch(@RequestBody SiteQuery siteQuery) {
        return siteService.siteSearch(siteQuery);
    }

    /**
     * 销售自提点位搜索
     */
    @PostMapping("/query/site-search-self")
    public TmsResult<List<SiteDTO>> siteSearchAddress() {
        return siteService.siteSearchAddress();
    }

    /**
     * 详细地址点位新增(采购在用)
     */
    @PostMapping("/upsert/site-add")
    public TmsResult<SiteDTO> siteAdd(@RequestBody SiteDTO siteDTO) {
        return siteService.siteAdd(siteDTO);
    }

    /**
     * 新增点位
     * @param siteCommand 命令
     * @return 结果
     */
    @PostMapping("/upsert/save")
    public TmsResult<Long> saveSite(@RequestBody @Validate SiteCommand siteCommand) {
        return TmsResult.success(siteService.saveSite(siteCommand));
    }

    /**
     * 查询点位详情
     * @param siteIdQuery 查询
     * @return 结果
     */
    @PostMapping("/query/detail")
    public TmsResult<SiteDTO> queryDetail(@RequestBody @Validate SiteIdQuery siteIdQuery) {
        return TmsResult.success(siteService.queryDetail(siteIdQuery));
    }

    /**
     * 点位搜索过滤未开放版本
     */
    @PostMapping("/query/valid-site-search")
    public TmsResult<List<SiteDTO>> validSiteSearch(@RequestBody SiteQuery siteQuery) {
        return TmsResult.success(siteService.validSiteSearch(siteQuery));
    }

    /**
     * 点位POI修改（后门接口）
     */
    @GetMapping("/upsert/poi-change")
    public TmsResult<Void> poiChange(String outOrderId,String poi) {
        if(StringUtils.isBlank(outOrderId) || StringUtils.isBlank(poi)){
            throw new TmsRuntimeException("outOrderId or poi is not null");
        }
        siteService.poiChange(outOrderId,poi);
        return TmsResult.VOID_SUCCESS;
    }
}
