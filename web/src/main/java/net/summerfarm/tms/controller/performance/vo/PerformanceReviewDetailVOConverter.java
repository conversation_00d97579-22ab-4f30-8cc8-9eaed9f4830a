package net.summerfarm.tms.controller.performance.vo;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.enums.CarStorageEnum;
import net.summerfarm.tms.enums.CarTypeEnum;
import net.summerfarm.tms.performance.dto.DeliveryPerformReviewDetailDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/7/5 10:40<br/>
 *
 * <AUTHOR> />
 */
public class PerformanceReviewDetailVOConverter {

    public static PageInfo<ReviewDetailCitySignOutVO> citySignOutPage2VOPage(PageInfo<DeliveryPerformReviewDetailDTO> reviewDetailDTOPageInfo) {
        if (reviewDetailDTOPageInfo == null) {
            return null;
        }
        List<DeliveryPerformReviewDetailDTO> deliveryPerformReviewDetailDTOs = reviewDetailDTOPageInfo.getList();

        PageInfo<ReviewDetailCitySignOutVO> reviewDetailCitySignOutVOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(reviewDetailDTOPageInfo, reviewDetailCitySignOutVOPageInfo);
        if (CollectionUtils.isEmpty(deliveryPerformReviewDetailDTOs)) {
            return reviewDetailCitySignOutVOPageInfo;
        }

        reviewDetailCitySignOutVOPageInfo.setList(deliveryPerformReviewDetailDTOs.stream().map(PerformanceReviewDetailVOConverter::citySignOutDTO2VO).collect(Collectors.toList()));

        return reviewDetailCitySignOutVOPageInfo;
    }

    public static ReviewDetailCitySignOutVO citySignOutDTO2VO(DeliveryPerformReviewDetailDTO deliveryPerformReviewDetailDTO) {
        if (deliveryPerformReviewDetailDTO == null) {
            return null;
        }

        ReviewDetailCitySignOutVO reviewDetailCitySignOutVO = new ReviewDetailCitySignOutVO();
        reviewDetailCitySignOutVO.setPerformanceReviewDetailId(deliveryPerformReviewDetailDTO.getId());
        reviewDetailCitySignOutVO.setPerformanceReviewTaskId(deliveryPerformReviewDetailDTO.getPerformanceReviewTaskId());
        if (deliveryPerformReviewDetailDTO.getDeliveryTime() != null) {
            reviewDetailCitySignOutVO.setDeliveryTime(deliveryPerformReviewDetailDTO.getDeliveryTime().toLocalDate());
        }
        reviewDetailCitySignOutVO.setBeginName(deliveryPerformReviewDetailDTO.getBeginSiteName());
        reviewDetailCitySignOutVO.setPathCode(deliveryPerformReviewDetailDTO.getPathCode());
        reviewDetailCitySignOutVO.setPathName(deliveryPerformReviewDetailDTO.getPathName());
        reviewDetailCitySignOutVO.setSignOutTemperature(deliveryPerformReviewDetailDTO.getSignOutTemperature());
        reviewDetailCitySignOutVO.setDriverName(deliveryPerformReviewDetailDTO.getDriverName());
        reviewDetailCitySignOutVO.setDriverPhone(deliveryPerformReviewDetailDTO.getDriverPhone());
        reviewDetailCitySignOutVO.setCarNumber(deliveryPerformReviewDetailDTO.getCarNumber());
        if (CarTypeEnum.getCarTypeByCode(deliveryPerformReviewDetailDTO.getCarType()) != null) {
            reviewDetailCitySignOutVO.setCarType(CarTypeEnum.getCarTypeByCode(deliveryPerformReviewDetailDTO.getCarType()).getDesc());
        }
        if (CarStorageEnum.getCarStorageByCode(deliveryPerformReviewDetailDTO.getCarStorage()) != null) {
            reviewDetailCitySignOutVO.setStorageDesc(CarStorageEnum.getCarStorageByCode(deliveryPerformReviewDetailDTO.getCarStorage()).getName());
        }
        reviewDetailCitySignOutVO.setLoadPics(deliveryPerformReviewDetailDTO.getCityLoadPics());
        reviewDetailCitySignOutVO.setVehiclePlatePics(deliveryPerformReviewDetailDTO.getCityVehiclePlatePics());
        reviewDetailCitySignOutVO.setRefrigeratePics(deliveryPerformReviewDetailDTO.getRefrigeratePics());
        reviewDetailCitySignOutVO.setFreezePics(deliveryPerformReviewDetailDTO.getFreezePics());
        reviewDetailCitySignOutVO.setState(deliveryPerformReviewDetailDTO.getState());
        reviewDetailCitySignOutVO.setPenaltyMoney(deliveryPerformReviewDetailDTO.getPenaltyMoney());
        reviewDetailCitySignOutVO.setNonFruitFreezeNum(deliveryPerformReviewDetailDTO.getNonFruitFreezeNum());
        reviewDetailCitySignOutVO.setNonFruitColdNum(deliveryPerformReviewDetailDTO.getNonFruitColdNum());
        reviewDetailCitySignOutVO.setAppealStatus(deliveryPerformReviewDetailDTO.getAppealStatus());
        if (StringUtils.isNotBlank(deliveryPerformReviewDetailDTO.getSitePicReason())) {
            reviewDetailCitySignOutVO.setSitePicReasons(Arrays.asList(deliveryPerformReviewDetailDTO.getSitePicReason().split(",")));
        }
        reviewDetailCitySignOutVO.setDeliverySiteId(deliveryPerformReviewDetailDTO.getDeliverySiteId());

        return reviewDetailCitySignOutVO;
    }

    public static PageInfo<ReviewDetailCitySignInVO> citySignInPage2VOPage(PageInfo<DeliveryPerformReviewDetailDTO> reviewDetailDTOPageInfo) {
        if (reviewDetailDTOPageInfo == null) {
            return null;
        }
        List<DeliveryPerformReviewDetailDTO> deliveryPerformReviewDetailDTOs = reviewDetailDTOPageInfo.getList();

        PageInfo<ReviewDetailCitySignInVO> reviewDetailCitySignInVOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(reviewDetailDTOPageInfo, reviewDetailCitySignInVOPageInfo);
        if (CollectionUtils.isEmpty(deliveryPerformReviewDetailDTOs)) {
            return reviewDetailCitySignInVOPageInfo;
        }

        reviewDetailCitySignInVOPageInfo.setList(deliveryPerformReviewDetailDTOs.stream().map(PerformanceReviewDetailVOConverter::citySignInDTO2VO).collect(Collectors.toList()));

        return reviewDetailCitySignInVOPageInfo;
    }

    public static ReviewDetailCitySignInVO citySignInDTO2VO(DeliveryPerformReviewDetailDTO deliveryPerformReviewDetailDTO) {
        if(deliveryPerformReviewDetailDTO == null){
            return null;
        }
        ReviewDetailCitySignInVO reviewDetailCitySignInVO = new ReviewDetailCitySignInVO();
        reviewDetailCitySignInVO.setPerformanceReviewDetailId(deliveryPerformReviewDetailDTO.getId());
        reviewDetailCitySignInVO.setPerformanceReviewTaskId(deliveryPerformReviewDetailDTO.getPerformanceReviewTaskId());
        if (deliveryPerformReviewDetailDTO.getDeliveryTime() != null) {
            reviewDetailCitySignInVO.setDeliveryTime(deliveryPerformReviewDetailDTO.getDeliveryTime().toLocalDate());
        }
        reviewDetailCitySignInVO.setBeginName(deliveryPerformReviewDetailDTO.getBeginSiteName());
        reviewDetailCitySignInVO.setPathCode(deliveryPerformReviewDetailDTO.getPathCode());
        reviewDetailCitySignInVO.setPathName(deliveryPerformReviewDetailDTO.getPathName());
        reviewDetailCitySignInVO.setDriverName(deliveryPerformReviewDetailDTO.getDriverName());
        reviewDetailCitySignInVO.setDriverPhone(deliveryPerformReviewDetailDTO.getDriverPhone());
        reviewDetailCitySignInVO.setCarNumber(deliveryPerformReviewDetailDTO.getCarNumber());
        if (CarTypeEnum.getCarTypeByCode(deliveryPerformReviewDetailDTO.getCarType()) != null) {
            reviewDetailCitySignInVO.setCarType(CarTypeEnum.getCarTypeByCode(deliveryPerformReviewDetailDTO.getCarType()).getDesc());
        }
        if (CarStorageEnum.getCarStorageByCode(deliveryPerformReviewDetailDTO.getCarStorage()) != null) {
            reviewDetailCitySignInVO.setStorageDesc(CarStorageEnum.getCarStorageByCode(deliveryPerformReviewDetailDTO.getCarStorage()).getName());
        }
        reviewDetailCitySignInVO.setSequence(deliveryPerformReviewDetailDTO.getSequence());
        reviewDetailCitySignInVO.setSendWay(deliveryPerformReviewDetailDTO.getSendWay());
        reviewDetailCitySignInVO.setAddress(deliveryPerformReviewDetailDTO.getSiteAddress());
        reviewDetailCitySignInVO.setClientName(deliveryPerformReviewDetailDTO.getOuterClientName());
        reviewDetailCitySignInVO.setOutDistance(deliveryPerformReviewDetailDTO.getOutDistance());
        reviewDetailCitySignInVO.setTemperatureConditions(deliveryPerformReviewDetailDTO.getTemperatureConditions());
        reviewDetailCitySignInVO.setBrandName(deliveryPerformReviewDetailDTO.getOuterBrandName());

        // 兼容历史数据，原来是业务场景按照OutDistance判定是否正常、异常签收
        reviewDetailCitySignInVO.setSignInStatus(deliveryPerformReviewDetailDTO.getSignInStatus() != null ? deliveryPerformReviewDetailDTO.getSignInStatus() : deliveryPerformReviewDetailDTO.getOutDistance());
        reviewDetailCitySignInVO.setSignInRemark(deliveryPerformReviewDetailDTO.getSignInRemark());
        reviewDetailCitySignInVO.setDeliveryPic(deliveryPerformReviewDetailDTO.getCityDeliveryPic());
        reviewDetailCitySignInVO.setSignPic(deliveryPerformReviewDetailDTO.getCitySignPic());
        reviewDetailCitySignInVO.setProductPic(deliveryPerformReviewDetailDTO.getCityProductPic());
        reviewDetailCitySignInVO.setState(deliveryPerformReviewDetailDTO.getState());
        reviewDetailCitySignInVO.setPenaltyMoney(deliveryPerformReviewDetailDTO.getPenaltyMoney());
        reviewDetailCitySignInVO.setAppealStatus(deliveryPerformReviewDetailDTO.getAppealStatus());
        reviewDetailCitySignInVO.setSignInStatus(deliveryPerformReviewDetailDTO.getSignInStatus());
        reviewDetailCitySignInVO.setSignInRemark(deliveryPerformReviewDetailDTO.getSignInRemark());
        if (StringUtils.isNotBlank(deliveryPerformReviewDetailDTO.getSitePicReason())) {
            reviewDetailCitySignInVO.setSitePicReasons(Arrays.asList(deliveryPerformReviewDetailDTO.getSitePicReason().split(",")));
        }
        return reviewDetailCitySignInVO;
    }

}