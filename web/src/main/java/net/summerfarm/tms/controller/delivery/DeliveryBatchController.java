package net.summerfarm.tms.controller.delivery;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.AbstractPageQuery;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.controller.delivery.vo.DeliveryBatchVO;
import net.summerfarm.tms.controller.delivery.vo.DeliveryBatchVOConverter;
import net.summerfarm.tms.controller.delivery.vo.DeliverySiteVO;
import net.summerfarm.tms.controller.delivery.vo.DeliverySiteVOConverter;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.exceptions.HandleException;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * 公共-配送批次相关接口
 */
@RestController
@HandleException
@RequestMapping("/tms-new/delivery-batch")
public class DeliveryBatchController {

    @Resource
    private DeliveryBatchService deliveryBatchService;

    /**
     * 分页查询运输任务
     */
    @PostMapping("/query/task-page")
    public TmsResult<PageInfo<DeliveryBatchVO>> queryTaskPage(@RequestBody DeliveryBatchQuery deliveryBatchQuery) {
        deliveryBatchQuery.initTrunkType();
        deliveryBatchQuery.setSortList(Collections.singletonList(new AbstractPageQuery.PageSortInput("plan_begin_time", "desc")));
        return TmsResult.success(DeliveryBatchVOConverter.dtoPage2Vo(deliveryBatchService.queryTaskPage(deliveryBatchQuery)));
    }

    /**
     * 查询运输任务详情
     */
    @PostMapping("/query/task-detail")
    public TmsResult<DeliveryBatchVO> queryTaskDetail(Long deliveryBatchId) {
        //调度单信息
        return TmsResult.success(DeliveryBatchVOConverter.dto2Vo(deliveryBatchService.queryTaskDetail(deliveryBatchId).getData()));
    }

    /**
     * 查看打卡记录
     */
    @PostMapping("/query/punch-detail")
    public TmsResult<List<DeliverySiteVO>> punchDetail(Long deliveryBatchId) {
        return TmsResult.success(DeliverySiteVOConverter.dto2Vo(deliveryBatchService.punchDetail(deliveryBatchId).getData()));
    }

}
