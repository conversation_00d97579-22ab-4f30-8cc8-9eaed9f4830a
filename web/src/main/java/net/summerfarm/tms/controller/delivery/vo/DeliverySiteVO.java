package net.summerfarm.tms.controller.delivery.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.base.site.dto.SiteDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: 运输点位
 * date: 2022/12/6 11:30
 *
 * <AUTHOR>
 */
@Data
public class DeliverySiteVO {

    /**
     * 运输节点ID
     */
    private Long id;
    /**
     * 运输节点类型(起点/途径点/终点)
     */
    private Integer type;
    /**
     * 运输路线序号
     */
    private Integer sequence;
    /**
     * 运输节点配送状态
     */
    private Integer status;
    /**
     * 运输节点配送状态描述
     */
    private String statusDesc;
    /**
     * 点位名称
     */
    private String siteName;
    /**
     * 点位ID
     */
    private Long siteId;
    /**
     * 点位类型
     */
    private Integer siteType;
    /**
     * 点位类型描述
     */
    private String siteTypeDesc;
    /**
     * 点位信息
     */
    private SiteDTO siteDTO;
    /**
     * 计划达到时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planArriveTime;
    /**
     * 签到点位
     */
    private String signInPoi;
    /**
     * 签到点位地址
     */
    private String signInAddress;
    /**
     * 到达打卡距离差值/千米
     */
    private BigDecimal signInDiffKm;
    /**
     * 实际到达时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signInTime;
    /**
     * 到达打卡时间差值/分
     */
    private Integer signInDiffMinute;
    /**
     * 签到错误类型
     */
    private String signInErrType;
    /**
     * 到达打卡备注
     */
    private String signInRemark;
    /**
     * 到达打卡照片
     */
    private String signInPics;
    /**
     * 签到(签收)状态
     */
    private Integer signInStatus;

    /**
     * 实际出发点位
     */
    private String signOutPoi;
    /**
     * 实际出发点位地址
     */
    private String signOutAddress;
    /**
     * 出发打卡距离差值/千米
     */
    private BigDecimal signOutDiffKm;
    /**
     * 实际出发时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signOutTime;
    /**
     * 出发打卡时间差值/分
     */
    private Integer signOutDiffMinute;
    /**
     * 出发错误类型
     */
    private String signOutErrType;
    /**
     * 出发打卡备注
     */
    private String signOutRemark;
    /**
     * 出发打卡照片
     */
    private String signOutPics;
    /**
     * 出发状态
     */
    private Integer signOutStatus;
    /**
     * 距离下一个运输点位之间的距离
     */
    private BigDecimal nextSiteDistanceKm;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;

    /**
     * 封签照片
     */
    private String sealPics;


    /**
     * 计划出发时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planOutTime;

}
