package net.summerfarm.tms.controller.performance.vo;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.performance.dto.DeliveryPerformReviewTaskDTO;
import net.summerfarm.tms.performance.dto.PerformanceBeginSiteDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/7/4 13:59<br/>
 *
 * <AUTHOR> />
 */
public class PerformanceReviewTaskVOConverter {

    public static PageInfo<PerformanceReviewTaskVO> dtoPage2taskPageVO(PageInfo<DeliveryPerformReviewTaskDTO> deliveryPerformReviewTaskDTOPageInfo){
        List<DeliveryPerformReviewTaskDTO> reviewTaskDTOS = deliveryPerformReviewTaskDTOPageInfo.getList();

        PageInfo<PerformanceReviewTaskVO> performanceReviewTaskVOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(deliveryPerformReviewTaskDTOPageInfo, performanceReviewTaskVOPageInfo);
        if(CollectionUtils.isEmpty(reviewTaskDTOS)){
            performanceReviewTaskVOPageInfo.setList(null);
            return performanceReviewTaskVOPageInfo;
        }
        List<PerformanceReviewTaskVO> performanceReviewTaskVOS = reviewTaskDTOS.stream().map(PerformanceReviewTaskVOConverter::dto2task).collect(Collectors.toList());
        performanceReviewTaskVOPageInfo.setList(performanceReviewTaskVOS);

        return performanceReviewTaskVOPageInfo;
    }

    public static PerformanceReviewTaskVO dto2task(DeliveryPerformReviewTaskDTO deliveryPerformReviewTaskDTO){
        if(deliveryPerformReviewTaskDTO == null){
            return null;
        }
        PerformanceReviewTaskVO performanceReviewTaskVO = new PerformanceReviewTaskVO();
        performanceReviewTaskVO.setId(deliveryPerformReviewTaskDTO.getId());
        performanceReviewTaskVO.setReviewTaskType(deliveryPerformReviewTaskDTO.getReviewTaskType());
        performanceReviewTaskVO.setName(deliveryPerformReviewTaskDTO.getName());
        performanceReviewTaskVO.setPenaltyStandards(deliveryPerformReviewTaskDTO.getPenaltyStandards());

        if(!CollectionUtils.isEmpty(deliveryPerformReviewTaskDTO.getStoreNames())){
            performanceReviewTaskVO.setTaskConditionType(String.join("、", deliveryPerformReviewTaskDTO.getStoreNames()));
        }
        if(!CollectionUtils.isEmpty(deliveryPerformReviewTaskDTO.getBatchTypeNames())){
            performanceReviewTaskVO.setTaskConditionType(String.join("、", deliveryPerformReviewTaskDTO.getBatchTypeNames()));
        }
        if(deliveryPerformReviewTaskDTO.getBeginDeliveryTime() != null && deliveryPerformReviewTaskDTO.getEndDeliveryTime() != null){
            performanceReviewTaskVO.setTaskConditionDeliveryTime(
                    deliveryPerformReviewTaskDTO.getBeginDeliveryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                    +"~"
                    +deliveryPerformReviewTaskDTO.getEndDeliveryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
            );
        }
        performanceReviewTaskVO.setState(deliveryPerformReviewTaskDTO.getState());
        performanceReviewTaskVO.setCreateName(deliveryPerformReviewTaskDTO.getCreateName());
        performanceReviewTaskVO.setCreateTime(deliveryPerformReviewTaskDTO.getCreateTime());
        performanceReviewTaskVO.setReviewMode(deliveryPerformReviewTaskDTO.getReviewMode());
        return performanceReviewTaskVO;
    }

    public static PerformanceReviewTaskDetailVO dto2PerformanceReviewTaskDetailVO(DeliveryPerformReviewTaskDTO deliveryPerformReviewTaskDTO){
        if(deliveryPerformReviewTaskDTO == null){
            return null;
        }
        PerformanceReviewTaskDetailVO performanceReviewTaskDetailVO = new PerformanceReviewTaskDetailVO();

        performanceReviewTaskDetailVO.setId(deliveryPerformReviewTaskDTO.getId());
        performanceReviewTaskDetailVO.setReviewTaskType(deliveryPerformReviewTaskDTO.getReviewTaskType());
        performanceReviewTaskDetailVO.setName(deliveryPerformReviewTaskDTO.getName());
        performanceReviewTaskDetailVO.setPenaltyStandards(deliveryPerformReviewTaskDTO.getPenaltyStandards());

        if(!CollectionUtils.isEmpty(deliveryPerformReviewTaskDTO.getStoreNames())){
            performanceReviewTaskDetailVO.setTaskConditionType(String.join("、", deliveryPerformReviewTaskDTO.getStoreNames()));
        }
        if(!CollectionUtils.isEmpty(deliveryPerformReviewTaskDTO.getBatchTypeNames())){
            performanceReviewTaskDetailVO.setTaskConditionType(String.join("、", deliveryPerformReviewTaskDTO.getBatchTypeNames()));
        }
        if(deliveryPerformReviewTaskDTO.getBeginDeliveryTime() != null && deliveryPerformReviewTaskDTO.getEndDeliveryTime() != null){
            performanceReviewTaskDetailVO.setTaskConditionDeliveryTime(
                    deliveryPerformReviewTaskDTO.getBeginDeliveryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                            +" ~ "
                            +deliveryPerformReviewTaskDTO.getEndDeliveryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
            );
        }
        performanceReviewTaskDetailVO.setState(deliveryPerformReviewTaskDTO.getState());
        performanceReviewTaskDetailVO.setCreateName(deliveryPerformReviewTaskDTO.getCreateName());
        performanceReviewTaskDetailVO.setCreateTime(deliveryPerformReviewTaskDTO.getCreateTime());

        performanceReviewTaskDetailVO.setAuditComplianceTotalNum(deliveryPerformReviewTaskDTO.getOkNum());
        performanceReviewTaskDetailVO.setAuditNoComplianceTotalNum(deliveryPerformReviewTaskDTO.getNoOkNum());
        performanceReviewTaskDetailVO.setWaitAuditTotalNum(deliveryPerformReviewTaskDTO.getWaitAuditNum());
        performanceReviewTaskDetailVO.setCannotAuditTotalNum(deliveryPerformReviewTaskDTO.getNoAuditNum());

        performanceReviewTaskDetailVO.setAuditDetailTotalNum(
                deliveryPerformReviewTaskDTO.getNoOkNum()
                + deliveryPerformReviewTaskDTO.getOkNum()
                + deliveryPerformReviewTaskDTO.getWaitAuditNum()
                + deliveryPerformReviewTaskDTO.getNoAuditNum()
        );

        if(!CollectionUtils.isEmpty(deliveryPerformReviewTaskDTO.getPerformanceBeginSiteDTOs())){
            List<PerformanceBeginSiteVO> performanceBeginSiteVOS = deliveryPerformReviewTaskDTO.getPerformanceBeginSiteDTOs().stream().map(PerformanceReviewTaskVOConverter::performanceSiteDTO2Vo).collect(Collectors.toList());
            performanceReviewTaskDetailVO.setPerformanceBeginSiteVOS(performanceBeginSiteVOS);
        }
        return performanceReviewTaskDetailVO;
    }

    public static PerformanceBeginSiteVO performanceSiteDTO2Vo(PerformanceBeginSiteDTO performanceBeginSiteDTO){
        if(performanceBeginSiteDTO == null){
            return null;
        }
        PerformanceBeginSiteVO performanceBeginSiteVO = new PerformanceBeginSiteVO();

        performanceBeginSiteVO.setSiteId(performanceBeginSiteDTO.getSiteId());
        performanceBeginSiteVO.setSiteName(performanceBeginSiteDTO.getSiteName());

        return performanceBeginSiteVO;
    }
}
