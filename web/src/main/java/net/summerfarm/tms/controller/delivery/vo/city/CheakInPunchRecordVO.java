package net.summerfarm.tms.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: 到店打卡记录
 * date: 2023/10/25 17:00<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CheakInPunchRecordVO {

    /**
     * 打卡时间
     */
    @JSONField(format = "MM-dd HH:mm:ss")
    private LocalDateTime punchTime;

    /**
     * 配送点位Id
     */
    private Long deliverySiteId;

    /**
     * 打卡范围km
     */
    private BigDecimal punchRange;

    /**
     * 打卡详细地址
     */
    private String punchAddress;

    /**
     * 打卡POI
     */
    private String punchPoi;

    /**
     * 打卡到店距离km
     */
    private BigDecimal distanceToStore;

    /**
     * 超区原因
     */
    private String exceedReason;

}
