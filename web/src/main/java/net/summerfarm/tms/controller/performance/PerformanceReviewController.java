package net.summerfarm.tms.controller.performance;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.controller.performance.vo.*;
import net.summerfarm.tms.delivery.DeliveryPickRepository;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempUnit;
import net.summerfarm.tms.enums.TmsTemperatureEnum;
import net.summerfarm.tms.performance.DeliveryPerformanceReviewCommandService;
import net.summerfarm.tms.performance.DeliveryPerformanceReviewQueryService;
import net.summerfarm.tms.performance.TmsDeliveryPerformanceReviewDetailAiReviewQueryRepository;
import net.summerfarm.tms.performance.dto.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewTaskEntity;
import net.summerfarm.tms.performance.entity.TmsDeliveryPerformanceReviewDetailAiReviewEntity;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewTaskRepository;
import net.summerfarm.tms.query.delivery.PerformanceReviewCityQuery;
import net.summerfarm.tms.query.delivery.PerformanceReviewDetailQuery;
import net.summerfarm.tms.query.delivery.PerformanceReviewTaskQuery;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 履约审核
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tms-new/performance-review")
@Slf4j
public class PerformanceReviewController {

    @Resource
    private DeliveryPerformanceReviewQueryService deliveryPerformanceReviewQueryService;
    @Resource
    private DeliveryPerformanceReviewCommandService deliveryPerformanceReviewCommandService;
    @Resource
    private TmsDeliveryPerformanceReviewDetailAiReviewQueryRepository tmsDeliveryPerformanceReviewDetailAiReviewQueryRepository;
    @Resource
    private DeliveryPerformanceReviewTaskRepository deliveryPerformanceReviewTaskRepository;
    @Resource
    private DeliveryPickRepository deliveryPickRepository;

    /**
     * 截单任务查看-履约信息-出仓
     */
    @PostMapping("/query/city-out-delivery-performance")
    public TmsResult<PageInfo<CityPerformanceVO>> queryCitySignOutDeliveryPerformance(@RequestBody PerformanceReviewCityQuery performanceReviewQuery) {
        TmsAssert.notNull(performanceReviewQuery.getStoreNo(), ErrorCodeEnum.PARAM_NOT_NULL, "storeNo不能为空");
        TmsAssert.notNull(performanceReviewQuery.getDeliveryTime(), ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime不能为空");

        return TmsResult.success(deliveryPerformanceReviewQueryService.queryCitySignOutDeliveryPerformance(performanceReviewQuery));
    }


    /**
     * 截单任务查看-履约信息-签收
     */
    @PostMapping("/query/city-in-delivery-performance")
    public TmsResult<PageInfo<CityPerformanceVO>> queryCitySignInDeliveryPerformance(@RequestBody PerformanceReviewCityQuery performanceReviewQuery) {
        TmsAssert.notNull(performanceReviewQuery.getStoreNo(), ErrorCodeEnum.PARAM_NOT_NULL, "storeNo不能为空");
        TmsAssert.notNull(performanceReviewQuery.getDeliveryTime(), ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime不能为空");

        return TmsResult.success(deliveryPerformanceReviewQueryService.queryCitySignInDeliveryPerformance(performanceReviewQuery));
    }

    /**
     * 履约审核-审核任务创建
     */
    @PostMapping("/upsert/create-performance-review-task")
    public TmsResult<Void> createPerformanceReviewTask(@RequestBody PerformanceReviewTaskCommand performanceReviewTaskCommand) {
        deliveryPerformanceReviewCommandService.createPerformanceReviewTask(performanceReviewTaskCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 履约审核-审核任务分页查询
     */
    @PostMapping("/query/performance-review-task")
    public TmsResult<PageInfo<PerformanceReviewTaskVO>> queryPerformanceReviewTask(@RequestBody PerformanceReviewTaskQuery performanceReviewTaskQuery) {
        PageInfo<DeliveryPerformReviewTaskDTO> deliveryPerformReviewTaskDTOPageInfo = deliveryPerformanceReviewQueryService.queryPerformanceReviewTask(performanceReviewTaskQuery);
        return TmsResult.success(PerformanceReviewTaskVOConverter.dtoPage2taskPageVO(deliveryPerformReviewTaskDTOPageInfo));
    }

    /**
     * 履约审核-审核任务-详情
     * @param performanceReviewTaskId 审核任务ID
     * @return
     */
    @PostMapping("/query/performance-review-task-detail")
    public TmsResult<PerformanceReviewTaskDetailVO> queryPerformanceReviewTaskDetail(@RequestParam Long performanceReviewTaskId) {
        DeliveryPerformReviewTaskDTO deliveryPerformReviewTaskDTO = deliveryPerformanceReviewQueryService.queryPerformanceReviewTaskDetail(performanceReviewTaskId);
        return TmsResult.success(PerformanceReviewTaskVOConverter.dto2PerformanceReviewTaskDetailVO(deliveryPerformReviewTaskDTO));
    }

    /**
     * 履约审核-签收面单关键字
     */
    @PostMapping("/query/signInPicKeyword")
    public TmsResult<List<String>> signInPicKeyword() {
        return TmsResult.success(deliveryPerformanceReviewQueryService.signInPicKeyword());
    }

    /**
     * 履约审核-审核任务-城配出仓详情
     */
    @PostMapping("/query/review-detail-city-sign-out")
    public TmsResult<PageInfo<ReviewDetailCitySignOutVO>> queryReviewDetailCitySignOut(@RequestBody PerformanceReviewDetailQuery performanceReviewDetailQuery) {
        PageInfo<DeliveryPerformReviewDetailDTO> reviewDetailDTOPageInfo = deliveryPerformanceReviewQueryService.queryReviewDetailCity(performanceReviewDetailQuery);
        PageInfo<ReviewDetailCitySignOutVO> result = PerformanceReviewDetailVOConverter.citySignOutPage2VOPage(reviewDetailDTOPageInfo);

        if (!CollectionUtils.isEmpty(result.getList())) {
            List<Long> reviewDetailIdList = result.getList().stream()
                    .map(ReviewDetailCitySignOutVO::getPerformanceReviewDetailId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, TmsDeliveryPerformanceReviewDetailAiReviewEntity> map =
                    tmsDeliveryPerformanceReviewDetailAiReviewQueryRepository
                            .mapByReviewDetailIdList(reviewDetailIdList);
            List<Long> performanceReviewTaskIdList = result.getList().stream()
                    .map(ReviewDetailCitySignOutVO::getPerformanceReviewTaskId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, DeliveryPerformanceReviewTaskEntity> taskEntityMap = deliveryPerformanceReviewTaskRepository
                    .mapByIdList(performanceReviewTaskIdList);

            List<Long> deliverySiteIdList =  result.getList().stream()
                    .map(ReviewDetailCitySignOutVO::getDeliverySiteId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, List<DeliverySiteStandardTempUnit>> deliveryTempUnitMap =
                deliveryPickRepository.queryPickStandardItemTemperatureUnitByDelSiteIds(deliverySiteIdList);

            for (ReviewDetailCitySignOutVO reviewDetailCitySignOutVO : result.getList()) {
                List<DeliverySiteStandardTempUnit> deliverySiteStandardTempUnitList = deliveryTempUnitMap.get(
                        reviewDetailCitySignOutVO.getDeliverySiteId());
                reviewDetailCitySignOutVO.setNonFruitColdBoxNum(
                        CollectionUtils.isEmpty(deliverySiteStandardTempUnitList) ? 0 :
                                deliverySiteStandardTempUnitList.stream()
                                        .filter(deliverySiteStandardTempUnit -> Integer.valueOf(TmsTemperatureEnum.COLD.getCode())
                                                .equals(deliverySiteStandardTempUnit.getTemperature()) &&
                                                "箱".equals(deliverySiteStandardTempUnit.getUnit()))
                                        .map(DeliverySiteStandardTempUnit::getCount)
                                        .reduce(0, Integer::sum)
                );
                reviewDetailCitySignOutVO.setNonFruitColdPartNum(
                        CollectionUtils.isEmpty(deliverySiteStandardTempUnitList) ? 0 :
                                deliverySiteStandardTempUnitList.stream()
                                        .filter(deliverySiteStandardTempUnit -> Integer.valueOf(TmsTemperatureEnum.COLD.getCode())
                                                .equals(deliverySiteStandardTempUnit.getTemperature()) &&
                                                !"箱".equals(deliverySiteStandardTempUnit.getUnit()))
                                        .map(DeliverySiteStandardTempUnit::getCount)
                                        .reduce(0, Integer::sum)
                );
                reviewDetailCitySignOutVO.setNonFruitFreezeBoxNum(
                        CollectionUtils.isEmpty(deliverySiteStandardTempUnitList) ? 0 :
                                deliverySiteStandardTempUnitList.stream()
                                        .filter(deliverySiteStandardTempUnit -> Integer.valueOf(TmsTemperatureEnum.FREEZE.getCode())
                                                .equals(deliverySiteStandardTempUnit.getTemperature()) &&
                                                "箱".equals(deliverySiteStandardTempUnit.getUnit()))
                                        .map(DeliverySiteStandardTempUnit::getCount)
                                        .reduce(0, Integer::sum)
                );
                reviewDetailCitySignOutVO.setNonFruitFreezePartNum(
                        CollectionUtils.isEmpty(deliverySiteStandardTempUnitList) ? 0 :
                                deliverySiteStandardTempUnitList.stream()
                                        .filter(deliverySiteStandardTempUnit -> Integer.valueOf(TmsTemperatureEnum.FREEZE.getCode())
                                                .equals(deliverySiteStandardTempUnit.getTemperature()) &&
                                                !"箱".equals(deliverySiteStandardTempUnit.getUnit()))
                                        .map(DeliverySiteStandardTempUnit::getCount)
                                        .reduce(0, Integer::sum)
                );

                TmsDeliveryPerformanceReviewDetailAiReviewEntity aiReviewEntity = map.get(
                        reviewDetailCitySignOutVO.getPerformanceReviewDetailId());
                if (aiReviewEntity == null){
                    DeliveryPerformanceReviewTaskEntity taskEntity = taskEntityMap.get(
                            reviewDetailCitySignOutVO.getPerformanceReviewTaskId());
                    if (taskEntity != null && Integer.valueOf(1).equals(taskEntity.getReviewMode())){
                        ReviewDetailAIResultVO reviewDetailAIResultVO = new ReviewDetailAIResultVO();
                        reviewDetailAIResultVO.setAiAllPass(2);
                        reviewDetailCitySignOutVO.setReviewDetailAIResultVO(reviewDetailAIResultVO);
                    }
                    continue;
                }

                ReviewDetailAIResultVO reviewDetailAIResultVO = new ReviewDetailAIResultVO();
                reviewDetailAIResultVO.setAiAllPass(aiReviewEntity.getAllPass());
                reviewDetailAIResultVO.setAiSignPicPass(aiReviewEntity.getCitySignPicPass());
                reviewDetailAIResultVO.setAiSignPicPassResults(aiReviewEntity.getCitySignPicResults());
                reviewDetailAIResultVO.setAiDeliveryPicPass(aiReviewEntity.getCityDeliveryPicPass());
                reviewDetailAIResultVO.setAiDeliveryPicPassResults(aiReviewEntity.getCityDeliveryPicResults());
                reviewDetailAIResultVO.setAiProductPicPass(aiReviewEntity.getCityProductPicPass());
                reviewDetailAIResultVO.setAiProductPicPassResults(aiReviewEntity.getCityProductPicResults());
                reviewDetailAIResultVO.setAiVehiclePlatePicPass(aiReviewEntity.getCityVehiclePlatePicPass());
                reviewDetailAIResultVO.setAiVehiclePlatePicPassResults(aiReviewEntity.getCityVehiclePlatePicResults());
                reviewDetailAIResultVO.setAiVehicleLoadPicPass(aiReviewEntity.getCityLoadPicPass());
                reviewDetailAIResultVO.setAiVehicleLoadPicPassResults(aiReviewEntity.getCityLoadPicResults());

                reviewDetailCitySignOutVO.setReviewDetailAIResultVO(reviewDetailAIResultVO);
            }
        }
        return TmsResult.success(result);
    }

    /**
     * 履约审核-审核任务-城配签收详情
     */
    @PostMapping("/query/review-detail-city-sign-in")
    public TmsResult<PageInfo<ReviewDetailCitySignInVO>> queryReviewDetailCitySignIn(@RequestBody PerformanceReviewDetailQuery performanceReviewDetailQuery) {
        PageInfo<DeliveryPerformReviewDetailDTO> reviewDetailDTOPageInfo = deliveryPerformanceReviewQueryService.queryReviewDetailCity(performanceReviewDetailQuery);
        PageInfo<ReviewDetailCitySignInVO> result = PerformanceReviewDetailVOConverter.citySignInPage2VOPage(reviewDetailDTOPageInfo);
        if (!CollectionUtils.isEmpty(result.getList())) {
            List<Long> reviewDetailIdList = result.getList().stream()
                    .map(ReviewDetailCitySignInVO::getPerformanceReviewDetailId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, TmsDeliveryPerformanceReviewDetailAiReviewEntity> map =
                    tmsDeliveryPerformanceReviewDetailAiReviewQueryRepository
                            .mapByReviewDetailIdList(reviewDetailIdList);
            List<Long> performanceReviewTaskIdList = result.getList().stream()
                    .map(ReviewDetailCitySignInVO::getPerformanceReviewTaskId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, DeliveryPerformanceReviewTaskEntity> taskEntityMap = deliveryPerformanceReviewTaskRepository
                    .mapByIdList(performanceReviewTaskIdList);

            for (ReviewDetailCitySignInVO reviewDetailCitySignInVO : result.getList()) {
                TmsDeliveryPerformanceReviewDetailAiReviewEntity aiReviewEntity = map.get(
                        reviewDetailCitySignInVO.getPerformanceReviewDetailId());
                if (aiReviewEntity == null){
                    DeliveryPerformanceReviewTaskEntity taskEntity = taskEntityMap.get(
                            reviewDetailCitySignInVO.getPerformanceReviewTaskId());
                    if (taskEntity != null && Integer.valueOf(1).equals(taskEntity.getReviewMode())){
                        ReviewDetailAIResultVO reviewDetailAIResultVO = new ReviewDetailAIResultVO();
                        reviewDetailAIResultVO.setAiAllPass(2);
                        reviewDetailCitySignInVO.setReviewDetailAIResultVO(reviewDetailAIResultVO);
                    }
                    continue;
                }

                ReviewDetailAIResultVO reviewDetailAIResultVO = new ReviewDetailAIResultVO();
                reviewDetailAIResultVO.setAiAllPass(aiReviewEntity.getAllPass());
                reviewDetailAIResultVO.setAiSignPicPass(aiReviewEntity.getCitySignPicPass());
                reviewDetailAIResultVO.setAiSignPicPassResults(aiReviewEntity.getCitySignPicResults());
                reviewDetailAIResultVO.setAiDeliveryPicPass(aiReviewEntity.getCityDeliveryPicPass());
                reviewDetailAIResultVO.setAiDeliveryPicPassResults(aiReviewEntity.getCityDeliveryPicResults());
                reviewDetailAIResultVO.setAiProductPicPass(aiReviewEntity.getCityProductPicPass());
                reviewDetailAIResultVO.setAiProductPicPassResults(aiReviewEntity.getCityProductPicResults());
                reviewDetailAIResultVO.setAiVehiclePlatePicPass(aiReviewEntity.getCityVehiclePlatePicPass());
                reviewDetailAIResultVO.setAiVehiclePlatePicPassResults(aiReviewEntity.getCityVehiclePlatePicResults());
                reviewDetailAIResultVO.setAiVehicleLoadPicPass(aiReviewEntity.getCityLoadPicPass());
                reviewDetailAIResultVO.setAiVehicleLoadPicPassResults(aiReviewEntity.getCityLoadPicResults());

                reviewDetailCitySignInVO.setReviewDetailAIResultVO(reviewDetailAIResultVO);
            }
        }
        return TmsResult.success(result);
    }


    /**
     * 履约审核-审核任务-干线签收详情
     */
    @PostMapping("/query/review-detail-trunk-sign-in")
    public TmsResult<PageInfo<ReviewDetailTrunkSignInVO>> queryReviewDetailTrunkSignIn(@RequestBody PerformanceReviewDetailQuery performanceReviewDetailQuery) {
        return TmsResult.success(deliveryPerformanceReviewQueryService.queryReviewDetailTrunkSignIn(performanceReviewDetailQuery));
    }

    /**
     * 履约审核-审核任务-状态变更
     */
    @PostMapping("/upsert/review-task-change-state")
    public TmsResult<Void> reviewTaskChangeState(@RequestBody @Validated PerformanceTaskUpdateCommand performanceTaskUpdateCommand) {
        deliveryPerformanceReviewCommandService.reviewTaskChangeState(performanceTaskUpdateCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 履约审核-审核任务-审核
     */
    @PostMapping("/upsert/review-detail-audit")
    public TmsResult<Void> reviewDetailAudit(@RequestBody @Validated List<PerformanceDetailUpdateCommand> performanceDetailUpdateCommands) {
        deliveryPerformanceReviewCommandService.reviewDetailAudit(performanceDetailUpdateCommands);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 履约审核-审核任务-导出
     * @param performanceReviewTaskQuery 审核任务ID集合
     * @return url链接
     */
    @PostMapping("/excel/review-task")
        public TmsResult<String> excelReviewTask(@RequestBody PerformanceReviewTaskQuery performanceReviewTaskQuery) {
        return TmsResult.success(deliveryPerformanceReviewQueryService.excelReviewTask(performanceReviewTaskQuery.getPerformanceReviewTaskIds()));
    }
}
