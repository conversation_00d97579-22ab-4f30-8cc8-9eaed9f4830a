package net.summerfarm.tms.controller.delivery.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:配送批次
 * date: 2022/12/7 15:20
 *
 * <AUTHOR>
 */
@Data
public class DeliveryBatchVO {

    /**
     * 调度单ID
     */
    private Long batchId;
    /**
     * 调度单状态
     */
    private Integer status;
    /**
     * 调度单状态描述
     */
    private String statusDesc;

    /**
     * 调度单类型
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer type;
    /**
     * 调度单类型描述
     */
    private String typeDesc;

    /**
     * 线路名称
     */
    private String pathName;
    /**
     * 承运商ID
     */
    private Long carrierId;
    /**
     * 承运商名称
     */
    private String carrierName;
    /**
     * 司机ID
     */
    private Long driverId;
    /**
     * 司机名称
     */
    private String driverName;
    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 车辆id
     */
    private Long carId;
    /**
     * 车牌号
     */
    private String carNum;
    /**
     * 车型
     */
    private Integer carType;
    /**
     * 车型描述
     */
    private String carTypeDesc;
    /**
     * 车辆存储条件
     */
    private String storageName;
    /**
     * 履约时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;
    /**
     * 承运时间(开始)
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;
    /**
     * 创单时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creatTime;
    /**
     * 关闭原因
     */
    private String closeReason;
    /**
     * 预估费用
     */
    private BigDecimal estimateFare;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 运输路线
     */
    private List<DeliverySiteDTO> deliverySiteList;
    /**
     * 配送单信息
     */
    private List<DeliveryOrderDTO> deliveryOrderDTOList;
    /**
     * 用车备注
     */
    private String remark;
}
