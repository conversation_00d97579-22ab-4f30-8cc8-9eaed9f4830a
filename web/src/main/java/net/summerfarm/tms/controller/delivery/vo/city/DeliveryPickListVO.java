package net.summerfarm.tms.controller.delivery.vo.city;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description: <br/>
 * date: 2022/12/23 16:33<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPickListVO implements Serializable {

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 批次 id
     */
    private Long batchId;

    /**
     * 路线
     */
    private String path;

    /**
     * 拣货数量
     */
    private long skuCnt;

    /**
     * 商家数量
     */
    private int concatCnt;

    /**
     * 任务状态，20待捡货，30配送中，40配送完成
     */
    private Integer taskStatus;

    /**
     * 拦截标识
     * 0 无回收拦截关闭  1 有回收拦截关闭
     */
    private Integer interceptFlag;

    /**
     * 是否需要扫码标识
     * true 需要扫码，false无需扫码
     */
    private Boolean needScanCodeFlag;
}
