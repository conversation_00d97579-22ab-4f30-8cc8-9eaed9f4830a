package net.summerfarm.tms.controller.delivery;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.constants.DataSychConstants;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.aspect.TmsDataPermission;
import net.summerfarm.tms.controller.aspect.TmsMethodStop;
import net.summerfarm.tms.controller.delivery.vo.city.*;
import net.summerfarm.tms.delivery.DeliveryBatchCommandService;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.delivery.group.ValidationGroups;
import net.summerfarm.tms.domain.DeliveryCarPath;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.HandleException;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.summerfarm.tms.message.out.CompletePathMessage;
import net.summerfarm.tms.message.out.StoreCompletePathMessage;
import net.summerfarm.tms.path.dto.TmsPathDTO;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryPathQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.util.ThreadLocalUtil;
import net.xianmu.redis.support.lock.annotation.XmLock;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 城配
 */

@RestController
@RequestMapping("/tms-new/city-delivery-batch")
@HandleException
@Slf4j
public class CityDeliveryBatchController {
    @Resource
    DeliverySiteService deliverySiteService;
    @Resource
    DeliveryBatchService deliveryBatchService;
    @Resource
    SiteService siteService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    DeliveryBatchCommandService deliveryBatchCommandService;
    @Resource
    private MqProducer mqProducer;

    /**
     * 开始配送
     */
    @PostMapping("/upsert/begin-delivery")
    public TmsResult<Void> beginDelivery(@RequestBody @Validated BeginDeliveryCommand beginDeliveryCommand) {
        DeliverySiteDTO deliverySiteDTO = new DeliverySiteDTO();
        deliverySiteDTO.setId(beginDeliveryCommand.getDeliverySiteId());
        deliverySiteDTO.setSignOutTime(LocalDateTime.now());
        deliverySiteDTO.setSignOutPic1(beginDeliveryCommand.getLoadingPhotos());
        deliverySiteDTO.setVehiclePlatePics(beginDeliveryCommand.getVehiclePlatePics());
        deliverySiteDTO.setSignOutTemperature(beginDeliveryCommand.getOutWarehouseTemperature());
        deliverySiteDTO.setSignOutRemark(beginDeliveryCommand.getOverOutTimeReason());
        deliverySiteDTO.setRefrigeratePics(beginDeliveryCommand.getRefrigeratePics());
        deliverySiteDTO.setFreezePics(beginDeliveryCommand.getFreezePics());
        return deliverySiteService.signOut(deliverySiteDTO);
    }

    /**
     * 排线方案刷新
     */
    @PostMapping("/query/delivery-refresh")
    public TmsResult<CityDeliveryPathVO> deliveryRefresh(@RequestParam Integer storeNo, @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate deliveryTime) {
        DeliveryPathQuery deliveryPathQuery = new DeliveryPathQuery();
        deliveryPathQuery.setStoreNo(storeNo);
        deliveryPathQuery.setEndDate(deliveryTime.plusDays(-1));

        return this.queryBatchPath(deliveryPathQuery);
    }

    /**
     * 排线方案查询
     */
    @PostMapping("/query/delivery-site")
    public TmsResult<CityDeliveryPathVO> deliverySite(@RequestBody DeliveryPathQuery deliveryPathQuery) {
        return this.queryBatchPath(deliveryPathQuery);
    }

    /**
     * 查询排线信息
     * storeNo: 1
     * startDate: 2022-10-19
     * endDate: 2022-10-20
     * mname:
     */
    @PostMapping("/query/batch-path")
    @TmsDataPermission
    public TmsResult<CityDeliveryPathVO> queryBatchPath(DeliveryPathQuery deliveryPathQuery) {
        //验证参数
        TmsAssert.notNull(deliveryPathQuery.getEndDate(), ErrorCodeEnum.PARAM_NOT_NULL, "endDate");
        TmsAssert.notNull(deliveryPathQuery.getStoreNo(), ErrorCodeEnum.PARAM_NOT_NULL, "storeNo");

        deliveryPathQuery.setDeliveryTime(deliveryPathQuery.getEndDate().plusDays(1));
        deliveryPathQuery.setDeliveryBatchType(DeliveryBatchTypeEnum.city.getCode());
        TmsResult<List<DeliveryBatchDTO>> deliveryBatchDTOTmsResult = deliveryBatchService.getBatchDetailDelivery(deliveryPathQuery);
        if (!deliveryBatchDTOTmsResult.isSuccess()) {
            return TmsResult.fail(deliveryBatchDTOTmsResult.getCode(), deliveryBatchDTOTmsResult.getErrorMessage());
        }
        List<DeliveryBatchDTO> deliveryBatchDTOList = deliveryBatchDTOTmsResult.getData();
        DeliveryBatchDTO deliveryBatchDTO = deliveryBatchDTOList.get(0);

        TmsResult<SiteDTO> result = siteService.siteDetail(deliveryBatchDTO.getBeginSiteId(), null);
        if (!result.isSuccess()) {
            return TmsResult.fail(result.getErrCode(), result.getErrorMessage());
        }
        //排除未排线的
        List<DeliveryBatchDTO> havePathBatchDTOList = deliveryBatchDTOList.stream().filter(batch -> batch.getPathId() >= 0).collect(Collectors.toList());
        CityDeliveryPathVO cityDeliveryPathVO = new CityDeliveryPathVO();
        if (!CollectionUtils.isEmpty(havePathBatchDTOList)) {
            //排线店铺最多的
            DeliveryBatchDTO maxNumBatch = havePathBatchDTOList.stream().max(Comparator.comparing(DeliveryBatchDTO::getTotalSiteNum)).get();
            //获取批次价格最多的一个
            DeliveryBatchDTO maxPriceBatch = havePathBatchDTOList.stream().max(Comparator.comparing(DeliveryBatchDTO::getPriceTotal)).get();
            //体积最大的
            DeliveryBatchDTO maxVolumeBatch = havePathBatchDTOList.stream().max(Comparator.comparing(DeliveryBatchDTO::getVolumeTotal)).get();
            //路线最大距离
            DeliveryBatchDTO maxDistanceBatch = havePathBatchDTOList.stream().max(Comparator.comparing(DeliveryBatchDTO::getPlanTotalDistance)).get();
            if(maxDistanceBatch.getPlanTotalDistance().compareTo(new BigDecimal(0)) > 0 ){
                cityDeliveryPathVO.setMaxDistance(PathMaxVO.builder().path(maxDistanceBatch.getPathCode()).value(maxDistanceBatch.getPlanTotalDistance().toString()).build());
            }
            cityDeliveryPathVO.setMaxNumber(PathMaxVO.builder().path(maxNumBatch.getPathCode()).value(maxNumBatch.getTotalSiteNum().toString()).build());
            cityDeliveryPathVO.setMaxPrice(PathMaxVO.builder().path(maxPriceBatch.getPathCode()).value(maxPriceBatch.getPriceTotal().toString()).build());
            cityDeliveryPathVO.setMaxVolume(PathMaxVO.builder().path(maxVolumeBatch.getPathCode()).value(maxVolumeBatch.getVolumeTotal().toString()).build());
        }
        SiteDTO siteDTO = result.getData();
        cityDeliveryPathVO.setAreaInfo(CityDeliveryPathVoConverter.siteDto2Vo(siteDTO));
        //按照路线排序
        List<DeliveryBatchDTO> haveSortBatchDtoList = havePathBatchDTOList.stream().sorted(Comparator.comparing(DeliveryBatchDTO::getPathCode)).collect(Collectors.toList());

        List<DeliveryPathSortVO> sort = new ArrayList<>();
        for (DeliveryBatchDTO batchDTO : haveSortBatchDtoList) {
            batchDTO.setBeginSiteDto(siteDTO);
            DeliveryPathSortVO deliveryPathSortVO = new DeliveryPathSortVO();
            deliveryPathSortVO.setDeliveryCarPath(CityDeliveryPathVoConverter.deBatchDTO2DePathVo(batchDTO,siteDTO));
            deliveryPathSortVO.setPath(batchDTO.getPathCode());
            if (!CollectionUtils.isEmpty(batchDTO.getDeliverySiteDTOList())) {
                List<DeliveryPathVO> deliveryPathVOList = batchDTO.getDeliverySiteDTOList().stream().map(CityDeliveryPathVoConverter::deSiteDTO2DpVo).collect(Collectors.toList());
                //过滤没有完成时间的数据，按照完成配送时间排序
                List<DeliveryPathVO> finishDeliveryList = deliveryPathVOList.stream()
                        .filter(deliveryPath -> deliveryPath.getFinishDeliveryTime() != null)
//                        .filter(deliveryPath -> deliveryPath.getSendWay() != 1)
                        .sorted((Comparator.comparing(DeliveryPathVO::getFinishDeliveryTime))).collect(Collectors.toList());
                //设置路线和完成配送时间的
                HashMap<Long, Integer> idFinishNoMap = new HashMap<>();
                if(!CollectionUtils.isEmpty(finishDeliveryList)){
                    for (int i = 0; i < finishDeliveryList.size(); i++) {
                        idFinishNoMap.put(finishDeliveryList.get(i).getId(),i+1);
                    }
                }
                //设置完成序号
                deliveryPathVOList.forEach(deliveryPathVO ->{
                    deliveryPathVO.setFinishNo(idFinishNoMap.get(deliveryPathVO.getId()));
                });
                deliveryPathVOList.forEach(deliveryPathVO -> {
                    deliveryPathVO.setStoreNo(Integer.parseInt(siteDTO.getOutBusinessNo()));
                    deliveryPathVO.setPath(batchDTO.getPathCode());
                    if(batchDTO.getStatus() >= DeliveryBatchStatusEnum.IN_DELIVERY.getCode() &&
                            !Objects.equals(deliveryPathVO.getPathStatus(), 2)){
                        deliveryPathVO.setPathStatus(1);
                    }
                    if(batchDTO.getStatus() == DeliveryBatchStatusEnum.TO_BE_PICKED.getCode() &&
                            !Objects.equals(deliveryPathVO.getPathStatus(), 2)){
                        deliveryPathVO.setPathStatus(0);
                    }
                });
                List<DeliveryPathVO> sortDeliveryPath = deliveryPathVOList.stream().sorted(Comparator.comparing(DeliveryPathVO::getSort,Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
                deliveryPathSortVO.setList(sortDeliveryPath);
            } else {
                deliveryPathSortVO.setList(new ArrayList<>());
            }
            sort.add(deliveryPathSortVO);
        }
        cityDeliveryPathVO.setSort(sort);
        //获取未排线的店铺批次
        List<DeliveryBatchDTO> noSortBatchDtoList = deliveryBatchDTOList.stream().filter(batch -> batch.getPathId() < 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(noSortBatchDtoList)){
            throw new TmsRuntimeException(ErrorCodeEnum.NO_BATCH_ERROR);
        }
        DeliveryBatchDTO noSortDeliveryBatchDTO = noSortBatchDtoList.get(0);
        List<DeliverySiteDTO> deliverySiteDTOList = noSortDeliveryBatchDTO.getDeliverySiteDTOList();
        List<DeliveryPathVO> deliveryPathVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(deliverySiteDTOList)) {
            deliveryPathVOList = deliverySiteDTOList.stream().map(CityDeliveryPathVoConverter::deSiteDTO2DpVo).collect(Collectors.toList());
            deliveryPathVOList.forEach(deliveryPathVO -> {
                deliveryPathVO.setStoreNo(Integer.parseInt(siteDTO.getOutBusinessNo()));
                deliveryPathVO.setSort(null);
            });
            cityDeliveryPathVO.setUnSort(deliveryPathVOList);
            cityDeliveryPathVO.setUnSortNumber(deliveryPathVOList.size());
        }
        //平均路线店铺数
        BigDecimal totalPath = new BigDecimal(haveSortBatchDtoList.size() == 0 ? 1 : haveSortBatchDtoList.size());
        int totalHavePathSite = haveSortBatchDtoList.stream().mapToInt(DeliveryBatchDTO::getTotalSiteNum).sum();

        BigDecimal average = new BigDecimal(totalHavePathSite)
                .divide(totalPath, 4, BigDecimal.ROUND_HALF_DOWN);
        cityDeliveryPathVO.setAverage(average);
        if (!CollectionUtils.isEmpty(havePathBatchDTOList)) {
            cityDeliveryPathVO.setPathStatus(havePathBatchDTOList.get(0).getStatus() != DeliveryBatchStatusEnum.TO_BE_WIRED.getCode());
        } else {
            cityDeliveryPathVO.setPathStatus(false);
        }
        cityDeliveryPathVO.setSortNumber(totalHavePathSite);
        cityDeliveryPathVO.setTotalNumber(totalHavePathSite + deliveryPathVOList.size());
        cityDeliveryPathVO.setTotalPath(totalPath.intValue());
        cityDeliveryPathVO.setTotalVolume(havePathBatchDTOList.stream().map(DeliveryBatchDTO::getVolumeTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        cityDeliveryPathVO.setUnSortNumber(deliveryPathVOList.size());
        cityDeliveryPathVO.setTotalWeight(deliveryBatchDTOList.stream().map(DeliveryBatchDTO::getWeightTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        cityDeliveryPathVO.setPlanTotalDistance(deliveryBatchDTOList.stream().map(DeliveryBatchDTO::getPlanTotalDistance).reduce(BigDecimal.ZERO, BigDecimal::add));
        cityDeliveryPathVO.setIntelligenceTotalDistance(deliveryBatchDTOList.stream().map(DeliveryBatchDTO::getIntelligenceTotalDistance).reduce(BigDecimal.ZERO, BigDecimal::add));
        //路线满载率计算
        BigDecimal reduce = sort.stream().map(srotPath -> srotPath.getDeliveryCarPath().getPathFullLoadRatio()).reduce(BigDecimal.ZERO, BigDecimal::add);
        cityDeliveryPathVO.setFullLoadRatio(reduce.divide(totalPath,2,BigDecimal.ROUND_HALF_DOWN));
        return TmsResult.success(cityDeliveryPathVO);
    }

    /**
     * 新增路线和批次
     * storeNo: 1
     * deliveryTime: 2022-10-21
     */
    @PostMapping("/upsert/batch-path-Save")
    @XmLock(prefixKey = RedisConstants.Delivery.TMS_CITY_DELIVERY_BATCH_PATH_SAVE, key = "{deliveryCarPathVO.storeNo}",waitTime = 1000, message = "操作频繁，请稍后重试")
    public TmsResult<TmsPathDTO> batchPathSave(DeliveryCarPathVO deliveryCarPathVO) {
        TmsResult<SiteDTO> result = siteService.query(SiteQuery.builder()
                .outBusinessNo(String.valueOf(deliveryCarPathVO.getStoreNo()))
                .type(TmsSiteTypeEnum.STORE.getCode()).build());
        if (!result.isSuccess()) {
            return TmsResult.fail(result.getErrCode(), result.getErrorMessage());
        }
        if (result.getData() == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.FAILED, "storeNo"));
        }
        DeliveryBatchDTO deliveryBatchDTO = new DeliveryBatchDTO();
        deliveryBatchDTO.setDeliveryTime(deliveryCarPathVO.getDeliveryTime().atStartOfDay());
        deliveryBatchDTO.setBeginSiteId(result.getData().getId());
        deliveryBatchDTO.setType(DeliveryBatchTypeEnum.city.getCode());
        return deliveryBatchService.addSiteBatch(deliveryBatchDTO);
    }

    /**
     * 完成排线
     * storeNo: 1
     * startDate: 2022-11-07
     * endDate: 2022-11-08
     * deliveryCarPath: [{"storeNo":1,"totalNum":1,"path":"A","deliveryTime":"2022-11-09","deliveryCarId":331,"totalDistance":2.45},
     * {"storeNo":1,"totalNum":0,"path":"B","deliveryTime":"2022-11-09","deliveryCarId":331,"totalDistance":0}]
     */
    @PostMapping("/upsert/complete-path")
    @RequiresPermissions(value = {"tms:completeDeliveryPath"}, logical = Logical.OR)
    @TmsMethodStop
    public TmsResult<Void> completePath(@RequestBody @Validated CompletePathVO completePathVO) throws InterruptedException {
        long beginTimeMillis = System.currentTimeMillis();

        TmsResult<SiteDTO> result = siteService.query(SiteQuery.builder()
                .outBusinessNo(String.valueOf(completePathVO.getStoreNo()))
                .type(TmsSiteTypeEnum.STORE.getCode()).build());
        if (!result.isSuccess()) {
            return TmsResult.fail(result.getErrCode(), result.getErrorMessage());
        }
        if (result.getData() == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.FAILED, "storeNo"));
        }
        LocalDate deliveryTime = completePathVO.getEndDate().plusDays(1);
        List<DeliveryCarPathVO> deliveryCarPaths = completePathVO.getDeliveryCarPath();

        ArrayList<DeliveryBatchDTO> deliveryBatchDTOS = new ArrayList<>();

        deliveryCarPaths.forEach(deliveryCarPath -> {
            DeliveryBatchDTO deliveryBatchDTO = new DeliveryBatchDTO();
            deliveryBatchDTO.setDeliveryTime(deliveryTime.atStartOfDay());
            deliveryBatchDTO.setType(DeliveryBatchTypeEnum.city.getCode());
            deliveryBatchDTO.setPathCode(deliveryCarPath.getPath());
            deliveryBatchDTO.setBeginSiteId(result.getData().getId());
            deliveryBatchDTO.setTotalSiteNum(deliveryCarPath.getTotalNum());
            deliveryBatchDTO.setDriverId(deliveryCarPath.getDeliveryCarId());
            deliveryBatchDTO.setStoreNo(completePathVO.getStoreNo());

            deliveryBatchDTOS.add(deliveryBatchDTO);
        });

        StringJoiner key = new StringJoiner("-");
        String deliveryTimeStr = deliveryTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        key.add(RedisConstants.Delivery.TMS_COMPLETE_PATH).add(deliveryTimeStr).add(deliveryBatchDTOS.get(0).getBeginSiteId().toString());
        RLock redissonLock = redissonClient.getLock(key.toString());
        try {
            if (!redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS)) {
                throw new TmsRuntimeException("正在处理，请稍后");
            }
            TmsResult<Void> voidTmsResult = deliveryBatchService.completePath(deliveryBatchDTOS);
            if (voidTmsResult.isSuccess() && ThreadLocalUtil.getThreadLocal().get(DataSychConstants.Manage.COMPLETE_PATH) != null) {
                CompletePathMessage completePathMessage = new CompletePathMessage();
                completePathMessage.setDeliveryBatchIds((List<Long>) ThreadLocalUtil.getThreadLocal().get(DataSychConstants.Manage.COMPLETE_PATH));
                //完成排线通知总线,数据同步，大客户拣货、通知OFC，后续消息分发 减少时间提升性能
                deliveryBatchService.finishCompletePathAllMessageSend(completePathMessage);

                // 发送消息通知到WMS完成排线消息
                mqProducer.send(MqConstants.Topic.TMS_PATH, MqConstants.Tag.TAG_DELIVERY_STORE_COMPLETE_PATH, StoreCompletePathMessage.builder()
                        .storeNo(completePathVO.getStoreNo())
                        .deliveryTime(deliveryTime).build());
            }
            return voidTmsResult;
        } catch (Exception e) {
            if (e instanceof InterruptedException) {
                throw new TmsRuntimeException("正在处理，请稍后");
            } else if (e instanceof TmsRuntimeException) {
                throw (TmsRuntimeException) e;
            } else {
                throw e;
            }
        } finally {
            ThreadLocalUtil.removeValue(DataSychConstants.Manage.COMPLETE_PATH);
            //ThreadLocalUtil.removeValue(DataSychConstants.Manage.COMPLETE_PATH_MALL);
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
            long endTimeMillis = System.currentTimeMillis();
            log.info("城配仓:{},配送日期:{},完成排线调用耗时:{}",completePathVO.getStoreNo(),deliveryTime,endTimeMillis - beginTimeMillis);
        }
    }

    /**
     * 编辑点位路线
     * {"id":9680976,"path":"A"}
     */
    @PostMapping("/upsert/update-site-batch")
    @XmLock(prefixKey = RedisConstants.PathOpearation.TMS_PATH_OPERATION_COMMON, key = "{deliverySiteUpdateCommand.storeNo}",waitTime = 1000, message = "操作频繁，请稍后重试")
    public TmsResult<UpdateSiteBatchVO> updateSiteBatch(@RequestBody @Validated(value = {ValidationGroups.UpdateSiteBatch.class}) DeliverySiteUpdateCommand deliverySiteUpdateCommand) throws InterruptedException {
        TmsResult<DeliverySiteDTO> deliverySiteDTOTmsResult = deliverySiteService.query(DeliverySiteQuery.builder().deliverySiteId(deliverySiteUpdateCommand.getId()).build());
        if (!deliverySiteDTOTmsResult.isSuccess()) {
            return TmsResult.fail(deliverySiteDTOTmsResult.getErrCode(), deliverySiteDTOTmsResult.getErrorMessage());
        }
        //根据id获取配送点位信息
        DeliverySiteDTO deliverySiteResultData = deliverySiteDTOTmsResult.getData();
        if (deliverySiteResultData == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.NOT_FIND, "点位"));
        }
        //禁止对已拦截点位进行修改路线操作
        //cancel(1, "取消"),本应该判断 == 1,deliverySiteDTO.setInterceptState(deliverySiteEntity.getDeliverySiteInterceptState().getCode()-1);
        if (deliverySiteResultData.getInterceptState() != null && deliverySiteResultData.getInterceptState() == 0){
            return TmsResult.fail(ErrorCodeEnum.DELIVERY_SITE_INTERCEPTED.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DELIVERY_SITE_INTERCEPTED, deliverySiteResultData.getId()));
        }
        //路线为空，调用移除方法
        if(StringUtils.isBlank(deliverySiteUpdateCommand.getPath())){
            if(deliveryBatchService.deliveryBatchValidator(deliverySiteResultData.getDeliveryBatchId())){
                return TmsResult.fail(ErrorCodeEnum.DB_DATA_ERROR.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DB_DATA_ERROR, "已经完成排线"));
            }
            deliverySiteService.siteRemove(deliverySiteResultData.getId());
            return TmsResult.success(null);
        }
        TmsResult<SiteDTO> result = siteService.query(SiteQuery.builder().type(TmsSiteTypeEnum.STORE.getCode()).outBusinessNo(String.valueOf(deliverySiteUpdateCommand.getStoreNo())).build());
        if (!result.isSuccess()) {
            return TmsResult.fail(result.getErrCode(), result.getErrorMessage());
        }
        if (result.getData() == null || result.getData().getId() == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.FAILED, "storeNo"));
        }
        //根据时间和路线code获取批次
        DeliveryBatchQuery deliveryBatchQuery = DeliveryBatchQuery.builder()
                .beginSiteId(result.getData().getId())
                .deliveryTime(deliverySiteResultData.getPlanArriveTime())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .pathCode(deliverySiteUpdateCommand.getPath()).build();

        TmsResult<DeliveryBatchDTO> deliveryBatchDTOTmsResult = deliveryBatchService.query(deliveryBatchQuery);
        if (!deliveryBatchDTOTmsResult.isSuccess()) {
            return TmsResult.fail(deliveryBatchDTOTmsResult.getErrCode(), deliveryBatchDTOTmsResult.getErrorMessage());
        }
        if (deliveryBatchDTOTmsResult.getData() == null || deliveryBatchDTOTmsResult.getData().getDeliveryBatchId() == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.NOT_FIND, "路线" + deliverySiteUpdateCommand.getPath()));
        }

        RLock redissonLock = redissonClient.getLock(RedisConstants.Delivery.CHANGE_SITE_BATCH + ":"+deliveryBatchDTOTmsResult.getData().getDeliveryBatchId()+":" + deliverySiteUpdateCommand.getPath());
        if (!redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS)) {
            throw new TmsRuntimeException("正在处理，请稍后");
        }

        try {
            DeliverySiteDTO deliverySiteDTO = new DeliverySiteDTO();
            //更改点位排线
            deliverySiteDTO.setId(deliverySiteUpdateCommand.getId());
            deliverySiteDTO.setDeliveryBatchId(deliveryBatchDTOTmsResult.getData().getDeliveryBatchId());
            deliverySiteDTO.setSequence(deliverySiteUpdateCommand.getSort());
            deliverySiteDTO.setPlanArriveTime(deliverySiteResultData.getPlanArriveTime());
            deliverySiteDTO.setSendWay(deliverySiteUpdateCommand.getSendWay());
            //不是待排线变更为专车配送,特有逻辑
            if(deliveryBatchDTOTmsResult.getData().getStatus() != DeliveryBatchStatusEnum.TO_BE_WIRED.getCode() &&
                    Objects.equals(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue(),deliverySiteUpdateCommand.getSendWay())){
                deliverySiteDTO.setSendWay(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue());
                TmsResult<Void> voidTmsResult = deliverySiteService.changeSpecialCarSendWay(deliverySiteDTO);
                if (!voidTmsResult.isSuccess()) {
                    return TmsResult.fail(voidTmsResult.getErrCode(), voidTmsResult.getErrorMessage());
                }
            }else{
                if(deliveryBatchService.deliveryBatchValidator(deliverySiteResultData.getDeliveryBatchId())){
                    return TmsResult.fail(ErrorCodeEnum.DB_DATA_ERROR.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DB_DATA_ERROR, "已经完成排线"));
                }
                TmsResult<Void> voidTmsResult = deliverySiteService.changeSiteBatch(Arrays.asList(deliverySiteDTO));
                if (!voidTmsResult.isSuccess()) {
                    return TmsResult.fail(voidTmsResult.getErrCode(), voidTmsResult.getErrorMessage());
                }
            }
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }

        return TmsResult.success(DeliverySiteVoConverter.dto2UpdateSiteBatchVO(deliverySiteService.queryWithSiteBatchById(deliverySiteUpdateCommand.getId())));
    }

    /**
     * 批量修改路线
     * {"ids":[9680973,9680974,9680975,9680965,9680969,9680972],"path":"A"}
     */
    @PostMapping("/upsert/batch-update-site-route")
    @XmLock(prefixKey = RedisConstants.PathOpearation.TMS_PATH_OPERATION_COMMON, key = "{deliveryPathBatchUpdateVO.storeNo}",waitTime = 1000, message = "操作频繁，请稍后重试")
    public TmsResult<Void> batchUpdateSiteRoute(@RequestBody @Validated(value = {ValidationGroups.BatchUpdateSiteRoute.class})
                                                DeliveryPathBatchUpdateVO deliveryPathBatchUpdateVO) throws InterruptedException {
        List<Integer> ids = deliveryPathBatchUpdateVO.getIds();
        //根据id获取配送点位信息
        TmsResult<DeliverySiteDTO> deliverySiteResult = deliverySiteService.query(DeliverySiteQuery.builder().deliverySiteId(Long.parseLong(String.valueOf(ids.get(0)))).build());
        if (!deliverySiteResult.isSuccess()) {
            return TmsResult.fail(deliverySiteResult.getErrCode(), deliverySiteResult.getErrorMessage());
        }
        DeliverySiteDTO deliverySiteResultData = deliverySiteResult.getData();
        if (deliverySiteResultData == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.NOT_FIND, "点位"));
        }
        //禁止对已拦截点位进行修改路线操作
        //cancel(1, "取消"),本应该判断 == 1,deliverySiteDTO.setInterceptState(deliverySiteEntity.getDeliverySiteInterceptState().getCode()-1);
        if (deliverySiteResultData.getInterceptState() != null && deliverySiteResultData.getInterceptState() == 0){
            return TmsResult.fail(ErrorCodeEnum.DELIVERY_SITE_INTERCEPTED.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DELIVERY_SITE_INTERCEPTED, deliverySiteResultData.getId()));
        }
        //根据时间和路线code获取批次
        TmsResult<SiteDTO> siteDTOResult = siteService.query(SiteQuery.builder()
                .type(TmsSiteTypeEnum.STORE.getCode())
                .outBusinessNo(String.valueOf(deliveryPathBatchUpdateVO.getStoreNo())).build());
        if (!siteDTOResult.isSuccess()) {
            return TmsResult.fail(siteDTOResult.getErrCode(), siteDTOResult.getErrorMessage());
        }
        //根据时间和路线code获取批次
        DeliveryBatchQuery deliveryBatchQuery = DeliveryBatchQuery.builder()
                .beginSiteId(siteDTOResult.getData().getId())
                .deliveryTime(deliverySiteResultData.getPlanArriveTime())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .pathCode(deliveryPathBatchUpdateVO.getPath()).build();
        TmsResult<DeliveryBatchDTO> deliveryBatchDTOTmsResult = deliveryBatchService.query(deliveryBatchQuery);
        if (!deliveryBatchDTOTmsResult.isSuccess()) {
            return TmsResult.fail(deliveryBatchDTOTmsResult.getErrCode(), deliveryBatchDTOTmsResult.getErrorMessage());
        }
        if (deliveryBatchDTOTmsResult.getData() == null) {
            return TmsResult.fail(ErrorCodeEnum.NOT_FIND.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.NOT_FIND, "路线" + deliveryPathBatchUpdateVO.getPath()));
        }

        //更改点位排线
        ArrayList<DeliverySiteDTO> deliverySiteDTOS = new ArrayList<>();
        ids.forEach(id -> {
            DeliverySiteDTO deliverySiteDTO = new DeliverySiteDTO();
            deliverySiteDTO.setId(Long.parseLong(id.toString()));
            deliverySiteDTO.setDeliveryBatchId(deliveryBatchDTOTmsResult.getData().getDeliveryBatchId());
            deliverySiteDTO.setPlanArriveTime(deliverySiteResultData.getPlanArriveTime());
            deliverySiteDTO.setSendWay(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue());
            deliverySiteDTOS.add(deliverySiteDTO);
        });
        if(deliveryBatchService.deliveryBatchValidator(deliveryBatchDTOTmsResult.getData().getDeliveryBatchId())){
            return TmsResult.fail(ErrorCodeEnum.DB_DATA_ERROR.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DB_DATA_ERROR, "已经完成排线"));
        }

        //对路线添加锁
        TmsResult<Void> voidTmsResult;
        RLock redissonLock = redissonClient.getLock(RedisConstants.Delivery.CHANGE_SITE_BATCH + ":"+deliveryBatchDTOTmsResult.getData().getDeliveryBatchId()+":" + deliveryPathBatchUpdateVO.getPath());
        if (!redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS)) {
            throw new TmsRuntimeException("正在处理，请稍后");
        }
        try {
            //批量更新点位信息
            voidTmsResult = deliverySiteService.changeSiteBatch(deliverySiteDTOS);
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
        return voidTmsResult;
    }

    /**
     * 路线配送师傅修改
     * delivery_car_path
     * {"id":561737,"deliveryCarId":2}
     */
    @PostMapping("/upsert/update-path-driver")
    public TmsResult<Void> updatePathDriver(@RequestBody @Validated(value = {ValidationGroups.UpdatePathDriver.class}) DeliveryCarPath deliveryCarPath) {
        Integer deliveryCarId = deliveryCarPath.getDeliveryCarId();
        Integer id = deliveryCarPath.getId();

        DeliveryBatchDTO deliveryBatchDTO = new DeliveryBatchDTO();
        deliveryBatchDTO.setDeliveryBatchId(Long.parseLong(String.valueOf(id)));
        deliveryBatchDTO.setDriverId(Long.parseLong(String.valueOf(deliveryCarId)));
        if(deliveryBatchService.deliveryBatchValidator(deliveryBatchDTO.getDeliveryBatchId())){
            return TmsResult.fail(ErrorCodeEnum.DB_DATA_ERROR.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DB_DATA_ERROR, "已经完成排线"));
        }
        return deliveryBatchService.updatePathDriver(deliveryBatchDTO);
    }

    /**
     * 修改路线名称
     * pathName: 测试
     * path: A
     * storeNo: 1
     * deliveryTime: 2022-10-21
     * id: 561737
     */
    @PostMapping("/upsert/update-path-name")
    public TmsResult<Void> updatePathName(@RequestBody @Validated(value = {ValidationGroups.UpdatePathDriver.class}) DeliveryCarPath deliveryCarPath) {
        DeliveryBatchDTO deliveryBatchDTO = new DeliveryBatchDTO();
        deliveryBatchDTO.setDeliveryBatchId(Long.parseLong(String.valueOf(deliveryCarPath.getId())));
        deliveryBatchDTO.setPathCode(deliveryCarPath.getPath());
        deliveryBatchDTO.setPathName(deliveryCarPath.getPathName());
        if(deliveryBatchService.deliveryBatchValidator(deliveryBatchDTO.getDeliveryBatchId())){
            return TmsResult.fail(ErrorCodeEnum.DB_DATA_ERROR.code, ErrorCodeEnum.formatErrorMsg(ErrorCodeEnum.DB_DATA_ERROR, "已经完成排线"));
        }
        return deliveryBatchService.updatePathName(deliveryBatchDTO);
    }

    /**
     * 是否存在未拍店铺
     */
    @PostMapping("/query/is-have-no-path")
    public TmsResult<Boolean> isHaveNoPath(@RequestParam Integer storeNo, @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                           @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        TmsResult<SiteDTO> siteDTOResult = siteService.query(SiteQuery.builder()
                .type(TmsSiteTypeEnum.STORE.getCode())
                .outBusinessNo(String.valueOf(storeNo)).build());
        if (!siteDTOResult.isSuccess()) {
            return TmsResult.fail(siteDTOResult.getErrCode(), siteDTOResult.getErrorMessage());
        }
        SiteDTO siteDTO = siteDTOResult.getData();
        return deliveryBatchService.isHaveNoPath(siteDTO.getId(), endDate.plusDays(1));
    }


    /**
     * 更新预计公里数
     */
    @PostMapping("/upsert/batch-distance")
    public TmsResult<Void> updateBatchDistance(@RequestBody DeliveryBatchQuery deliveryBatchQuery) {
        return deliveryBatchService.updateBatchDistance(deliveryBatchQuery);
    }
    /**
     * 查询拣货单列表
     */
    @PostMapping("/query/pick-list")
    public TmsResult<PageInfo<DeliveryPickListVO>> queryPickList(@RequestBody DeliveryBatchQuery deliveryBatchQuery){
        return TmsResult.success(DeliveryBatchVoConverter.dtoPage2Vo(deliveryBatchService.queryCurrentDriverBatchPage(deliveryBatchQuery)));
    }

    /**
     * 获取拣货单详情
     */
    @PostMapping("/query/pick-detail")
    public TmsResult<List<DeliveryPickDetailVO>> queryPickDetail(@RequestBody DeliveryBatchQuery deliveryBatchQuery){
        TmsResult<DeliveryBatchDTO> batchDTOTmsResult = deliveryBatchService.queryPickDetail(deliveryBatchQuery);
        if(!batchDTOTmsResult.isSuccess()){
            return TmsResult.fail(batchDTOTmsResult.getErrCode(), batchDTOTmsResult.getErrorMessage());
        }
        return TmsResult.success(DeliveryBatchVoConverter.dtoPage2PickVo(batchDTOTmsResult));
    }

    /**
     * 配送单列表详情
     */
    @PostMapping("/query/delivery-site-list")
    public TmsResult<List<DeliverySiteVO>> queryDeliverySiteList(@RequestBody DeliveryBatchQuery deliveryBatchQuery){
        TmsResult<DeliveryBatchDTO> batchDtoTmsResult = deliveryBatchService.queryDeliverySiteList(deliveryBatchQuery);
        return TmsResult.success(DeliveryBatchVoConverter.dtoPage2DeliverySiteVo(batchDtoTmsResult));
    }

    /**
     * 是否已经完成排线
     */
    @PostMapping("/query/batch-status")
    public TmsResult<Boolean> queryBatchStatus(@RequestBody DeliveryBatchQuery deliveryBatchQuery){
        TmsAssert.notEmpty(deliveryBatchQuery.getStoreNo(), ErrorCodeEnum.PARAM_NOT_NULL, "storeNo");
        TmsAssert.notNull(deliveryBatchQuery.getDeliveryTime(), ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");

        TmsResult<SiteDTO> siteDTOResult = siteService.query(SiteQuery.builder()
                .type(TmsSiteTypeEnum.STORE.getCode())
                .outBusinessNo(deliveryBatchQuery.getStoreNo()).build());
        if (!siteDTOResult.isSuccess()) {
            return TmsResult.fail(siteDTOResult.getErrCode(), siteDTOResult.getErrorMessage());
        }
        deliveryBatchQuery.setBeginSiteId(siteDTOResult.getData().getId());
        deliveryBatchQuery.setDeliveryBatchType(DeliveryBatchTypeEnum.city.getCode());
        TmsResult<List<DeliveryBatchDTO>> deliveryBatchDTOTmsResult = deliveryBatchService.queryList(deliveryBatchQuery);
        if (!deliveryBatchDTOTmsResult.isSuccess()) {
            return TmsResult.fail(deliveryBatchDTOTmsResult.getErrCode(), deliveryBatchDTOTmsResult.getErrorMessage());
        }
        List<DeliveryBatchDTO> deliveryBatchDTOList = deliveryBatchDTOTmsResult.getData();
        long count = deliveryBatchDTOList.stream()
                .filter(batch -> !Objects.equals(batch.getStatus(), DeliveryBatchStatusEnum.TO_BE_WIRED.getCode()))
                .count();
        return TmsResult.success(count > 0);
    }

    /**
     * 路线智能排线
     */
    @PostMapping("/upsert/intelligent-path")
    public TmsResult<Void> intelligentPath(@RequestParam(required = true) Long batchId) throws InterruptedException {
        RLock redissonLock = redissonClient.getLock((RedisConstants.Delivery.INTELLIGENT_PATH + batchId + ""));
        if (!redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS)) {
            throw new TmsRuntimeException("正在处理，请稍后");
        }
        try {
            // 根据批次查询城配仓信息
            Integer storeNo = deliveryBatchService.queryStoreNoById(batchId);
            deliveryBatchService.intelligentPath(IntelligentPathUpdateCommand.builder()
                    .batchId(batchId)
                    .storeNo(storeNo)
                    .build());
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 手动生成拣货任务，排除大客户加工的
     */
    @PostMapping("/upsert/complete-path-pick-self")
    public TmsResult<Void> completePathPickBySelf(@RequestBody List<Long> batchIds){
        for (Long batchId : batchIds) {
            deliveryBatchService.completePathBySelf(batchId);
        }
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 初始化点位存储条件
     * @param batchIds 批次ID集合
     */
    @PostMapping("/upsert/init-site-temperature_conditions")
    public TmsResult<Void> initSiteTemperatureConditions(@RequestBody List<Long> batchIds){
        TmsAssert.notEmpty(batchIds, ErrorCodeEnum.PARAM_NOT_NULL, "batchIds");
        log.info("初始化点位存储条件接受参数:{}", JSON.toJSONString(batchIds));
        deliverySiteService.siteTemConditionsCreate(batchIds);
        log.info("初始化点位存储条件执行成功:{}", JSON.toJSONString(batchIds));
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 查询配送时效
     */
    @PostMapping("/query/delivery-ageing")
    public TmsResult<List<DeliveryAgeingVO>> queryDeliveryAgeing(@RequestBody DeliveryPathQuery deliveryPathQuery){
        //验证参数
        TmsAssert.notNull(deliveryPathQuery.getEndDate(), ErrorCodeEnum.PARAM_NOT_NULL, "endDate");
        TmsAssert.notNull(deliveryPathQuery.getStoreNo(), ErrorCodeEnum.PARAM_NOT_NULL, "storeNo");
        deliveryPathQuery.setDeliveryTime(deliveryPathQuery.getEndDate().plusDays(1));
        deliveryPathQuery.setDeliveryBatchType(DeliveryBatchTypeEnum.city.getCode());

        List<DeliverySiteDTO> deliverySiteDTOS = deliveryBatchService.queryDeliveryAgeing(deliveryPathQuery);
        if(CollectionUtils.isEmpty(deliverySiteDTOS)){
            return TmsResult.success(Collections.emptyList());
        }
        return TmsResult.success(deliverySiteDTOS.stream().map(DeliverySiteVoConverter::delSiteDTO2DelAgeingVO).collect(Collectors.toList()));
    }

    /**
     * 蚁群算法后门接口
     */
    @PostMapping("/upsert/ant-algorithm-calc-distance")
    public TmsResult<Void> antAlgorithmCalcDistance(@RequestBody List<Long> batchIds){
        if(CollectionUtils.isEmpty(batchIds)){
            throw new TmsRuntimeException("batchIds不能为空");
        }
        for (Long batchId : batchIds) {
            deliveryBatchCommandService.batchAntAlgorithmCalcDistance(batchId);
        }
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 蚁群算法后门接口
     */
    @PostMapping("/upsert/ant-algorithm-calc-distance-process")
    public TmsResult<Void> antAlgorithmCalcDistance(@RequestBody String str){
        if(StringUtils.isBlank(str)){
            throw new TmsRuntimeException("str不能为空");
        }
        CalcTmsPathDistanceMessage msg = null;
        try {
            msg = JSON.parseObject(str, CalcTmsPathDistanceMessage.class);
        } catch (Exception e) {
            throw new TmsRuntimeException("str格式不正确");
        }
        //只有完成排线才走蚁群算法
        if(!Objects.equals(msg.getType(), DeliverySectionEnums.Type.complete_path)){
            throw new TmsRuntimeException("类型不是完成排线");
        }
        if (CollectionUtils.isEmpty(msg.getWaypointsInputList())) {
            throw new TmsRuntimeException("路段信息为空,请检查");
        }
        if (Objects.isNull(msg.getBatchId())) {
            throw new TmsRuntimeException("批次id信息为空请检查");
        }
        deliveryBatchCommandService.antAlgorithmCalcDistance(msg);
        return TmsResult.VOID_SUCCESS;
    }
}
