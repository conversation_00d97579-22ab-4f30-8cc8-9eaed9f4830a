package net.summerfarm.tms.controller.performance.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/6/26 18:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PerformanceReviewTaskVO {

    private Long id;

    /**
     * 审核类型 0城配-出仓、1城配-签收、2干线-签收
     */
    private Integer reviewTaskType;

    /**
     * 名称
     */
    private String name;

    /**
     * 判罚标准
     */
    private BigDecimal penaltyStandards;
    /**
     * 任务条件-（调度类型、城配仓）
     */
    private String taskConditionType;

    /**
     * 任务条件-（履约日期）
     */
    private String taskConditionDeliveryTime;

    /**
     * 状态0审核中、1审核完成、2已关闭
     */
    private Integer state;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建人时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 城配点位集合
     */
    private List<PerformanceBeginSiteVO> performanceBeginSiteVOS;


    /**
     * 审核模式
     * 0-人工审核，1-AI审核
     */
    private Integer reviewMode;
}
