package net.summerfarm.tms.controller.base.distance;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.distance.OutDistanceService;
import net.summerfarm.tms.base.distance.dto.OutDistanceConfigSaveCommand;
import net.summerfarm.tms.base.distance.dto.OutDistanceConfigUpdateCommand;
import net.summerfarm.tms.base.distance.dto.OutDistanceConfigVO;
import net.summerfarm.tms.aspect.TmsDataPermission;
import net.summerfarm.tms.query.base.distance.OutDistanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 签收围栏配置
 */
@RestController
@RequestMapping("/tms-new/outDistance")
public class OutDistanceConfigController {

    @Autowired
    private OutDistanceService outDistanceService;

    /**
     * 分页查询
     */
    @PostMapping("/query/page")
    @TmsDataPermission
    public TmsResult<PageInfo<OutDistanceConfigVO>> queryPage(@RequestBody OutDistanceQuery outDistanceQuery) {
        return TmsResult.success(outDistanceService.queryPage(outDistanceQuery));
    }

    /**
     * 新增
     */
    @PostMapping("/upsert/save")
    @TmsDataPermission
    public TmsResult<Void> save(@RequestBody @Validated OutDistanceConfigSaveCommand outDistanceConfigSaveCommand) {
        outDistanceService.save(outDistanceConfigSaveCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 更新
     */
    @PostMapping("/upsert/update")
    public TmsResult<Void> update(@RequestBody @Validated OutDistanceConfigUpdateCommand outDistanceConfigUpdateCommand) {
        outDistanceService.update(outDistanceConfigUpdateCommand);
        return TmsResult.VOID_SUCCESS;
    }
}
