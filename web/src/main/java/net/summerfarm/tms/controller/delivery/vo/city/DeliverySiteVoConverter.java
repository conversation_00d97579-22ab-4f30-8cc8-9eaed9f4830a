package net.summerfarm.tms.controller.delivery.vo.city;

import cn.hutool.core.util.StrUtil;
import jodd.util.StringUtil;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.delivery.dto.cheakinpunch.DeliverySiteCheckinPunchDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.dist.dto.DistOrderItemDTO;
import net.summerfarm.tms.enums.*;
import org.apache.commons.compress.utils.Lists;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/1/5 13:38<br/>
 *
 * <AUTHOR> />
 */
public class DeliverySiteVoConverter {

    public static UpdateSiteBatchVO dto2UpdateSiteBatchVO(DeliverySiteDTO deliverySiteDTO) {
        if (deliverySiteDTO == null) {
            return null;
        }
        UpdateSiteBatchVO updateSiteBatchVO = new UpdateSiteBatchVO();

        updateSiteBatchVO.setPathCode(deliverySiteDTO.getDeliveryBatchDTO() != null ? deliverySiteDTO.getDeliveryBatchDTO().getPathCode() : null);
        updateSiteBatchVO.setSort(deliverySiteDTO.getSequence());
        updateSiteBatchVO.setMname(deliverySiteDTO.getOuterClientName());
        updateSiteBatchVO.setAddress(deliverySiteDTO.getSiteDTO() != null ? deliverySiteDTO.getSiteDTO().getFullAddress() : null);

        return updateSiteBatchVO;
    }

    public static DeliverySiteDetailVO dto2DeliverySiteDetailVo(TmsResult<DeliverySiteDTO> deliverySiteDTOTmsResult) {
        DeliverySiteDTO deliverySiteDTO = deliverySiteDTOTmsResult.getData();
        //批次信息
        DeliveryBatchDTO deliveryBatchDTO = deliverySiteDTO.getDeliveryBatchDTO();
        Integer status = deliveryBatchDTO.getStatus();
        //点位信息
        SiteDTO siteDTO = deliverySiteDTO.getSiteDTO();
        //委托单信息
        List<DistOrderDTO> distOrderDTOS = deliverySiteDTO.getDistOrderDTOS();
        List<DistOrderItemDTO> distOrderItemDTOList = distOrderDTOS.stream().map(DistOrderDTO::getSkuList).flatMap(Collection::stream).collect(Collectors.toList());
        Map<String, List<DistOrderItemDTO>> itemMap = distOrderItemDTOList.stream().collect(Collectors.groupingBy(DistOrderItemDTO::getOuterItemId));

        //点位配送详情信息
        List<DeliverySiteItemDTO> deliverySiteItemDTOList = deliverySiteDTO.getDeliverySiteItemDTOList();

        DeliverySiteDetailVO deliverySiteDetailVO = new DeliverySiteDetailVO();
        deliverySiteDetailVO.setAddress(siteDTO.getFullAddress());
        deliverySiteDetailVO.setDeliveryPic(deliverySiteDTO.getSignInPic1());
        deliverySiteDetailVO.setDeliveryTime(deliverySiteDTO.getPlanArriveTime().toLocalDate());
        deliverySiteDetailVO.setFinishDeliveryTime(deliverySiteDTO.getSignInTime());
        deliverySiteDetailVO.setFinishDistance(deliverySiteDTO.getSignInDiffKm() == null ? 0 : deliverySiteDTO.getSignInDiffKm().intValue());
        deliverySiteDetailVO.setFinishPoi(deliverySiteDTO.getSignInPoi());
        deliverySiteDetailVO.setDeliverySiteId(deliverySiteDTO.getId());
        deliverySiteDetailVO.setMName(deliverySiteDTO.getOuterClientName());
        deliverySiteDetailVO.setMId(!CollectionUtils.isEmpty(distOrderDTOS) ? distOrderDTOS.get(0).getOutClientId() : "");
        deliverySiteDetailVO.setStoreNo(deliverySiteDTO.getDeliveryBatchDTO().getBeginSiteDto().getOutBusinessNo());
        deliverySiteDetailVO.setPickUpTime(deliveryBatchDTO.getPickUpTime());
        deliverySiteDetailVO.setProductPic(deliverySiteDTO.getSignInPic3());
        deliverySiteDetailVO.setOutPic(deliverySiteDTO.getOutPic());
        deliverySiteDetailVO.setOutReason(deliverySiteDTO.getOutReason());
        deliverySiteDetailVO.setOutReasonType(deliverySiteDTO.getOutReasonType());
        deliverySiteDetailVO.setOuterBrandName(deliverySiteDTO.getOuterBrandName());

        List<Integer> sourceList = distOrderDTOS.stream().map(DistOrderDTO::getSource).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(sourceList)){
            for (Integer source : sourceList) {
                if(!DistOrderSourceEnum.getXmOrderTypeCode().contains(source)){
                    deliverySiteDetailVO.setSaasFlag(true);
                    break;
                }
            }
        }
//        deliverySiteDetailVO.setDeliverySitePropertyJson(deliverySiteDTO.getDeliverySitePropertyJson());
        deliverySiteDetailVO.setSignPic(deliverySiteDTO.getSignInPic2());
        deliverySiteDetailVO.setRecentHeadPic(deliverySiteDTO.getRecentHeadPic());
        List<OrderItem> orderItemList = new ArrayList<>();
        for (DeliverySiteItemDTO deliverySiteItemDTO : deliverySiteItemDTOList) {
            OrderItem orderItem = new OrderItem();

            orderItem.setDeliverySiteItemId(deliverySiteItemDTO.getId());
            orderItem.setAmount(deliverySiteItemDTO.getPlanReceiptCount());
            orderItem.setCodeAmount(deliverySiteItemDTO.getScanCount());
            orderItem.setInterceptNum(deliverySiteItemDTO.getInterceptCount());
            orderItem.setDeliveryType(deliverySiteItemDTO.getType());
            orderItem.setPdName(deliverySiteItemDTO.getOutItemName());
            orderItem.setShortCnt(deliverySiteItemDTO.getShortCount());
            orderItem.setSku(deliverySiteItemDTO.getOutItemId());
            orderItem.setSkuPic(deliverySiteItemDTO.getSkuPic());
            orderItem.setWithOutQuantity(deliverySiteItemDTO.getNoscanCount());
            orderItem.setWithOutRemake(deliverySiteItemDTO.getNoscanReason());
            orderItem.setWithOutPictures(deliverySiteItemDTO.getNoscanPics());
            orderItem.setSkuType(deliverySiteItemDTO.getSkuType());
            orderItem.setFruitsType(deliverySiteItemDTO.getOutItemType());
            orderItem.setExtType(deliverySiteItemDTO.getExtType());
            if (itemMap.get(deliverySiteItemDTO.getOutItemId()) != null) {
                orderItem.setUnit(itemMap.get(deliverySiteItemDTO.getOutItemId()).get(0).getUnit());
                orderItem.setSpecification(itemMap.get(deliverySiteItemDTO.getOutItemId()).get(0).getSpecification());
            }
            orderItem.setRemark(deliverySiteItemDTO.getRemark());
            orderItem.setNameRemakes(deliverySiteItemDTO.getNameRemakes());
            orderItem.setStorageArea(deliverySiteItemDTO.getStorageArea());
            Integer packType = deliverySiteItemDTO.getPackType();
            orderItem.setSkuOutOrderFlag(Objects.equals(DistItemEnums.PackType.PACKAGE.getValue(),packType));
            //加工信息
            List<DeliverySiteProcessItemDTO> deliverySiteProcessItemDTOList = deliverySiteItemDTO.getDeliverySiteProcessItemDTOList();
            if (!CollectionUtils.isEmpty(deliverySiteProcessItemDTOList)) {
                List<ProcessItemVO> processItems = new ArrayList<>();
                for (DeliverySiteProcessItemDTO deliverySiteProcessItemDTO : deliverySiteProcessItemDTOList) {
                    if (deliverySiteProcessItemDTO.getProcessFlag() == 0) {
                        ProcessItemVO processItemVO = new ProcessItemVO();
                        processItemVO.setQuantity(deliverySiteProcessItemDTO.getQuantity());
                        processItemVO.setWeight(deliverySiteProcessItemDTO.getWeight());
                        processItemVO.setUnit(deliverySiteProcessItemDTO.getUnit());
                        processItems.add(processItemVO);
                    } else {
                        ProcessItemVO processItemVO = new ProcessItemVO();
                        processItemVO.setQuantity(deliverySiteProcessItemDTO.getQuantity());
                        processItemVO.setWeight(itemMap.get(deliverySiteItemDTO.getOutItemId()).get(0).getSpecification());
                        processItemVO.setUnit(itemMap.get(deliverySiteItemDTO.getOutItemId()).get(0).getUnit());
                        processItems.add(processItemVO);
                    }
                }
                orderItem.setProcessItems(processItems);
            }
            //回收信息
            //完成排线会落配送点位物品表 而配送点位物品状态默认正常 导致刚完成排线查询配送详情时会默认展示状态正常 此时应该没有这个状态处理一下为null
            DeliverySiteItemEnums.Status itemStatus = DeliverySiteItemEnums.Status.getStatusByValue(deliverySiteItemDTO.getStatus());
            boolean isFinishDelivery = Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
            orderItem.setStatus(isFinishDelivery ? itemStatus.getValue() : null);
            orderItem.setStatusDesc(isFinishDelivery ? itemStatus.getContent() : null);
            //历史回收异常数据兼容处理
            if (Objects.equals(deliverySiteItemDTO.getType(), DeliverySiteItemTypeEnum.RECYCLE.getCode())) {
                if (deliverySiteItemDTO.getDeliverySiteRecycleDTO() == null) {
                    DeliverySiteItemRecycleDTO deliverySiteRecycleDTO = new DeliverySiteItemRecycleDTO();
                    if (StrUtil.isBlank(deliverySiteItemDTO.getRemark())){
                        deliverySiteRecycleDTO.setSpecificationQuantity(BigDecimal.valueOf(deliverySiteItemDTO.getPlanReceiptCount()));
                        deliverySiteRecycleDTO.setSpecificationUnit(itemMap.get(deliverySiteItemDTO.getOutItemId()).get(0).getUnit());
                    }else {
                        deliverySiteRecycleDTO.setRemark(deliverySiteItemDTO.getRemark());
                    }
                    deliverySiteItemDTO.setDeliverySiteRecycleDTO(deliverySiteRecycleDTO);
                }
            }
            orderItem.setDeliverySiteItemRecycleDTO(deliverySiteItemDTO.getDeliverySiteRecycleDTO());
            orderItem.setBarcodes(deliverySiteItemDTO.getBarcodes());
            orderItem.setPickShortCnt(deliverySiteItemDTO.getPickShortCount());
            orderItemList.add(orderItem);
        }
        deliverySiteDetailVO.setOrderItemList(orderItemList);

        deliverySiteDetailVO.setOrderNo(distOrderDTOS.stream().map(DistOrderDTO::getOutOrderId).collect(Collectors.joining(",")));
        deliverySiteDetailVO.setPath(deliveryBatchDTO.getPathCode());
        if (Objects.equals(DeliverySiteStatusEnum.NO.getCode(), deliverySiteDTO.getStatus())) {
            deliverySiteDetailVO.setPathStatus(0);
        }
        if (status >= DeliveryBatchStatusEnum.IN_DELIVERY.getCode() &&
                !Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode()) &&
                !Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_PICK.getCode())) {
            deliverySiteDetailVO.setPathStatus(1);
        }
        if (Objects.equals(DeliverySiteStatusEnum.FINISH_PICK.getCode(), deliverySiteDTO.getStatus())) {
            deliverySiteDetailVO.setPathStatus(1);
        }
        if (Objects.equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode(), deliverySiteDTO.getStatus())) {
            deliverySiteDetailVO.setPathStatus(2);
        }
        deliverySiteDetailVO.setPhone(siteDTO.getPhone());
        deliverySiteDetailVO.setPoiNote(siteDTO.getPoi());
        deliverySiteDetailVO.setRemark(distOrderDTOS.stream().map(DistOrderDTO::getCloseReason).filter(StrUtil::isNotBlank).collect(Collectors.joining(",")));
        deliverySiteDetailVO.setSignForRemarks(deliverySiteDTO.getSignInRemark());
        deliverySiteDetailVO.setSignForStatus(deliverySiteDTO.getSignInStatus());
        deliverySiteDetailVO.setSort(deliverySiteDTO.getSequence());
        deliverySiteDetailVO.setTimeFrame(deliverySiteDTO.getTimeFrame());
        deliverySiteDetailVO.setBdName(deliverySiteDTO.getBdName());
        deliverySiteDetailVO.setBdPhone(deliverySiteDTO.getBdPhone());
        deliverySiteDetailVO.setContactId(!CollectionUtils.isEmpty(distOrderDTOS) ? distOrderDTOS.get(0).getOutContactId() : "");
        deliverySiteDetailVO.setContactStatus(deliverySiteDTO.getContactStatus());
        deliverySiteDetailVO.setPlanOutTime(deliverySiteDTO.getPlanOutTime());
        deliverySiteDetailVO.setVehiclePlatePics(deliverySiteDTO.getVehiclePlatePics());
        deliverySiteDetailVO.setSealPics(deliverySiteDTO.getSealPics());
        ExpenseDTO expenseDTO = deliverySiteDTO.getExpenseDTO();
        if (expenseDTO != null) {
            deliverySiteDetailVO.setExpenseId(expenseDTO.getId());
            deliverySiteDetailVO.setExpenseStatus(expenseDTO.getStatus());
        }
        deliverySiteDetailVO.setSendRemark(deliverySiteDTO.getSendRemark());
        deliverySiteDetailVO.setScanCodeFlag(deliverySiteDTO.getScanCodeFlag() == null || deliverySiteDTO.getScanCodeFlag());

        deliverySiteDetailVO.setCheckinPunchFlag(deliverySiteDTO.getCheckinPunchFlag());
        deliverySiteDetailVO.setPunchRange(deliverySiteDTO.getPunchRange());
        if(deliverySiteDTO.getCheckinPunchDTO() != null){
            DeliverySiteCheckinPunchDTO checkinPunchDTO = deliverySiteDTO.getCheckinPunchDTO();

            CheakInPunchRecordVO cheakInPunchRecordVO = new CheakInPunchRecordVO();
            cheakInPunchRecordVO.setPunchTime(checkinPunchDTO.getCreateTime());
            cheakInPunchRecordVO.setDeliverySiteId(checkinPunchDTO.getDeliverySiteId());
            cheakInPunchRecordVO.setPunchRange(checkinPunchDTO.getPunchRange());
            cheakInPunchRecordVO.setPunchAddress(checkinPunchDTO.getPunchAddress());
            cheakInPunchRecordVO.setPunchPoi(checkinPunchDTO.getPunchPoi());
            cheakInPunchRecordVO.setDistanceToStore(checkinPunchDTO.getDistanceToSite());
            cheakInPunchRecordVO.setExceedReason(checkinPunchDTO.getExceedReason());
            deliverySiteDetailVO.setCheakInPunchRecordVO(cheakInPunchRecordVO);
        }
        deliverySiteDetailVO.setPickLackFlag(deliverySiteDTO.getPickLackFlag());
        return deliverySiteDetailVO;
    }

    public static DeliverySiteDetailPcVO dto2DeliverySiteDetailPcVo(TmsResult<DeliverySiteDTO> deliverySiteDTOTmsResult) {
        DeliverySiteDTO deliverySiteDTO = deliverySiteDTOTmsResult.getData();
        DeliverySiteDTO storeSiteDTO = deliverySiteDTO.getStoreSiteDTO();
        //批次信息
        DeliveryBatchDTO deliveryBatchDTO = deliverySiteDTO.getDeliveryBatchDTO();
        Integer status = deliveryBatchDTO.getStatus();
        //点位信息
        SiteDTO siteDTO = deliverySiteDTO.getSiteDTO();
        //委托单信息
        List<DistOrderDTO> distOrderDTOS = deliverySiteDTO.getDistOrderDTOS();
        List<DistOrderItemDTO> distOrderItemDTOList = distOrderDTOS.stream().map(DistOrderDTO::getSkuList).flatMap(Collection::stream).collect(Collectors.toList());
        Map<String, List<DistOrderItemDTO>> itemMap = distOrderItemDTOList.stream().collect(Collectors.groupingBy(DistOrderItemDTO::getOuterItemId));
        //点位配送详情信息
        List<DeliverySiteItemDTO> deliverySiteItemDTOList = deliverySiteDTO.getDeliverySiteItemDTOList();

        DeliverySiteDetailPcVO deliverySiteDetailPcVO = new DeliverySiteDetailPcVO();

        deliverySiteDetailPcVO.setOrderNo(distOrderDTOS.stream().map(DistOrderDTO::getOutOrderId).collect(Collectors.joining(",")));
        if (Objects.equals(DeliverySiteStatusEnum.NO.getCode(), deliverySiteDTO.getStatus())) {
            deliverySiteDetailPcVO.setPathStatus(0);
        }
        if (status >= DeliveryBatchStatusEnum.IN_DELIVERY.getCode() &&
                !Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode()) &&
                !Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_PICK.getCode())) {
            deliverySiteDetailPcVO.setPathStatus(1);
        }
        if (Objects.equals(DeliverySiteStatusEnum.FINISH_PICK.getCode(), deliverySiteDTO.getStatus())) {
            deliverySiteDetailPcVO.setPathStatus(1);
        }
        if (Objects.equals(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode(), deliverySiteDTO.getStatus())) {
            deliverySiteDetailPcVO.setPathStatus(2);
        }
        deliverySiteDetailPcVO.setTimeFrame(deliverySiteDTO.getTimeFrame());
        deliverySiteDetailPcVO.setDriverName(deliveryBatchDTO.getDriver());
        deliverySiteDetailPcVO.setDriverPhone(deliveryBatchDTO.getDriverPhone());
        deliverySiteDetailPcVO.setSort(deliverySiteDTO.getSequence());
        deliverySiteDetailPcVO.setPath(deliveryBatchDTO.getPathCode());
        deliverySiteDetailPcVO.setAddress(siteDTO.getFullAddress());
        deliverySiteDetailPcVO.setSignInAddress(deliverySiteDTO.getSignInAddress());
        deliverySiteDetailPcVO.setSignInDistance(deliverySiteDTO.getSignInDistance());
        deliverySiteDetailPcVO.setOutDistance(deliverySiteDTO.getOutDistance() == null ? 0 : deliverySiteDTO.getOutDistance());
        deliverySiteDetailPcVO.setOutRemark(deliverySiteDTO.getOutReason());
        deliverySiteDetailPcVO.setDeliveryPic(deliverySiteDTO.getSignInPic1());
        deliverySiteDetailPcVO.setSignForRemarks(deliverySiteDTO.getSignInRemark());
        deliverySiteDetailPcVO.setSignForStatus(deliverySiteDTO.getSignInStatus());
        deliverySiteDetailPcVO.setDeliveryTime(deliverySiteDTO.getPlanArriveTime().toLocalDate());
        deliverySiteDetailPcVO.setMName(deliverySiteDTO.getOuterClientName());
        deliverySiteDetailPcVO.setFinishPoi(deliverySiteDTO.getSignInPoi());
        deliverySiteDetailPcVO.setFinishDistance(deliverySiteDTO.getSignInDistance() != null ? deliverySiteDTO.getSignInDistance().intValue() : null);
        deliverySiteDetailPcVO.setSourceDescs(distOrderDTOS.stream().map(DistOrderDTO::getSourceDesc).distinct().collect(Collectors.joining(";")));
        deliverySiteDetailPcVO.setSignPic(deliverySiteDTO.getSignInPic2());
        deliverySiteDetailPcVO.setProductPic(deliverySiteDTO.getSignInPic3());
        deliverySiteDetailPcVO.setOutReasonType(deliverySiteDTO.getOutReasonType());
        deliverySiteDetailPcVO.setOutPic(deliverySiteDTO.getOutPic());
        deliverySiteDetailPcVO.setSendRemark(deliverySiteDTO.getSendRemark());
//        deliverySiteDetailPcVO.setDeliverySitePropertyJson(deliverySiteDTO.getDeliverySitePropertyJson());
        deliverySiteDetailPcVO.setVehiclePlatePics(deliverySiteDTO.getVehiclePlatePics());
        deliverySiteDetailPcVO.setSealPics(deliverySiteDTO.getSealPics());
        List<OrderItemPc> orderItemList = new ArrayList<>();
        for (DeliverySiteItemDTO deliverySiteItemDTO : deliverySiteItemDTOList) {
            OrderItemPc orderItem = new OrderItemPc();

            orderItem.setAmount(deliverySiteItemDTO.getPlanReceiptCount());
            orderItem.setCodeAmount(deliverySiteItemDTO.getScanCount());
            orderItem.setInterceptNum(deliverySiteItemDTO.getInterceptCount());
            orderItem.setDeliveryType(deliverySiteItemDTO.getType());
            orderItem.setPdName(deliverySiteItemDTO.getOutItemName());
            orderItem.setShortCnt(deliverySiteItemDTO.getShortCount());
            orderItem.setSku(deliverySiteItemDTO.getOutItemId());
            orderItem.setWithOutQuantity(deliverySiteItemDTO.getNoscanCount());
            orderItem.setWithOutRemake(deliverySiteItemDTO.getNoscanReason());
            orderItem.setWithOutPictures(deliverySiteItemDTO.getNoscanPics());
            if (itemMap.get(deliverySiteItemDTO.getOutItemId()) != null) {
                orderItem.setUnit(itemMap.get(deliverySiteItemDTO.getOutItemId()).get(0).getUnit());
                orderItem.setSpecification(itemMap.get(deliverySiteItemDTO.getOutItemId()).get(0).getSpecification());
            }
            orderItem.setRemark(deliverySiteItemDTO.getRemark());

            List<DeliverySiteItemCodeDTO> deliverySiteItemCodeDTOS = deliverySiteItemDTO.getDeliverySiteItemCodeDTOS();
            if (!CollectionUtils.isEmpty(deliverySiteItemCodeDTOS)) {
                orderItem.setCodes(deliverySiteItemCodeDTOS.stream().map(DeliverySiteItemCodeDTO::getOnlyCode).collect(Collectors.joining(",")));
            }
            orderItem.setBatchs(deliverySiteItemDTO.getBatchs());
            orderItem.setStorageArea(deliverySiteItemDTO.getStorageArea());
            //回收信息
            //完成排线会落配送点位物品表 而配送点位物品状态默认正常 导致刚完成排线查询配送详情时会默认展示状态正常 此时应该没有这个状态处理一下为null
            DeliverySiteItemEnums.Status itemStatus = DeliverySiteItemEnums.Status.getStatusByValue(deliverySiteItemDTO.getStatus());
            boolean isFinishDelivery = Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
            orderItem.setStatus(isFinishDelivery ? itemStatus.getValue() : null);
            orderItem.setStatusDesc(isFinishDelivery ? itemStatus.getContent() : null);
            //历史回收异常数据兼容处理
            if (Objects.equals(deliverySiteItemDTO.getType(), DeliverySiteItemTypeEnum.RECYCLE.getCode())) {
                if (deliverySiteItemDTO.getDeliverySiteRecycleDTO() == null) {
                    DeliverySiteItemRecycleDTO deliverySiteRecycleDTO = new DeliverySiteItemRecycleDTO();
                    if (StrUtil.isBlank(deliverySiteItemDTO.getRemark())){
                        deliverySiteRecycleDTO.setSpecificationQuantity(BigDecimal.valueOf(deliverySiteItemDTO.getPlanReceiptCount()));
                        deliverySiteRecycleDTO.setSpecificationUnit(itemMap.get(deliverySiteItemDTO.getOutItemId()).get(0).getUnit());
                    }else {
                        deliverySiteRecycleDTO.setRemark(deliverySiteItemDTO.getRemark());
                    }
                    deliverySiteItemDTO.setDeliverySiteRecycleDTO(deliverySiteRecycleDTO);
                }
            }
            orderItem.setDeliverySiteItemRecycleDTO(deliverySiteItemDTO.getDeliverySiteRecycleDTO());
            orderItem.setBarcodes(deliverySiteItemDTO.getBarcodes());
            orderItemList.add(orderItem);
        }
        deliverySiteDetailPcVO.setOrderItemList(orderItemList);

        List<OperateRecord> operateRecords = new ArrayList();
        if (deliveryBatchDTO.getBePathTime() != null) {
            OperateRecord operateRecord = new OperateRecord();
            operateRecord.setTypeName("任务生成");
            operateRecord.setTime(deliveryBatchDTO.getBePathTime());
            operateRecord.setOperator(deliveryBatchDTO.getCreator());
            operateRecords.add(operateRecord);
        }
        if (storeSiteDTO.getSignInTime() != null) {
            OperateRecord operateRecord = new OperateRecord();
            operateRecord.setTypeName("到仓打卡");
            operateRecord.setTime(storeSiteDTO.getSignInTime());
            operateRecord.setOperator(deliveryBatchDTO.getDriver());
            operateRecord.setRemark(new StringBuilder()
                    .append("打卡地址:")
                    .append(storeSiteDTO.getSignInAddress())
                    .append(",距离:")
                    .append(storeSiteDTO.getSignInDistance())
                    .toString()
            );
            operateRecords.add(operateRecord);

            OperateRecord operateRecordPick = new OperateRecord();
            operateRecordPick.setTypeName("开始拣货");
            operateRecordPick.setTime(storeSiteDTO.getSignInTime());
            operateRecordPick.setOperator(deliveryBatchDTO.getDriver());
            operateRecords.add(operateRecordPick);
        }
        if (deliveryBatchDTO.getPickUpTime() != null) {
            OperateRecord operateRecord = new OperateRecord();
            operateRecord.setTypeName("完成捡货");
            operateRecord.setTime(deliveryBatchDTO.getPickUpTime());
            operateRecord.setOperator(deliveryBatchDTO.getDriver());
            operateRecords.add(operateRecord);

            OperateRecord operateRecordSend = new OperateRecord();
            operateRecordSend.setTypeName("开始配送");
            operateRecordSend.setTime(deliveryBatchDTO.getPickUpTime());
            operateRecordSend.setOperator(deliveryBatchDTO.getDriver());
            operateRecords.add(operateRecordSend);
        }
        if(deliverySiteDTO.getCheckinPunchDTO() != null){
            OperateRecord operateRecord = new OperateRecord();
            operateRecord.setTypeName("到店打卡");
            operateRecord.setTime(deliverySiteDTO.getCheckinPunchDTO().getCreateTime());
            operateRecord.setRemark("打卡地址:"+deliverySiteDTO.getCheckinPunchDTO().getPunchAddress()+",距离:"+deliverySiteDTO.getCheckinPunchDTO().getDistanceToSite());
            operateRecord.setOperator(deliveryBatchDTO.getDriver());
            operateRecords.add(operateRecord);
        }
        if (Objects.equals(deliverySiteDTO.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())) {
            OperateRecord operateRecord = new OperateRecord();
            operateRecord.setTypeName("配送完成");
            operateRecord.setTime(deliverySiteDTO.getSignInTime());
            operateRecord.setOperator(deliveryBatchDTO.getDriver());
            operateRecords.add(operateRecord);
        }
        if(deliverySiteDTO.getCheckinPunchDTO() != null){
            DeliverySiteCheckinPunchDTO checkinPunchDTO = deliverySiteDTO.getCheckinPunchDTO();
            CheakInPunchRecordVO cheakInPunchRecordVO = new CheakInPunchRecordVO();
            cheakInPunchRecordVO.setPunchTime(checkinPunchDTO.getCreateTime());
            cheakInPunchRecordVO.setDeliverySiteId(checkinPunchDTO.getDeliverySiteId());
            cheakInPunchRecordVO.setPunchRange(checkinPunchDTO.getPunchRange());
            cheakInPunchRecordVO.setPunchAddress(checkinPunchDTO.getPunchAddress());
            cheakInPunchRecordVO.setPunchPoi(checkinPunchDTO.getPunchPoi());
            cheakInPunchRecordVO.setDistanceToStore(checkinPunchDTO.getDistanceToSite());
            cheakInPunchRecordVO.setExceedReason(checkinPunchDTO.getExceedReason());
            deliverySiteDetailPcVO.setCheakInPunchRecordVO(cheakInPunchRecordVO);
        }
        deliverySiteDetailPcVO.setOperateRecords(operateRecords);
        return deliverySiteDetailPcVO;
    }

    public static DriverAppraiseVO driverAppraiseDTO2VO(TmsDriverAppraiseDTO tmsDriverAppraiseDTO) {
        if(tmsDriverAppraiseDTO == null){
            return null;
        }
        DriverAppraiseVO driverAppraiseVO = new DriverAppraiseVO();
        driverAppraiseVO.setSatisfactionLevel(tmsDriverAppraiseDTO.getSatisfactionLevel());
        driverAppraiseVO.setTag(tmsDriverAppraiseDTO.getTag());
        driverAppraiseVO.setRemark(tmsDriverAppraiseDTO.getRemark());
        driverAppraiseVO.setOperatorAccountId(tmsDriverAppraiseDTO.getOperatorAccountId());
        driverAppraiseVO.setOperator(tmsDriverAppraiseDTO.getOperator());
        driverAppraiseVO.setOperatorPhone(tmsDriverAppraiseDTO.getOperatorPhone());
        driverAppraiseVO.setOrderNo(tmsDriverAppraiseDTO.getOrderNo());

        return driverAppraiseVO;
    }

    public static DeliveryAgeingVO delSiteDTO2DelAgeingVO(DeliverySiteDTO deliverySiteDTO){
        if(deliverySiteDTO == null){
            return null;
        }
        DeliveryAgeingVO deliveryAgeingVO = new DeliveryAgeingVO();
        deliveryAgeingVO.setId(deliverySiteDTO.getId());
        deliveryAgeingVO.setTimeFrame(deliverySiteDTO.getTimeFrame());
        return deliveryAgeingVO;
    }
}
